package com.ruoyi.instruction.domain;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

import java.util.Date;

/**
 * 风险排查详情-工作进展对象 t_risk_detail_work
 *
 * <AUTHOR>
 * @date 2024-01-16
 */
@Data
public class RiskDetailWork extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键id */
    private Long id;

    /** 记录 */
    @Excel(name = "记录")
    private String record;

    /** 附件ids */
    @Excel(name = "附件ids")
    private String fileIds;

    /** 状态 1：正常 9：删除 */
    @Excel(name = "状态 1：正常 9：删除")
    private String status;

    /** 风险排查详情id */
    @Excel(name = "风险排查详情id")
    private Long riskDetailId;

    /** 开展类别 */
    @Excel(name = "开展类别")
    private String category;

    /** 创建时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date createTime;

    /**
     * 重写父类的 getUpdateTime 方法，
     * 以便通过 @JsonFormat 注解，
     * 将其返回给前端的JSON格式修改为 "yyyy-MM-dd"
     */
    @Override
    @JsonFormat(pattern = "yyyy-MM-dd")
    public Date getUpdateTime() {
        return super.getUpdateTime();
    }

}
