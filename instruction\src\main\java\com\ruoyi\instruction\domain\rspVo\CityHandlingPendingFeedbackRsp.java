package com.ruoyi.instruction.domain.rspVo;

import lombok.Data;

/**
 * 市级指令待反馈响应
 * 
 * <AUTHOR>
 * @date 2025-01-20
 */
@Data
public class CityHandlingPendingFeedbackRsp {

    /**
     * 待反馈7天数量（特急）
     */
    private Integer pendingFeedback7Days;

    /**
     * 待反馈15天数量（紧急）
     */
    private Integer pendingFeedback15Days;

    /**
     * 待反馈30天数量（一般）
     */
    private Integer pendingFeedback30Days;

    /**
     * 当前用户所属区域
     */
    private String currentArea;

    /**
     * 统计时间范围开始
     */
    private String startTime;

    /**
     * 统计时间范围结束
     */
    private String endTime;
}
