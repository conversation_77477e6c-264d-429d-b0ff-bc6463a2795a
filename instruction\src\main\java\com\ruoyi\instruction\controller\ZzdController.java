package com.ruoyi.instruction.controller;

import cn.hutool.http.HttpRequest;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.ruoyi.common.constant.ZzdConstant;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.domain.entity.SysUser;
import com.ruoyi.common.helper.DingtalkHelper;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.common.utils.sign.Md5Utils;
import com.ruoyi.instruction.domain.JczzEventType;
import com.ruoyi.instruction.domain.reqVo.ZzdApiAddUpdateEmployeeVo;
import com.ruoyi.instruction.domain.reqVo.ZzdApiDeleteEmployeeVo;
import com.ruoyi.instruction.service.IJczzEventTypeService;
import com.ruoyi.system.mapper.SysUserMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;


/**
 * 浙政钉接口（接收浙政钉回调）
 *
 * <AUTHOR>
 * @date 2023-06-07
 */
@RestController
@RequestMapping("/instruction/zzd")
public class ZzdController extends BaseController {
    @Resource
    SysUserMapper sysUserMapper;
    @Autowired
    DingtalkHelper dingtalkHelper;

    @Autowired
    private IJczzEventTypeService jczzEventTypeService;

    /**
     * 浙政钉注册消息回调
     *
     * @param eventTag
     * @param dispatchId
     * @param content
     * @return
     */
    @RequestMapping(value = "/postCallbackUrl", method = RequestMethod.POST)
    public HashMap postCallbackUrl(@RequestParam(name = "eventTag", required = false) String eventTag,
                                   @RequestParam(name = "dispatchId", required = false) String dispatchId,
                                   @RequestParam(name = "content", required = false) String content) {
        logger.info(" ===============> 开始调用 postCallbackUrl 方法  <=============== ");
        logger.info("eventTag:" + eventTag);
        logger.info("dispatchId:" + dispatchId);
        logger.info("content:" + content);
        if (!StringUtils.isEmpty(eventTag)) {
            if (ZzdConstant.API_NAME_ADD_UPDATE_EMPLOYEE.equals(eventTag)) {
                if (!StringUtils.isEmpty(content)) {
                    ZzdApiAddUpdateEmployeeVo zzdApiAddUpdateEmployeeVo = JSON.parseObject(content, ZzdApiAddUpdateEmployeeVo.class);
                    if (ZzdConstant.EMPLOYEE_CHANGE_TYPE_UPDATE.equals(zzdApiAddUpdateEmployeeVo.getEmployeeChangeType())) {
                        zzdApiAddUpdateEmployeeVo.getEmployeeChangeMoMap().forEach((k, v) -> {
                            SysUser sysUser1 = sysUserMapper.selectUserByEmployeeCode(k);
                            if (sysUser1 != null) {
                                sysUser1.setNickName(v.getCurrEmployee().getEmployeeName());
                                sysUserMapper.updateUser(sysUser1);
                            }
                        });
                    }
                    if (ZzdConstant.EMPLOYEE_CHANGE_TYPE_ADD.equals(zzdApiAddUpdateEmployeeVo.getEmployeeChangeType())) {
                        zzdApiAddUpdateEmployeeVo.getEmployeeChangeMoMap().forEach((k, v) -> {
                            SysUser sysUser1 = sysUserMapper.selectUserByEmployeeCode(k);
                            if (sysUser1 == null) {
                                SysUser userInfo = dingtalkHelper.getUserInfo(k, null);
                                sysUser1 = new SysUser();
                                sysUser1.setEmployeeCode(k);
                                sysUser1.setNickName(userInfo.getNickName());
                                String username = StringUtils.getStringRandom(5);
                                sysUser1.setUserName(username);
                                sysUserMapper.insertUser(sysUser1);
                            }
                        });
                    }
                    System.out.println(zzdApiAddUpdateEmployeeVo);
                }
            } else if (ZzdConstant.API_NAME_LEAVE_EMPLOYEE.equals(eventTag)) {
                if (!StringUtils.isEmpty(content)) {
                    ZzdApiDeleteEmployeeVo zzdApiAddUpdateEmployeeVo = JSON.parseObject(content, ZzdApiDeleteEmployeeVo.class);
                    List<ZzdApiDeleteEmployeeVo.UserInfoBean> userInfo = zzdApiAddUpdateEmployeeVo.getUserInfo();
                    if (!CollectionUtils.isEmpty(userInfo)) {
                        for (ZzdApiDeleteEmployeeVo.UserInfoBean u : userInfo) {
                            SysUser sysUser1 = sysUserMapper.selectUserByEmployeeCode(u.getEmployeeCode());
                            if (sysUser1 != null) {
                                sysUserMapper.deleteUserById(sysUser1.getUserId());
                            }
                        }
                    }

                }
            }
        }
        //您的业务处理
        HashMap hashMap = new HashMap(12);
        hashMap.put("errcode", "0");
        hashMap.put("errmsg", "成功");
        return hashMap;
    }

    @GetMapping("/testJczlData")
    public AjaxResult testJczlData(String appSecret, String parentCode) {

        String substring = getEventData(appSecret, parentCode);
        // List<Map<String, String>> maps = (List<Map<String, String>>) JSONArray.parse(substring);
        // for (Map<String, String> map : maps) {
        //     //一级分类
        //     String code = map.get("code");
        //     String name = map.get("name");
        //     String secondData = getEventData(appSecret, code);
        //     List<Map<String, String>> secondDataList = (List<Map<String, String>>) JSONArray.parse(secondData);
        //     for (Map<String, String> secondMap : secondDataList) {
        //         //二级分类
        //         String secCode = secondMap.get("code");
        //         String secName = secondMap.get("name");
        //         String thirdData = getEventData(appSecret, secCode);
        //         List<Map<String, String>> thirdDataList = (List<Map<String, String>>) JSONArray.parse(thirdData);
        //         for (Map<String, String> thirdMap : thirdDataList) {
        //             String thirdCode = thirdMap.get("code");
        //             String thirdName = thirdMap.get("name");
        //             //新增事件分类
        //             JczzEventType jczzEventType = new JczzEventType();
        //             jczzEventType.setFirstCode(code);
        //             jczzEventType.setFirstName(name);
        //             jczzEventType.setSecondCode(secCode);
        //             jczzEventType.setSecondName(secName);
        //             jczzEventType.setThirdCode(thirdCode);
        //             jczzEventType.setThirdName(thirdName);
        //             jczzEventTypeService.insertJczzEventType(jczzEventType);
        //         }
        //     }
        // }

        return AjaxResult.success(substring);
    }

    /**
     * 获取事件数据
     *
     * @return
     */
    public String getEventData(String appSecret, String parentCode) {
        String appid = "A330701373889202304000005";
        long timestamp = System.currentTimeMillis();
        System.out.println("当前时间戳" + timestamp);
        String s = Md5Utils.generateSignature(appid + appSecret + timestamp);
        System.out.println("ssign" + s);
        String url = "http://dw.jinhua.gov.cn/gateway/api/001008007012243/GrassrootsGovernance/bmRc3nB24b7A79g6.htm";
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("appKey", appid);
        jsonObject.put("requestTime", timestamp);
        jsonObject.put("sign", s);
        if (parentCode != null && !parentCode.equals("")) {
            jsonObject.put("parentCode", parentCode);
        }
        String body = HttpRequest.post(url).header("Content-Type", "application/json").body(jsonObject.toJSONString()).execute().body();
        System.out.println(body);
        int index = body.indexOf("[");
        int lastIndexOf = body.lastIndexOf("]");
        System.out.println(index + "==" + lastIndexOf);
        String substring = body.substring(index, lastIndexOf + 1).replaceAll("\\\\", "");
        return substring;
    }
}
