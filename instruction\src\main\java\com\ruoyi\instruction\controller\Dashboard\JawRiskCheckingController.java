package com.ruoyi.instruction.controller.Dashboard;

import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.instruction.domain.RiskCheck;
import com.ruoyi.instruction.service.IJawRiskCheckingService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 金安稳风险排查Controller
 *
 * <AUTHOR>
 * @date 2025/3/5
 */
@RestController
@RequestMapping("/risk/checking")
public class JawRiskCheckingController {
    @Autowired
    private IJawRiskCheckingService jawRiskCheckingService;

    /**
     * 风险等级查询
     * @return
     */
    @GetMapping("/risklevel")
    public AjaxResult RiskLevel(RiskCheck riskCheck){
        return AjaxResult.success(jawRiskCheckingService.selectRiskLevel(riskCheck));
    }

    /**
     * 风险类型分布查询
     * @return
     */
    @GetMapping("/risktype")
    public AjaxResult RiskType(RiskCheck riskCheck){
        return AjaxResult.success(jawRiskCheckingService.selectRiskType(riskCheck));
    }

    /**
     * 风险县市区分布查询
     * @return
     */
    @GetMapping("/riskcounties")
    public AjaxResult RiskCounties(RiskCheck riskCheck){
        return AjaxResult.success(jawRiskCheckingService.selectRiskCounties(riskCheck));
    }

    /**
     * 风险总查询
     * @return
     */
    @GetMapping("/risksum")
    public AjaxResult RiskSum(RiskCheck riskCheck){
        return AjaxResult.success(jawRiskCheckingService.selectRiskSum(riskCheck));
    }

    /**
     * 风险等级化解查询
     * @return
     */
    @GetMapping("/risklevelresolve")
    public AjaxResult RiskLevelResolve(RiskCheck riskCheck){
        return AjaxResult.success(jawRiskCheckingService.selectRiskLevelResolve(riskCheck));
    }

    /**
     * 风险分布化解查询
     * @return
     */
    @GetMapping("/riskdistributionresolveleft")
    public AjaxResult RiskDistributionResolveLeft(RiskCheck riskCheck){
        return AjaxResult.success(jawRiskCheckingService.selectRiskDistributionResolveLeft(riskCheck));
    }

    /**
     * 风险分布化解查询
     * @return
     */
    @GetMapping("/riskdistributionresolveright")
    public AjaxResult RiskDistributionResolveRight(RiskCheck riskCheck){
        return AjaxResult.success(jawRiskCheckingService.selectRiskDistributionResolveRight(riskCheck));
    }
}
