package com.ruoyi.instruction.domain;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 人员风险值预估对象 yj_event
 *
 * <AUTHOR>
 * @date 2024-12-10
 */
@Data
public class YjEvent extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** $column.columnComment */
    private String id;

    /** 预警类型(1:事预警;2:人预警;3:群体预警;4:一人多事预警) */
    @Excel(name = "预警类型(1:事预警;2:人预警;3:群体预警;4:一人多事预警)")
    private String yjType;

    /** 事预警时对应的事件ID，其他类型预警可为空 */
    @Excel(name = "事预警时对应的事件ID，其他类型预警可为空")
    private Long bizEvtId;

    /** 事预警时对应的数据源 */
    @Excel(name = "事预警时对应的数据源")
    private String source;

    /**
     * 数据源名称
     */
    private String sourceName;

    /** 事件类型id */
    @Excel(name = "事件类型id")
    private String evtType;

    /** 事件属地行政编码 */
    @Excel(name = "事件属地行政编码")
    private String areaCode;

    /** 事发时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "事发时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date occurDate;

    /** 交办状态（0-未交办；10-已交办） */
    @Excel(name = "交办状态", readConverterExp = "0=-未交办；10-已交办")
    private String assignStatus;

    /** 化解状态（0-未化解；10-已化解） */
    @Excel(name = "化解状态", readConverterExp = "0=-未化解；10-已化解")
    private String disposeStatus;

    /** 预警风险等级 */
    @Excel(name = "预警风险等级")
    private Long riskLevel;

    /** 事件内容 */
    @Excel(name = "事件内容")
    private String content;

    /** 指令id */
    @Excel(name = "指令id")
    private Long instructionId;

    /** 放置理由 */
    @Excel(name = "放置理由")
    private String reasonPlace;

    /**
     * 事件名称
     */
    private String eventName;

    /**
     * 区域名称
     */
    private String areaName;

    /**
     * 责任所属地
     */
    private String dutyPlace;

    /**
     * 预警时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createDate;

    /**
     * 人员姓名
     */
    private String username;

    /**
     * 身份证号码
     */
    private String idCard;

    /**
     * 特征
     */
    private String behavior;

    /**
     * 该人员涉及多个事项
     */
    private Integer isMore;

    /**
     * 处理类型  0: 双排双办  1: 金安稳
     */
    private Integer dealType;

    /**
     * 移交时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date transferDate;

}
