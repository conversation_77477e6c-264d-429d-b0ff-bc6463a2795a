package com.ruoyi.instruction.mapper;

import java.time.LocalDate;
import java.util.Date;
import java.util.List;
import java.util.Map;

import com.ruoyi.instruction.domain.*;
import com.ruoyi.instruction.domain.rspVo.*;
import org.apache.ibatis.annotations.Param;

/**
 * 事件基本信息Mapper接口
 *
 * <AUTHOR>
 * @date 2022-12-16
 */
public interface InstructionEventMapper {
    /**
     * 查询事件基本信息
     *
     * @param id 事件基本信息主键
     * @return 事件基本信息
     */
    public InstructionEvent selectInstructionEventById(Long id);

    /**
     * 查询事件基本信息列表
     *
     * @param instructionEvent 事件基本信息
     * @return 事件基本信息集合
     */
    public List<InstructionEvent> selectInstructionEventList(InstructionEvent instructionEvent);

    /**
     * 新增事件基本信息
     *
     * @param instructionEvent 事件基本信息
     * @return 结果
     */
    public int insertInstructionEvent(InstructionEvent instructionEvent);

    /**
     * 修改事件基本信息
     *
     * @param instructionEvent 事件基本信息
     * @return 结果
     */
    public int updateInstructionEvent(InstructionEvent instructionEvent);

    /**
     * 删除事件基本信息
     *
     * @param id 事件基本信息主键
     * @return 结果
     */
    public int deleteInstructionEventById(Long id);

    /**
     * 批量删除事件基本信息
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteInstructionEventByIds(Long[] ids);

    /**
     * 根据人员id查询人员关联事件信息
     *
     * @param id
     * @return
     */
    List<InstructionEvent> findByPersonId(@Param("id") Long id);

    /**
     * 根据群体id查询关联事件
     *
     * @param id
     * @return
     */
    List<InstructionEvent> getEventListByGroupId(@Param("id") Long id);

    /**
     * 根据事件名称查询事件
     *
     * @param eventTitle
     * @return
     */
    InstructionEvent findByEventTile(@Param("eventTitle") String eventTitle);

    /**
     * 根据事件名称、推送时间
     *
     * @param eventTitle
     * @param pushTime
     * @return
     */
    InstructionEvent findByEventTileAndPushTime(@Param("eventTitle") String eventTitle, @Param("pushTime") Date pushTime);

    /**
     * 根据指令id查询指令对应事件
     *
     * @param infoId
     * @return
     */
    InstructionEvent selectInstructionEventByInstrucationId(@Param("id") Long infoId);

    /**
     * 获取事件总数、重大事件数、区县事件数、市级事件数
     * @param map
     * @return
     */
    EventDataRspVo getEventData(Map<String,Object> map);

    /**
     * 获取事件交办数
     * @return
     */
    List<EventAssignRspVo> getAssignData();

    /**
     * 获取信息类别
     * @return
     */
    List<Map<String, Integer>> getInfoCategory();

    /**
     * 获取事件类型
     * @return
     */
    List<Map<String, Integer>> getType();

    /**
     * 获取事件-基本情况
     * @return
     */
    String getEventBaseSituation();

    /**
     * 获取事件关联超过三次的群体名称
     * @param groupCount
     * @return
     */
    List<String> getGroupName(@Param("groupCount")Integer groupCount);

    /**
     * 获取信息来源
     * @return
     */
    List<Map<String, Integer>> getInfoSource();

    /**
     * 获取事件多发区域排名
     * @return
     */
    List<Map<String, Integer>> getAreaRank();

    /**
     * 获取事件子类型数据
     * @param id
     * @return
     */
    List<Map<String, Integer>> getChildType(@Param("id") Integer id);

    /**
     * 获取大屏事件列表
     * @param date
     * @param nextDate
     * @param dutyUnit
     * @return
     */
    List<BigScreenListEventRspVo> getBigScreenEventList(@Param("date") String date,@Param("nextDate")String nextDate,@Param("dutyUnit")String dutyUnit);

    /**
     * 获取事件统计
     * @param date
     * @param nextDate
     * @param dutyUnit
     * @return
     */
    BigScreenEventStatistics getStatistics(@Param("date") String date,@Param("nextDate")String nextDate,@Param("dutyUnit")String dutyUnit);

    /**
     * 获取事件统计-县市区关联事件数
     * @param date
     * @param nextDate
     * @return
     */
    List<Map<String, Integer>> getCountyEventCount(@Param("date") String date, @Param("nextDate") String nextDate);

    /**
     * 根据id获取事件详情
     * @param id
     * @return
     */
    BigScreenEventInfo getInfoById(@Param("id") Long id);

    /**
     * 根据群组id指令id
     * @param groupId
     * @return
     */
    List<Long> selectInstructionIdByGroupId(@Param("groupId") Long groupId);

    /**
     * 根据群组id获取事件详情
     * @param groupId
     * @return
     */
    List<DisposalProcessEventVo> selectEventByGroupId(@Param("groupId") Long groupId);

    /**
     * 根据群组id获取事件详情,步骤为5时的列表
     * @param groupId
     * @return
     */
    List<DisposalProcessEventVo> selectEventByGroupIdForEnd(@Param("groupId") Long groupId);

    /**
     * 未关联群体事件 更新相关群体id
     * @return
     */
    int synchronizationGroupId();

    /**
     * 查询事件数量在时间范围内
     * @param date
     * @return
     */
    Integer getCountByTime(@Param("date") LocalDate date);
    /**
     * 查询所有事件的责任单位
     * @return
     */
    List<String> selectDutyUnit();

    /**
     * 统计某段时间内的人员
     * @param date
     * @return
     */
    String getPersonIdsByTime(@Param("date") LocalDate date);

    /**
     * 统计事件
     * @param date
     * @param thresholdValue
     * @param value
     * @return
     */
    List<Map<String, Object>> getAreaEventCount(@Param("date") LocalDate date, @Param("thresholdValue") Integer thresholdValue, @Param("thresholdRule") Integer value);

    /**
     * 事件在事件范围内增速
     * @param date
     * @return
     */
    Integer eventGrowthRate(@Param("date") LocalDate date);

    /**
     * 查询重大事件板块左下角的重大事件清单
     * @param year
     * @param distinct
     * @return
     */
    List<InstructionEventInfos> getEventInfo(@Param("year") String year,@Param("distinct") String distinct);

    /**
     * 金安智治驾驶舱根据事件类型统计维稳事件
     * @return
     */
    List<BigScreenJazzCommonVo> getJazzType();

    /**
     * 首页动态监测（维稳事件）
     * @return
     */
    List<InstructionEvent> getJazzEventTypeList();

    /**
     * 查询事件类型增速
     * @param date
     * @param valueOf
     * @param thresholdRule
     * @param county
     * @return
     */
    List<Map<String, Object>> eventTypeGrowthRate(@Param("date") LocalDate date,@Param("value") Integer valueOf, @Param("rule") String thresholdRule,@Param("county") String county);


    /**
     * 按月根据事件统计结果
     * @param instructionEvent
     * @return
     */
    List<BigScreenJazzPageYearCountVo> getJazzEventTypeByYearList(InstructionEvent instructionEvent);

    /**
     * 获取生态环境安全
     * @param year
     * @return
     */
    InstructionEcologicalEnv selectEcologicalEnv(int year);

    /**
     * 金安大数据生产安全板块  事件详情
     * @return
     */
    List<InstructionEvent> getProduceSafeEventInfo();

    /**
     * 获取趋势
     * @param year
     * @return
     */
    InstructionTrend getTrend(int year);

    /**
     * 根据groupId获取单位
     * @param id
     * @param startTime
     * @param endTime
     * @return
     */
    String selectDutyUnitByGroupId(@Param("id") Long id,@Param("startTime") Date startTime,@Param("endTime")Date endTime);

    /**
     * 根据上访类型查询人员id
     * @param groupId 群体id
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return
     */
    List<InstructionEvent> selectIdByPetitionType(@Param("groupId") Long groupId,@Param("startTime") Date startTime,@Param("endTime") Date endTime);
    /**
     * 根据群体id查询事件单表
     * @param groupId  群体id
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return
     */
    List<InstructionEvent> selectEventForGroupId(@Param("groupId") Long groupId,@Param("startTime") Date startTime,@Param("endTime") Date endTime);

    /**
     * 根据事件类型获取事件信息，用于事件分析
     * @param typeId
     * @param startTime
     * @param endTime
     * @param area
     * @param infoSource
     * @param eventProperties
     * @param petitionType
     * @return
     */
    List<InstructionEvent> selectEventForTypeId(@Param("typeIds") List<Long> typeId, @Param("startTime") Date startTime, @Param("endTime") Date endTime, @Param("area") String area, @Param("infoSource") String infoSource,@Param("eventProperties")  Integer eventProperties, @Param("petitionType")String petitionType);

    /**
     * 根据事件类型获取事件信息，用于事件分析（兼容旧版本）
     * @param typeId
     * @param startTime
     * @param endTime
     * @param area
     * @param infoSource 这个参数在旧版本中实际上是重复的area
     * @return
     */
    default List<InstructionEvent> selectEventForTypeId(@Param("typeIds") List<Long> typeId, @Param("startTime") Date startTime, @Param("endTime") Date endTime, @Param("area") String area, @Param("infoSource") String infoSource) {
        return selectEventForTypeId(typeId, startTime, endTime, area, infoSource, null, null);
    }

    /**
     * 获取人员id
     * @param typeId
     * @param startTime
     * @param endTime
     * @return
     */
    String selectPeopleIdByTypeId(@Param("typeIds") List<Long> typeId,@Param("startTime") Date startTime,@Param("endTime") Date endTime,@Param("area")String area);
    /**
     * <AUTHOR>
     *  重点工作 指挥体系（暂时汇报用）亚运维稳安保工作组
     */
    List<InstructionCommand> getCommand1();
    /**
     * <AUTHOR>
     *  重点工作 指挥体系（暂时汇报用）应急小组
     */
    List<InstructionCommand> getCommand2(InstructionCommand instructionCommand);

    /**
     * 获取移动端事件数
     * @return
     */
    Map<String, Long> getYdEventCount();

    /**
     * 获取事件月度趋势
     * @return
     */
    List<Map<String, Object>> getEventMonthlyTrend();

    /**
     * 根据id获取重点群体每月事件数
     * @param id
     * @return
     */
    List<BigScreenJazzCommonVo> getGroupEventNum(@Param("id") Long id);

    /**
     * 获取每日会商
     * @param date
     * @return
     */
    List<Map<String, Object>> getDayMeet(@Param("pushDate") String date);

    /**
     * 根据指令id删除事件指令id
     * @param id
     */
    void deleteEventInstrutionId(@Param("id") Long id);

    /**
     * 统计市外人数
     * @param id
     * @param startTime
     * @param endTime
     * @return
     */
    BigScreenJazzCommonVo countOutsidePerson(@Param("id") Long id,@Param("startTime") Date startTime,@Param("endTime")Date endTime);

    /**
     * 查询事件信息
     * @return
     */
    List<InstructionEvent> selectEventList();

    /**
     * 根据群体id及部门名称查询事件信息
     * @param id
     * @param deptName
     * @return
     */
    List<InstructionEvent> getEventListByGroupIdAndDeptName(@Param("groupId") Long id, @Param("deptName") String deptName);

    /**
     * 根据日期查询出现异动情况的人员id
     * @param months
     * @return
     */
    String findTransactionPersonByMonths(@Param("months") Integer months);

    /**
     * 查询在时间区间内的事件数
     * @param instructionGroup
     * @return
     */
    Long findGroupCount(InstructionGroup instructionGroup);

    /**
     * 通过事件查询人员ids
     * @param person
     * @return
     */
    String selectPersonIdsByTime(InstrucationPerson person);

    /**
     * 获取近一个月异动人员ids
     * @return
     */
    String findPersonIdsByMonth();

    List<InstructionEvent> selectEventListForTime(InstructionGroup instructionGroup);

    String findPersonIdsByTime(@Param("startTime") String startTime, @Param("endTime") String endTime);
}
