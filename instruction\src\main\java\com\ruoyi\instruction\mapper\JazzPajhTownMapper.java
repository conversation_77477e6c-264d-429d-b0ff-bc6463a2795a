package com.ruoyi.instruction.mapper;

import java.util.List;

import com.ruoyi.instruction.domain.JazzPajhTown;
import org.apache.ibatis.annotations.Mapper;

/**
 * 平安金华乡镇信息Mapper接口
 * 
 * <AUTHOR>
 * @date 2023-05-15
 */
@Mapper
public interface JazzPajhTownMapper 
{
    /**
     * 查询平安金华乡镇信息
     * 
     * @param id 平安金华乡镇信息主键
     * @return 平安金华乡镇信息
     */
    public JazzPajhTown selectJazzPajhTownById(Long id);

    /**
     * 查询平安金华乡镇信息列表
     * 
     * @param jazzPajhTown 平安金华乡镇信息
     * @return 平安金华乡镇信息集合
     */
    public List<JazzPajhTown> selectJazzPajhTownList(JazzPajhTown jazzPajhTown);

    /**
     * 新增平安金华乡镇信息
     * 
     * @param jazzPajhTown 平安金华乡镇信息
     * @return 结果
     */
    public int insertJazzPajhTown(JazzPajhTown jazzPajhTown);

    /**
     * 修改平安金华乡镇信息
     * 
     * @param jazzPajhTown 平安金华乡镇信息
     * @return 结果
     */
    public int updateJazzPajhTown(JazzPajhTown jazzPajhTown);

    /**
     * 删除平安金华乡镇信息
     * 
     * @param id 平安金华乡镇信息主键
     * @return 结果
     */
    public int deleteJazzPajhTownById(Long id);

    /**
     * 批量删除平安金华乡镇信息
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteJazzPajhTownByIds(Long[] ids);
}
