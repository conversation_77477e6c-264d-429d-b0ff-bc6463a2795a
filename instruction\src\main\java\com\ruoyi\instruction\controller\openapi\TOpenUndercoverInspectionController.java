package com.ruoyi.instruction.controller.openapi;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.DecimalFormat;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.*;
import java.util.stream.Collectors;

import com.ruoyi.common.constant.Constants;
import com.ruoyi.common.constant.InstructionRolesConstants;
import com.ruoyi.common.core.domain.entity.SysDept;
import com.ruoyi.common.core.domain.entity.SysRole;
import com.ruoyi.common.core.domain.entity.SysUser;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.instruction.domain.InstructionCountyFeedback;
import com.ruoyi.instruction.domain.OpenUndercoverInspectionDetails;
import com.ruoyi.instruction.mapper.OpenUndercoverInspectionDetailsMapper;
import com.ruoyi.instruction.service.impl.InstructionInfoServiceImpl;
import com.ruoyi.system.service.impl.SysDeptServiceImpl;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.instruction.domain.TOpenUndercoverInspection;
import com.ruoyi.instruction.service.ITOpenUndercoverInspectionService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import lombok.extern.slf4j.Slf4j;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 暗访督察列Controller
 *
 * <AUTHOR>
 * @date 2023-07-20
 */
@RestController
@RequestMapping("/open/undercoverInspection")
@Slf4j
public class TOpenUndercoverInspectionController extends BaseController {
    @Autowired
    private ITOpenUndercoverInspectionService tOpenUndercoverInspectionService;

    @Autowired
    private OpenUndercoverInspectionDetailsMapper openUndercoverInspectionDetailsMapper;

    @Autowired
    private InstructionInfoServiceImpl infoService;

    @Autowired
    private SysDeptServiceImpl deptService;


    /**
     * 查询暗访督察列列表
     */
    @PreAuthorize("@ss.hasPermi('instruction:openUndercoverInspection:list')")
    @GetMapping("/list")
    public TableDataInfo list(TOpenUndercoverInspection tOpenUndercoverInspection) {
        SysUser user = SecurityUtils.getLoginUser().getUser();
        List<SysRole> roles = user.getRoles();
        List<String> roleList = roles.stream().map(SysRole::getRoleKey).collect(Collectors.toList());
        if (tOpenUndercoverInspection.getDealResult() != null && !tOpenUndercoverInspection.getDealResult().isEmpty()) {
            if (tOpenUndercoverInspection.getDealResult().equals("整改中") &&tOpenUndercoverInspection.getDealType() != null&&tOpenUndercoverInspection.getDealType().equals("已反馈")){
                return getDataTable(new ArrayList<>());
            }
            if (tOpenUndercoverInspection.getDealResult().equals("整改中") && (tOpenUndercoverInspection.getDealType() == null || tOpenUndercoverInspection.getDealType().isEmpty())) {
                tOpenUndercoverInspection.setDealType("整改中");
            } else if (tOpenUndercoverInspection.getDealResult().equals("已整改")) {
                tOpenUndercoverInspection.setDealType("已反馈");
            }
        }
        if (roleList.contains(InstructionRolesConstants.COUNTY_INSTRUCTION)) {
            //角色中包含县市区指令 根据祖级部门ID判断属于哪个县市区
            SysDept sysdept = infoService.getDeptIdByAncestors(user.getDept());
            String deptName = sysdept.getDeptName();
            String replace = deptName.replace("委政法委", "").replace("政法委", "").replace("政法办", "");
            tOpenUndercoverInspection.setCounty(replace);
        }
        List<OpenUndercoverInspectionDetails> openUndercoverInspectionDetails = new ArrayList<>();
        OpenUndercoverInspectionDetails details = new OpenUndercoverInspectionDetails();
        if (tOpenUndercoverInspection.getParams().size() > 0) {
            Map<String, Object> params = tOpenUndercoverInspection.getParams();
            Object beginFeedBackTime = params.get("beginFeedBackTime");
            Object endFeedBackTime = params.get("endFeedBackTime");
            Map<String, Object> params1 = details.getParams();
            if (beginFeedBackTime != null && endFeedBackTime != null) {
                params1.put("beginFeedBackTime", beginFeedBackTime);
                params1.put("endFeedBackTime", endFeedBackTime);
                openUndercoverInspectionDetails = openUndercoverInspectionDetailsMapper.selectOpenUndercoverInspectionDetailsList(details);
            }
        }

        //处理类型为已处理时查询其对应事件、指令id
        if (tOpenUndercoverInspection.getCheckItemDomain() != null && !tOpenUndercoverInspection.getCheckItemDomain().equals("")) {
            details.setCheckItemDomain(tOpenUndercoverInspection.getCheckItemDomain());
            openUndercoverInspectionDetails = openUndercoverInspectionDetailsMapper.selectOpenUndercoverInspectionDetailsList(details);
            if (openUndercoverInspectionDetails.size()==0){
                return getDataTable(new ArrayList<>());
            }
        }
        if (tOpenUndercoverInspection.getDealType() != null && !tOpenUndercoverInspection.getDealType().equals("")) {
            //查询已处理、未处理详情inspectId
            details.setDealType(tOpenUndercoverInspection.getDealType());
            openUndercoverInspectionDetails = openUndercoverInspectionDetailsMapper.selectOpenUndercoverInspectionDetailsList(details);
            if (openUndercoverInspectionDetails.size()==0){
                return getDataTable(new ArrayList<>());
            }
        }
        if (tOpenUndercoverInspection.getCheckItemLevel() != null && !tOpenUndercoverInspection.getCheckItemLevel().equals("")) {
            //查询已处理、未处理详情inspectId
            details.setCheckItemLevel(tOpenUndercoverInspection.getCheckItemLevel());
            openUndercoverInspectionDetails = openUndercoverInspectionDetailsMapper.selectOpenUndercoverInspectionDetailsList(details);
            if (openUndercoverInspectionDetails.size() == 0) {
                return getDataTable(new ArrayList<>());
            }
        }
        if (openUndercoverInspectionDetails.size() != 0) {
            List<Long> collect = openUndercoverInspectionDetails.stream().map(OpenUndercoverInspectionDetails::getInspectionId).collect(Collectors.toList());
            Map<String, Object> params = tOpenUndercoverInspection.getParams();
            params.put("inspectionIds", collect);
            tOpenUndercoverInspection.setParams(params);
        }

        //查询合格/不合格
        if (tOpenUndercoverInspection.getCheckResult() != null && !tOpenUndercoverInspection.getCheckResult().equals("")) {
            //查询合格点位inspectionId
            List<String> belowStandard = tOpenUndercoverInspectionService.findBelowStandard();
            Map<String, Object> params = tOpenUndercoverInspection.getParams();
            params.put("inspectionIdsByBelowStandard", belowStandard);
            tOpenUndercoverInspection.setParams(params);
        }

        //查询不合格点位已全部处理点位inspectionIds

        startPage();
        List<TOpenUndercoverInspection> list = tOpenUndercoverInspectionService.selectTOpenUndercoverInspectionList(tOpenUndercoverInspection);
        if (openUndercoverInspectionDetails.size() == 0) {
            if (tOpenUndercoverInspection.getCheckResult() != null && tOpenUndercoverInspection.getCheckResult().equals("合格")) {

            } else {
                //查询点位相关问题集合
                List<Long> collect = list.stream().map(TOpenUndercoverInspection::getInspectionId).collect(Collectors.toList());
                Map<String, Object> params = details.getParams();
                params.put("inspectionIds", collect);
                details.setParams(params);
                openUndercoverInspectionDetails = openUndercoverInspectionDetailsMapper.selectOpenUndercoverInspectionDetailsList(details);
                if (openUndercoverInspectionDetails.size()==0){
                    return getDataTable(new ArrayList<>());
                }
            }
        }
        List<OpenUndercoverInspectionDetails> finalOpenUndercoverInspectionDetails = openUndercoverInspectionDetails;
        List<Map<String, Object>> finalMapList = new ArrayList<>();
        list.stream().forEach(inspection -> {
            List<OpenUndercoverInspectionDetails> detailsList = finalOpenUndercoverInspectionDetails.stream().filter(i -> i.getInspectionId().equals(inspection.getInspectionId())).collect(Collectors.toList());
            if (tOpenUndercoverInspection.getDealType() == null || !tOpenUndercoverInspection.getDealType().equals("未处理")) {
                detailsList.stream().forEach(openUnderDetail -> {
                    String uniqueNo = openUnderDetail.getUniqueNo();
                    for (Map<String, Object> map : finalMapList) {
                        String mcaf_id = String.valueOf(map.get("mcaf_id"));
                        if (uniqueNo.equals(mcaf_id)) {
                            String type = String.valueOf(map.get("type"));
                            String infoId = String.valueOf(map.get("infoId"));
                            openUnderDetail.setDisposeType(Long.valueOf(type));
                            openUnderDetail.setInfoId(Long.valueOf(infoId));
                        }
                    }
                });
            }

            inspection.setReplyDetailList(detailsList);
        });
        TableDataInfo tableDataInfo = new TableDataInfo();
        tableDataInfo = getDataTable(list);
        //查询问题清单
        List<TOpenUndercoverInspection> allList = tOpenUndercoverInspectionService.selectTOpenUndercoverInspectionList(tOpenUndercoverInspection);
        List<Long> collect = allList.stream().map(TOpenUndercoverInspection::getInspectionId).collect(Collectors.toList());
        Map<String, Object> params = details.getParams();
        params.put("inspectionIds", collect);
        details.setParams(params);
        List<OpenUndercoverInspectionDetails> allDetails = openUndercoverInspectionDetailsMapper.selectOpenUndercoverInspectionDetailsList(details);
        tableDataInfo.setDetails(allDetails.size());
        return tableDataInfo;

    }


    /**
     * 导出暗访督察列列表
     */
    @PreAuthorize("@ss.hasPermi('instruction:openUndercoverInspection:export')")
    @Log(title = "暗访督察列", businessType = BusinessType.EXPORT)
    @GetMapping("/export")
    public AjaxResult export(TOpenUndercoverInspection tOpenUndercoverInspection) {
        ExcelUtil<TOpenUndercoverInspection> util = new ExcelUtil<TOpenUndercoverInspection>(TOpenUndercoverInspection.class);
        SysUser user = SecurityUtils.getLoginUser().getUser();
        List<SysRole> roles = user.getRoles();
        List<String> roleList = roles.stream().map(SysRole::getRoleKey).collect(Collectors.toList());
        if (roleList.contains(InstructionRolesConstants.COUNTY_INSTRUCTION)) {
            //角色中包含县市区指令 根据祖级部门ID判断属于哪个县市区
            SysDept sysdept = infoService.getDeptIdByAncestors(user.getDept());
            String deptName = sysdept.getDeptName();
            String replace = deptName.replace("委政法委", "").replace("政法委", "").replace("政法办", "");
            tOpenUndercoverInspection.setCounty(replace);
        }
        List<OpenUndercoverInspectionDetails> openUndercoverInspectionDetails = new ArrayList<>();
        OpenUndercoverInspectionDetails details = new OpenUndercoverInspectionDetails();
        if (tOpenUndercoverInspection.getParams().size() > 0) {
            Map<String, Object> params = tOpenUndercoverInspection.getParams();
            Object beginFeedBackTime = params.get("beginFeedBackTime");
            Object endFeedBackTime = params.get("endFeedBackTime");
            Map<String, Object> params1 = details.getParams();
            if (beginFeedBackTime != null && endFeedBackTime != null) {
                params1.put("beginFeedBackTime", beginFeedBackTime);
                params1.put("endFeedBackTime", endFeedBackTime);
                openUndercoverInspectionDetails = openUndercoverInspectionDetailsMapper.selectOpenUndercoverInspectionDetailsList(details);
            }
        }

        //处理类型为已处理时查询其对应事件、指令id
        if (tOpenUndercoverInspection.getCheckItemDomain() != null && !tOpenUndercoverInspection.getCheckItemDomain().equals("")) {
            details.setCheckItemDomain(tOpenUndercoverInspection.getCheckItemDomain());
        }
        if (tOpenUndercoverInspection.getDealType() != null && !tOpenUndercoverInspection.getDealType().equals("")) {
            //查询已处理、未处理详情inspectId
            details.setDealType(tOpenUndercoverInspection.getDealType());
            openUndercoverInspectionDetails = openUndercoverInspectionDetailsMapper.selectOpenUndercoverInspectionDetailsList(details);

            if (openUndercoverInspectionDetails.size() == 0) {
                return util.exportExcel(new ArrayList<>(), "openUndercoverInspection");
            }
        }

        if (tOpenUndercoverInspection.getCheckItemLevel() != null && !tOpenUndercoverInspection.getCheckItemLevel().equals("")) {
            //查询已处理、未处理详情inspectId
            details.setCheckItemLevel(tOpenUndercoverInspection.getCheckItemLevel());
            openUndercoverInspectionDetails = openUndercoverInspectionDetailsMapper.selectOpenUndercoverInspectionDetailsList(details);
            if (openUndercoverInspectionDetails.size() == 0) {
                return util.exportExcel(new ArrayList<>(), "openUndercoverInspection");
            }
        }
        if (openUndercoverInspectionDetails.size() != 0) {
            List<Long> collect = openUndercoverInspectionDetails.stream().map(OpenUndercoverInspectionDetails::getInspectionId).collect(Collectors.toList());
            Map<String, Object> params = tOpenUndercoverInspection.getParams();
            params.put("inspectionIds", collect);
            tOpenUndercoverInspection.setParams(params);
        }

        //查询合格/不合格
        if (tOpenUndercoverInspection.getCheckResult() != null && !tOpenUndercoverInspection.getCheckResult().equals("")) {
            //查询合格点位inspectionId
            List<String> belowStandard = tOpenUndercoverInspectionService.findBelowStandard();
            Map<String, Object> params = tOpenUndercoverInspection.getParams();
            params.put("inspectionIdsByBelowStandard", belowStandard);
            tOpenUndercoverInspection.setParams(params);
        }

        //查询不合格点位已全部处理点位inspectionIds

        List<TOpenUndercoverInspection> list = tOpenUndercoverInspectionService.selectTOpenUndercoverInspectionList(tOpenUndercoverInspection);
        if (openUndercoverInspectionDetails.size() == 0) {
            if (tOpenUndercoverInspection.getCheckResult() != null && tOpenUndercoverInspection.getCheckResult().equals("合格")) {

            } else {
                //查询点位相关问题集合
                List<Long> collect = list.stream().map(TOpenUndercoverInspection::getInspectionId).collect(Collectors.toList());
                Map<String, Object> params = details.getParams();
                params.put("inspectionIds", collect);
                details.setParams(params);
                openUndercoverInspectionDetails = openUndercoverInspectionDetailsMapper.selectOpenUndercoverInspectionDetailsList(details);
                if (openUndercoverInspectionDetails.size() == 0) {
                    return util.exportExcel(new ArrayList<>(), "openUndercoverInspection");
                }
            }
        }


        List<OpenUndercoverInspectionDetails> finalOpenUndercoverInspectionDetails = openUndercoverInspectionDetails;
        if (finalOpenUndercoverInspectionDetails.size()==0){
            return util.exportExcel(list, "明查暗访");
        }

        List<OpenUndercoverInspectionDetails> exportInspectionDetails = new ArrayList<>();
        OpenUndercoverInspectionDetails details1 = new OpenUndercoverInspectionDetails();
        //查询县市区已反馈记录
        list.stream().forEach(inspection -> {
            List<OpenUndercoverInspectionDetails> detailsList = finalOpenUndercoverInspectionDetails.stream().filter(i -> i.getInspectionId().equals(inspection.getInspectionId())).collect(Collectors.toList());
            if (detailsList.size() != 0) {
                for (OpenUndercoverInspectionDetails detail : detailsList) {
                    detail.setOpenUndercoverInspection(inspection);
                    exportInspectionDetails.add(detail);
                }
            } else {
                details1.setOpenUndercoverInspection(inspection);
                exportInspectionDetails.add(details1);
            }
        });
        ExcelUtil<OpenUndercoverInspectionDetails> util2 = new ExcelUtil<OpenUndercoverInspectionDetails>(OpenUndercoverInspectionDetails.class);
        return util2.exportExcel(exportInspectionDetails, "明查暗访");
    }

    /**
     * 获取暗访督察列详细信息
     */
    @PreAuthorize("@ss.hasPermi('instruction:openUndercoverInspection:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") String id) {
        return AjaxResult.success(openUndercoverInspectionDetailsMapper.selectOpenUndercoverInspectionDetailsByUniqueNo(id));
    }

    /**
     * 新增暗访督察列
     */
    @PreAuthorize("@ss.hasPermi('instruction:openUndercoverInspection:add')")
    @Log(title = "暗访督察列", businessType = BusinessType.INSERT)
    @PostMapping("/add")
    public AjaxResult add(@RequestBody TOpenUndercoverInspection tOpenUndercoverInspection) {
        return toAjax(tOpenUndercoverInspectionService.insertTOpenUndercoverInspection(tOpenUndercoverInspection));
    }

    /**
     * 修改暗访督察列
     */
    @PreAuthorize("@ss.hasPermi('instruction:openUndercoverInspection:edit')")
    @Log(title = "暗访督察列", businessType = BusinessType.UPDATE)
    @PostMapping("/edit")
    public AjaxResult edit(@RequestBody TOpenUndercoverInspection tOpenUndercoverInspection) {
        TOpenUndercoverInspection newTOpenUndercoverInspection = new TOpenUndercoverInspection();
        newTOpenUndercoverInspection.setInspectionId(tOpenUndercoverInspection.getInspectionId());
        newTOpenUndercoverInspection.setCounty(tOpenUndercoverInspection.getCounty());
        newTOpenUndercoverInspection.setTown(tOpenUndercoverInspection.getTown());
        return toAjax(tOpenUndercoverInspectionService.updateTOpenUndercoverInspection(newTOpenUndercoverInspection));
    }

    /**
     * 删除暗访督察列
     */
    @PreAuthorize("@ss.hasPermi('instruction:openUndercoverInspection:remove')")
    @Log(title = "暗访督察列", businessType = BusinessType.DELETE)
    @PostMapping("/remove/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids) {
        return toAjax(tOpenUndercoverInspectionService.deleteTOpenUndercoverInspectionByIds(ids));
    }

    /**
     * 批量生成指令
     */
    @PreAuthorize("@ss.hasPermi('instruction:mcaf:createInfo')")
    @Log(title = "暗访督察批量生成指令", businessType = BusinessType.INSERT)
    @PostMapping("/creatInstructionInfo/{inspectionIds}")
    public AjaxResult creatInstructionInfo(@PathVariable Long[] inspectionIds) {
        return toAjax(tOpenUndercoverInspectionService.creatInstructionInfo(inspectionIds));
    }


    /**
     * 批量生成事件
     */
    @PreAuthorize("@ss.hasPermi('instruction:mcaf:creatEvent')")
    @Log(title = "暗访督察批量生成指令", businessType = BusinessType.INSERT)
    @PostMapping("/creatEvent/{inspectionIds}")
    public AjaxResult creatEvent(@PathVariable Long[] inspectionIds) {
        return toAjax(tOpenUndercoverInspectionService.creatEvent(inspectionIds));
    }


    /**
     * 修改数据格式，在数据里添加文件id字段
     */
    @PreAuthorize("@ss.hasPermi('instruction:openUndercoverInspection:creatInstructionInfo')")
    @Log(title = "修改数据格式", businessType = BusinessType.INSERT)
    @PostMapping("/correct")
    public AjaxResult correct() {
        tOpenUndercoverInspectionService.correct();
        return AjaxResult.success();
    }

    /**
     * 获取暗访问题类别
     *
     * @return
     */
    @GetMapping("/getCheckItemDomain")
    public AjaxResult getCheckItemDomain() {
        List<String> checkDomains = openUndercoverInspectionDetailsMapper.getCheckItemDomain();
        return AjaxResult.success(checkDomains);
    }

    /**
     * 获取县市区-乡镇街道
     *
     * @param type
     * @param code
     * @return
     */
    @GetMapping("/getCityArea")
    public AjaxResult getCityArea(String type, String code) {
        List<Map<String, Object>> mapList = openUndercoverInspectionDetailsMapper.getCityArea(type, code);
        return AjaxResult.success(mapList);
    }


    /**
     * 处理
     *
     * @return
     */
    @GetMapping("/dealUndercoverDetails")
    public AjaxResult dealUndercoverDetails() {
        //获取反馈时间、反馈情况
        List<Map<String, Object>> mapList = openUndercoverInspectionDetailsMapper.getDealUndercoverDetails();
        mapList.stream().forEach(map -> {
            String unique_no = (String) map.get("unique_no");
            String situation = (String) map.get("situation");
            LocalDateTime localDateTime = (LocalDateTime) map.get("receive_time");
            Date date = Date.from(localDateTime.atZone(ZoneId.systemDefault()).toInstant());

            OpenUndercoverInspectionDetails details = new OpenUndercoverInspectionDetails();
            details.setUniqueNo(unique_no);
            details.setFeedbackSituation(situation);
            details.setFeedbackTime(date);
            openUndercoverInspectionDetailsMapper.updateOpenUndercoverInspectionDetails(details);
        });
        return AjaxResult.success();
    }

    /**
     * 处理暗访图片
     * @return
     */
    @GetMapping("/dealUndercoverDetailsFiles")
    public AjaxResult dealUndercoverDetailsFiles(){
        List<Map<String, Object>> mapList = openUndercoverInspectionDetailsMapper.getDealUndercoverDetailsFiles();
        mapList.forEach(map->{
            String mcaf_id = (String) map.get("mcaf_id");
            String fileIds = (String) map.get("fileIds");
            OpenUndercoverInspectionDetails details = new OpenUndercoverInspectionDetails();
            details.setUniqueNo(mcaf_id);
            details.setDealFiles(fileIds);
            openUndercoverInspectionDetailsMapper.updateOpenUndercoverInspectionDetails(details);
        });
        return AjaxResult.success();
    }

    @GetMapping("/getMissionNameList")
    public AjaxResult getMissionNameList(Integer type) {
        if (type == null) {
            type = 1;
        }
        String deptName = "";
        if (type == 2) {
            //县市平安指令页面
            SysDept dept = SecurityUtils.getLoginUser().getUser().getDept();
            if (!dept.getDeptId().equals(Constants.JINHUA_CITY_DEPT_ID) && !dept.getParentId().equals(Constants.JINHUA_CITY_DEPT_ID)) {
                //县市区、乡镇街道账号
                SysDept parentDept = deptService.selectDeptById(dept.getParentId());
                deptName = parentDept.getDeptName().replace("委政法委", "").replace("政法委", "").replace("政法办", "");
            }
        }
        List<String> missionNameList = new ArrayList<>();
        missionNameList = tOpenUndercoverInspectionService.selectMissionNameList(type,deptName);
        return AjaxResult.success(missionNameList);
    }



    /**
     * 查询暗访督查列表接口
     */
    @PreAuthorize("@ss.hasPermi('instruction:openUndercoverInspection:list')")
    @GetMapping("/getStatistics")
    public AjaxResult getStatistics(TOpenUndercoverInspection tOpenUndercoverInspection) {
        Map<String, Object> result = new HashMap<>();
        result.put("total",0);
        result.put("problemCount",0);
        result.put("noProblemPonit",0);
        result.put("problemPoint",0);
        result.put("noFeedback", 0);
        result.put("Feedback", 0);
        result.put("noFeedbackRate",0);
        SysUser user = SecurityUtils.getLoginUser().getUser();
        List<SysRole> roles = user.getRoles();
        List<String> roleList = roles.stream().map(SysRole::getRoleKey).collect(Collectors.toList());
        if (roleList.contains(InstructionRolesConstants.COUNTY_INSTRUCTION)) {
            //角色中包含县市区指令 根据祖级部门ID判断属于哪个县市区
            SysDept sysdept = infoService.getDeptIdByAncestors(user.getDept());
            String deptName = sysdept.getDeptName();
            String replace = deptName.replace("委政法委", "").replace("政法委", "").replace("政法办", "");
            tOpenUndercoverInspection.setCounty(replace);
        }
        List<OpenUndercoverInspectionDetails> openUndercoverInspectionDetails = new ArrayList<>();
        OpenUndercoverInspectionDetails details = new OpenUndercoverInspectionDetails();
        if (tOpenUndercoverInspection.getDealResult() != null && !tOpenUndercoverInspection.getDealResult().isEmpty()) {
            if (tOpenUndercoverInspection.getDealResult().equals("整改中") &&tOpenUndercoverInspection.getDealType() != null&&tOpenUndercoverInspection.getDealType().equals("已反馈")){
                return AjaxResult.success(result);
            }
            if (tOpenUndercoverInspection.getDealResult().equals("整改中") && (tOpenUndercoverInspection.getDealType() == null || tOpenUndercoverInspection.getDealType().isEmpty())) {
                tOpenUndercoverInspection.setDealType("整改中");
            } else if (tOpenUndercoverInspection.getDealResult().equals("已整改")) {
                tOpenUndercoverInspection.setDealType("已反馈");
            }
        }
        if (tOpenUndercoverInspection.getParams().size() > 0) {
            Map<String, Object> params = tOpenUndercoverInspection.getParams();
            Object beginFeedBackTime = params.get("beginFeedBackTime");
            Object endFeedBackTime = params.get("endFeedBackTime");
            Map<String, Object> params1 = details.getParams();
            if (beginFeedBackTime != null && endFeedBackTime != null) {
                params1.put("beginFeedBackTime", beginFeedBackTime);
                params1.put("endFeedBackTime", endFeedBackTime);
                openUndercoverInspectionDetails = openUndercoverInspectionDetailsMapper.selectOpenUndercoverInspectionDetailsList(details);
            }
        }

        //处理类型为已处理时查询其对应事件、指令id
        if (tOpenUndercoverInspection.getCheckItemDomain() != null && !tOpenUndercoverInspection.getCheckItemDomain().equals("")) {
            details.setCheckItemDomain(tOpenUndercoverInspection.getCheckItemDomain());
            openUndercoverInspectionDetails = openUndercoverInspectionDetailsMapper.selectOpenUndercoverInspectionDetailsList(details);
            if (openUndercoverInspectionDetails.size()==0){
                return AjaxResult.success(result);
            }
        }
        if (tOpenUndercoverInspection.getDealType() != null && !tOpenUndercoverInspection.getDealType().equals("")) {
            //查询已处理、未处理详情inspectId
            details.setDealType(tOpenUndercoverInspection.getDealType());
            openUndercoverInspectionDetails = openUndercoverInspectionDetailsMapper.selectOpenUndercoverInspectionDetailsList(details);
            if (openUndercoverInspectionDetails.size()==0){
                return AjaxResult.success(result);
            }
        }
        if (tOpenUndercoverInspection.getCheckItemLevel() != null && !tOpenUndercoverInspection.getCheckItemLevel().equals("")) {
            //查询已处理、未处理详情inspectId
            details.setCheckItemLevel(tOpenUndercoverInspection.getCheckItemLevel());
            openUndercoverInspectionDetails = openUndercoverInspectionDetailsMapper.selectOpenUndercoverInspectionDetailsList(details);
            if (openUndercoverInspectionDetails.size() == 0) {
                return AjaxResult.success(result);
            }
        }
        if (openUndercoverInspectionDetails.size() != 0) {
            List<Long> collect = openUndercoverInspectionDetails.stream().map(OpenUndercoverInspectionDetails::getInspectionId).collect(Collectors.toList());
            Map<String, Object> params = tOpenUndercoverInspection.getParams();
            params.put("inspectionIds", collect);
            tOpenUndercoverInspection.setParams(params);
        }

        //查询合格/不合格
        if (tOpenUndercoverInspection.getCheckResult() != null && !tOpenUndercoverInspection.getCheckResult().equals("")) {
            //查询合格点位inspectionId
            List<String> belowStandard = tOpenUndercoverInspectionService.findBelowStandard();
            Map<String, Object> params = tOpenUndercoverInspection.getParams();
            params.put("inspectionIdsByBelowStandard", belowStandard);
            tOpenUndercoverInspection.setParams(params);
        }

        //查询不合格点位已全部处理点位inspectionIds

        List<TOpenUndercoverInspection> list = tOpenUndercoverInspectionService.selectTOpenUndercoverInspectionList(tOpenUndercoverInspection);
        if (openUndercoverInspectionDetails.size() == 0) {
            if (tOpenUndercoverInspection.getCheckResult() != null && tOpenUndercoverInspection.getCheckResult().equals("合格")) {

            } else {
                //查询点位相关问题集合
                List<Long> collect = list.stream().map(TOpenUndercoverInspection::getInspectionId).collect(Collectors.toList());
                Map<String, Object> params = details.getParams();
                params.put("inspectionIds", collect);
                details.setParams(params);
                openUndercoverInspectionDetails = openUndercoverInspectionDetailsMapper.selectOpenUndercoverInspectionDetailsList(details);
                if (openUndercoverInspectionDetails.size()==0){
                    return AjaxResult.success(result);
                }
            }
        }
        //暗访总数
        result.put("total",list.size());
        if (list.size()!=0) {
            //查询问题清单
            List<TOpenUndercoverInspection> allList = tOpenUndercoverInspectionService.selectTOpenUndercoverInspectionList(tOpenUndercoverInspection);
            List<Long> collect = allList.stream().map(TOpenUndercoverInspection::getInspectionId).collect(Collectors.toList());
            Map<String, Object> params = details.getParams();
            params.put("inspectionIds", collect);
            details.setParams(params);
            List<OpenUndercoverInspectionDetails> allDetails = openUndercoverInspectionDetailsMapper.selectOpenUndercoverInspectionDetailsList(details);
            //问题总数
            result.put("problemCount", allDetails.size());
            //搜索条件检查结果不为空
            if (tOpenUndercoverInspection.getCheckResult() != null && !tOpenUndercoverInspection.getCheckResult().equals("")) {
                //查询合格点位inspectionId
                List<String> belowStandard = tOpenUndercoverInspectionService.findBelowStandard();
                Map<String, Object> params1 = tOpenUndercoverInspection.getParams();
                params1.put("inspectionIdsByBelowStandard", belowStandard);
                tOpenUndercoverInspection.setParams(params1);
                if (tOpenUndercoverInspection.getCheckResult().equals("合格")){
                    List<TOpenUndercoverInspection> noProblemList = tOpenUndercoverInspectionService.selectTOpenUndercoverInspectionList(tOpenUndercoverInspection);
                    result.put("noProblemPonit", noProblemList.size());
                }else if (tOpenUndercoverInspection.getCheckResult().equals("不合格")){
                    List<TOpenUndercoverInspection> problemList = tOpenUndercoverInspectionService.selectTOpenUndercoverInspectionList(tOpenUndercoverInspection);
                    result.put("problemPoint", problemList.size());
                    //未整改数
                    List<OpenUndercoverInspectionDetails> feedbakcList = allDetails.stream().filter(item -> item.getDealStep() != null && item.getDealStep().equals("已反馈")).collect(Collectors.toList());
                    result.put("noFeedback", allDetails.size() - feedbakcList.size());
                    if (allDetails.size() != 0 && feedbakcList.size() != 0) {
                        double rate = (double) feedbakcList.size() / allDetails.size() * 100;
                        BigDecimal bd = new BigDecimal(rate).setScale(2, RoundingMode.DOWN);
                        result.put("rate", rate);
                        result.put("noFeedbackRate", bd.doubleValue() + "%");
                    }
                }

            }else {
                //检查结果为空
                //无问题点位
                tOpenUndercoverInspection.setCheckResult("合格");

                //查询合格点位inspectionId
                List<String> belowStandard = tOpenUndercoverInspectionService.findBelowStandard();
                Map<String, Object> params1 = tOpenUndercoverInspection.getParams();
                params1.put("inspectionIdsByBelowStandard", belowStandard);
                tOpenUndercoverInspection.setParams(params1);

                List<TOpenUndercoverInspection> noProblemList = tOpenUndercoverInspectionService.selectTOpenUndercoverInspectionList(tOpenUndercoverInspection);
                result.put("noProblemPonit", noProblemList.size());
                //问题点位数
                // tOpenUndercoverInspection.setCheckResult("不合格");
                // List<TOpenUndercoverInspection> problemList = tOpenUndercoverInspectionService.selectTOpenUndercoverInspectionList(tOpenUndercoverInspection);
                // result.put("problemPoint", problemList.size());
                //未整改数
                List<OpenUndercoverInspectionDetails> feedbakcList = allDetails.stream().filter(item -> item.getDealStep() != null && item.getDealStep().equals("已反馈")).collect(Collectors.toList());
                result.put("Feedback", feedbakcList.size());
                result.put("noFeedback", allDetails.size() - feedbakcList.size());
                if (allDetails.size() != 0 && feedbakcList.size() != 0) {
                    double rate = (double) feedbakcList.size() / allDetails.size() * 100;
                    BigDecimal bd = new BigDecimal(rate).setScale(2, RoundingMode.DOWN);
                    result.put("rate", rate);
                    result.put("noFeedbackRate", bd.doubleValue() + "%");
                }

            }

        }
        return AjaxResult.success(result);

    }

}
