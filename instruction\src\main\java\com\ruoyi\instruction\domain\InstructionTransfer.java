package com.ruoyi.instruction.domain;

import java.util.Date;
import java.util.List;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 指令转接对象 t_instruction_transfer
 *
 * <AUTHOR>
 * @date 2022-12-19
 */
@Data
public class InstructionTransfer extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /**
     * 指令流转id
     */
    private Long id;

    /**
     * 转交部门
     */
    @Excel(name = "转交部门")
    private String transferDept;

    /**
     * 接收人
     */
    @Excel(name = "接收人")
    private String transferBy;

    /**
     * 接收时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "接收时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date transferTime;

    /**
     * 接收id
     */
    @Excel(name = "接收id")
    private Long receiveId;

    /**
     * 指令id
     */
    @Excel(name = "指令id")
    private Long instructionId;

    /** 1:正常 9：删除 */
    @Excel(name = "1:正常 9：删除")
    private Integer status;


    /** 1:展示 0:不展示 */
    @Excel(name = "1:展示 0:不展示")
    private Integer isShow;

    /** 转交部门id */
    @Excel(name = "转交部门id")
    private Long transferDeptId;

    /**
     * 处置表信息
     */
    private InstructionDispose instructionDispose;

    /** 是否为mzx 1:是 2:不是 */
    private Long isMzx;

}
