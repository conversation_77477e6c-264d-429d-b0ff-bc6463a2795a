package com.ruoyi.instruction.controller;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.support.ExcelTypeEnum;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.domain.entity.SysDept;
import com.ruoyi.common.core.domain.entity.SysUser;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.instruction.domain.RiskDetails;
import com.ruoyi.instruction.domain.reqVo.TotalAnalysisVo;
import com.ruoyi.instruction.domain.rspVo.*;
import com.ruoyi.instruction.mapper.RiskDetailsMapper;
import com.ruoyi.instruction.mapper.RiskMapper;
import com.ruoyi.instruction.service.IRiskDetailsService;
import com.ruoyi.system.mapper.SysDeptMapper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.OutputStream;
import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 风险排查详情Controller
 *
 * <AUTHOR>
 * @date 2023-12-25
 */
@RestController
@RequestMapping("/system/riskDetails")
public class RiskDetailsController extends BaseController {
    @Autowired
    private IRiskDetailsService riskDetailsService;

    @Autowired
    private RiskDetailsMapper riskDetailsMapper;

    @Autowired
    private SysDeptMapper deptMapper;

    @Autowired
    private RiskMapper riskMapper;

    /**
     * 查询风险排查详情列表
     */
    // @PreAuthorize("@ss.hasPermi('system:riskDetails:list')")
    @GetMapping("/list")
    public TableDataInfo list(RiskDetails riskDetails) {
        SysUser user = SecurityUtils.getLoginUser().getUser();
        SysDept dept = user.getDept();
        List<Long> parentIds = Arrays.asList(204L, 205L, 206L, 207L, 208L, 209L, 210L, 211L, 212L, 261L);
        if (dept.getParentId() != 202 && dept.getDeptId() != 202 && !parentIds.contains(dept.getParentId())) {
            SysDept parentDept = deptMapper.selectDeptById(dept.getParentId());
            String deptName = parentDept.getDeptName().replace("委政法委", "").replace("政法委", "").replace("政法办", "");
            riskDetails.setDutyUnit(deptName);
        } else if (parentIds.contains(dept.getParentId())) {
            SysDept parentDept = deptMapper.selectDeptById(dept.getParentId());
            String deptName = parentDept.getDeptName().replace("委政法委", "").replace("政法委", "").replace("政法办", "");
            riskDetails.setDutyUnit(deptName);
            //乡镇街道
            riskDetails.setAreaName(dept.getDeptName());
        }
        if (riskDetails.getCheckMin() != null && riskDetails.getCheckBig() != null && riskDetails.getCheckBig().equals(riskDetails.getCheckMin())) {
            riskDetails.setCheckMin(null);
        }
        List<RiskDetails> list = riskDetailsService.selectRiskDetailsList(riskDetails);
        return getDataTable(list);
    }
    /**
     * 查询风险排查详情列表添加任务名
     */
    @GetMapping("/listNew")
    public TableDataInfo listNew(RiskDetails riskDetails) {
        SysUser user = SecurityUtils.getLoginUser().getUser();
        SysDept dept = user.getDept();
        List<Long> parentIds = Arrays.asList(204L, 205L, 206L, 207L, 208L, 209L, 210L, 211L, 212L, 261L);
        if (dept.getParentId() != 202 && dept.getDeptId() != 202 && !parentIds.contains(dept.getParentId())) {
            SysDept parentDept = deptMapper.selectDeptById(dept.getParentId());
            String deptName = parentDept.getDeptName().replace("委政法委", "").replace("政法委", "").replace("政法办", "");
            riskDetails.setDutyUnit(deptName);
        } else if (parentIds.contains(dept.getParentId())) {
            SysDept parentDept = deptMapper.selectDeptById(dept.getParentId());
            String deptName = parentDept.getDeptName().replace("委政法委", "").replace("政法委", "").replace("政法办", "");
            riskDetails.setDutyUnit(deptName);
            //乡镇街道
            riskDetails.setAreaName(dept.getDeptName());
        }
        if (riskDetails.getCheckMin() != null && riskDetails.getCheckBig() != null && riskDetails.getCheckBig().equals(riskDetails.getCheckMin())) {
            riskDetails.setCheckMin(null);
        }
        List<RiskDetails> list = riskDetailsService.selectRiskDetailsListNew(riskDetails);
        return getDataTable(list);
    }

    /**
     * 获取风险统计接口新
     */
    @GetMapping("/getStatisticsNew")
    public TableDataInfo getStatisticsNew(RiskDetails riskDetails) {
        SysUser user = SecurityUtils.getLoginUser().getUser();
        SysDept dept = user.getDept();
        List<Long> parentIds = Arrays.asList(204L, 205L, 206L, 207L, 208L, 209L, 210L, 211L, 212L, 261L);
        if (dept.getParentId() != 202 && dept.getDeptId() != 202 && !parentIds.contains(dept.getParentId())) {
            SysDept parentDept = deptMapper.selectDeptById(dept.getParentId());
            String deptName = parentDept.getDeptName().replace("委政法委", "").replace("政法委", "").replace("政法办", "");
            riskDetails.setDutyUnit(deptName);
        } else if (parentIds.contains(dept.getParentId())) {
            SysDept parentDept = deptMapper.selectDeptById(dept.getParentId());
            String deptName = parentDept.getDeptName().replace("委政法委", "").replace("政法委", "").replace("政法办", "");
            riskDetails.setDutyUnit(deptName);
            //乡镇街道
            riskDetails.setAreaName(dept.getDeptName());
        }
        if (riskDetails.getCheckMin() != null && riskDetails.getCheckBig() != null && riskDetails.getCheckBig().equals(riskDetails.getCheckMin())) {
            riskDetails.setCheckMin(null);
        }
        List<Map<String, Object>> map = riskDetailsService.getStatisticsNew(riskDetails);
        return getDataTable(map);
    }

    /**
     * 导出风险排查详情列表
     */
    // @PreAuthorize("@ss.hasPermi('system:riskDetails:export')")
    @Log(title = "风险排查详情", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, RiskDetails riskDetails) {
        SysUser user = SecurityUtils.getLoginUser().getUser();
        SysDept dept = user.getDept();
        List<Long> parentIds = Arrays.asList(204L, 205L, 206L, 207L, 208L, 209L, 210L, 211L, 212L, 261L);
        if (dept.getParentId() != 202 && dept.getDeptId() != 202 && !parentIds.contains(dept.getParentId())) {
            SysDept parentDept = deptMapper.selectDeptById(dept.getParentId());
            String deptName = parentDept.getDeptName().replace("委政法委", "").replace("政法委", "").replace("政法办", "");
            riskDetails.setDutyUnit(deptName);
        } else if (parentIds.contains(dept.getParentId())) {
            //乡镇街道
            riskDetails.setAreaName(dept.getDeptName());
        }
        // Risk risk = riskMapper.selectRiskById(riskDetails.getRiskId());
        List<RiskDetails> list = riskDetailsService.selectRiskDetailsListNew(riskDetails);
        // if (risk.getRiskType() == 1) {
        //     ExcelUtil<RiskDetails> util = new ExcelUtil<RiskDetails>(RiskDetails.class);
        //     util.exportExcel(response, list, "风险排查详情数据");
        // } else if (risk.getRiskType() == 2) {
        ExcelUtil<ProblemBuildExport> util1 = new ExcelUtil<ProblemBuildExport>(ProblemBuildExport.class);
        List<ProblemBuildExport> list1 = new ArrayList<>();
        for (RiskDetails details : list) {
            ProblemBuildExport problemBuildExport = new ProblemBuildExport();
            BeanUtils.copyProperties(details, problemBuildExport);
            list1.add(problemBuildExport);
        }
        util1.exportExcel(response, list1, "风险排查详情数据");
        // }
    }

    /**
     * 获取风险排查详情详细信息
     */
    // @PreAuthorize("@ss.hasPermi('system:riskDetails:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id) {
        return success(riskDetailsService.selectRiskDetailsById(id));
    }

    /**
     * 新增风险排查详情
     */
    // @PreAuthorize("@ss.hasPermi('system:riskDetails:add')")
    @Log(title = "风险排查详情", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody RiskDetails riskDetails) {
        return riskDetailsService.insertRiskDetails(riskDetails);
    }

    /**
     * 修改风险排查详情
     */
    // @PreAuthorize("@ss.hasPermi('system:riskDetails:edit')")
    @Log(title = "风险排查详情", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody RiskDetails riskDetails) {
        return toAjax(riskDetailsService.updateRiskDetails(riskDetails));
    }

    /**
     * 删除风险排查详情
     */
    // @PreAuthorize("@ss.hasPermi('system:riskDetails:remove')")
    @Log(title = "风险排查详情", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long ids) {
        return toAjax(riskDetailsService.deleteRiskDetailsById(ids));
    }

    /**
     * 根据父级id查询风险类别
     *
     * @param parentId
     * @return
     */
    @GetMapping("/getRiskCategory")
    public AjaxResult getRiskCategory(Long parentId) {
        List<Map<String, Object>> list = riskDetailsMapper.getRiskCategory(parentId);
        return AjaxResult.success(list);
    }

    /**
     * 获取县市区列表
     *
     * @return
     */
    @GetMapping("/getDeptList")
    public AjaxResult getDeptList() {
        SysDept dept = SecurityUtils.getLoginUser().getUser().getDept();
        List<Long> parentIds = Arrays.asList(204L, 205L, 206L, 207L, 208L, 209L, 210L, 211L, 212L, 261L);
        List<SysDept> deptList = new ArrayList<>();
        //获取责任所属地
        SysDept dept1 = deptMapper.selectDeptById(dept.getParentId());
        String town = "";
        if (parentIds.contains(dept.getParentId())) {
            //乡镇街道账号
            deptList.add(dept);
            town = dept.getDeptName();
        } else {
            //非乡镇街道账号
            deptList = deptMapper.selectTownListByParentId(dept.getParentId());
        }
        AjaxResult success = AjaxResult.success(deptList);
        String replace = dept1.getDeptName().replace("委政法委", "").replace("政法委", "").replace("政法办", "");
        success.put("dutyPlace", replace);
        success.put("town", town);
        return success;
    }

    /**
     * 获取风险统计接口
     *
     * @param riskDetails
     * @return
     */
    @GetMapping("/getStatistics")
    public AjaxResult getStatistics(RiskDetails riskDetails) {
        SysUser user = SecurityUtils.getLoginUser().getUser();
        SysDept dept = user.getDept();
        if (dept.getParentId() != 202 && dept.getDeptId() != 202) {
            Long parentId = user.getDept().getParentId();
            riskDetails.setCreateDeptId(parentId);
        }
        List<Map<String, Object>> map = riskDetailsMapper.getStatistics(riskDetails);
        return AjaxResult.success(map);
    }

    /**
     * 获取四大类型统计
     *
     * @param riskDetails
     * @return
     */
    @GetMapping("/getCategoryStatistics")
    public AjaxResult getCategoryStatistics(RiskDetails riskDetails) {
        //获取类别分类
        List<Map<String, Object>> mapList = riskDetailsMapper.getCategoryStatistics(riskDetails);
        riskDetails.setRiskType(1L);
        List<RiskDetails> riskDetailsList = riskDetailsService.selectRiskDetailsList(riskDetails);
        for (Map<String, Object> map : mapList) {
            Long id = (Long) map.get("id");
            Map<String, Long> collect = riskDetailsList.stream().filter(riskDetails1 -> riskDetails1.getCheckBig().equals(id)).collect(Collectors.groupingBy(RiskDetails::getLevel, Collectors.counting()));
            map.put("major", 0);
            map.put("high", 0);
            map.put("middle", 0);
            map.put("low", 0);
            if (collect.get("1") != null) {
                map.put("major", collect.get("1"));
            }
            if (collect.get("2") != null) {
                map.put("high", collect.get("2"));
            }
            if (collect.get("3") != null) {
                map.put("middle", collect.get("3"));
            }
            if (collect.get("4") != null) {
                map.put("low", collect.get("4"));
            }
        }
        return AjaxResult.success(mapList);
    }

    /**
     * 获取县市排查数
     *
     * @return
     */
    @GetMapping("/getCountyNumberStatistics")
    public AjaxResult getCountyNumberStatistics(RiskDetails riskDetails) {
        List<RiskDetails> list = riskDetailsService.selectRiskDetailsList(riskDetails);
        List<Map<String, Object>> mapList = riskDetailsMapper.getCountyNumberStatistics(riskDetails);
        for (Map<String, Object> map : mapList) {
            Long dept_id = (Long) map.get("dept_id");
            Map<String, Long> collect = list.stream().filter(riskDetails1 -> riskDetails1.getCreateDeptId().equals(dept_id)).collect(Collectors.groupingBy(RiskDetails::getLevel, Collectors.counting()));
            map.put("major", 0);
            map.put("high", 0);
            map.put("middle", 0);
            map.put("low", 0);
            map.put("other", 0);
            if (collect.get("1") != null) {
                map.put("major", collect.get("1"));
            }
            if (collect.get("2") != null) {
                map.put("high", collect.get("2"));
            }
            if (collect.get("3") != null) {
                map.put("middle", collect.get("3"));
            }
            if (collect.get("4") != null) {
                map.put("low", collect.get("4"));
            }
            if (collect.get("") != null) {
                map.put("other", collect.get(""));
            }
        }
        return AjaxResult.success(mapList);
    }

    /**
     * 获取风险排查年份
     *
     * @return
     */
    @GetMapping("/getRiskYear")
    public AjaxResult getRiskYear() {
        List<String> years = riskDetailsMapper.getRiskYear();
        return AjaxResult.success(years);
    }

    /**
     * 获取风险排查任务名和ID
     *
     * @return
     */
    @GetMapping("/getRiskNameAndId")
    public AjaxResult getRiskNameAndId() {
        List<Map<String, Object>> riskNameAndId = riskDetailsMapper.getRiskNameAndId();
        return AjaxResult.success(riskNameAndId);
    }



    /**
     * 下载人员导入模板
     *
     * @param response
     */
    @PostMapping("/importTemplate")
    public void importTemplate(HttpServletResponse response) {
        ExcelUtil<RiskDetails> util = new ExcelUtil<RiskDetails>(RiskDetails.class);
        util.importTemplateExcel(response, "风险排查");
    }

    /**
     * 导入风险排查数据
     *
     * @param file
     * @return
     * @throws Exception
     */
    @Log(title = "风险排查", businessType = BusinessType.IMPORT)
    @PostMapping("/importRiskDetailData")
    public AjaxResult importRiskDetailData(MultipartFile file, Long riskId) throws Exception {
        ExcelUtil<RiskDetails> util = new ExcelUtil<RiskDetails>(RiskDetails.class);
        List<RiskDetails> riskDetailsList = util.importExcel(file.getInputStream());
        return riskDetailsService.importRiskDetailData(riskDetailsList, riskId);
    }

    @PostMapping("/test")
    public void test(HttpServletResponse response, @RequestBody TotalAnalysisVo totalAnalysisVo) {
        ExcelUtil<TotalAnalysis> util = new ExcelUtil<TotalAnalysis>(TotalAnalysis.class);
        List<TotalAnalysis> totalAnalysisList = riskDetailsService.selectRiskDetailsListByConditions(
                totalAnalysisVo.getYears(),
                totalAnalysisVo.getRiskIds(),
                totalAnalysisVo.getCountyNames()
        );
        util.exportExcel(response, totalAnalysisList, "区县整体化解分析");

    }

    @PostMapping("/test2")
    @ResponseBody
    public void test2(HttpServletResponse response, @RequestBody TotalAnalysisVo totalAnalysisVo) throws IOException {
        List<RiskLevelAnalysis> riskLevelAnalysis = riskDetailsService.countRiskLevelByConditions(
                totalAnalysisVo.getYears(),
                totalAnalysisVo.getRiskIds(),
                totalAnalysisVo.getCountyNames()
        );
        OutputStream outputStream=response.getOutputStream();
        try {
            this.setExcelResponseProp(response, "等级分析(非专项)");
            EasyExcel.write(outputStream,RiskLevelAnalysis.class)
                    .excelType(ExcelTypeEnum.XLSX)
                    .sheet("等级分析(非专项)")
                    .head(RiskLevelAnalysis.class)
                    .doWrite(riskLevelAnalysis);
        } catch (IOException e) {
            throw new RuntimeException(e);
        }finally {
            outputStream.flush();
            outputStream.close();
        }
    }

    @PostMapping("/test3")
    @ResponseBody
    public void test3(HttpServletResponse response, @RequestBody TotalAnalysisVo totalAnalysisVo) throws IOException {
        List<RiskTypeAnalysis> riskTypeAnalysisList = riskDetailsService.countTypeByConditions(
                totalAnalysisVo.getYears(),
                totalAnalysisVo.getRiskIds(),
                totalAnalysisVo.getCountyNames()
        );
        OutputStream outputStream=response.getOutputStream();
        try {
            this.setExcelResponseProp(response, "化解类型分析(非专项)");
            EasyExcel.write(outputStream,RiskTypeAnalysis.class)
                    .excelType(ExcelTypeEnum.XLSX)
                    .sheet("化解类型分析(非专项)")
                    .head(RiskTypeAnalysis.class)
                    .doWrite(riskTypeAnalysisList);
        } catch (IOException e) {
            throw new RuntimeException(e);
        }finally {
            outputStream.flush();
            outputStream.close();
        }
    }

    @PostMapping("/test4")
    @ResponseBody


    public void test4(HttpServletResponse response, @RequestBody TotalAnalysisVo totalAnalysisVo) throws IOException {
        List<RiskSpecialTypeAnalysis> riskSpecialTypeAnalysisList = riskDetailsService.countRiskSpecialTypeByConditions(
                totalAnalysisVo.getYears(),
                totalAnalysisVo.getRiskIds(),
                totalAnalysisVo.getCountyNames()
        );

        OutputStream outputStream=response.getOutputStream();
        try {
            this.setExcelResponseProp(response, "化解类型分析(专项)");
            // 模拟根据条件在数据库查询数据
            //这个实现方式非常简单直接，使用EasyExcel的write方法将查询到的数据进行处理，以流的形式写出即可
            EasyExcel.write(outputStream,RiskSpecialTypeAnalysis.class)//对应的导出实体类
                    .excelType(ExcelTypeEnum.XLSX)//excel文件类型，包括CSV、XLS、XLSX
                    .sheet("化解类型分析(专项)")//导出sheet页名称
                    .head(RiskSpecialTypeAnalysis.class)
                    .doWrite(riskSpecialTypeAnalysisList); //查询获取的数据集合List<T>，转成excel
        } catch (IOException e) {
            throw new RuntimeException(e);
        }finally {
            outputStream.flush();
            outputStream.close();
        }

    }

    private void setExcelResponseProp(HttpServletResponse response, String rawFileName) throws UnsupportedEncodingException {

        response.setContentType("application/vnd.vnd.ms-excel");

        response.setCharacterEncoding("utf-8");

        String fileName = URLEncoder.encode(rawFileName.concat(".xlsx"), "UTF-8");

        response.setHeader("Content-disposition", "attachment;filename*=utf-8''" + fileName);
    }

    @PostMapping("/riskReporting")
    @ResponseBody
    public void test5(HttpServletResponse response, @RequestBody TotalAnalysisVo totalAnalysisVo) throws IOException {
        {
             //这里调用之前写的四个service方法，获取到数据
            List<TotalAnalysis> totalAnalysisList = riskDetailsService.selectRiskDetailsListByConditions(
                    totalAnalysisVo.getYears(),
                    totalAnalysisVo.getRiskIds(),
                    totalAnalysisVo.getCountyNames()
            );
            List<RiskLevelAnalysis> riskLevelAnalysis = riskDetailsService.countRiskLevelByConditions(
                    totalAnalysisVo.getYears(),
                    totalAnalysisVo.getRiskIds(),
                    totalAnalysisVo.getCountyNames()
            );
            List<RiskTypeAnalysis> riskTypeAnalysisList = riskDetailsService.countTypeByConditions(
                    totalAnalysisVo.getYears(),
                    totalAnalysisVo.getRiskIds(),
                    totalAnalysisVo.getCountyNames()
            );
            List<RiskSpecialTypeAnalysis> riskSpecialTypeAnalysisList = riskDetailsService.countRiskSpecialTypeByConditions(
                    totalAnalysisVo.getYears(),
                    totalAnalysisVo.getRiskIds(),
                    totalAnalysisVo.getCountyNames()
            );


            OutputStream outputStream = response.getOutputStream();
            try {
                String fileName = "风险报告";
                this.setExcelResponseProp(response, "风险报告");
                ExcelWriter excelWriter = EasyExcel.write(outputStream).build();
                WriteSheet writeSheet = EasyExcel.writerSheet(0, "区县整体化解分析").head(TotalAnalysis.class).build();
                excelWriter.write(totalAnalysisList, writeSheet);
                writeSheet = EasyExcel.writerSheet(1, "等级分析(非专项)").head(RiskLevelAnalysis.class).build();
                excelWriter.write(riskLevelAnalysis, writeSheet);
                writeSheet = EasyExcel.writerSheet(2, "化解类型分析(非专项)").head(RiskTypeAnalysis.class).build();
                excelWriter.write(riskTypeAnalysisList, writeSheet);
                writeSheet = EasyExcel.writerSheet(3, "化解类型分析(专项)").head(RiskSpecialTypeAnalysis.class).build();
                excelWriter.write(riskSpecialTypeAnalysisList, writeSheet);

                excelWriter.finish();
            } catch (IOException e) {
                throw new RuntimeException(e);
            } finally {
                outputStream.flush();
                outputStream.close();
            }

        }
    }

    /**
     * 获取风险概要统计
     * <p>
     * 接口功能：展示排查总数、未化解总数、高风险以上(重大+高)总件数、高风险以上未化解件数。
     *
     * @param riskDetails 接收前端可能传入的过滤条件，如年份、任务ID等
     * @return 包含统计数据的 AjaxResult
     */
    @GetMapping("/getRiskSummary")
    public AjaxResult getRiskSummary(RiskDetails riskDetails) {

        SysUser user = SecurityUtils.getLoginUser().getUser();
        SysDept dept = user.getDept();
        List<Long> parentIds = Arrays.asList(204L, 205L, 206L, 207L, 208L, 209L, 210L, 211L, 212L, 261L);

        if (dept.getParentId() != 202 && dept.getDeptId() != 202 && !parentIds.contains(dept.getParentId())) {
            SysDept parentDept = deptMapper.selectDeptById(dept.getParentId());
            if (parentDept != null) {
                String deptName = parentDept.getDeptName().replace("委政法委", "").replace("政法委", "").replace("政法办", "");
                riskDetails.setDutyUnit(deptName);
            }
        } else if (parentIds.contains(dept.getParentId())) {
            SysDept parentDept = deptMapper.selectDeptById(dept.getParentId());
            if (parentDept != null) {
                String deptName = parentDept.getDeptName().replace("委政法委", "").replace("政法委", "").replace("政法办", "");
                riskDetails.setDutyUnit(deptName);
            }
            //乡镇街道
            riskDetails.setAreaName(dept.getDeptName());
        }

        if (riskDetails.getCheckMin() != null && riskDetails.getCheckBig() != null && riskDetails.getCheckBig().equals(riskDetails.getCheckMin())) {
            riskDetails.setCheckMin(null);
        }

        RiskSummaryRspVo summaryData = riskDetailsService.getRiskSummaryStatistics(riskDetails);

        return AjaxResult.success(summaryData);
    }

}
