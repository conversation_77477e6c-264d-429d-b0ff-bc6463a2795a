package com.ruoyi.instruction.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;

import com.ruoyi.common.config.RuoYiConfig;
import com.ruoyi.common.utils.file.FileUploadUtils;
import com.ruoyi.common.utils.file.FileUtils;
import com.ruoyi.framework.config.ServerConfig;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.instruction.domain.InstructionFile;
import com.ruoyi.instruction.service.IInstructionFileService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;
import org.springframework.web.multipart.MultipartFile;

/**
 * 指令文件Controller
 *
 * <AUTHOR>
 * @date 2022-12-19
 */
@RestController
@RequestMapping("/instruction/file")
public class InstructionFileController extends BaseController {
    @Autowired
    private IInstructionFileService instructionFileService;

    @Autowired
    private ServerConfig serverConfig;

    /**
     * 查询指令文件列表
     */
    // @PreAuthorize("@ss.hasPermi('instruction:file:list')")
    @GetMapping("/list")
    public TableDataInfo list(InstructionFile instructionFile) {
        startPage();
        List<InstructionFile> list = instructionFileService.selectInstructionFileList(instructionFile);
        return getDataTable(list);
    }

    /**
     * 导出指令文件列表
     */
    // @PreAuthorize("@ss.hasPermi('instruction:file:export')")
    @Log(title = "指令文件", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, InstructionFile instructionFile) {
        List<InstructionFile> list = instructionFileService.selectInstructionFileList(instructionFile);
        ExcelUtil<InstructionFile> util = new ExcelUtil<InstructionFile>(InstructionFile.class);
        util.exportExcel(response, list, "指令文件数据");
    }

    /**
     * 获取指令文件详细信息
     */
    // @PreAuthorize("@ss.hasPermi('instruction:file:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id) {
        return success(instructionFileService.selectInstructionFileById(id));
    }

    /**
     * 新增指令文件
     */
    // @PreAuthorize("@ss.hasPermi('instruction:file:add')")
    @Log(title = "指令文件", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody InstructionFile instructionFile) {
        return toAjax(instructionFileService.insertInstructionFile(instructionFile));
    }

    /**
     * 修改指令文件
     */
    // @PreAuthorize("@ss.hasPermi('instruction:file:edit')")
    @Log(title = "指令文件", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody InstructionFile instructionFile) {
        return toAjax(instructionFileService.updateInstructionFile(instructionFile));
    }

    /**
     * 删除指令文件
     */
    // @PreAuthorize("@ss.hasPermi('instruction:file:remove')")
    @Log(title = "指令文件", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids) {
        return toAjax(instructionFileService.deleteInstructionFileByIds(ids));
    }

    @PostMapping("/instructionUploadFile")
    public AjaxResult instructionUploadFile(MultipartFile file) throws Exception {
        try {
            // 上传文件路径
            String filePath = RuoYiConfig.getUploadPath();
            // 上传并返回新文件名称
            String fileName = FileUploadUtils.upload(filePath, file);
            // String url = serverConfig.getUrl() + fileName;
            AjaxResult ajax = AjaxResult.success();
            String url = fileName.replace("/profile/", "http://*************:9100/file/upload");
            //插入文件信息
            InstructionFile instructionFile = new InstructionFile();
            instructionFile.setFileName(FileUtils.getName(fileName));
            instructionFile.setFilePath(fileName);
            instructionFile.setFileUrl(url);
            instructionFile.setOriginalFileName(file.getOriginalFilename());
            int row = instructionFileService.insertInstructionFile(instructionFile);
            InstructionFile instructionFile1 = instructionFileService.selectInstructionFileById(instructionFile.getId());
            ajax.put("data",instructionFile1);
            ajax.put("FileId", instructionFile.getId());
            return ajax;
        } catch (Exception e) {
            return AjaxResult.error(e.getMessage());
        }
    }

    @GetMapping("/selectByFileIds")
    public AjaxResult selectByFileIds(String fileIds) {
        List<InstructionFile> list = instructionFileService.selectByFileIds(fileIds);
        return AjaxResult.success(list);
    }

}
