package com.ruoyi.instruction.controller;

import cn.hutool.core.date.DateUtil;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.exception.GlobalException;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.examineAir.service.IExamineService;
import com.ruoyi.examineAir.vo.BigScreenExamineVo;
import com.ruoyi.examineAir.vo.BigScreenPageRegionStatisticsVo;
import com.ruoyi.instruction.domain.*;
import com.ruoyi.instruction.domain.reqVo.JazzPajhAreaStatisticsVo;
import com.ruoyi.instruction.domain.rspVo.*;
import com.ruoyi.instruction.service.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.*;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 金安智治驾驶舱首页
 * <AUTHOR> @version 1.0
 * @date 2
 *
 */
@RestController
@RequestMapping("/bigScreen/jazz/page")
public class BigScreenJazzPageController extends BaseController {

    @Autowired
    private IInstructionEventService instructionEventService;

    @Autowired
    private IInstrucationPersonService iInstrucationPersonService;
    @Autowired
    private IExamineService examineService;

    @Autowired
    private IInstructionEndService instructionEndService;
    @Autowired
    private IInstructionAssignService instructionAssignService;
    @Autowired
    private IInstructionInfoService instructionInfoService;
    @Autowired
    private IInstructionRealtimeinfoService instructionRealtimeinfoService;

    @Autowired
    private IJazzPajhAreaStatisticsService jazzPajhAreaStatisticsService;
    /**
     * 首页金安大数据
     */
    @GetMapping("/eventWwCount")
    public AjaxResult eventWwCount() {
        BigScreenJazzPageJadsjCountVo b=new BigScreenJazzPageJadsjCountVo();
        List<BigScreenJazzCommonVo> jazzType = instructionEventService.getJazzType();
        b.setJazzTypeList(jazzType);
        LocalDate current_date = LocalDate.now();
        int current_Year = current_date.getYear();
        List<String> oneMonth = getOneMonth(current_Year);
        InstructionEvent instructionEvent=new InstructionEvent();
        Date yearFirst = DateUtils.getYearFirst(current_Year);
        instructionEvent.setCreateTime(yearFirst);
        List<BigScreenJazzPageYearCountVo> jazzEventTypeByYearList = instructionEventService.getJazzEventTypeByYearList(instructionEvent);
        List<String> collect1 = jazzEventTypeByYearList.stream().map(BigScreenJazzPageYearCountVo::getYear).collect(Collectors.toList());

        List<String> collect = oneMonth.stream().filter(e -> {
            return !collect1.contains(e);
        }).collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(collect)){
            for (String s:collect){
                BigScreenJazzPageYearCountVo bigScreenJazzPageYearCountVo=new BigScreenJazzPageYearCountVo();
                bigScreenJazzPageYearCountVo.setYear(s);
                jazzEventTypeByYearList.add(bigScreenJazzPageYearCountVo);
            }
            Collections.reverse(jazzEventTypeByYearList);
        }
        b.setYearCountVoList(jazzEventTypeByYearList);
        return AjaxResult.success(b);
    }

    /**
     * 查询【实时信息】列表
     * <AUTHOR>
     */
    @GetMapping("/realtimeinfoList")
    public TableDataInfo list(InstructionRealtimeinfo instructionRealtimeinfo)
    {
        instructionRealtimeinfo.setIsRelease("1");
        startPage();
        List<InstructionRealtimeinfo> list = instructionRealtimeinfoService.selectInstructionRealtimeinfoList(instructionRealtimeinfo);
        return getDataTable(list);
    }
    /**
     * 首页动态监测（维稳事件）
     */
    @GetMapping("/eventWwList")
    public TableDataInfo eventWwList(InstructionEvent instructionEvent) {
//        startPage();
        List jazzEventTypeList = instructionEventService.getJazzEventTypeList();
        return getDataTable(jazzEventTypeList);
    }

    /**
     * 首页平安金华地图列表
     */
    @GetMapping("/safeJhMap")
    public AjaxResult safeJhMap() {
        String year="";
        String month="";
        JazzPajhAreaStatisticsVo jazzPajhAreaStatistics = new JazzPajhAreaStatisticsVo();
        jazzPajhAreaStatistics.setType(1L);
        JazzPajhAreaStatistics jazzPajhAreaStatistics1 = jazzPajhAreaStatisticsService.selectNew(1L);
        //获取最近的时间
        if (jazzPajhAreaStatistics1 != null) {
            year = jazzPajhAreaStatistics1.getYear();
            month = jazzPajhAreaStatistics1.getMonth();
            jazzPajhAreaStatistics.setYear(year);
            jazzPajhAreaStatistics.setMonth(month);
        } else {
            //获取当前年月
            LocalDate current_date = LocalDate.now();
            year = current_date.getYear() + "";
            month = current_date.getMonth().getValue() + "";
            jazzPajhAreaStatistics.setYear(year);
            jazzPajhAreaStatistics.setMonth(month);
        }
        List<JazzPajhAreaStatistics> list = jazzPajhAreaStatisticsService.selectList(jazzPajhAreaStatistics);

//        List<JazzPajhAreaStatistics> collect = list.stream().filter(x->!x.getArea().equals("开发区")).sorted(
//                        (o1, o2) -> {
//            if ((o1.getRanking()==null?0:o1.getRanking())-(o2.getRanking()==null?0:o2.getRanking())>0) {
//                return -1;
//            } else {
//                return 1;
//            }
//        }
//        ).collect(Collectors.toList());
        List<BigScreenPageRegionStatisticsVo> list1=new ArrayList();
        for (int i=0;i<list.size();i++){
            JazzPajhAreaStatistics jazzPajhAreaStatistics2 = list.get(i);
            BigScreenPageRegionStatisticsVo b=new BigScreenPageRegionStatisticsVo();
            b.setDutyPlace(jazzPajhAreaStatistics2.getArea());
            if (i<3){
                b.setLevel(1);
            }else if (i>5){
                b.setLevel(3);
            }else {
                b.setLevel(2);
            }
            b.setCount(jazzPajhAreaStatistics2.getScore()==null?"":jazzPajhAreaStatistics2.getScore().toString());
            list1.add(b);
        }
//        BigScreenJazzCommonVo
        return AjaxResult.success(list1);
    }


//-----------------------------------------------------------------------------------
    /**
     * 获取最近12个月月份
     */
    public static List<String> getOneMonth(Integer year) {
        List<String> monthList = new ArrayList<>();
        Calendar calendar = Calendar.getInstance();
        //1号就从上个月开始算
        int num = 1;
        if (isFirstDayOfMonth(calendar)){
            num = 0;
        }
        calendar.set(Calendar.MONTH,calendar.get(Calendar.MONTH)+num);
        for (int i = 0; i < 12; i++) {
            calendar.add(Calendar.MONTH, -1);//1个月前
            String month = calendar.get(Calendar.YEAR)+"-"+fillZero(calendar.get(Calendar.MONTH)+1);
            if (!month.contains(year+"")){
                continue;
            }

            monthList.add(month);
        }
        Collections.reverse(monthList);
        return monthList;
    }

    /**
     * 判断今天是否是1号
     * @param calendar  日历对象
     * @return          是否第一天
     */
    public static boolean isFirstDayOfMonth(Calendar calendar){
        calendar.setTime(new Date());
        calendar.set(Calendar.DATE,calendar.get(Calendar.DATE)+1);
        if(calendar.get(Calendar.DAY_OF_MONTH) == 2){
            return true;
        }else{
            return false;
        }
    }

    /**
     * 格式化月份
     */
    public static String fillZero(int i){
        String month = "";
        if(i<10){
            month = "0" + i;
        }else{
            month = String.valueOf(i);
        }
        return month;
    }

    /**
     * <AUTHOR>
     *  重点工作 指挥体系（暂时汇报用）
     */
    @GetMapping("/getCommand")
    public AjaxResult getCommand(InstructionCommand instructionCommand) throws Exception {
        List<InstructionCommand> list=instructionEventService.getCommand(instructionCommand);
        return AjaxResult.success(list);
    }






}
