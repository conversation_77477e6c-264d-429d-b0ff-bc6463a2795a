package com.ruoyi.instruction.mapper;

import java.util.List;
import com.ruoyi.instruction.domain.InstructionFollow;

/**
 * 用户关注指令信息Mapper接口
 * 
 * <AUTHOR>
 * @date 2024-12-31
 */
public interface InstructionFollowMapper 
{
    /**
     * 查询用户关注指令信息
     * 
     * @param id 用户关注指令信息主键
     * @return 用户关注指令信息
     */
    public InstructionFollow selectInstructionFollowById(Long id);

    /**
     * 查询用户关注指令信息列表
     * 
     * @param instructionFollow 用户关注指令信息
     * @return 用户关注指令信息集合
     */
    public List<InstructionFollow> selectInstructionFollowList(InstructionFollow instructionFollow);

    /**
     * 新增用户关注指令信息
     * 
     * @param instructionFollow 用户关注指令信息
     * @return 结果
     */
    public int insertInstructionFollow(InstructionFollow instructionFollow);

    /**
     * 修改用户关注指令信息
     * 
     * @param instructionFollow 用户关注指令信息
     * @return 结果
     */
    public int updateInstructionFollow(InstructionFollow instructionFollow);

    /**
     * 删除用户关注指令信息
     * 
     * @param id 用户关注指令信息主键
     * @return 结果
     */
    public int deleteInstructionFollowById(Long id);

    /**
     * 批量删除用户关注指令信息
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteInstructionFollowByIds(Long[] ids);
}
