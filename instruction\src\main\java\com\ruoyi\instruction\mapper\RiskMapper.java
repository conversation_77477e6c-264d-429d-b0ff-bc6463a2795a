package com.ruoyi.instruction.mapper;

import java.util.List;
import com.ruoyi.instruction.domain.Risk;

/**
 * 风险排查主Mapper接口
 * 
 * <AUTHOR>
 * @date 2023-12-25
 */
public interface RiskMapper 
{
    /**
     * 查询风险排查主
     * 
     * @param id 风险排查主主键
     * @return 风险排查主
     */
    public Risk selectRiskById(Long id);

    /**
     * 查询风险排查主列表
     * 
     * @param risk 风险排查主
     * @return 风险排查主集合
     */
    public List<Risk> selectRiskList(Risk risk);

    /**
     * 新增风险排查主
     * 
     * @param risk 风险排查主
     * @return 结果
     */
    public int insertRisk(Risk risk);

    /**
     * 修改风险排查主
     * 
     * @param risk 风险排查主
     * @return 结果
     */
    public int updateRisk(Risk risk);

    /**
     * 删除风险排查主
     * 
     * @param id 风险排查主主键
     * @return 结果
     */
    public int deleteRiskById(Long id);

    /**
     * 批量删除风险排查主
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteRiskByIds(Long[] ids);
}
