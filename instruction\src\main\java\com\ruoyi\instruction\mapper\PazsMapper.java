package com.ruoyi.instruction.mapper;

import java.util.List;
import java.util.Map;

import com.ruoyi.instruction.domain.Pazs;
import com.ruoyi.instruction.domain.reqVo.RiskHiddenDangerReq;
import com.ruoyi.instruction.domain.rspVo.DtPazsRsp;
import com.ruoyi.instruction.domain.rspVo.PazsYdqsRsp;
import com.ruoyi.instruction.domain.rspVo.SyPakhRsp;
import org.apache.ibatis.annotations.Param;

/**
 * 平安指数Mapper接口
 *
 * <AUTHOR>
 * @date 2023-08-09
 */
public interface PazsMapper
{
    /**
     * 查询平安指数
     *
     * @param id 平安指数主键
     * @return 平安指数
     */
    public Pazs selectPazsById(Long id);

    /**
     * 查询平安指数列表
     *
     * @param pazs 平安指数
     * @return 平安指数集合
     */
    public List<Pazs> selectPazsList(Pazs pazs);

    /**
     * 新增平安指数
     *
     * @param pazs 平安指数
     * @return 结果
     */
    public int insertPazs(Pazs pazs);

    /**
     * 修改平安指数
     *
     * @param pazs 平安指数
     * @return 结果
     */
    public int updatePazs(Pazs pazs);

    /**
     * 删除平安指数
     *
     * @param id 平安指数主键
     * @return 结果
     */
    public int deletePazsById(Long id);

    /**
     * 批量删除平安指数
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deletePazsByIds(Long[] ids);


    public List<Pazs> selectPazsNewListByCounty(@Param("year") String year, @Param("condition") String condition, @Param("rankName") String rankName, @Param("rankType") String rankType);


    public List<Pazs> selectPazsNewListByMonth(@Param("year") String year, @Param("condition") String condition, @Param("rankName") String rankName, @Param("rankType") String rankType);

    /**
     * 地图平安指数
     * @return
     */
    List<DtPazsRsp> dtPazs();

    /**
     * 首页平安考核
     * @param riskHiddenDangerReq
     * @return
     */
    SyPakhRsp syPazs(RiskHiddenDangerReq riskHiddenDangerReq);

    /**
     * 平安指数月度趋势
     * @param riskHiddenDangerReq
     * @return
     */
    List<PazsYdqsRsp> ydqs(RiskHiddenDangerReq riskHiddenDangerReq);
}
