package com.ruoyi.instruction.controller;

import com.alibaba.fastjson2.JSONObject;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.exception.CustomException;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.common.utils.sm3.SM3Utils;
import com.ruoyi.common.utils.sm3.SymmetricEncoder;
import com.ruoyi.instruction.domain.reqVo.SecretParam;
import com.ruoyi.instruction.domain.reqVo.ShareQueryParam;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.client.RestTemplate;

import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.UUID;

/**
 * 矛盾纠纷
 * <AUTHOR>
 * @version 1.0
 * @date 2024/5/30 8:38
 */
@RestController
@RequestMapping("/contradiction/dispute")
public class ContradictionDisputeController  extends BaseController {

    private static final Logger log = LoggerFactory.getLogger(ContradictionDisputeController.class);

    private static final String tenantCode = "TE240529737932362773";
    private static final String pwd = "1qaz2wsx";
    private static final String secretParam = "11024f80-44ed-4b1e-baab-482391ecc15d";
    private static final String resourceCode = "DB240523331774552194";



    /**
     * 获取纠纷事件类型接口
     * @return
     */
    @GetMapping("/getEventType")
    public String getEventType() {
        RestTemplate restTemplate = new RestTemplate();
        String url = "http://10.45.178.221:8881/eventSummary/getEventType";
        String nonce = UUID.randomUUID().toString();
        DateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        String created = sdf.format(new Date());
        String responseData = forGetSecretKey(tenantCode, pwd, secretParam);
        String code = JSONObject.parseObject(responseData).getString("code");
        if (!code.equals(String.valueOf(200))) {
            String msg = JSONObject.parseObject(responseData).getString("msg");
            throw new CustomException(msg);
        }
        String resultData = JSONObject.parseObject(responseData).getString("data");
        String secretKey = SymmetricEncoder.AESDncode(secretParam, resultData);

        String secret = nonce + "_" + created + "_" + secretKey;
        String PasswdDigest = new SM3Utils().sm3(secret);
        ShareQueryParam shareQueryParam = new ShareQueryParam();
        shareQueryParam.setResourceCode(resourceCode);
        shareQueryParam.setResourceSecret(secretParam);
        shareQueryParam.setTenantCode(tenantCode);
        shareQueryParam.setPasswdDigest(PasswdDigest);
        shareQueryParam.setNonce(nonce);
        shareQueryParam.setCreated(created);

        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        headers.set("tenantCode", tenantCode);
        headers.set("resCode", resourceCode);
        HttpEntity<ShareQueryParam> request =
                new HttpEntity<ShareQueryParam>(shareQueryParam, headers);
        ResponseEntity<String> responseEntity = restTemplate.postForEntity(url, request, String.class);
        String resData = responseEntity.getBody();
        log.info("接收到共享查询的响应信息为：{}", resData);
        String resCode = JSONObject.parseObject(resData).getString("code");
        if (!resCode.equals("200")) {
            String msg = JSONObject.parseObject(resData).getString("msg");
            throw new CustomException(msg);
        } else {

            return resData;
        }
    }

    /**
     * 获取纠纷趋势查询接口
     * @return
     */
    @GetMapping("/findPage")
    public String findPage(ShareQueryParam shareQueryParam) {
        RestTemplate restTemplate = new RestTemplate();
        String url = "http://10.45.178.221:8881/eventSummary/findPage";
        String nonce = UUID.randomUUID().toString();
        DateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        String created = sdf.format(new Date());
        String responseData = forGetSecretKey(tenantCode, pwd, secretParam);
        String code = JSONObject.parseObject(responseData).getString("code");
        if (!code.equals(String.valueOf(200))) {
            String msg = JSONObject.parseObject(responseData).getString("msg");
            throw new CustomException(msg);
        }
        String resultData = JSONObject.parseObject(responseData).getString("data");
        String secretKey = SymmetricEncoder.AESDncode(secretParam, resultData);

        String secret = nonce + "_" + created + "_" + secretKey;
        String PasswdDigest = new SM3Utils().sm3(secret);

        shareQueryParam.setResourceCode(resourceCode);
        shareQueryParam.setResourceSecret(secretParam);
        shareQueryParam.setTenantCode(tenantCode);
        shareQueryParam.setPasswdDigest(PasswdDigest);
        shareQueryParam.setNonce(nonce);
        shareQueryParam.setCreated(created);

        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        headers.set("tenantCode", tenantCode);
        headers.set("resCode", resourceCode);
        HttpEntity<ShareQueryParam> request =
                new HttpEntity<ShareQueryParam>(shareQueryParam, headers);
        ResponseEntity<String> responseEntity = restTemplate.postForEntity(url, request, String.class);
        String resData = responseEntity.getBody();
        log.info("接收到共享查询的响应信息为：{}", resData);
        String resCode = JSONObject.parseObject(resData).getString("code");
        if (!resCode.equals("200")) {
            String msg = JSONObject.parseObject(resData).getString("msg");
            throw new CustomException(msg);
        } else {

            return resData;
        }
    }

    /**
     * 获取纠纷趋势查询接口
     * @return
     */
    @GetMapping("/statTrend")
    public String statTrend(String searchTimeType) {
        RestTemplate restTemplate = new RestTemplate();
        String url = "http://10.45.178.221:8881/eventSummary/statTrend";
        String nonce = UUID.randomUUID().toString();
        DateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        String created = sdf.format(new Date());
        String responseData = forGetSecretKey(tenantCode, pwd, secretParam);
        String code = JSONObject.parseObject(responseData).getString("code");
        if (!code.equals(String.valueOf(200))) {
            String msg = JSONObject.parseObject(responseData).getString("msg");
            throw new CustomException(msg);
        }
        String resultData = JSONObject.parseObject(responseData).getString("data");
        String secretKey = SymmetricEncoder.AESDncode(secretParam, resultData);

        String secret = nonce + "_" + created + "_" + secretKey;
        String PasswdDigest = new SM3Utils().sm3(secret);

        ShareQueryParam shareQueryParam = new ShareQueryParam();
        shareQueryParam.setResourceCode(resourceCode);
        shareQueryParam.setResourceSecret(secretParam);
        shareQueryParam.setTenantCode(tenantCode);
        shareQueryParam.setPasswdDigest(PasswdDigest);
        shareQueryParam.setNonce(nonce);
        shareQueryParam.setCreated(created);
        shareQueryParam.setSearchTimeType(searchTimeType);

        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        headers.set("tenantCode", tenantCode);
        headers.set("resCode", resourceCode);
        HttpEntity<ShareQueryParam> request =
                new HttpEntity<ShareQueryParam>(shareQueryParam, headers);
        ResponseEntity<String> responseEntity = restTemplate.postForEntity(url, request, String.class);
        String resData = responseEntity.getBody();
        log.info("接收到共享查询的响应信息为：{}", resData);
        String resCode = JSONObject.parseObject(resData).getString("code");
        if (!resCode.equals("200")) {
            String msg = JSONObject.parseObject(resData).getString("msg");
            throw new CustomException(msg);
        } else {

            return resData;
        }
    }


    /**
     * 获取纠纷排名查询接口
     * @return
     */
    @GetMapping("/statRanking")
    public String statRanking(String type) {
        RestTemplate restTemplate = new RestTemplate();
        String url = "http://10.45.178.221:8881/eventSummary/statRanking";
        String nonce = UUID.randomUUID().toString();
        DateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        String created = sdf.format(new Date());
        String responseData = forGetSecretKey(tenantCode, pwd, secretParam);
        String code = JSONObject.parseObject(responseData).getString("code");
        if (!code.equals(String.valueOf(200))) {
            String msg = JSONObject.parseObject(responseData).getString("msg");
            throw new CustomException(msg);
        }
        String resultData = JSONObject.parseObject(responseData).getString("data");
        String secretKey = SymmetricEncoder.AESDncode(secretParam, resultData);

        String secret = nonce + "_" + created + "_" + secretKey;
        String PasswdDigest = new SM3Utils().sm3(secret);

        ShareQueryParam shareQueryParam = new ShareQueryParam();
        shareQueryParam.setResourceCode(resourceCode);
        shareQueryParam.setResourceSecret(secretParam);
        shareQueryParam.setTenantCode(tenantCode);
        shareQueryParam.setPasswdDigest(PasswdDigest);
        shareQueryParam.setNonce(nonce);
        shareQueryParam.setCreated(created);
        shareQueryParam.setType(type);

        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        headers.set("tenantCode", tenantCode);
        headers.set("resCode", resourceCode);


        HttpEntity<ShareQueryParam> request =
                new HttpEntity<ShareQueryParam>(shareQueryParam, headers);


        ResponseEntity<String> responseEntity = restTemplate.postForEntity(url, request, String.class);

        String resData = responseEntity.getBody();
        log.info("接收到共享查询的响应信息为：{}", resData);
        String resCode = JSONObject.parseObject(resData).getString("code");
        if (!resCode.equals("200")) {
            String msg = JSONObject.parseObject(resData).getString("msg");
            throw new CustomException(msg);
        } else {

            return resData;
        }
    }

    /**
     * 获取矛盾纠纷数查询接口
     *
     * @return
     */
    @GetMapping("/statCount")
    public JSONObject statCount() {

        RestTemplate restTemplate = new RestTemplate();
        String url = "http://10.45.178.221:8881/eventSummary/statCount";
        String nonce = UUID.randomUUID().toString();
        DateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        String created = sdf.format(new Date());
        // String responseData = "sRpjHFk3Lyu1CNBJL02c5gviG5fFChKbQ99T/fZNhPTAAfUbNqX4GGAo3wMMyL4o\r\n";
        String responseData = forGetSecretKey(tenantCode, pwd, secretParam);
        String code = JSONObject.parseObject(responseData).getString("code");
        if (!code.equals(String.valueOf(200))) {
            String msg = JSONObject.parseObject(responseData).getString("msg");
            throw new CustomException(msg);
        }
        // JSONObject data = JSONObject.parseObject(responseData).getJSONObject("data");
        String resultData = JSONObject.parseObject(responseData).getString("data");
        String secretKey = SymmetricEncoder.AESDncode(secretParam, resultData);
        // log.info("接收到获取密钥key为：{}", secretKey);

        String secret = nonce + "_" + created + "_" + secretKey;
        String PasswdDigest = new SM3Utils().sm3(secret);

        //String dataParam = "resourceCode=" + resourceCode + "&resourceSecret=" + secretParam + "&tenantCode=" + tenantCode + "&PasswdDigest=" + PasswdDigest + "&Nonce=" + nonce + "&Created=" + created + "&pageNum=" + pageNumS + "&pageSize=" + pageSizeS;
        // log.info("接收到共享查询请求地址：{},请求参数：appKey->{},tenantCode->{},passwdDigest->{},nonce->{},created->{},pageNum->{},pageSize->{}", url, secret, tenantCode, PasswdDigest, nonce, created, pageNumS, pageSizeS);

        ShareQueryParam shareQueryParam = new ShareQueryParam();
        shareQueryParam.setResourceCode(resourceCode);
        shareQueryParam.setResourceSecret(secretParam);
        shareQueryParam.setTenantCode(tenantCode);
        shareQueryParam.setPasswdDigest(PasswdDigest);
        shareQueryParam.setNonce(nonce);
        shareQueryParam.setCreated(created);
        // shareQueryParam.setPageNum(pageNumS);
        // shareQueryParam.setPageSize(pageSizeS);
        // shareQueryParam.setCondition(condition);


        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        headers.set("tenantCode", tenantCode);
        headers.set("resCode", resourceCode);


        HttpEntity<ShareQueryParam> request =
                new HttpEntity<ShareQueryParam>(shareQueryParam, headers);


        ResponseEntity<String> responseEntity = restTemplate.postForEntity(url, request, String.class);

        String resData = responseEntity.getBody();
        log.info("接收到共享查询的响应信息为：{}", resData);
        String resCode = JSONObject.parseObject(resData).getString("code");
        if (!resCode.equals("200")) {
            String msg = JSONObject.parseObject(resData).getString("msg");
            throw new CustomException(msg);
        } else {
            JSONObject resDataResult = JSONObject.parseObject(resData).getJSONObject("data");
            return resDataResult;
        }

    }

    private String forGetSecretKey(final String tentCode, final String password, final String secret) {
        String url = "http://10.45.178.221:8881/eventSummary/getSecretKey";
        String sign = new SM3Utils().sm3(secret + "#" + tentCode + "#" + password);
        RestTemplate restTemplate = new RestTemplate();
        SecretParam secretParam = new SecretParam();
        secretParam.setResourceSecret(secret);
        secretParam.setSign(sign);
        secretParam.setTenantCode(tentCode);

        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);

        HttpEntity<SecretParam> request =
                new HttpEntity<SecretParam>(secretParam, headers);

        ResponseEntity<String> responseEntity = restTemplate.postForEntity(url, request, String.class);
        log.info(responseEntity.getBody());
        String responseData = responseEntity.getBody();
        return responseData;
    }
}
