# 市级指令办理情况API文档

## 概述

市级指令办理情况模块提供了对市级指令办理情况的统计和查询功能，包括超时接收、超时处置、应处置未处置、待反馈等数据的统计和详细列表查询。

## API接口

### 1. 获取市级指令办理情况统计数据

**接口地址：** `GET /instruction/cityHandling/data`

**功能说明：** 默认查询金华市全部数据，不受当前用户部门限制

**请求参数：**
- `startTime` (可选): 开始时间，格式：yyyy-MM-dd
- `endTime` (可选): 结束时间，格式：yyyy-MM-dd

**使用示例：**
```
GET /instruction/cityHandling/data?startTime=2025-08-01&endTime=2025-08-31
```

**响应数据：**
```json
{
  "code": 200,
  "msg": "操作成功",
  "data": {
    "timeoutReceiveCount": 2,
    "timeoutDisposeCount": 11,
    "unprocessedUnitCount": 22,
    "unprocessedDeptCount": 2,
    "pendingFeedback7Days": 1,
    "pendingFeedback15Days": 2,
    "pendingFeedback30Days": 3
  }
}
```

### 2. 获取市级指令办理情况详细列表

**接口地址：** `GET /instruction/cityHandling/list`

**功能说明：** 默认查询金华市全部数据，不受当前用户部门限制。返回所有统计指标对应的具体数据列表。

**请求参数：**
- `startTime` (可选): 开始时间，格式：yyyy-MM-dd
- `endTime` (可选): 结束时间，格式：yyyy-MM-dd

**使用示例：**
```
GET /instruction/cityHandling/list?startTime=2025-08-01&endTime=2025-08-31
```

**响应数据：**
```json
{
  "code": 200,
  "msg": "操作成功",
  "data": {
    "timeoutReceiveList": [
      {
        "id": 1001,
        "instructionTitle": "某某事件处置指令",
        "emergencyDegree": "紧急",
        "instructionType": "双排双办",
        "assignTime": "2025-08-01 10:00:00",
        "handleTime": "2025-08-15 18:00:00",
        "receiveDept": "东阳市某某镇",
        "unitType": "主办单位",
        "overdueDays": 5
      }
    ],
    "timeoutDisposeList": [
      {
        "id": 1002,
        "instructionTitle": "另一个处置指令",
        "emergencyDegree": "特急",
        "instructionType": "预警研判",
        "assignTime": "2025-08-02 09:00:00",
        "handleTime": "2025-08-10 17:00:00",
        "receiveDept": "婺城区某某街道",
        "unitType": "协办单位",
        "overdueDays": 3
      }
    ],
    "unprocessedList": [
      {
        "id": 1003,
        "instructionTitle": "未处置指令",
        "emergencyDegree": "一般",
        "instructionType": "双排双办",
        "assignTime": "2025-08-03 14:00:00",
        "receiveDept": "义乌市某某镇",
        "unitType": "主办单位",
        "overdueDays": 10
      }
    ],
    "pendingFeedback7DaysList": [
      {
        "id": 1004,
        "instructionTitle": "特急待反馈指令",
        "emergencyDegree": "特急",
        "instructionType": "双排双办",
        "assignTime": "2025-08-04 11:00:00",
        "receiveDept": "兰溪市某某镇",
        "unitType": "主办单位",
        "lastFeedbackTime": "2025-08-05 16:00:00",
        "overdueDays": 2
      }
    ],
    "pendingFeedback15DaysList": [
      {
        "id": 1005,
        "instructionTitle": "紧急待反馈指令",
        "emergencyDegree": "紧急",
        "instructionType": "预警研判",
        "assignTime": "2025-08-05 08:00:00",
        "receiveDept": "浦江县某某镇",
        "unitType": "协办单位",
        "lastFeedbackTime": "2025-08-06 10:00:00",
        "overdueDays": 8
      }
    ],
    "pendingFeedback30DaysList": [
      {
        "id": 1006,
        "instructionTitle": "一般待反馈指令",
        "emergencyDegree": "一般",
        "instructionType": "双排双办",
        "assignTime": "2025-08-06 15:00:00",
        "receiveDept": "永康市某某镇",
        "unitType": "主办单位",
        "lastFeedbackTime": "2025-08-07 12:00:00",
        "overdueDays": 20
      }
    ]
  }
}
```

### 3. 导出办理情况数据

**接口地址：** `POST /instruction/cityHandling/export`

**功能说明：** 默认导出金华市全部数据，不受当前用户部门限制

**请求参数：**
- `startTime` (可选): 开始时间
- `endTime` (可选): 结束时间

**响应：** Excel文件下载

## 业务逻辑说明

### 1. 时间参数
- 默认按照交办时间进行筛选
- 如果不传时间参数，则查询全量数据

### 2. 超时接收
- 参照金安治市级指令里的超时接收逻辑
- 根据用户所属区域（如东阳市）筛选对应区域的数据
- 点击数据可以穿透到对应的指令列表

### 3. 超时处置
- 参照金安治市级指令里的超时处置逻辑
- 根据用户所属区域筛选数据
- 点击数据可以穿透到对应的指令列表

### 4. 应处置未处置
- 对应市级指令工作台应处置未处置的逻辑
- 数据分解到县级层级下面的乡镇和镇街
- 按照市级指令要求：一般30天反馈一次，紧急15天一次，特急7天一次
- 统计应处置未处置的单位数和部门数

### 5. 待反馈
- 分成7天（特急）、15天（紧急）、30天（一般）展示对应的需反馈指令数
- 需反馈逻辑：乡镇已经办结需要县市向上反馈的 + 市级针对县市层级驳回的指令
- 一旦县市层级向上反馈之后或者县市层级向下驳回之后，不再统计
- 点击数据可以穿透到对应的指令列表

## 数据权限

**重要说明：** 该模块默认查询金华市全部数据，不受当前用户部门限制。所有接口都会返回金华市范围内的完整数据。

## 导出功能

提供Excel导出功能，包含超时接收、超时处置、应处置未处置三个工作表，方便用户进行数据分析和报告。
