package com.ruoyi.instruction.controller;

import java.util.List;
import java.util.Map;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.instruction.domain.InstructionAssign;
import com.ruoyi.instruction.service.IInstructionAssignService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 指令交办Controller
 * 
 * <AUTHOR>
 * @date 2022-12-19
 */
@RestController
@RequestMapping("/instruction/assign")
public class InstructionAssignController extends BaseController
{
    @Autowired
    private IInstructionAssignService instructionAssignService;

    /**
     * 查询指令交办列表
     */
    @PreAuthorize("@ss.hasPermi('instruction:assign:list')")
    @GetMapping("/list")
    public TableDataInfo list(InstructionAssign instructionAssign)
    {
        startPage();
        List<InstructionAssign> list = instructionAssignService.selectInstructionAssignList(instructionAssign);
        return getDataTable(list);
    }

    /**
     * 导出指令交办列表
     */
    @PreAuthorize("@ss.hasPermi('instruction:assign:export')")
    @Log(title = "指令交办", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, InstructionAssign instructionAssign)
    {
        List<InstructionAssign> list = instructionAssignService.selectInstructionAssignList(instructionAssign);
        ExcelUtil<InstructionAssign> util = new ExcelUtil<InstructionAssign>(InstructionAssign.class);
        util.exportExcel(response, list, "指令交办数据");
    }

    /**
     * 获取指令交办详细信息
     */
    @PreAuthorize("@ss.hasPermi('instruction:assign:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(instructionAssignService.selectInstructionAssignById(id));
    }

    /**
     * 新增指令交办
     */
    @PreAuthorize("@ss.hasPermi('instruction:assign:add')")
    @Log(title = "指令交办", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody InstructionAssign instructionAssign)
    {
        return toAjax(instructionAssignService.insertInstructionAssign(instructionAssign));
    }

    /**
     * 修改指令交办
     */
    @PreAuthorize("@ss.hasPermi('instruction:assign:edit')")
    @Log(title = "指令交办", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody InstructionAssign instructionAssign)
    {
        return toAjax(instructionAssignService.updateInstructionAssign(instructionAssign));
    }

    /**
     * 删除指令交办
     */
    @PreAuthorize("@ss.hasPermi('instruction:assign:remove')")
    @Log(title = "指令交办", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(instructionAssignService.deleteInstructionAssignByIds(ids));
    }
}
