package com.ruoyi.instruction.domain;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 平安指数对象 t_pazs
 *
 * <AUTHOR>
 * @date 2023-08-09
 */
public class Pazs extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 序号 */
    private Long id;

    /** 年份 */
    @Excel(name = "年份")
    private Integer year;

    /** 月份 */
    @Excel(name = "月份", combo = "1,2,3,4,5,6,7,8,9,10,11,12")
    private Integer month;

    /** 县（市、区） */
    @Excel(name = "县（市、区）", combo = "金华市,婺城区,金东区,兰溪市,东阳市,义乌市,永康市,浦江县,武义县,磐安县,开发区")
    private String county;

    /** 平安指数 */
    @Excel(name = "平安\n指数")
    private Double pazs;

    /** 排名 */
    @Excel(name = "平安指数排名")
    private Long pazsRank;

    /** 社会政治安全 */
    @Excel(name = "社会政治安全")
    private Double shzz;

    /** 排名 */
    @Excel(name = "社会政治安全排名")
    private Long shzzRank;

    /** 社会治安安全 */
    @Excel(name = "社会治安安全")
    private Double shza;

    /** 排名 */
    @Excel(name = "社会治安安全排名")
    private Long shzaRank;

    /** 经济金融安全 */
    @Excel(name = "经济金融安全")
    private Double jjjr;

    /** 排名 */
    @Excel(name = "经济金融安全排名")
    private Long jjjrRank;

    /** 生产安全 */
    @Excel(name = "生产\n安全")
    private Double sc;

    /** 排名 */
    @Excel(name = "生产安全排名")
    private Long scRank;

    /** 食品药品安全 */
    @Excel(name = "食品药品安全")
    private Double spyp;

    /** 排名 */
    @Excel(name = "食品药品安全排名")
    private Long spypRank;

    /** 生态环境安全 */
    @Excel(name = "生态环境安全")
    private Double sthj;

    /** 排名 */
    @Excel(name = "生态环境安全排名")
    private Long sthjRank;

    /** 网络与信息安全 */
    @Excel(name = "网络与信息安全")
    private Double wlyxx;

    /** 排名 */
    @Excel(name = "网络与信息安全排名")
    private Long wlyxxRank;

    /** 突发公共事件 */
    @Excel(name = "突发公共事件")
    private Double tfggsj;

    /** 排名 */
    @Excel(name = "突发公共事件排名")
    private Long thggsjRank;

    /** 状态 1：正常  9：删除 */
    private Integer status;

    private String type;

    private String condition;

    private String rankName;

    private String rankType;

    public void setId(Long id)
    {
        this.id = id;
    }

    public Long getId()
    {
        return id;
    }
    public void setYear(Integer year)
    {
        this.year = year;
    }

    public Integer getYear()
    {
        return year;
    }
    public void setMonth(Integer month)
    {
        this.month = month;
    }

    public Integer getMonth()
    {
        return month;
    }
    public void setCounty(String county)
    {
        this.county = county;
    }

    public String getCounty()
    {
        return county;
    }
    public void setPazs(Double pazs)
    {
        this.pazs = pazs;
    }

    public Double getPazs()
    {
        return pazs;
    }
    public void setPazsRank(Long pazsRank)
    {
        this.pazsRank = pazsRank;
    }

    public Long getPazsRank()
    {
        return pazsRank;
    }
    public void setShzz(Double shzz)
    {
        this.shzz = shzz;
    }

    public Double getShzz()
    {
        return shzz;
    }
    public void setShzzRank(Long shzzRank)
    {
        this.shzzRank = shzzRank;
    }

    public Long getShzzRank()
    {
        return shzzRank;
    }
    public void setShza(Double shza)
    {
        this.shza = shza;
    }

    public Double getShza()
    {
        return shza;
    }
    public void setShzaRank(Long shzaRank)
    {
        this.shzaRank = shzaRank;
    }

    public Long getShzaRank()
    {
        return shzaRank;
    }
    public void setJjjr(Double jjjr)
    {
        this.jjjr = jjjr;
    }

    public Double getJjjr()
    {
        return jjjr;
    }
    public void setJjjrRank(Long jjjrRank)
    {
        this.jjjrRank = jjjrRank;
    }

    public Long getJjjrRank()
    {
        return jjjrRank;
    }
    public void setSc(Double sc)
    {
        this.sc = sc;
    }

    public Double getSc()
    {
        return sc;
    }
    public void setScRank(Long scRank)
    {
        this.scRank = scRank;
    }

    public Long getScRank()
    {
        return scRank;
    }
    public void setSpyp(Double spyp)
    {
        this.spyp = spyp;
    }

    public Double getSpyp()
    {
        return spyp;
    }
    public void setSpypRank(Long spypRank)
    {
        this.spypRank = spypRank;
    }

    public Long getSpypRank()
    {
        return spypRank;
    }
    public void setSthj(Double sthj)
    {
        this.sthj = sthj;
    }

    public Double getSthj()
    {
        return sthj;
    }
    public void setSthjRank(Long sthjRank)
    {
        this.sthjRank = sthjRank;
    }

    public Long getSthjRank()
    {
        return sthjRank;
    }
    public void setWlyxx(Double wlyxx)
    {
        this.wlyxx = wlyxx;
    }

    public Double getWlyxx()
    {
        return wlyxx;
    }
    public void setWlyxxRank(Long wlyxxRank)
    {
        this.wlyxxRank = wlyxxRank;
    }

    public Long getWlyxxRank()
    {
        return wlyxxRank;
    }
    public void setTfggsj(Double tfggsj)
    {
        this.tfggsj = tfggsj;
    }

    public Double getTfggsj()
    {
        return tfggsj;
    }
    public void setThggsjRank(Long thggsjRank)
    {
        this.thggsjRank = thggsjRank;
    }

    public Long getThggsjRank()
    {
        return thggsjRank;
    }
    public void setStatus(Integer status)
    {
        this.status = status;
    }

    public Integer getStatus()
    {
        return status;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getCondition() {
        return condition;
    }

    public void setCondition(String condition) {
        this.condition = condition;
    }

    public String getRankName() {
        return rankName;
    }

    public void setRankName(String rankName) {
        this.rankName = rankName;
    }

    public String getRankType() {
        return rankType;
    }

    public void setRankType(String rankType) {
        this.rankType = rankType;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("year", getYear())
            .append("month", getMonth())
            .append("county", getCounty())
            .append("pazs", getPazs())
            .append("pazsRank", getPazsRank())
            .append("shzz", getShzz())
            .append("shzzRank", getShzzRank())
            .append("shza", getShza())
            .append("shzaRank", getShzaRank())
            .append("jjjr", getJjjr())
            .append("jjjrRank", getJjjrRank())
            .append("sc", getSc())
            .append("scRank", getScRank())
            .append("spyp", getSpyp())
            .append("spypRank", getSpypRank())
            .append("sthj", getSthj())
            .append("sthjRank", getSthjRank())
            .append("wlyxx", getWlyxx())
            .append("wlyxxRank", getWlyxxRank())
            .append("tfggsj", getTfggsj())
            .append("thggsjRank", getThggsjRank())
            .append("status", getStatus())
            .toString();
    }
}
