package com.ruoyi.instruction.domain.excelVo;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import lombok.Data;

/**
 * 人员类型变更ExcelVo
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2024/9/7 10:12
 */
@Data
public class PersonTypeChangeExcelVo {

    @ColumnWidth(20)
    @ExcelProperty("县市区域")
    private String county;

    @ExcelProperty({"涉稳警情类", "红色"})
    private Long swjqRedNum;

    @ExcelProperty({"涉稳警情类", "橙色"})
    private Long swjqOrangeNum;

    @ExcelProperty({"涉稳警情类", "黄色"})
    private Long swjqYellowNum;

    @ExcelProperty({"涉稳警情类", "小计"})
    private Long swjqTotalNum;

    @ExcelProperty({"诉求群体类", "红色"})
    private Long sqqtRedNum;

    @ExcelProperty({"诉求群体类", "橙色"})
    private Long sqqtOrangeNum;

    @ExcelProperty({"诉求群体类", "黄色"})
    private Long sqqtYellowNum;

    @ExcelProperty({"诉求群体类", "小计"})
    private Long sqqtTotalNum;

    @ExcelProperty({"个人极端类", "红色"})
    private Long grjdRedNum;

    @ExcelProperty({"个人极端类", "橙色"})
    private Long grjdOrangeNum;

    @ExcelProperty({"个人极端类", "黄色"})
    private Long grjdYellowNum;

    @ExcelProperty({"个人极端类", "小计"})
    private Long grjdTotalNum;

    @ExcelProperty({"煽动串联类", "红色"})
    private Long sdclRedNum;

    @ExcelProperty({"煽动串联类", "橙色"})
    private Long sdclOrangeNum;

    @ExcelProperty({"煽动串联类", "黄色"})
    private Long sdclYellowNum;

    @ExcelProperty({"煽动串联类", "小计"})
    private Long sdclTotalNum;

    @ExcelProperty({"易肇事肇祸精神病", "红色"})
    private Long yzszhjsbRedNum;

    @ExcelProperty({"易肇事肇祸精神病", "橙色"})
    private Long yzszhjsbOrangeNum;

    @ExcelProperty({"易肇事肇祸精神病", "黄色"})
    private Long yzszhjsbYellowNum;

    @ExcelProperty({"易肇事肇祸精神病", "小计"})
    private Long yzszhjsbTotalNum;

    @ExcelProperty({"其他", "红色"})
    private Long otherRedNum;

    @ExcelProperty({"其他", "橙色"})
    private Long otherOrangeNum;

    @ExcelProperty({"其他", "黄色"})
    private Long otherYellowNum;

    @ExcelProperty({"其他", "小计"})
    private Long otherTotalNum;

    @ExcelProperty({"总计", "红色"})
    private Long totalRedNum;

    @ExcelProperty({"总计", "橙色"})
    private Long totalOrangeNum;

    @ExcelProperty({"总计", "黄色"})
    private Long totalYellowNum;

    @ExcelProperty({"总计", "小计"})
    private Long totalTotalNum;

}
