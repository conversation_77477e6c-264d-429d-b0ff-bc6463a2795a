package com.ruoyi.instruction.domain.rspVo;

import com.ruoyi.common.annotation.Excel;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/1/30 13:34
 */
@Data
public class InstructionPersonNoGkExportVo {


    @Excel(name = "序号",needMerge = true)
    private Integer index;

    /** 人员姓名 */
    @Excel(name = "人员姓名")
    private String personName;


    /** 户籍所在地 */
    @Excel(name = "户籍地")
    private String housePlace;

    /** 当前居住地 */
    @Excel(name = "常住地")
    private String currentPlace;

    /** 电话号码 */
    @Excel(name = "电话号码")
    private String personPhone;

    @Excel(name = "属地",combo = {"婺城区","金东区","兰溪市","东阳市","义乌市","永康市","浦江县","武义县","磐安县","开发区"})
    private String dutyPlace;

    /** 人员所属乡镇 */
    @Excel(name = "责任乡镇")
    private String pTown;

    /** 责任所在地 */
    @Excel(name = "管控级别(必填)", combo = {"红,橙,黄"},readConverterExp = "1=黄,2=橙,3=红")
    private Integer pLevel;

    /**
     * 关联事件数
     */
    @Excel(name = "活跃次数", type = Excel.Type.EXPORT)
    private Integer eventNum;

    /**
     *挑头次数
     */
    @Excel(name = "挑头次数", type = Excel.Type.EXPORT)
    private Integer leaderNum;

    /**
     * 人员最后一次类型名称
     */
    @Excel(name = "事件类型", type = Excel.Type.EXPORT)
    private String typeName;

    /**
     * 人员最后一次类型名称
     */
    @Excel(name = "关联群体", type = Excel.Type.EXPORT)
    private String groupName;

    /** 人员类型  */
    @Excel(name = "人员类型",combo ="涉稳警情类,诉求群体类,个人极端类,煽动串联类,易肇事肇祸精神病")
    private String pType;




    /** 人员来源 1:系统变更  2：新增 */
    private Long personSource;

    /** 进京次数 */
    @Excel(name = "进京次数")
    private Long capitalNum;

    /** 赴省次数 */
    @Excel(name = "赴省次数")
    private Long provinceNum;

    public String getpTown() {
        return pTown;
    }

    public void setpTown(final String pTown) {
        this.pTown = pTown;
    }

    public Integer getpLevel() {
        return pLevel;
    }

    public void setpLevel(final Integer pLevel) {
        this.pLevel = pLevel;
    }
}
