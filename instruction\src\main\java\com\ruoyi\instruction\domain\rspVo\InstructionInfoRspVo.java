package com.ruoyi.instruction.domain.rspVo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.common.annotation.Excel;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/12/21 19:39
 */
@Data
public class InstructionInfoRspVo {
    /**
     * 指令id
     */
    private Long id;

    /**
     * 指令标题
     */
    private String instructionTitle;

    /**
     * 当前办理区县
     */
    private String currentCounty;

    /**
     * 关联人数
     */
    private int personCount;

    /**
     * 交办时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date assignTime;

    /**
     * 接收时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date reveiveTime;

    /**
     * mzx接收时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date mzxReveiveTime;

    /**
     * 处置时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date disposeTime;

    /**
     * 反馈时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date feedbackTime;

    /**
     * mzx反馈时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date mzxFeedbackTime;

    /**
     * 销号时间时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date endTime;
    /**
     * 指令创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    /**
     * 县市区反馈时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date countyFeedbackTime;

    /**
     * mzx县市区反馈时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date mzxCountyFeedbackTime;

    /**
     * 县市区已反馈部门
     */
    private Integer countDeptNum;

    /**
     * 接收部门个数
     */
    private Integer receiveUnitCount;

    /**
     * 该条记录是否可以处理 0：可以处理 1：不可处理
     */
    private Integer isDeal;

    /** 指令创建者部门id */
    private Long createDeptId;

    /** 事件类型Id */
    private String type;

    /** 事件类型名称 */
    private String typeName;

    /** 信息来源 */
    private String sourceInfo;

    /** 接收单位 */
    private String receiveUnit;


    /** 办理期限 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date handleTime;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date pushTime;

    /** 反馈要求 */
    private String feedback;

    /** 人员ids */
    private String personIds;

    /** 指令内容 */
    private String instructionContent;

    /** 基本情况 */
    private String baseInfo;

    /**
     * 牵头人员ids
     */
    private String leadPersonIds;

    /**
     * 添加字段，用于驾驶舱标记状态
     */
    private String statusName;

    /** 是否发布 1：发布 2：未发布 */
    private String isRelease;

    /**
     * 督办次数
     */
    private Integer handleNumber;

    /**
     * 提醒次数
     */
    private Integer remindNumber;

    /** 指令创建id */
    private Long instructionCreateDeptId;

    /** 暗访任务名称 */
    private String missionName;

    /** 暗访点位名称 */
    private String companyName;

    /** 县市区 */
    private String county;

    /** 乡镇街道 */
    private String town;

    /** 点位详细信息 */
    private String companyAddress;


    /** 是否审核 0:待审核1：审核 2：未审核 */
    private String isAudit;

    /** 审核人员 */
    private String auditPerson;

    /** 审核时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date auditTime;

    /**
     * 已读部门
     */
    private String readUnits;

    /**
     * 部门
     */
    private String departmentStr;

    private String mzxId;

    /**
     * 市外人员
     */
    private Integer outsidePerson;


    /** 纠纷所在地 */
    private String mzxPlace;

    /** 纠纷性质 */
    private String mzxNature;

    /** 纠纷人员关系 */
    private String mzxRelation;

    /** 纠纷发生时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date mzxOccurTime;

    /** 纠纷化解责任单位 */
    private String mzxDutyDept;

    /** 纠纷类型 */
    private String mzxType;

    /** 纠纷上报来源 */
    private String mzxSourceInfo;

    /**
     * 紧急程度
     */
    @Excel(name = "紧急程度")
    private String emergencyDegree;

    /**
     * 指令类型 1：维稳 2：平安 3：基层  4：工作督查  5:失联失控 6：wz预警事件类型
     */
    private Integer instructionType;

    /**
     * 稳控状态
     */
    private String wkStatus;

    private Long handleDelayId;

    /**
     * 处置部门
     */
    private String transferDept;

    /**
     * 案件类型 优秀案例/反面案例
     */
    private String caseType;

    /**
     * 案例理由
     */
    private String caseReason;

    /**
     * 化解状态
     */
    private String resolveStatus;

    /**
     * 是否已移交
     */
    private Integer isTransferred;

    /**
     * 是否可移交
     */
    private boolean canBeTransferred;

    private Long transferId;
}
