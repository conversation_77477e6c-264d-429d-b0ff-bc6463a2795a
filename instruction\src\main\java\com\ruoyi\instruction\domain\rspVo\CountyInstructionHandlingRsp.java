package com.ruoyi.instruction.domain.rspVo;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import lombok.Data;

import java.util.Date;

/**
 * 县市层级指令办理情况响应VO
 * <AUTHOR>
 * @version 1.0
 * @date 2025/8/2
 */
@Data
public class CountyInstructionHandlingRsp {

    /**
     * 指令ID
     */
    @ExcelProperty("指令ID")
    private Long id;

    /**
     * 指令标题
     */
    @ExcelProperty("指令标题")
    @ColumnWidth(30)
    private String instructionTitle;

    /**
     * 紧急程度
     */
    @ExcelProperty("紧急程度")
    @ColumnWidth(15)
    private String emergencyDegree;

    /**
     * 指令类型
     */
    @ExcelProperty("指令类型")
    @ColumnWidth(15)
    private String instructionType;

    /**
     * 交办时间
     */
    @ExcelProperty("交办时间")
    @ColumnWidth(20)
    private Date assignTime;

    /**
     * 办理期限
     */
    @ExcelProperty("办理期限")
    @ColumnWidth(20)
    private Date handleTime;

    /**
     * 接收部门/处置部门
     */
    @ExcelProperty("部门名称")
    @ColumnWidth(25)
    private String deptName;

    /**
     * 部门类型（县级部门/乡镇街道）
     */
    @ExcelProperty("部门类型")
    @ColumnWidth(15)
    private String deptType;

    /**
     * 单位类型（主办单位/协办单位）
     */
    @ExcelProperty("单位类型")
    @ColumnWidth(15)
    private String unitType;

    /**
     * 接收时间
     */
    @ExcelProperty("接收时间")
    @ColumnWidth(20)
    private Date receiveTime;

    /**
     * 最新反馈时间
     */
    @ExcelProperty("最新反馈时间")
    @ColumnWidth(20)
    private Date lastFeedbackTime;

    /**
     * 超期天数
     */
    @ExcelProperty("超期天数")
    @ColumnWidth(15)
    private Integer overdueDays;

    /**
     * 所属县市区
     */
    @ExcelProperty("所属县市区")
    @ColumnWidth(15)
    private String countyName;
}
