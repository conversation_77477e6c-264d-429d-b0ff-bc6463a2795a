package com.ruoyi.instruction.domain;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

import java.util.Date;

/**
 * 日督查信息对象 t_day_view
 * 
 * <AUTHOR>
 * @date 2023-09-02
 */
@Data
public class DayView extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键 */
    private Long id;

    /** 总体情况 */
    @Excel(name = "总体情况")
    private String ztqk;

    /** 问题隐患 */
    @Excel(name = "问题隐患")
    private String wtyh;

    /** 下步计划 */
    @Excel(name = "下步计划")
    private String xbjh;

    /** 工作建议 */
    @Excel(name = "工作建议")
    private String gzjy;

    /** 经验做法 */
    @Excel(name = "经验做法")
    private String jyzf;

    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date pushTime;


}
