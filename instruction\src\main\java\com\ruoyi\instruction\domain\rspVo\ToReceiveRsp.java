package com.ruoyi.instruction.domain.rspVo;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import lombok.Data;

/**
 * 应接收未接收
 * <AUTHOR>
 * @version 1.0
 * @date 2025/5/8 16:14
 */
@Data
public class ToReceiveRsp {

    @ExcelProperty("序号")
    private Long id;

    @ExcelProperty("指令标题")
    @ColumnWidth(20)
    private String instructionTitle;

    @ExcelProperty("紧急程度")
    @ColumnWidth(20)
    private String emergencyDegree;

    @ExcelProperty("指令类型")
    @ColumnWidth(20)
    private String instructionType;

    @ExcelProperty("交办时间")
    @ColumnWidth(20)
    private String assignTime;

    @ExcelProperty("办理期限")
    @ColumnWidth(20)
    private String handleTime;

    @ExcelProperty("应接收未接收单位")
    @ColumnWidth(20)
    private String receiveUnit;

    @ExcelProperty("类型")
    @ColumnWidth(20)
    private String isMzx;



}
