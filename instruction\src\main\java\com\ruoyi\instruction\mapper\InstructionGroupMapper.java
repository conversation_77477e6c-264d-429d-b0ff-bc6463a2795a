package com.ruoyi.instruction.mapper;

import java.util.Date;
import java.util.List;
import java.util.Map;

import com.ruoyi.instruction.domain.InstructionGroup;
import com.ruoyi.instruction.domain.rspVo.GroupDataRspVo;
import com.ruoyi.instruction.domain.rspVo.GroupRspVo;
import org.apache.ibatis.annotations.Param;

/**
 * 群体基本信息Mapper接口
 *
 * <AUTHOR>
 * @date 2022-12-16
 */
public interface InstructionGroupMapper
{
    /**
     * 查询群体基本信息
     *
     * @param id 群体基本信息主键
     * @return 群体基本信息
     */
    public InstructionGroup selectInstructionGroupById(Long id);

    /**
     * 查询群体基本信息列表
     *
     * @param instructionGroup 群体基本信息
     * @return 群体基本信息集合
     */
    public List<InstructionGroup> selectInstructionGroupList(InstructionGroup instructionGroup);

    /**
     * 查询群体信息，按人次统计，用于分析报告
     * @param id  群体id
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return
     */
    public List<InstructionGroup> selectGroupListForAnalysisReport(@Param("id") Long id, @Param("startTime") Date startTime, @Param("endTime")Date endTime);

    /**
     * 新增群体基本信息
     *
     * @param instructionGroup 群体基本信息
     * @return 结果
     */
    public int insertInstructionGroup(InstructionGroup instructionGroup);

    /**
     * 修改群体基本信息
     *
     * @param instructionGroup 群体基本信息
     * @return 结果
     */
    public int updateInstructionGroup(InstructionGroup instructionGroup);

    /**
     * 删除群体基本信息
     *
     * @param id 群体基本信息主键
     * @return 结果
     */
    public int deleteInstructionGroupById(Long id);

    /**
     * 批量删除群体基本信息
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteInstructionGroupByIds(Long[] ids);

    /**
     * 根据群体名称查询群体信息
     * @param groupName
     * @return
     */
    InstructionGroup findByGroupName(@Param("groupName") String groupName);

    /**
     * 根据群体名称查询关联人员id是
     * @param groupName
     * @return
     */
    String selectPersonIdsByGroupName(@Param("groupName") String groupName);

    /**
     * 获取群体总数、高、中、低管控级别数据
     * @param map
     * @return
     */
    GroupDataRspVo getGroupData(Map<String,Object> map);

    /**
     * 获取群体关联人员排名
     * @return
     */
    List<Map<String, Integer>> getPersonCount();

    /**
     * 获取群体关联事件排名
     * @return
     */
    List<Map<String, Integer>> getEventCount();

    /**
     * 获取群体类型
     * @return
     */
    List<Map<String, Integer>> getType();

    /**
     * 获取群体高频人员
     * @return
     */
    List<Map<String, Integer>> getGroupPerson();

    /**
     * 驾驶舱查询群体基本信息列表，事件在新的一年有提交，也能在新的一年中查出
     *
     * @param instructionGroup 群体基本信息
     * @return 群体基本信息集合
     */
    public List<InstructionGroup> selectInstructionGroupListYear(InstructionGroup instructionGroup);

    /**
     * 根据群体id获取群体人员信息
     * @param id
     * @return
     */
    String getPersonIdsById(@Param("id") Long id);

    /**
     * 根据群体id查询重点人员
     * @param groupIds
     * @return
     */
    String selectPersonIdsByGroupId(@Param("groupIds") Long groupIds);

    /**
     * 根据群体id查询关联人员数
     * @param group
     * @return
     */
    String getPersonIds(InstructionGroup group);

    /**
     * 获取群体列表数据
     * @param group
     * @return
     */
    List<Map<String, Object>> getGroupDataForBigScreen(InstructionGroup group);

    /**
     * 获取群体关联事件市外人员总数
     * @param group
     * @return
     */
    Map<String,Object> getOutSidePerson(InstructionGroup group);

    /**
     * 根据人员id查询所属事件关联群体集合
     * @param id
     * @return
     */
    List<InstructionGroup> findByPersonId(@Param("personId") Long id);

    /**
     * 根据人员id查询所属事件关联群体名称
     * @param id
     * @return
     */
    String findEventGroupNameByPersonId(@Param("personId") Long id);

    /**
     * 获取群体id及群体名称
     * @param instructionGroup
     * @return
     */
    List<GroupRspVo> getNewContactList(InstructionGroup instructionGroup);

    /**
     * 查询异动分析统计中异动数
     * @param group
     * @return
     */
    Long getGroupCount(InstructionGroup group);

    /**
     * 更新群体关联事件数
     * @param groupId
     */
    void updateEventCountById(@Param("groupId") Long groupId);

    /**
     * 获取统计分析页面数据
     * @param instructionGroup
     * @return
     */
    List<InstructionGroup> getBigScreenStatistics(InstructionGroup instructionGroup);

    List<Map<String, Object>> getGroupControllevel();

    /**
     * 获取群体类型
     * @return
     */
    List<Map<String, Object>> getGroupType();

    Long getGroupTypeCount(@Param("typeIds") List<String> typeIds);

    String getGroupTypePersonIds(@Param("typeIds")List<String> typeIds);

    /**
     * 获取近3个月高频异动群体数
     * @return
     */
    List<InstructionGroup> getRiskCheckPoor();

}
