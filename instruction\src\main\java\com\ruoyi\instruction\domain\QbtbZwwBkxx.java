package com.ruoyi.instruction.domain;

import ch.qos.logback.classic.db.names.TableName;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 布控对象 qbtb_zww_bkxx
 * 
 * <AUTHOR>
 * @date 2023-06-03
 */
public class QbtbZwwBkxx extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键 */
    private Long nXh;

    /** 临空指令编号 */
    @Excel(name = "临空指令编号")
    private String lkzlbh;

    /** 临控接受单位 */
    @Excel(name = "临控接受单位")
    private String zljsdw;

    /** 临控类型 */
    @Excel(name = "临控类型")
    private String lkzllx;

    /** 申请人姓名 */
    @Excel(name = "申请人姓名")
    private String sqrxm;

    /** 申请人身份证 */
    @Excel(name = "申请人身份证")
    private String sqrsfzh;

    /** 申请人单位代码 */
    @Excel(name = "申请人单位代码")
    private String sqrdwdm;

    /** 申请人单位名称 */
    @Excel(name = "申请人单位名称")
    private String sqrdwmc;

    /** 申请人联系电话 */
    @Excel(name = "申请人联系电话")
    private String sqrlxdh;

    /** 申请时间 */
    @Excel(name = "申请时间")
    private String dSqsj;

    /** 布控理由 */
    @Excel(name = "布控理由")
    private String bxcksy;

    /** 布控范围 */
    @Excel(name = "布控范围")
    private String bkdyfw;

    /** 布控天数 */
    @Excel(name = "布控天数")
    private String bkts;

    /** 布控开始时间 */
    @Excel(name = "布控开始时间")
    private String dBkqssj;

    /** 布控结束时间 */
    @Excel(name = "布控结束时间")
    private String dBkjzsj;

    /** 布控证件类型 */
    @Excel(name = "布控证件类型")
    private String bbkrzjlx;

    /** 被布控人身份证 */
    @Excel(name = "被布控人身份证")
    private String bbkrzjhm;

    /** 处置措施 */
    @Excel(name = "处置措施")
    private String czcsyq;

    /** 布控接受人 */
    @Excel(name = "布控接受人")
    private String bkjgjsr;

    /** 布控接受人电话 */
    @Excel(name = "布控接受人电话")
    private String bkjgjsrlxdh;

    /** 审核人 */
    // @Excel(name = "审核人")
    // private String shr;

    /** 审核人身份证 */
    @Excel(name = "审核人身份证")
    private String shrsfzh;

    /** 审核单位 */
    @Excel(name = "审核单位")
    private String shdw;

    /** 审核状态 */
    @Excel(name = "审核状态")
    private String nShzt;

    /** 审核意见 */
    @Excel(name = "审核意见")
    private String shyj;

    /** 审核时间 */
    @Excel(name = "审核时间")
    private String dShsj;

    /** 撤空人 */
    @Excel(name = "撤空人")
    private String ckr;

    /** 撤控人身份证 */
    @Excel(name = "撤控人身份证")
    private String ckrsfzh;

    /** 撤空人单位 */
    @Excel(name = "撤空人单位")
    private String ckrdw;

    /** 撤空时间 */
    @Excel(name = "撤空时间")
    private String dCksj;

    /** 撤空理由 */
    @Excel(name = "撤空理由")
    private String ckly;

    /** 续控人 */
    @Excel(name = "续控人")
    private String xkr;

    /** 续控人身份证 */
    @Excel(name = "续控人身份证")
    private String xkrsfzh;

    /** 续控单位 */
    @Excel(name = "续控单位")
    private String xkdw;

    /** 续控时间 */
    @Excel(name = "续控时间")
    private String dXksj;

    /** 安保任务 */
    @Excel(name = "安保任务")
    private String abrw;

    /** 有效性 */
    @Excel(name = "有效性")
    private String nYxx;

    /** 布控理由 */
    @Excel(name = "布控理由")
    private String bkly;

    /** 布控警总 */
    @Excel(name = "布控警总")
    private String bkjz;

    /** 原主键 */
    @Excel(name = "原主键")
    private String nYzj;

    /** 是否管控 */
    @Excel(name = "是否管控")
    private String sfgk;

    /** 布控接受人身份证 */
    @Excel(name = "布控接受人身份证")
    private String bkjgjsrsfzh;

    /** 预留1 */
    @Excel(name = "预留1")
    private String bz1;

    /** 预留2 */
    @Excel(name = "预留2")
    private String bz2;

    /** 预留3 */
    @Excel(name = "预留3")
    private String bz3;

    public void setnXh(Long nXh)
    {
        this.nXh = nXh;
    }

    public Long getnXh()
    {
        return nXh;
    }
    public void setLkzlbh(String lkzlbh)
    {
        this.lkzlbh = lkzlbh;
    }

    public String getLkzlbh()
    {
        return lkzlbh;
    }
    public void setZljsdw(String zljsdw)
    {
        this.zljsdw = zljsdw;
    }

    public String getZljsdw()
    {
        return zljsdw;
    }
    public void setLkzllx(String lkzllx)
    {
        this.lkzllx = lkzllx;
    }

    public String getLkzllx()
    {
        return lkzllx;
    }
    public void setSqrxm(String sqrxm)
    {
        this.sqrxm = sqrxm;
    }

    public String getSqrxm()
    {
        return sqrxm;
    }
    public void setSqrsfzh(String sqrsfzh)
    {
        this.sqrsfzh = sqrsfzh;
    }

    public String getSqrsfzh()
    {
        return sqrsfzh;
    }
    public void setSqrdwdm(String sqrdwdm)
    {
        this.sqrdwdm = sqrdwdm;
    }

    public String getSqrdwdm()
    {
        return sqrdwdm;
    }
    public void setSqrdwmc(String sqrdwmc)
    {
        this.sqrdwmc = sqrdwmc;
    }

    public String getSqrdwmc()
    {
        return sqrdwmc;
    }
    public void setSqrlxdh(String sqrlxdh)
    {
        this.sqrlxdh = sqrlxdh;
    }

    public String getSqrlxdh()
    {
        return sqrlxdh;
    }
    public void setdSqsj(String dSqsj)
    {
        this.dSqsj = dSqsj;
    }

    public String getdSqsj()
    {
        return dSqsj;
    }
    public void setBxcksy(String bxcksy)
    {
        this.bxcksy = bxcksy;
    }

    public String getBxcksy()
    {
        return bxcksy;
    }
    public void setBkdyfw(String bkdyfw)
    {
        this.bkdyfw = bkdyfw;
    }

    public String getBkdyfw()
    {
        return bkdyfw;
    }
    public void setBkts(String bkts)
    {
        this.bkts = bkts;
    }

    public String getBkts()
    {
        return bkts;
    }
    public void setdBkqssj(String dBkqssj)
    {
        this.dBkqssj = dBkqssj;
    }

    public String getdBkqssj()
    {
        return dBkqssj;
    }
    public void setdBkjzsj(String dBkjzsj)
    {
        this.dBkjzsj = dBkjzsj;
    }

    public String getdBkjzsj()
    {
        return dBkjzsj;
    }
    public void setBbkrzjlx(String bbkrzjlx)
    {
        this.bbkrzjlx = bbkrzjlx;
    }

    public String getBbkrzjlx()
    {
        return bbkrzjlx;
    }
    public void setBbkrzjhm(String bbkrzjhm)
    {
        this.bbkrzjhm = bbkrzjhm;
    }

    public String getBbkrzjhm()
    {
        return bbkrzjhm;
    }
    public void setCzcsyq(String czcsyq)
    {
        this.czcsyq = czcsyq;
    }

    public String getCzcsyq()
    {
        return czcsyq;
    }
    public void setBkjgjsr(String bkjgjsr)
    {
        this.bkjgjsr = bkjgjsr;
    }

    public String getBkjgjsr()
    {
        return bkjgjsr;
    }
    public void setBkjgjsrlxdh(String bkjgjsrlxdh)
    {
        this.bkjgjsrlxdh = bkjgjsrlxdh;
    }

    public String getBkjgjsrlxdh()
    {
        return bkjgjsrlxdh;
    }

    public void setShrsfzh(String shrsfzh)
    {
        this.shrsfzh = shrsfzh;
    }

    public String getShrsfzh()
    {
        return shrsfzh;
    }
    public void setShdw(String shdw)
    {
        this.shdw = shdw;
    }

    public String getShdw()
    {
        return shdw;
    }
    public void setnShzt(String nShzt)
    {
        this.nShzt = nShzt;
    }

    public String getnShzt()
    {
        return nShzt;
    }
    public void setShyj(String shyj)
    {
        this.shyj = shyj;
    }

    public String getShyj()
    {
        return shyj;
    }
    public void setdShsj(String dShsj)
    {
        this.dShsj = dShsj;
    }

    public String getdShsj()
    {
        return dShsj;
    }
    public void setCkr(String ckr)
    {
        this.ckr = ckr;
    }

    public String getCkr()
    {
        return ckr;
    }
    public void setCkrsfzh(String ckrsfzh)
    {
        this.ckrsfzh = ckrsfzh;
    }

    public String getCkrsfzh()
    {
        return ckrsfzh;
    }
    public void setCkrdw(String ckrdw)
    {
        this.ckrdw = ckrdw;
    }

    public String getCkrdw()
    {
        return ckrdw;
    }
    public void setdCksj(String dCksj)
    {
        this.dCksj = dCksj;
    }

    public String getdCksj()
    {
        return dCksj;
    }
    public void setCkly(String ckly)
    {
        this.ckly = ckly;
    }

    public String getCkly()
    {
        return ckly;
    }
    public void setXkr(String xkr)
    {
        this.xkr = xkr;
    }

    public String getXkr()
    {
        return xkr;
    }
    public void setXkrsfzh(String xkrsfzh)
    {
        this.xkrsfzh = xkrsfzh;
    }

    public String getXkrsfzh()
    {
        return xkrsfzh;
    }
    public void setXkdw(String xkdw)
    {
        this.xkdw = xkdw;
    }

    public String getXkdw()
    {
        return xkdw;
    }
    public void setdXksj(String dXksj)
    {
        this.dXksj = dXksj;
    }

    public String getdXksj()
    {
        return dXksj;
    }
    public void setAbrw(String abrw)
    {
        this.abrw = abrw;
    }

    public String getAbrw()
    {
        return abrw;
    }
    public void setnYxx(String nYxx)
    {
        this.nYxx = nYxx;
    }

    public String getnYxx()
    {
        return nYxx;
    }
    public void setBkly(String bkly)
    {
        this.bkly = bkly;
    }

    public String getBkly()
    {
        return bkly;
    }
    public void setBkjz(String bkjz)
    {
        this.bkjz = bkjz;
    }

    public String getBkjz()
    {
        return bkjz;
    }
    public void setnYzj(String nYzj)
    {
        this.nYzj = nYzj;
    }

    public String getnYzj()
    {
        return nYzj;
    }
    public void setSfgk(String sfgk)
    {
        this.sfgk = sfgk;
    }

    public String getSfgk()
    {
        return sfgk;
    }
    public void setBkjgjsrsfzh(String bkjgjsrsfzh)
    {
        this.bkjgjsrsfzh = bkjgjsrsfzh;
    }

    public String getBkjgjsrsfzh()
    {
        return bkjgjsrsfzh;
    }
    public void setBz1(String bz1)
    {
        this.bz1 = bz1;
    }

    public String getBz1()
    {
        return bz1;
    }
    public void setBz2(String bz2)
    {
        this.bz2 = bz2;
    }

    public String getBz2()
    {
        return bz2;
    }
    public void setBz3(String bz3)
    {
        this.bz3 = bz3;
    }

    public String getBz3()
    {
        return bz3;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("nXh", getnXh())
            .append("lkzlbh", getLkzlbh())
            .append("zljsdw", getZljsdw())
            .append("lkzllx", getLkzllx())
            .append("sqrxm", getSqrxm())
            .append("sqrsfzh", getSqrsfzh())
            .append("sqrdwdm", getSqrdwdm())
            .append("sqrdwmc", getSqrdwmc())
            .append("sqrlxdh", getSqrlxdh())
            .append("dSqsj", getdSqsj())
            .append("bxcksy", getBxcksy())
            .append("bkdyfw", getBkdyfw())
            .append("bkts", getBkts())
            .append("dBkqssj", getdBkqssj())
            .append("dBkjzsj", getdBkjzsj())
            .append("bbkrzjlx", getBbkrzjlx())
            .append("bbkrzjhm", getBbkrzjhm())
            .append("czcsyq", getCzcsyq())
            .append("bkjgjsr", getBkjgjsr())
            .append("bkjgjsrlxdh", getBkjgjsrlxdh())
            .append("shrsfzh", getShrsfzh())
            .append("shdw", getShdw())
            .append("nShzt", getnShzt())
            .append("shyj", getShyj())
            .append("dShsj", getdShsj())
            .append("ckr", getCkr())
            .append("ckrsfzh", getCkrsfzh())
            .append("ckrdw", getCkrdw())
            .append("dCksj", getdCksj())
            .append("ckly", getCkly())
            .append("xkr", getXkr())
            .append("xkrsfzh", getXkrsfzh())
            .append("xkdw", getXkdw())
            .append("dXksj", getdXksj())
            .append("abrw", getAbrw())
            .append("nYxx", getnYxx())
            .append("bkly", getBkly())
            .append("bkjz", getBkjz())
            .append("nYzj", getnYzj())
            .append("sfgk", getSfgk())
            .append("bkjgjsrsfzh", getBkjgjsrsfzh())
            .append("bz1", getBz1())
            .append("bz2", getBz2())
            .append("bz3", getBz3())
            .toString();
    }
}
