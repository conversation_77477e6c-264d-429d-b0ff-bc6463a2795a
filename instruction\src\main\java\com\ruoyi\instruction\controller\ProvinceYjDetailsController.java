package com.ruoyi.instruction.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.instruction.domain.ProvinceYjDetails;
import com.ruoyi.instruction.service.IProvinceYjDetailsService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 省级预警详情Controller
 * 
 * <AUTHOR>
 * @date 2023-09-21
 */
@RestController
@RequestMapping("/province/yjdetails")
public class ProvinceYjDetailsController extends BaseController
{
    @Autowired
    private IProvinceYjDetailsService provinceYjDetailsService;

    /**
     * 查询省级预警详情列表
     */
    @PreAuthorize("@ss.hasPermi('province:yjdetails:list')")
    @GetMapping("/list")
    public TableDataInfo list(ProvinceYjDetails provinceYjDetails)
    {
        startPage();
        List<ProvinceYjDetails> list = provinceYjDetailsService.selectProvinceYjDetailsList(provinceYjDetails);
        return getDataTable(list);
    }

    /**
     * 导出省级预警详情列表
     */
    @PreAuthorize("@ss.hasPermi('province:yjdetails:export')")
    @Log(title = "省级预警详情", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, ProvinceYjDetails provinceYjDetails)
    {
        List<ProvinceYjDetails> list = provinceYjDetailsService.selectProvinceYjDetailsList(provinceYjDetails);
        ExcelUtil<ProvinceYjDetails> util = new ExcelUtil<ProvinceYjDetails>(ProvinceYjDetails.class);
        util.exportExcel(response, list, "省级预警详情数据");
    }

    /**
     * 获取省级预警详情详细信息
     */
    @PreAuthorize("@ss.hasPermi('province:yjdetails:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(provinceYjDetailsService.selectProvinceYjDetailsById(id));
    }

    /**
     * 新增省级预警详情
     */
    @PreAuthorize("@ss.hasPermi('province:yjdetails:add')")
    @Log(title = "省级预警详情", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody ProvinceYjDetails provinceYjDetails)
    {
        return toAjax(provinceYjDetailsService.insertProvinceYjDetails(provinceYjDetails));
    }

    /**
     * 修改省级预警详情
     */
    @PreAuthorize("@ss.hasPermi('province:yjdetails:edit')")
    @Log(title = "省级预警详情", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody ProvinceYjDetails provinceYjDetails)
    {
        return toAjax(provinceYjDetailsService.updateProvinceYjDetails(provinceYjDetails));
    }

    /**
     * 删除省级预警详情
     */
    @PreAuthorize("@ss.hasPermi('province:yjdetails:remove')")
    @Log(title = "省级预警详情", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(provinceYjDetailsService.deleteProvinceYjDetailsByIds(ids));
    }
}
