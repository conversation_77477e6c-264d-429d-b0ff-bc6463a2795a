package com.ruoyi.instruction.mapper;

import java.util.List;

import com.ruoyi.instruction.domain.PersonAudit;
import com.ruoyi.instruction.domain.PersonLevelWarn;
import org.apache.ibatis.annotations.Param;

/**
 * 人员调档预警Mapper接口
 *
 * <AUTHOR>
 * @date 2024-10-22
 */
public interface PersonLevelWarnMapper
{
    /**
     * 查询人员调档预警
     *
     * @param id 人员调档预警主键
     * @return 人员调档预警
     */
    public PersonLevelWarn selectPersonLevelWarnById(Long id);

    /**
     * 查询人员调档预警列表
     *
     * @param personLevelWarn 人员调档预警
     * @return 人员调档预警集合
     */
    public List<PersonLevelWarn> selectPersonLevelWarnList(PersonLevelWarn personLevelWarn);

    /**
     * 新增人员调档预警
     *
     * @param personLevelWarn 人员调档预警
     * @return 结果
     */
    public int insertPersonLevelWarn(PersonLevelWarn personLevelWarn);

    /**
     * 修改人员调档预警
     *
     * @param personLevelWarn 人员调档预警
     * @return 结果
     */
    public int updatePersonLevelWarn(PersonLevelWarn personLevelWarn);

    /**
     * 删除人员调档预警
     *
     * @param id 人员调档预警主键
     * @return 结果
     */
    public int deletePersonLevelWarnById(Long id);

    /**
     * 批量删除人员调档预警
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deletePersonLevelWarnByIds(Long[] ids);

    /**
     * 批量插入人员调档预警记录
     * @param personLevelWarnList
     */
    void insertPersonLevelWarnBatch(@Param("list") List<PersonLevelWarn> personLevelWarnList);

    /**
     * 查询出未处置预警记录及已放置且免提醒时间大于当前时间的预警记录
     * @return
     */
    List<PersonLevelWarn> findNoRemind();

    /**
     * 更新人员等级预警记录
     * @param personAudit
     */
    void updateWarnPersonLevel(PersonAudit personAudit);
}
