package com.ruoyi.instruction.domain;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 办理期限延期表对象 t_handle_delay
 *
 * <AUTHOR>
 * @date 2025-01-02
 */
@Data
public class HandleDelay extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键id */
    private Long id;

    /** 指令id */
    @Excel(name = "指令id")
    private Long instructionId;

    /** 申请时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "申请时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date applyTime;

    /** 申请时长 */
    @Excel(name = "申请时长")
    private String applyDuration;

    /** 申请结果 1:同意  2：拒绝 */
    @Excel(name = "申请结果 1:同意  2：拒绝")
    private Integer applyResult;

    /** 申请理由 */
    @Excel(name = "申请理由")
    private String applyReason;

    /** 申请人 */
    @Excel(name = "申请人")
    private Long applyPerson;

    /**
     * 申请人姓名
     */
    private String applyPersonName;

    /** 申请部门 */
    @Excel(name = "申请部门")
    private Long applyDept;

    /**
     * 申请部门名称
     */
    private String applyDeptName;

    /** 审核人 */
    @Excel(name = "审核人")
    private String auditPerson;

    /** 状态  1：正常  9：异常 */
    @Excel(name = "状态  1：正常  9：异常")
    private String status;

    private Date auditTime;

}
