package com.ruoyi.instruction.controller;

import java.util.Arrays;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.instruction.domain.InstructionFollow;
import com.ruoyi.instruction.service.IInstructionFollowService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 用户关注指令信息Controller
 *
 * <AUTHOR>
 * @date 2024-12-31
 */
@RestController
@RequestMapping("/instruction/follow")
public class InstructionFollowController extends BaseController
{
    @Autowired
    private IInstructionFollowService instructionFollowService;

    /**
     * 查询用户关注指令信息列表
     */
    @GetMapping("/list")
    public TableDataInfo list(InstructionFollow instructionFollow)
    {
        startPage();
        List<InstructionFollow> list = instructionFollowService.selectInstructionFollowList(instructionFollow);
        return getDataTable(list);
    }

    /**
     * 导出用户关注指令信息列表
     */
    @Log(title = "用户关注指令信息", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, InstructionFollow instructionFollow)
    {
        List<InstructionFollow> list = instructionFollowService.selectInstructionFollowList(instructionFollow);
        ExcelUtil<InstructionFollow> util = new ExcelUtil<InstructionFollow>(InstructionFollow.class);
        util.exportExcel(response, list, "用户关注指令信息数据");
    }

    /**
     * 获取用户关注指令信息详细信息
     */
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(instructionFollowService.selectInstructionFollowById(id));
    }

    /**
     * 新增用户关注指令信息
     */
    @Log(title = "用户关注指令信息", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody InstructionFollow instructionFollow)
    {
        return toAjax(instructionFollowService.insertInstructionFollow(instructionFollow));
    }

    /**
     * 修改用户关注指令信息
     */
    @Log(title = "用户关注指令信息", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody InstructionFollow instructionFollow)
    {

        //查询用户是否有关注的指令
        InstructionFollow follow = instructionFollowService.selectInstructionFollowById(instructionFollow.getUserId());
        if (follow != null){
            //有值
            String oldIds = follow.getInstructionIds();
            String newIds = instructionFollow.getInstructionIds();
            if (instructionFollow.getType() != null && instructionFollow.getType() == 1) {
                //新增关注 将newIds添加到oldIds中
                String instructionIds = mergeIds(oldIds, newIds);
                follow.setInstructionIds(instructionIds);
            } else {
                //取消关注
                String instructionIds = removeIds(oldIds, newIds);
                follow.setInstructionIds(instructionIds);
            }
            instructionFollowService.updateInstructionFollow(follow);
        }else {
            //无记录进行新增
            instructionFollowService.insertInstructionFollow(instructionFollow);
        }
        return AjaxResult.success();
    }

    /**
     * 删除用户关注指令信息
     */
    @Log(title = "用户关注指令信息", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(instructionFollowService.deleteInstructionFollowByIds(ids));
    }


    public String mergeIds(String oldIdsStr, String newIdsStr) {
        if (oldIdsStr == null) {
            oldIdsStr = "";
        }
        if (newIdsStr == null) {
            newIdsStr = "";
        }

        Set<String> idSet = new HashSet<>(Arrays.asList(oldIdsStr.split(","))
                .stream()
                .filter(id -> !id.trim().isEmpty())
                .collect(Collectors.toList()));
        idSet.addAll(Arrays.asList(newIdsStr.split(","))
                .stream()
                .filter(id -> !id.trim().isEmpty())
                .collect(Collectors.toList()));

        return String.join(",", idSet);
    }

    public String removeIds(String oldIdsStr, String newIdsStr) {
        if (oldIdsStr == null) {
            oldIdsStr = "";
        }
        if (newIdsStr == null) {
            newIdsStr = "";
        }

        Set<String> idSet = new HashSet<>(Arrays.asList(oldIdsStr.split(","))
                .stream()
                .filter(id -> !id.trim().isEmpty())
                .collect(Collectors.toList()));
        Set<String> newIdSet = new HashSet<>(Arrays.asList(newIdsStr.split(","))
                .stream()
                .filter(id -> !id.trim().isEmpty())
                .collect(Collectors.toList()));

        idSet.removeAll(newIdSet);

        return String.join(",", idSet);
    }
}
