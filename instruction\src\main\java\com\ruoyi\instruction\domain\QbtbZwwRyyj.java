package com.ruoyi.instruction.domain;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

import java.util.List;

/**
 * 人员预警对象 qbtb_zww_ryyj
 *
 * <AUTHOR>
 * @date 2023-06-03
 */
public class QbtbZwwRyyj extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /**
     * 序号，自增字段
     */
    private Long nXh;

    /**
     * 预警主键
     */
    private String zj;

    /**
     * 关联轨迹id，政务网无用
     */
    private String glid;

    /**
     * 临控指令编号，对应布控的临控指令
     */
    private String lkzlbh;

    /**
     * 数据来源 D=本地  S=本省
     */
    private String cSjly;

    /**
     * 预警来源 D=本地  S=本省
     */
    private String cYjly;

    /**
     * 身份证
     */
    private String sfzh;

    /**
     * 预警接受单位代码，代码项，按照字典里的单位代码
     */
    private String yjjsdw;

    /**
     * 姓名
     */
    private String xm;

    /**
     * 预警级别，目前都为空
     */
    private String yjjb;

    /**
     * 人员类别，代码项，按照字典里的人员类别（controller_person_type）
     */
    private String rylb;

    /**
     * 人员小类，代码项，按照字典里的人员小类
     */
    private String ryxl;

    /**
     * 动态信息类别，代码项，按照字典里的动态信息类别
     */
    private String dtxxlb;

    /**
     * 活动相关信息
     */
    private String hdxgxx;

    /**
     * 活动发生地区划，代码项，按照区划代码
     */
    private String hdfsddqh;

    /**
     * 活动发生地详址
     */
    private String hdfsddxz;

    /**
     * 活动发生场所
     */
    private String hdfsddssshcs;

    /**
     * 活动发生时间
     */
    private String dHdfssj;

    /**
     * 信息比对时间
     */
    private String xxbdsj;

    /**
     * 预警发布时间
     */
    private String yjfbsj;

    /**
     * 预警标记 00=政法  02=信访
     */
    private String yjbj;

    /**
     * 处置措施要求，代码项，1=拦截劝返  2=核查稳控  3=跟踪关注
     */
    private String czcsyq;

    /**
     * 有效性 1=有效 0=无效
     */
    private String nYxx;

    /**
     * 入库时间
     */
    private String dRksj;

    /**
     * 经度
     */
    private String bz1;

    /**
     * 纬度
     */
    private String bz2;

    /**
     * 无用
     */
    private String bz3;

    /**
     * 无用
     */
    private String bz4;

    /**
     * 无用
     */
    private String bz5;

    /**
     * 布控人员ids
     */
    private List<String> ids;

    /**
     * 处理状态 1:已处理 2:未处理
     */
    private Integer dealType;

    /**
     * 1:交办 2：放置
     */
    private Integer disposeType;

    /**
     * 推送类型
     */
    private String tslx;

    /**
     * 事件名称
     */
    private String sjmc;

    /**
     * 指令id
     */
    private Integer infoId;

    /**
     * 已放置或已交办预警ids
     */
    private List<String> disposeIds;

    public List<String> getIds() {
        return ids;
    }

    public String getTslx() {
        return tslx;
    }

    public void setTslx(final String tslx) {
        this.tslx = tslx;
    }

    public String getSjmc() {
        return sjmc;
    }

    public void setSjmc(final String sjmc) {
        this.sjmc = sjmc;
    }

    public void setIds(final List<String> ids) {
        this.ids = ids;
    }

    public void setnXh(Long nXh) {
        this.nXh = nXh;
    }

    public Long getnXh() {
        return nXh;
    }

    public void setZj(String zj) {
        this.zj = zj;
    }

    public String getZj() {
        return zj;
    }

    public void setGlid(String glid) {
        this.glid = glid;
    }

    public String getGlid() {
        return glid;
    }

    public void setLkzlbh(String lkzlbh) {
        this.lkzlbh = lkzlbh;
    }

    public String getLkzlbh() {
        return lkzlbh;
    }

    public void setcSjly(String cSjly) {
        this.cSjly = cSjly;
    }

    public String getcSjly() {
        return cSjly;
    }

    public void setcYjly(String cYjly) {
        this.cYjly = cYjly;
    }

    public String getcYjly() {
        return cYjly;
    }

    public void setSfzh(String sfzh) {
        this.sfzh = sfzh;
    }

    public String getSfzh() {
        return sfzh;
    }

    public void setYjjsdw(String yjjsdw) {
        this.yjjsdw = yjjsdw;
    }

    public String getYjjsdw() {
        return yjjsdw;
    }

    public void setXm(String xm) {
        this.xm = xm;
    }

    public String getXm() {
        return xm;
    }

    public void setYjjb(String yjjb) {
        this.yjjb = yjjb;
    }

    public String getYjjb() {
        return yjjb;
    }

    public void setRylb(String rylb) {
        this.rylb = rylb;
    }

    public String getRylb() {
        return rylb;
    }

    public void setRyxl(String ryxl) {
        this.ryxl = ryxl;
    }

    public String getRyxl() {
        return ryxl;
    }

    public void setDtxxlb(String dtxxlb) {
        this.dtxxlb = dtxxlb;
    }

    public String getDtxxlb() {
        return dtxxlb;
    }

    public void setHdxgxx(String hdxgxx) {
        this.hdxgxx = hdxgxx;
    }

    public String getHdxgxx() {
        return hdxgxx;
    }

    public void setHdfsddqh(String hdfsddqh) {
        this.hdfsddqh = hdfsddqh;
    }

    public String getHdfsddqh() {
        return hdfsddqh;
    }

    public void setHdfsddxz(String hdfsddxz) {
        this.hdfsddxz = hdfsddxz;
    }

    public String getHdfsddxz() {
        return hdfsddxz;
    }

    public void setHdfsddssshcs(String hdfsddssshcs) {
        this.hdfsddssshcs = hdfsddssshcs;
    }

    public String getHdfsddssshcs() {
        return hdfsddssshcs;
    }

    public void setdHdfssj(String dHdfssj) {
        this.dHdfssj = dHdfssj;
    }

    public String getdHdfssj() {
        return dHdfssj;
    }

    public void setXxbdsj(String xxbdsj) {
        this.xxbdsj = xxbdsj;
    }

    public String getXxbdsj() {
        return xxbdsj;
    }

    public void setYjfbsj(String yjfbsj) {
        this.yjfbsj = yjfbsj;
    }

    public String getYjfbsj() {
        return yjfbsj;
    }

    public void setYjbj(String yjbj) {
        this.yjbj = yjbj;
    }

    public String getYjbj() {
        return yjbj;
    }

    public void setCzcsyq(String czcsyq) {
        this.czcsyq = czcsyq;
    }

    public String getCzcsyq() {
        return czcsyq;
    }

    public void setnYxx(String nYxx) {
        this.nYxx = nYxx;
    }

    public String getnYxx() {
        return nYxx;
    }

    public void setdRksj(String dRksj) {
        this.dRksj = dRksj;
    }

    public String getdRksj() {
        return dRksj;
    }

    public void setBz1(String bz1) {
        this.bz1 = bz1;
    }

    public String getBz1() {
        return bz1;
    }

    public void setBz2(String bz2) {
        this.bz2 = bz2;
    }

    public String getBz2() {
        return bz2;
    }

    public void setBz3(String bz3) {
        this.bz3 = bz3;
    }

    public String getBz3() {
        return bz3;
    }

    public void setBz4(String bz4) {
        this.bz4 = bz4;
    }

    public String getBz4() {
        return bz4;
    }

    public void setBz5(String bz5) {
        this.bz5 = bz5;
    }

    public String getBz5() {
        return bz5;
    }

    public List<String> getDisposeIds() {
        return disposeIds;
    }

    public void setDisposeIds(final List<String> disposeIds) {
        this.disposeIds = disposeIds;
    }

    public Integer getDealType() {
        return dealType;
    }

    public void setDealType(final Integer dealType) {
        this.dealType = dealType;
    }

    public Integer getDisposeType() {
        return disposeType;
    }

    public void setDisposeType(final Integer disposeType) {
        this.disposeType = disposeType;
    }

    public Integer getInfoId() {
        return infoId;
    }

    public void setInfoId(final Integer infoId) {
        this.infoId = infoId;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this, ToStringStyle.MULTI_LINE_STYLE)
                .append("nXh", getnXh())
                .append("zj", getZj())
                .append("glid", getGlid())
                .append("lkzlbh", getLkzlbh())
                .append("cSjly", getcSjly())
                .append("cYjly", getcYjly())
                .append("sfzh", getSfzh())
                .append("yjjsdw", getYjjsdw())
                .append("xm", getXm())
                .append("yjjb", getYjjb())
                .append("rylb", getRylb())
                .append("ryxl", getRyxl())
                .append("dtxxlb", getDtxxlb())
                .append("hdxgxx", getHdxgxx())
                .append("hdfsddqh", getHdfsddqh())
                .append("hdfsddxz", getHdfsddxz())
                .append("hdfsddssshcs", getHdfsddssshcs())
                .append("dHdfssj", getdHdfssj())
                .append("xxbdsj", getXxbdsj())
                .append("yjfbsj", getYjfbsj())
                .append("yjbj", getYjbj())
                .append("czcsyq", getCzcsyq())
                .append("nYxx", getnYxx())
                .append("dRksj", getdRksj())
                .append("bz1", getBz1())
                .append("bz2", getBz2())
                .append("bz3", getBz3())
                .append("bz4", getBz4())
                .append("bz5", getBz5())
                .toString();
    }
}
