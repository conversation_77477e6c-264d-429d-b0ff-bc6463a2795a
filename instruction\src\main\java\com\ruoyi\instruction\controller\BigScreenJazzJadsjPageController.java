package com.ruoyi.instruction.controller;

import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.examineAir.service.IExamineService;
import com.ruoyi.instruction.domain.*;
import com.ruoyi.instruction.domain.rspVo.*;
import com.ruoyi.instruction.mapper.InstructionInfoMapper;
import com.ruoyi.instruction.mapper.InstructionJazzWwEventMapper;
import com.ruoyi.instruction.service.*;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 金安智治驾驶舱金安大数据首页
 * <AUTHOR> @version 1.0
 * @date 2
 *
 */
@RestController
@RequestMapping("/bigScreen/jazz/JadsjPage")
public class BigScreenJazzJadsjPageController extends BaseController {

    @Autowired
    private IInstructionCaseService instructionCaseService;
    @Autowired
    private IInstructionEventService instructionEventService;

    @Autowired
    private InstructionJazzWwEventMapper instructionJazzWwEventMapper;
    @Autowired
    private  IJazzInstructionEventService jazzInstructionEventService;
    @Autowired
    private InstructionInfoMapper instructionInfoMapper;
    @Autowired
    private IInstructionFeedbackService instructionFeedbackService;
    /**
     * 查询【经典案例】列表
     * <AUTHOR>
     */
    @GetMapping("/caseList")
    public TableDataInfo caseList(InstructionCase instructionCase)
    {
        instructionCase.setIsRelease("1");
        startPage();
        List<InstructionCase> list = instructionCaseService.selectInstructionCaseList(instructionCase);
        return getDataTable(list);
    }

    /**
     *  金安大数据基础数据
     */
    @GetMapping("/basicData")
    public AjaxResult basicData() {
        List<BigScreenJazzCommonVo> bigScreenJazzCommonVos = instructionJazzWwEventMapper.basicData();
        return  AjaxResult.success(bigScreenJazzCommonVos);
    }

    /**
     *  风险提示
     */
    @GetMapping("/riskWarn")
    public AjaxResult riskWarn() {
        BigScreenRiskWarnRspVo bigScreenRiskWarnRspVo=new BigScreenRiskWarnRspVo();
        bigScreenRiskWarnRspVo.setFireControlIndex(85);
        bigScreenRiskWarnRspVo.setContradictionIndex(87);
        bigScreenRiskWarnRspVo.setTrafficIndex(77);
        //交通
        List<BigScreenJazzCommonVo> bigScreenJazzCommonVoList = jazzInstructionEventService.riskWarn(72);
        bigScreenRiskWarnRspVo.setTrafficList(bigScreenJazzCommonVoList.subList(0,3));
        //消防
        List<BigScreenJazzCommonVo> bigScreenJazzCommonVoList1 = jazzInstructionEventService.riskWarn(71);
        bigScreenRiskWarnRspVo.setFireControlList(bigScreenJazzCommonVoList1.subList(0,3));
        List<BigScreenJazzCommonVo> bigScreenJazzCommonVoList2 = jazzInstructionEventService.riskWarn(999);
        bigScreenRiskWarnRspVo.setContradictionList(bigScreenJazzCommonVoList2.subList(0,3));
        return  AjaxResult.success(bigScreenRiskWarnRspVo);
    }


    /**
     *  治理监测
     */
    @GetMapping("/administer")
    public AjaxResult administer() {
        HashMap hashMap=new HashMap();
        BigScreenJazzCommonVo bigScreenJazzCommonVo=new BigScreenJazzCommonVo();
        bigScreenJazzCommonVo.setCompleted(0);
        bigScreenJazzCommonVo.setNotCompleted(0);
        hashMap.put("contradiction",bigScreenJazzCommonVo);
        //交通
        BigScreenJazzCommonVo hashMaps = instructionJazzWwEventMapper.countTypeBig(72);
        hashMaps.setNotCompleted(hashMaps.getTotal()-hashMaps.getCompleted());
        hashMap.put("traffic",hashMaps);
        //消防
        BigScreenJazzCommonVo hashMaps1 = instructionJazzWwEventMapper.countTypeBig(71);
        hashMaps1.setNotCompleted(hashMaps1.getTotal()-hashMaps1.getCompleted());
        hashMap.put("fireControl",hashMaps1);
        return  AjaxResult.success(hashMap);
    }

    /**
     *  重点风险治理（指令）
     */
    @GetMapping("/riskGovernance")
    public AjaxResult riskGovernance() {
        BigScreenJazzRiskGovernanceVo b=new BigScreenJazzRiskGovernanceVo();
        List<JazzInstructionInfoStatisticsRspVo> jazzInstructionInfoStatisticsRspVos = instructionInfoMapper.selectInfo();
        int completed=0;
        int total=0;
        int notCompleted=0;
        if (!CollectionUtils.isEmpty(jazzInstructionInfoStatisticsRspVos)){
            total=jazzInstructionInfoStatisticsRspVos.size();
            for (JazzInstructionInfoStatisticsRspVo j:jazzInstructionInfoStatisticsRspVos){
                if (j.getInstrucationIsEnd()==null){
                    notCompleted=notCompleted+1;
                }else {
                    completed=completed+1;
                }

            }
        }
        b.setTotal(total);
        b.setCompleted(completed);
        b.setNotCompleted(notCompleted);
        return AjaxResult.success(b);
    }

    /**
     * 重点风险治理（指令）列表
     * type 0全部，1已完成，2未完成
     *
     */
    @GetMapping("/riskGovernanceList")
    public AjaxResult riskGovernanceList(Integer type) {
        BigScreenJazzRiskGovernanceVo b=new BigScreenJazzRiskGovernanceVo();
        List<JazzInstructionInfoStatisticsRspVo> jazzInstructionInfoStatisticsRspVos = instructionInfoMapper.selectInfo();
        List<BigScreenJazzCommonVo> list=new ArrayList<>();
        if (!CollectionUtils.isEmpty(jazzInstructionInfoStatisticsRspVos)){
            for (JazzInstructionInfoStatisticsRspVo j:jazzInstructionInfoStatisticsRspVos){
                if (type!=null&&type==1){
                    if (j.getInstrucationIsEnd()==null){
                        continue;
                    }
                }
                if (type!=null&&type==2){
                    if (j.getInstrucationIsEnd()!=null){
                        continue;
                    }
                }
                BigScreenJazzCommonVo bigScreenJazzCommonVo=new BigScreenJazzCommonVo();
                bigScreenJazzCommonVo.setName(j.getInstructionTitle());
                bigScreenJazzCommonVo.setId(j.getId());
//                bigScreenJazzCommonVo.setNum(100);
                if (j.getCreateTime()==null||j.getFeedbackTime()==null||j.getHandleTime()==null){
                    bigScreenJazzCommonVo.setNum(0);
                }else {
                    long nh = 1000 * 60 * 60;//每小时毫秒数
                    //预计花费时间
                    long yuji = j.getHandleTime().getTime()- j.getCreateTime().getTime();
                    //实际花费时间
                    long shiji = j.getFeedbackTime().getTime()- j.getCreateTime().getTime();
                    BigDecimal bigDecimal = new BigDecimal(nh);
                    BigDecimal divide = new BigDecimal(yuji).divide(bigDecimal, 1, BigDecimal.ROUND_HALF_UP);
                    BigDecimal divide1 = new BigDecimal(shiji).divide(bigDecimal, 1, BigDecimal.ROUND_HALF_UP);
                    bigScreenJazzCommonVo.setPlanTime(divide);
                    bigScreenJazzCommonVo.setRealityTime(divide1);
//                    BigDecimal divide1 = new BigDecimal(shiji * 100).divide(new BigDecimal(yuji), 0, BigDecimal.ROUND_HALF_UP);
//                    bigScreenJazzCommonVo.setNum(divide1.intValue());
                }
                list.add(bigScreenJazzCommonVo);
            }
        }
        b.setList(list);
        return AjaxResult.success(b);
    }

    /**
     * 重点风险治理通过id获取详情
     * @param id
     * @return
     */
    @GetMapping("/riskGovernanceDetails")
    public AjaxResult riskGovernanceDetails(Long id) {
        BigScreenJazzRiskGovernanceDetailVo b=new BigScreenJazzRiskGovernanceDetailVo();
        JazzInstructionInfoStatisticsRspVo a = instructionInfoMapper.selectInfoDetailsById(id);
        BeanUtils.copyProperties(a,b);
        InstructionFeedback instructionFeedback=new InstructionFeedback();
        instructionFeedback.setInstructionId(id);
        instructionFeedback.setStatus(1);
        List<InstructionFeedback> instructionFeedbacks = instructionFeedbackService.selectInstructionFeedbackList(instructionFeedback);
        List<String> collect = instructionFeedbacks.stream().map(InstructionFeedback::getFeedbackSituation).collect(Collectors.toList());
        b.setList(collect);
        return  AjaxResult.success(b);
    }
}
