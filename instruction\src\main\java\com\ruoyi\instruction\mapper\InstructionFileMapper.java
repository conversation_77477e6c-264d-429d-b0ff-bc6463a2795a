package com.ruoyi.instruction.mapper;

import java.util.List;
import com.ruoyi.instruction.domain.InstructionFile;
import org.apache.ibatis.annotations.Param;

/**
 * 指令文件Mapper接口
 * 
 * <AUTHOR>
 * @date 2022-12-19
 */
public interface InstructionFileMapper 
{
    /**
     * 查询指令文件
     * 
     * @param id 指令文件主键
     * @return 指令文件
     */
    public InstructionFile selectInstructionFileById(Long id);

    /**
     * 查询指令文件列表
     * 
     * @param instructionFile 指令文件
     * @return 指令文件集合
     */
    public List<InstructionFile> selectInstructionFileList(InstructionFile instructionFile);

    /**
     * 新增指令文件
     * 
     * @param instructionFile 指令文件
     * @return 结果
     */
    public int insertInstructionFile(InstructionFile instructionFile);

    /**
     * 修改指令文件
     * 
     * @param instructionFile 指令文件
     * @return 结果
     */
    public int updateInstructionFile(InstructionFile instructionFile);

    /**
     * 删除指令文件
     * 
     * @param id 指令文件主键
     * @return 结果
     */
    public int deleteInstructionFileById(Long id);

    /**
     * 批量删除指令文件
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteInstructionFileByIds(Long[] ids);

    /**
     * 根据文件id查询文件信息
     * @param longs
     * @return
     */
    List<InstructionFile> selectByFileIds(@Param("ids") long[] longs);

    /**
     * 根据ids 查询文件
     * @param split
     * @return
     */
    List<InstructionFile> selectByFileIdsString(@Param("ids") String[] split);
}
