package com.ruoyi.instruction.controller;

import java.util.ArrayList;
import java.util.List;
import javax.servlet.http.HttpServletResponse;


import com.ruoyi.common.utils.poi.ExcelUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.instruction.domain.InstructionPersonControl;
import com.ruoyi.instruction.service.IInstructionPersonControlService;
import org.springframework.web.multipart.MultipartFile;


/**
 * 重点人员-管控人员信息Controller
 *
 * <AUTHOR>
 * @date 2023-03-21
 */
@RestController
@RequestMapping("/instruction/personControl")
public class InstructionPersonControlController extends BaseController {

    @Autowired
    private IInstructionPersonControlService instructionPersonControlService;

    /**
     * 查询重点人员-管控人员信息列表
     */
    @GetMapping("/list")
    public AjaxResult list(InstructionPersonControl instructionPersonControl) {
        // startPage();
        List<InstructionPersonControl> list = instructionPersonControlService.selectInstructionPersonControlList(instructionPersonControl);
        return AjaxResult.success(list);
    }

    /**
     * 导出重点人员-管控人员信息列表
     */
    @Log(title = "重点人员-管控人员信息", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, InstructionPersonControl instructionPersonControl) {
        List<InstructionPersonControl> list = instructionPersonControlService.selectInstructionPersonControlList(instructionPersonControl);
        ExcelUtil<InstructionPersonControl> util = new ExcelUtil<InstructionPersonControl>(InstructionPersonControl.class);
        util.exportExcel(response, list, "重点人员-管控人员信息数据");
    }

    /**
     * 获取重点人员-管控人员信息详细信息
     */
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id) {
        return success(instructionPersonControlService.selectInstructionPersonControlById(id));
    }

    /**
     * 新增重点人员-管控人员信息
     */
    @Log(title = "重点人员-管控人员信息", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody InstructionPersonControl instructionPersonControl) {
        return toAjax(instructionPersonControlService.insertInstructionPersonControl(instructionPersonControl));
    }

    /**
     * 修改重点人员-管控人员信息
     */
    @Log(title = "重点人员-管控人员信息", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody InstructionPersonControl instructionPersonControl) {
        return toAjax(instructionPersonControlService.updateInstructionPersonControl(instructionPersonControl));
    }

    /**
     * 删除重点人员-管控人员信息
     */
    @Log(title = "重点人员-管控人员信息", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids) {
        return toAjax(instructionPersonControlService.deleteInstructionPersonControlByIds(ids));
    }

    @Log(title = "上传五包一人员", businessType = BusinessType.IMPORT)
    @PostMapping("/importPersonControlData")
    public AjaxResult importPersonControlData(MultipartFile file) throws Exception {
        ExcelUtil<InstructionPersonControl> util = new ExcelUtil<>(InstructionPersonControl.class);
        List<InstructionPersonControl> personList = util.importExcel(file.getInputStream());
        //获取操作用户
        String operName = getUsername();
        //存入数据
        String msg = instructionPersonControlService.importPersonData(personList, operName);
        return AjaxResult.success(msg);
    }

    /**
     * 下载五包一文件
     *
     * @param response
     */
    @PostMapping("/importTemplate")
    public void importTemplate(HttpServletResponse response) {
        ExcelUtil<InstructionPersonControl> util = new ExcelUtil<InstructionPersonControl>(InstructionPersonControl.class);
        util.importTemplateExcel(response, "五包一人员导入模板");
    }


}
