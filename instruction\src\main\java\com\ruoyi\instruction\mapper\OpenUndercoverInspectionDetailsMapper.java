package com.ruoyi.instruction.mapper;

import java.util.List;
import java.util.Map;

import com.ruoyi.instruction.domain.OpenUndercoverInspectionDetails;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * 暗访督察详情下层列Mapper接口
 *
 * <AUTHOR>
 * @date 2023-08-08
 */
@Mapper
public interface OpenUndercoverInspectionDetailsMapper
{
    /**
     * 查询暗访督察详情下层列
     *
     * @param uniqueNo 暗访督察详情下层列主键
     * @return 暗访督察详情下层列
     */
    public OpenUndercoverInspectionDetails selectOpenUndercoverInspectionDetailsByUniqueNo(String uniqueNo);

    /**
     * 查询暗访督察详情下层列列表
     *
     * @param openUndercoverInspectionDetails 暗访督察详情下层列
     * @return 暗访督察详情下层列集合
     */
    public List<OpenUndercoverInspectionDetails> selectOpenUndercoverInspectionDetailsList(OpenUndercoverInspectionDetails openUndercoverInspectionDetails);

    /**
     * 新增暗访督察详情下层列
     *
     * @param openUndercoverInspectionDetails 暗访督察详情下层列
     * @return 结果
     */
    public int insertOpenUndercoverInspectionDetails(OpenUndercoverInspectionDetails openUndercoverInspectionDetails);

    /**
     * 修改暗访督察详情下层列
     *
     * @param openUndercoverInspectionDetails 暗访督察详情下层列
     * @return 结果
     */
    public int updateOpenUndercoverInspectionDetails(OpenUndercoverInspectionDetails openUndercoverInspectionDetails);

    /**
     * 删除暗访督察详情下层列
     *
     * @param uniqueNo 暗访督察详情下层列主键
     * @return 结果
     */
    public int deleteOpenUndercoverInspectionDetailsByUniqueNo(String uniqueNo);

    /**
     * 批量删除暗访督察详情下层列
     *
     * @param uniqueNos 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteOpenUndercoverInspectionDetailsByUniqueNos(String[] uniqueNos);

    /**
     * 获取问题类别
     * @return
     */
    List<String> getCheckItemDomain();

    /**
     * 获取县市区-乡镇街道
     * @param type
     * @param code
     * @return
     */
    List<Map<String, Object>> getCityArea(@Param("type") String type, @Param("code") String code);

    /**
     * 根据InspectionId获取list
     * @param inspectionIdList
     * @return
     */
    List<OpenUndercoverInspectionDetails> listByInspectionId(@Param("inspectionIdList") List<Long> inspectionIdList);

    /**
     * 查询
     * @return
     */
    List<Map<String, Object>> getDealUndercoverDetails();

    /**
     * 查询已处置暗访详情
     * @return
     * @param dealStep
     */
    List<String> findDealInspectionId(@Param("dealStep") final String dealStep);

    /**
     * 处理暗访图片
     * @return
     */
    List<Map<String, Object>> getDealUndercoverDetailsFiles();

    /**
     * 根据uniqueNo更新暗访问题处置状态
     * @param mcafId
     */
    void updateDealStepByUniqueNo(@Param("uniqueNo") String mcafId);

    /**
     * 根据uniqueNo更新暗访问题处置状态
     * @param uniqueNo
     */
    void updateOpenUndercoverTurnDown(String uniqueNo);

    /**
     * 获取已反馈数据
     * @return
     */
    List<Map<String, Object>> getNoFeedbackDealUndercoverDetails();

    /**
     * 批量更新督查数据
     * @param detailsList
     */
    void batchUpdateOpenUndercoverInspectionDetails(@Param("detailsList") List<OpenUndercoverInspectionDetails> detailsList);
}
