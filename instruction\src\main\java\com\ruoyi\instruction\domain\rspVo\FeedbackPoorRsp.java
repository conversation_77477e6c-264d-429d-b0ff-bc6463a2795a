package com.ruoyi.instruction.domain.rspVo;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import lombok.Data;

import java.util.Date;

/**
 * 处置不力数据返回对象
 * <AUTHOR>
 * @version 1.0
 * @date 2025/4/22 20:35
 */
@Data
public class FeedbackPoorRsp {

    @ExcelProperty("序号")
    private Long id;

    @ExcelProperty("指令标题")
    @ColumnWidth(20)
    private String instructionTitle;

    @ColumnWidth(20)
    @ExcelProperty("紧急程度")
    private String emergencyDegree;

    @ColumnWidth(20)
    @ExcelProperty("指令类型")
    private String instructionType;

    @ColumnWidth(20)
    @ExcelProperty("交办时间")
    private Date assignTime;

    @ColumnWidth(20)
    @ExcelProperty("办理期限")
    private Date handleTime;

    @ColumnWidth(20)
    @ExcelProperty("处置不力单位")
    private String receiveDept;

    @ExcelProperty("类型")
    private String isMzx;

    @ColumnWidth(20)
    @ExcelProperty("接收时间")
    private Date receiveTime;

    @ColumnWidth(20)
    @ExcelProperty("流转处置单位")
    private String feedbackDept;

    @ColumnWidth(20)
    @ExcelProperty("处置时间")
    private Date feedbackTime;
}
