package com.ruoyi.instruction.controller;

import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.instruction.service.IBackPageDataService;
import com.ruoyi.instruction.service.IInstructionInfoService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.Date;
import java.util.HashMap;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/3/6 9:24
 * 后台管理首页数据controller
 */
@RestController
@RequestMapping("/page/data")
public class BackPageDataController {

    @Autowired
    private IBackPageDataService pageDataService;


    /**
     * 获取数据总数接口
     *
     * @return
     */
    @GetMapping("/getPageData")
    public AjaxResult getPageData() {
        return pageDataService.getPageData();
    }

    /**
     * 获取重大事件
     *
     * @return
     */
    @GetMapping("/getEventType")
    public AjaxResult getEventType() {
        List<HashMap> hashMaps = pageDataService.getEventType();
        return AjaxResult.success(hashMaps);
    }

    /**
     * 获取重大群体
     *
     * @return
     */
    @GetMapping("/getGroupType")
    public AjaxResult getGroupType() {
        List<HashMap> hashMaps = pageDataService.getGroupType();
        return AjaxResult.success(hashMaps);
    }

    /**
     * 获取重大事件类型
     *
     * @return
     */
    @GetMapping("/getMajorEvent")
    public AjaxResult getMajorEvent() {
        List<HashMap> hashMaps = pageDataService.getMajorEvent();
        return AjaxResult.success(hashMaps);
    }


    /**
     * 获取占比
     * @param type
     * @return
     */
    @GetMapping("/getProportion/{type}")
    public AjaxResult getProportion(@PathVariable("type") Integer type,String startTime,String endTime) {
        return pageDataService.getProportion(type,startTime,endTime);
    }

}
