package com.ruoyi.instruction.domain;

import java.math.BigDecimal;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 群体涉及户数、金额对象 t_group_house_details
 * 
 * <AUTHOR>
 * @date 2024-12-19
 */
public class GroupHouseDetails extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键id */
    private Long id;

    /** 县市区 */
    @Excel(name = "县市区")
    private String dutyPlace;

    /** 涉及个/户/车 数 */
    @Excel(name = "涉及个/户/车 数")
    private Long number;

    /** 涉及金额 */
    @Excel(name = "涉及金额")
    private BigDecimal amount;

    /** 群体id */
    @Excel(name = "群体id")
    private Long groupId;

    public void setId(Long id) 
    {
        this.id = id;
    }

    public Long getId() 
    {
        return id;
    }
    public void setDutyPlace(String dutyPlace) 
    {
        this.dutyPlace = dutyPlace;
    }

    public String getDutyPlace() 
    {
        return dutyPlace;
    }
    public void setNumber(Long number) 
    {
        this.number = number;
    }

    public Long getNumber() 
    {
        return number;
    }
    public void setAmount(BigDecimal amount) 
    {
        this.amount = amount;
    }

    public BigDecimal getAmount() 
    {
        return amount;
    }
    public void setGroupId(Long groupId) 
    {
        this.groupId = groupId;
    }

    public Long getGroupId() 
    {
        return groupId;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("dutyPlace", getDutyPlace())
            .append("number", getNumber())
            .append("amount", getAmount())
            .append("groupId", getGroupId())
            .toString();
    }
}
