package com.ruoyi.instruction.domain;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 人员走访记录对象 t_person_interview
 *
 * <AUTHOR>
 * @date 2024-01-05
 */
@Data
public class PersonInterview extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 人员-走访记录id */
    private Long id;

    /** 走访时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm")
    @Excel(name = "走访时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm")
    private Date interviewTime;

    /** 走访频次 */
    @Excel(name = "走访频次")
    private String interviewType;

    /** 走访记录 */
    @Excel(name = "走访记录")
    private String interviewRecord;

    /** 走访人员 */
    @Excel(name = "走访人员")
    private String interviewPerson;

    /** 1:正常 9：删除 */
    @Excel(name = "1:正常 9：删除")
    private String status;

    /** 创建部门id */
    @Excel(name = "创建部门id")
    private Long createDeptId;

    /** 关联人员id */
    @Excel(name = "关联人员id")
    private Long personId;

    /** 走访类型 见面、视频、电话、其他 */
    @Excel(name = "走访类型 见面、视频、电话、其他")
    private String typeName;

    /** 文件ids */
    @Excel(name = "文件ids")
    private String fileIds;

    /**
     * 预警id
     */
    private Long forewarnId;

    /** 走访状态 在控、失联 */
    @Excel(name = "走访状态 在控、失联")
    private String interviewStatus;

    /** 当前所在地 在金、在杭、在京、其他 */
    @Excel(name = "当前所在地 在金、在杭、在京、其他")
    private String currentPlace;

    /** 当前情绪情况  稳定、不稳定 */
    @Excel(name = "当前情绪情况  稳定、不稳定")
    private String currentEmo;

    /** 重大变故情况  发生重大健康变故、发生重大经济变故、无重大变故、不了解 */
    @Excel(name = "重大变故情况  发生重大健康变故、发生重大经济变故、无重大变故、不了解")
    private String majorAccident;

}
