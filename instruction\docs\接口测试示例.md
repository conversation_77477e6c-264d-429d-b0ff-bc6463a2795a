# 市级指令办理情况接口测试示例

## 测试说明

本文档提供了市级指令办理情况接口的测试示例，用于验证接口功能是否正常工作。

## 接口测试

### 1. 统计数据接口测试

**接口：** `GET /instruction/cityHandling/data`

**测试用例1：** 查询全量数据
```bash
curl -X GET "http://localhost:8080/instruction/cityHandling/data" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

**测试用例2：** 按时间范围查询
```bash
curl -X GET "http://localhost:8080/instruction/cityHandling/data?startTime=2025-01-01&endTime=2025-01-31" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

**预期响应：**
```json
{
  "code": 200,
  "msg": "操作成功",
  "data": {
    "timeoutReceiveCount": 2,
    "timeoutDisposeCount": 11,
    "unprocessedUnitCount": 22,
    "unprocessedDeptCount": 2,
    "pendingFeedback7Days": 1,
    "pendingFeedback15Days": 2,
    "pendingFeedback30Days": 3
  }
}
```

### 2. 详细列表接口测试

**接口：** `GET /instruction/cityHandling/list`

**测试用例1：** 查询全量详细列表
```bash
curl -X GET "http://localhost:8080/instruction/cityHandling/list" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

**测试用例2：** 按时间范围查询详细列表
```bash
curl -X GET "http://localhost:8080/instruction/cityHandling/list?startTime=2025-01-01&endTime=2025-01-31" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

**预期响应结构：**
```json
{
  "code": 200,
  "msg": "操作成功",
  "data": {
    "timeoutReceiveList": [...],
    "timeoutDisposeList": [...],
    "unprocessedList": [...],
    "pendingFeedback7DaysList": [...],
    "pendingFeedback15DaysList": [...],
    "pendingFeedback30DaysList": [...]
  }
}
```

### 3. 导出接口测试

**接口：** `POST /instruction/cityHandling/export`

**测试用例：** 导出Excel文件
```bash
curl -X POST "http://localhost:8080/instruction/cityHandling/export" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -H "Content-Type: application/x-www-form-urlencoded" \
  -d "startTime=2025-01-01&endTime=2025-01-31" \
  --output "市级指令办理情况.xlsx"
```

## 数据验证

### 1. 验证数据范围
确认所有接口返回的都是金华市范围内的数据：
- 检查返回数据中的 `receiveDept` 字段是否都包含金华市相关区域
- 验证数据量是否符合预期（应该是全市数据，不是单个区县数据）

### 2. 验证数据一致性
确认统计数据和详细列表的数据一致性（已修复DISTINCT问题）：
- `/data` 接口返回的 `timeoutReceiveCount` 应该等于 `/list` 接口返回的 `timeoutReceiveList` 的长度
- `/data` 接口返回的 `timeoutDisposeCount` 应该等于 `/list` 接口返回的 `timeoutDisposeList` 的长度
- `/data` 接口返回的 `unprocessedUnitCount` 应该等于 `/list` 接口返回的 `unprocessedList` 的长度
- `/data` 接口返回的 `pendingFeedback7Days` 应该等于 `/list` 接口返回的 `pendingFeedback7DaysList` 的长度
- `/data` 接口返回的 `pendingFeedback15Days` 应该等于 `/list` 接口返回的 `pendingFeedback15DaysList` 的长度
- `/data` 接口返回的 `pendingFeedback30Days` 应该等于 `/list` 接口返回的 `pendingFeedback30DaysList` 的长度

**修复说明**：所有列表查询都已添加 `DISTINCT` 关键字，确保按指令ID去重，与统计查询的 `COUNT(DISTINCT a.id)` 逻辑保持一致。

### 3. 验证时间筛选
测试时间参数的筛选功能：
- 不传时间参数应该返回全量数据
- 传入时间范围应该只返回该时间范围内的数据
- 开始时间和结束时间的边界值测试

## JavaScript测试示例

```javascript
// 测试统计数据接口
async function testDataAPI() {
    try {
        const response = await fetch('/instruction/cityHandling/data?startTime=2025-01-01&endTime=2025-01-31');
        const result = await response.json();
        console.log('统计数据：', result.data);
        return result.data;
    } catch (error) {
        console.error('统计数据接口测试失败：', error);
    }
}

// 测试详细列表接口
async function testListAPI() {
    try {
        const response = await fetch('/instruction/cityHandling/list?startTime=2025-01-01&endTime=2025-01-31');
        const result = await response.json();
        console.log('详细列表：', result.data);
        return result.data;
    } catch (error) {
        console.error('详细列表接口测试失败：', error);
    }
}

// 验证数据一致性
async function validateDataConsistency() {
    const statsData = await testDataAPI();
    const listData = await testListAPI();
    
    if (statsData && listData) {
        console.log('数据一致性验证：');
        console.log('超时接收 - 统计:', statsData.timeoutReceiveCount, '列表长度:', listData.timeoutReceiveList.length);
        console.log('超时处置 - 统计:', statsData.timeoutDisposeCount, '列表长度:', listData.timeoutDisposeList.length);
        console.log('应处置未处置单位 - 统计:', statsData.unprocessedUnitCount, '列表长度:', listData.unprocessedList.length);
        console.log('特急待反馈 - 统计:', statsData.pendingFeedback7Days, '列表长度:', listData.pendingFeedback7DaysList.length);
        console.log('紧急待反馈 - 统计:', statsData.pendingFeedback15Days, '列表长度:', listData.pendingFeedback15DaysList.length);
        console.log('一般待反馈 - 统计:', statsData.pendingFeedback30Days, '列表长度:', listData.pendingFeedback30DaysList.length);
    }
}
```

## 注意事项

1. **权限验证**：确保测试时使用有效的认证token
2. **数据范围**：所有接口都应该返回金华市全部数据，不受当前用户部门限制
3. **性能测试**：在生产环境中测试接口的响应时间，特别是详细列表接口
4. **错误处理**：测试各种异常情况，如无效的时间参数、网络错误等

## 预期结果

- 所有接口都能正常响应
- 返回的数据都是金华市范围内的完整数据
- 统计数据和详细列表的数量保持一致
- 时间筛选功能正常工作
- 导出功能能够生成正确的Excel文件
