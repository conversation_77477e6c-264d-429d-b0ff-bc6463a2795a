package com.ruoyi.instruction.domain;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;
import org.apache.poi.ss.usermodel.IndexedColors;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * 风险排查详情对象 t_risk_details
 *
 * <AUTHOR>
 * @date 2023-12-25
 */
@Data
public class RiskDetails extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键 */
    private Long id;

    /** 风险排查主键id */
    private Long riskId;

    /** 标题 */
    @Excel(name = "标题(必填)",headerColor = IndexedColors.RED)
    @NotNull(message = "标题不能为空")
    private String title;

    /** 排查类别大类 */
    @Excel(name = "排查类别(必填)",
            readConverterExp = "51=涉众金融投资领域,52=房地产领域,53=劳动关系领域,54=涉法涉诉领域,58=新业态新就业群体,59=拆迁安置,60=政策待遇,61=教育领域,62=生态环境,63=医疗纠纷,64=生产安全,65=其他",
            combo = {"涉众金融投资领域","房地产领域","劳动关系领域","涉法涉诉领域","新业态新就业群体","拆迁安置","政策待遇","教育领域","生态环境","医疗纠纷","生产安全","其他"})
    @NotNull(message = "排查类别不能为空")
    private Long checkBig;


    /** 问题类别 */
    @Excel(name = "问题类型"
            ,combo = {"网约车","货拉拉","外卖骑手","快递员","电商直播","其他"})
    private String problemCategory;


    /** 风险等级 1：重大  2：高  3：中  4：低 */
    @Excel(name = "风险等级(必填)",headerColor = IndexedColors.RED,readConverterExp = "1=重大,2=高,3=中,4=低",combo = {"重大","高","中","低"})
    @NotNull(message = "风险等级不能为空")
    private String level;

    /** 涉及金额 */
    @Excel(name = "涉及金额")
    private String amount;


    /** 涉及户数 */
    @Excel(name = "涉及人数")
    private Long households;

    /** 基本情况 */
    @Excel(name = "基本情况(必填)",headerColor = IndexedColors.RED)
    @NotNull(message = "基本情况不能为空")
    private String basicSituation;


    /** 主要诉求 */
    @Excel(name = "主要诉求(必填)",headerColor = IndexedColors.RED)
    @NotNull(message = "主要诉求不能为空")
    private String mainApply;


    /** 排查类别 */
    private String checkName;


    /** 责任单位 */
    @Excel(name = "属地(必填)",headerColor = IndexedColors.RED)
    @NotNull(message = "属地不能为空")
    private String dutyUnit;

    /** 所属区域名称 */
    @Excel(name = "责任乡镇(必填)",headerColor = IndexedColors.RED)
    @NotNull(message = "责任乡镇不能为空")
    private String areaName;

    /** 责任人 */
    @Excel(name = "责任人(必填)",headerColor = IndexedColors.RED)
    @NotNull(message = "责任人不能为空")
    private String dutyPerson;


    /** 填报单位 */
    @Excel(name = "填报部门(必填)",headerColor = IndexedColors.RED)
    @NotNull(message = "填报部门")
    private String fillUnit;

    /** 填报人 */
    @Excel(name = "填报人(必填)",headerColor = IndexedColors.RED)
    @NotNull(message = "填报人")
    private String fillPerson;

    /** 挂牌督办 */
    @Excel(name = "挂牌督办(非必填)")
    private String handel;


    /** 化解情况 1: 未化解  2：已化解 */
    @Excel(name = "化解情况(必填)",headerColor = IndexedColors.RED,readConverterExp = "1=未化解,2=已化解",combo = {"未化解","已化解"})
    @NotNull(message = "化解情况不能为空")
    private String dissolution;

    /** 化解时限 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "化解时限(必填 格式:yyyy-MM-dd )",headerColor = IndexedColors.RED, width = 30, dateFormat = "yyyy-MM-dd")
    @NotNull(message = "化解时限不能为空")
    private Date dissolutionTime;

    /** 正式化解措施 */
    private String measure;


    /** 敏感程度 1：无  2：内部 */
    private String sensitiveStr;


    /** 排查类别小类 */
    private Long checkMin;


    /** 创建时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date createTime;

    /** 更新时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date updateTime;

    /** 附件ids */
    private String fileIds;


    /** 状态 1：正常 9：删除 */
    private String status;

    /** 楼盘名称 */
    private String buildName;

    /** 所属区域部门id */
    private Long areaDept;


    /** 信访情况 */
    private String petition;

    /** 问题类型 */
    private String problem;


    /** 创建部门 */
    private Long createDeptId;

    /** 类型 1：常规  2：问题楼盘 */
    private Long riskType;

    /**
     * 年度
     */
    private String yearStr;

    /** 工作进展 */
    private String workProgress;

    /**
     * 处置数
     */
    private Long aCount;
    /**
     * 风险排查名称
     */
    private String riskName;
    /**
     * 时间
     */
    private String date;
    /**
     * 群体id
     */
    private  String groupId;

    /** 最新处置时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date dealTime;

    /**
     * 处置要求 (根据预警规则动态获取)
     */
    private String disposalRequirement;
}
