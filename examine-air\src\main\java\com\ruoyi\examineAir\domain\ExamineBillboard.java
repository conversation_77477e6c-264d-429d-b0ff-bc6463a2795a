package com.ruoyi.examineAir.domain;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 考核晾晒-红黄榜对象 t_examine_billboard
 * 
 * <AUTHOR>
 * @date 2023-04-19
 */
public class ExamineBillboard extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** $column.columnComment */
    private Long id;

    /** 内容 */
    @Excel(name = "内容")
    private String content;

    /** 创建时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "创建时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date cTime;

    /** 修改时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "修改时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date uTime;

    /** 状态，0未发布，1已发布 */
//    @Excel(name = "状态，0未发布，1已发布")
    private Long status;

    /** 创建者id */
    @Excel(name = "创建者id")
    private Long creatorUserId;

    /** 创建者姓名 */
    @Excel(name = "创建者姓名")
    private String creatorName;

    /** 榜单类型，0红榜，1黄榜 */
//    @Excel(name = "榜单类型，0红榜，1黄榜")
    private Long type;

    private Date startTime;

    //    @TableField(exist = false)
    private Date endTime;

    public void setId(Long id) 
    {
        this.id = id;
    }

    public Long getId() 
    {
        return id;
    }
    public void setContent(String content) 
    {
        this.content = content;
    }

    public String getContent() 
    {
        return content;
    }
    public void setcTime(Date cTime) 
    {
        this.cTime = cTime;
    }

    public Date getcTime() 
    {
        return cTime;
    }
    public void setuTime(Date uTime) 
    {
        this.uTime = uTime;
    }

    public Date getuTime() 
    {
        return uTime;
    }
    public void setStatus(Long status) 
    {
        this.status = status;
    }

    public Long getStatus() 
    {
        return status;
    }
    public void setCreatorUserId(Long creatorUserId) 
    {
        this.creatorUserId = creatorUserId;
    }

    public Long getCreatorUserId() 
    {
        return creatorUserId;
    }
    public void setCreatorName(String creatorName) 
    {
        this.creatorName = creatorName;
    }

    public String getCreatorName() 
    {
        return creatorName;
    }
    public void setType(Long type) 
    {
        this.type = type;
    }

    public Long getType() 
    {
        return type;
    }

    public Date getStartTime() {
        return startTime;
    }

    public void setStartTime(Date startTime) {
        this.startTime = startTime;
    }

    public Date getEndTime() {
        return endTime;
    }

    public void setEndTime(Date endTime) {
        this.endTime = endTime;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("content", getContent())
            .append("cTime", getcTime())
            .append("uTime", getuTime())
            .append("status", getStatus())
            .append("creatorUserId", getCreatorUserId())
            .append("creatorName", getCreatorName())
            .append("type", getType())
            .toString();
    }
}
