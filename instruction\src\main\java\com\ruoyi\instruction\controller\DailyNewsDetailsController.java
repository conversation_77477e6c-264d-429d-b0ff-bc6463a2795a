package com.ruoyi.instruction.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.instruction.domain.DailyNewsDetails;
import com.ruoyi.instruction.service.IDailyNewsDetailsService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 每日要闻详情Controller
 * 
 * <AUTHOR>
 * @date 2023-08-16
 */
@RestController
@RequestMapping("/instruction/newsDetails")
public class DailyNewsDetailsController extends BaseController
{
    @Autowired
    private IDailyNewsDetailsService dailyNewsDetailsService;

    /**
     * 查询每日要闻详情列表
     */
    @PreAuthorize("@ss.hasPermi('instruction:newsDetails:list')")
    @GetMapping("/list")
    public TableDataInfo list(DailyNewsDetails dailyNewsDetails)
    {
        startPage();
        List<DailyNewsDetails> list = dailyNewsDetailsService.selectDailyNewsDetailsList(dailyNewsDetails);
        return getDataTable(list);
    }

    /**
     * 导出每日要闻详情列表
     */
    @PreAuthorize("@ss.hasPermi('instruction:newsDetails:export')")
    @Log(title = "每日要闻详情", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, DailyNewsDetails dailyNewsDetails)
    {
        List<DailyNewsDetails> list = dailyNewsDetailsService.selectDailyNewsDetailsList(dailyNewsDetails);
        ExcelUtil<DailyNewsDetails> util = new ExcelUtil<DailyNewsDetails>(DailyNewsDetails.class);
        util.exportExcel(response, list, "每日要闻详情数据");
    }

    /**
     * 获取每日要闻详情详细信息
     */
    @PreAuthorize("@ss.hasPermi('instruction:newsDetails:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(dailyNewsDetailsService.selectDailyNewsDetailsById(id));
    }

    /**
     * 新增每日要闻详情
     */
    @PreAuthorize("@ss.hasPermi('instruction:newsDetails:add')")
    @Log(title = "每日要闻详情", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody DailyNewsDetails dailyNewsDetails)
    {
        return toAjax(dailyNewsDetailsService.insertDailyNewsDetails(dailyNewsDetails));
    }

    /**
     * 修改每日要闻详情
     */
    @PreAuthorize("@ss.hasPermi('instruction:newsDetails:edit')")
    @Log(title = "每日要闻详情", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody DailyNewsDetails dailyNewsDetails)
    {
        return toAjax(dailyNewsDetailsService.updateDailyNewsDetails(dailyNewsDetails));
    }

    /**
     * 删除每日要闻详情
     */
    @PreAuthorize("@ss.hasPermi('instruction:newsDetails:remove')")
    @Log(title = "每日要闻详情", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(dailyNewsDetailsService.deleteDailyNewsDetailsByIds(ids));
    }
}
