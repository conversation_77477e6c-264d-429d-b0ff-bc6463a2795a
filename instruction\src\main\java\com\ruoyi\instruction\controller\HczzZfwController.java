package com.ruoyi.instruction.controller;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import javax.servlet.http.HttpServletResponse;

import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.ruoyi.common.constant.CacheConstants;
import com.ruoyi.common.core.domain.entity.SysDept;
import com.ruoyi.common.core.domain.entity.SysDictData;
import com.ruoyi.common.core.domain.entity.SysUser;
import com.ruoyi.common.core.redis.RedisCache;
import com.ruoyi.common.utils.IDCardUtils;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.framework.web.domain.server.Sys;
import com.ruoyi.instruction.domain.InstrucationPerson;
import com.ruoyi.instruction.domain.SensitiveRecord;
import com.ruoyi.instruction.mapper.HczzZfwMapper;
import com.ruoyi.instruction.mapper.InstrucationPersonMapper;
import com.ruoyi.instruction.service.IInstructionInfoService;
import com.ruoyi.instruction.service.IJazzInstructionInfoService;
import com.ruoyi.instruction.service.ISensitiveRecordService;
import com.ruoyi.system.mapper.SysDeptMapper;
import com.ruoyi.system.mapper.SysUserMapper;
import com.ruoyi.system.service.ISysDictDataService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.instruction.domain.HczzZfw;
import com.ruoyi.instruction.service.IHczzZfwService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 情指推送事件Controller
 *
 * <AUTHOR>
 * @date 2023-06-07
 */
@RestController
@RequestMapping("/hczz/zfw")
public class HczzZfwController extends BaseController
{
    @Autowired
    private IHczzZfwService hczzZfwService;

    /**
     * 指令service
     */
    @Autowired
    private IInstructionInfoService infoService;

    @Autowired
    private ISysDictDataService dictDataService;


    @Autowired
    private ISensitiveRecordService sensitiveRecordService;

    @Autowired
    private SysDeptMapper sysDeptMapper;

    @Autowired
    private InstrucationPersonMapper personMapper;

    /**
     * 查询情指推送事件列表
     */
    @PreAuthorize("@ss.hasPermi('hczz:zfw:list')")
    @GetMapping("/list")
    public TableDataInfo list(HczzZfw hczzZfw)
    {
        //查询已交办、处置的情指id不进行查询
        List<Map<String,Object>> infoQzids1= infoService.findQzids1();
        List<String> qzid=
                infoQzids1.stream().map(m -> m.get("qzid").toString()).collect(Collectors.toList());
        hczzZfw.setDisposeIds(qzid);
        //判断用户是否是县市区
        SysDept dept = SecurityUtils.getLoginUser().getUser().getDept();
        Long parentId = dept.getParentId();
        if (parentId == 213L || parentId == 214L || parentId == 215L || parentId == 216L || parentId == 217L || parentId == 218L || parentId == 219L || parentId == 220L || parentId == 221L || parentId == 262L) {
            String fqdw = getFqdw(parentId);
            hczzZfw.setFqdw(fqdw);
        }
        String zrdw = "";
        if (hczzZfw.getFqdw()!=null&&!hczzZfw.getFqdw().equals("")){
            zrdw = "\"fqdw\":\""+hczzZfw.getFqdw()+"\"";
            hczzZfw.setZrdw(zrdw);
        }
        if (hczzZfw.getJsdw()!=null&&!hczzZfw.getJsdw().equals("")){
            String jsdw = ",\"jsdw\":\""+hczzZfw.getJsdw();
            hczzZfw.setZrdw(zrdw+jsdw);
        }
        startPage();
        List<HczzZfw> list = hczzZfwService.selectHczzZfwList(hczzZfw);
        list.stream().forEach(hczzZfw1 -> {
            if (StringUtils.isNotEmpty(hczzZfw1.getJbqk())){
                String jbqk = hczzZfw1.getJbqk();
                hczzZfw1.setJbqk(IDCardUtils.maskSensitiveInfo(jbqk));
            }

            // if (hczzZfw1.getRyxx()!=null&&!hczzZfw1.getRyxx().equals("")){
            //     String ryxx = hczzZfw1.getRyxx();
            //     hczzZfw1.setRyxx(IDCardUtils.maskSensitiveInfo(ryxx));
            // }

            String aString=hczzZfw1.getQzid().toString();
            for(Map<String, Object> map : infoQzids1){
               String qzid_id1= map.get("qzid").toString();
               if(qzid_id1.equals(aString)){
                   String type = String.valueOf(map.get("type"));
                   hczzZfw1.setDisposeType(Integer.valueOf(type));
                   continue;
               }
            }
        });
        return getDataTable(list);
    }



    private String getFqdw(final Long deptId) {
        String fqdw = "";
        if (deptId == 213L) {
            //磐安
            fqdw ="磐安县公安局";
        } else if (deptId == 214L) {
            //兰溪
            fqdw ="兰溪市公安局";
        } else if (deptId == 215L) {
            //东阳
            fqdw ="东阳市公安局";
        } else if (deptId == 216L) {
            //义乌
            fqdw ="义乌市公安局";
        } else if (deptId == 217L) {
            //浦江
            fqdw ="浦江县公安局";
        } else if (deptId == 218L) {
            //永康
            fqdw ="永康市公安局";
        } else if (deptId == 219L) {
            //金东
            fqdw ="金东分局";
        } else if (deptId == 220L) {
            //婺城
            fqdw ="婺城分局";
        } else if (deptId == 221L) {
            //开发
            fqdw ="江南分局";
        } else if (deptId == 262L) {
            //武义
            fqdw ="武义县公安局";
        }
        return fqdw;
    }


    @GetMapping("/listForBigScreen")
    public TableDataInfo listForBigScreen(HczzZfw hczzZfw)
    {
        //查询已交办、处置的情指id不进行查询
        List<Map<String,Object>> infoQzids1= infoService.findQzids1();
        List<String> qzid=
                infoQzids1.stream().map(m -> m.get("qzid").toString()).collect(Collectors.toList());
        hczzZfw.setDisposeIds(qzid);
        String zrdw = "";
        if (hczzZfw.getFqdw()!=null&&!hczzZfw.getFqdw().equals("")){
            zrdw = "\"fqdw\":\""+hczzZfw.getFqdw()+"\"";
            hczzZfw.setZrdw(zrdw);
        }
        if (hczzZfw.getJsdw()!=null&&!hczzZfw.getJsdw().equals("")){
            String jsdw = ",\"jsdw\":\""+hczzZfw.getJsdw();
            hczzZfw.setZrdw(zrdw+jsdw);
        }
        SysDictData dictData = new SysDictData();
        dictData.setDictType("controller_person_type");
        List<SysDictData> dictDataList = dictDataService.selectDictDataList(dictData);
        startPage();
        List<HczzZfw> list = hczzZfwService.selectHczzZfwList(hczzZfw);
        list.stream().forEach(hczzZfw1 -> {
            String rylb = hczzZfw1.getTslx();
            SysDictData sysDictData1 = dictDataList.stream().filter(sysDictData -> sysDictData.getDictValue().equals(rylb)).findFirst().orElse(null);
            if (sysDictData1!=null){
                hczzZfw1.setTslx(sysDictData1.getDictLabel());
            }
            String aString=hczzZfw1.getQzid().toString();
            for(Map<String, Object> map : infoQzids1){
                String qzid_id1= map.get("qzid").toString();
                if(qzid_id1.equals(aString)){
                    String type = String.valueOf(map.get("type"));
                    hczzZfw1.setDisposeType(Integer.valueOf(type));
                    continue;
                }
            }
        });
        return getDataTable(list);
    }

    /**
     * 导出情指推送事件列表
     */
    @PreAuthorize("@ss.hasPermi('hczz:zfw:export')")
    @Log(title = "情指推送事件", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, HczzZfw hczzZfw)
    {
        List<HczzZfw> list = hczzZfwService.selectHczzZfwList(hczzZfw);
        for (HczzZfw hczzZfw1 : list){
            String ryxx = hczzZfw1.getRyxx();
            if (!ryxx.equals("[]")){
                String s = encryptIdCardsInString(ryxx);
                hczzZfw1.setRyxx(s);
            }
        }
        ExcelUtil<HczzZfw> util = new ExcelUtil<HczzZfw>(HczzZfw.class);
        util.exportExcel(response, list, "情指推送事件数据");
    }

    /**
     * 对字符串中的身份证进行加密
     * @param inputString 输入的 JSON 字符串
     * @return 加密后的 JSON 字符串
     */
    private String encryptIdCardsInString(String inputString) {
        try {
            // 解析输入字符串为 JSONArray
            JSONArray jsonArray = JSONArray.parseArray(inputString);

            // 遍历 JSONArray
            for (int i = 0; i < jsonArray.size(); i++) {
                JSONObject jsonObject = jsonArray.getJSONObject(i);
                String idCard = jsonObject.getString("sfz").toLowerCase().replace(" ", "");
                String encryptedIDCard = IDCardUtils.encryptIDCard(idCard);
                jsonObject.put("sfz", encryptedIDCard);
                String phone = jsonObject.getString("phone").toLowerCase().replace(" ", "");
                String encryptedPhone = IDCardUtils.encryptIDCard(phone);
                jsonObject.put("phone", encryptedPhone);
            }

            // 返回加密后的 JSON 字符串
            return jsonArray.toJSONString();
        } catch (Exception e) {
            e.printStackTrace();
            return "加密失败: " + e.getMessage();
        }
    }

    /**
     * 获取情指推送事件详细信息
     */
    // @PreAuthorize("@ss.hasPermi('hczz:zfw:query')")
    @GetMapping(value = "/{qzid}")
    public AjaxResult getInfo(@PathVariable("qzid") String qzid,@RequestParam(name = "type",required = false) Integer type,@RequestParam(name = "menuName",required = false) String menuName)
    {
        HczzZfw hczzZfw = hczzZfwService.selectHczzZfwByQzid(qzid);
        if (type != null) {
            //查看信息脱敏
            SensitiveRecord sensitiveRecord = new SensitiveRecord();
            //当期用户信息
            SysUser user = SecurityUtils.getLoginUser().getUser();
            SysDept dept = user.getDept();
            SysDept parentDept = sysDeptMapper.selectDeptById(dept.getParentId());
            String deptName = parentDept.getDeptName().replace("委政法委", "").replace("政法委", "").replace("政法办", "");
            sensitiveRecord.setCreateDeptName(deptName + user.getDept().getDeptName());
            sensitiveRecord.setRjQzid(qzid);
            sensitiveRecord.setCreateBy(user.getNickName());
            sensitiveRecord.setCreateTime(new Date());
            sensitiveRecord.setCreateUserId(user.getUserId());
            sensitiveRecord.setCreateDeptId(user.getDeptId());
            sensitiveRecord.setRjTitle(hczzZfw.getSjmc());
            sensitiveRecord.setRjTsdw(hczzZfw.getZrdw());
            sensitiveRecord.setType(2);
            sensitiveRecord.setMenuName(menuName);
            sensitiveRecordService.insertSensitiveRecord(sensitiveRecord);
        }else {
            String jbqk = hczzZfw.getJbqk();
            hczzZfw.setJbqk(IDCardUtils.maskSensitiveInfo(jbqk));
            String ryxx = hczzZfw.getRyxx();
            hczzZfw.setRyxx(IDCardUtils.maskSensitiveInfo(ryxx));
        }
        return success(hczzZfw);
    }

    /**
     * 新增情指推送事件
     */
    @PreAuthorize("@ss.hasPermi('hczz:zfw:add')")
    @Log(title = "情指推送事件", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody HczzZfw hczzZfw)
    {
        return toAjax(hczzZfwService.insertHczzZfw(hczzZfw));
    }

    /**
     * 修改情指推送事件
     */
    @PreAuthorize("@ss.hasPermi('hczz:zfw:edit')")
    @Log(title = "情指推送事件", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody HczzZfw hczzZfw)
    {
        return toAjax(hczzZfwService.updateHczzZfw(hczzZfw));
    }

    /**
     * 删除情指推送事件
     */
    @PreAuthorize("@ss.hasPermi('hczz:zfw:remove')")
    @Log(title = "情指推送事件", businessType = BusinessType.DELETE)
	@DeleteMapping("/{qzids}")
    public AjaxResult remove(@PathVariable String[] qzids)
    {
        return toAjax(hczzZfwService.deleteHczzZfwByQzids(qzids));
    }

    /**
     * 根据公安身份证查询人员信息
     */
    @GetMapping("/findPersonByInfo")
    public AjaxResult findPersonByInfo(String qzid)
    {
        List<InstrucationPerson> personList = new ArrayList<>();
        HczzZfw hczzZfw = hczzZfwService.selectHczzZfwByQzid(qzid);
        if (hczzZfw == null || hczzZfw.getRyxx().equals("[]")) {
            return AjaxResult.success(personList);
        }
        String ryxx = hczzZfw.getRyxx();

        //字符串转数组对象
        JSONArray jsonArray = JSONArray.parseArray(ryxx);
        if (jsonArray.size()!=0){
            // 遍历数组对象
            for (int i = 0; i < jsonArray.size(); i++) {
                JSONObject jsonObject = jsonArray.getJSONObject(i);
                String idCard = jsonObject.getString("sfz").toLowerCase().replace(" ","");
                String encryptedIDCard = IDCardUtils.encryptIDCard(idCard);
                //根据身份证查询人员信息
                InstrucationPerson person = personMapper.selectInstrucationPersonByIdCard("", encryptedIDCard);
                if (person == null) {
                    person = new InstrucationPerson();
                    //库中没有该人员信息
                    String name = jsonObject.getString("name");
                    person.setPersonName(name);
                    person.setPersonPhone(jsonObject.getString("phone"));
                }
                person.setIdCard(idCard);
                personList.add(person);
            }
        }
        return AjaxResult.success(personList);
    }

    @PostMapping("/findPerson")
    public AjaxResult findPerson(@RequestBody HczzZfw hczzZfw)
    {
        List<InstrucationPerson> personList = new ArrayList<>();
        if (hczzZfw == null || hczzZfw.getRyxx().equals("[]")) {
            return AjaxResult.success(personList);
        }
        String ryxx = hczzZfw.getRyxx();
        //字符串转数组对象
        JSONArray jsonArray = JSONArray.parseArray(ryxx);
        if (jsonArray.size()!=0){
            // 遍历数组对象
            for (int i = 0; i < jsonArray.size(); i++) {
                JSONObject jsonObject = jsonArray.getJSONObject(i);
                String idCard = jsonObject.getString("sfz").toLowerCase().replace(" ","");
                String encryptedIDCard = IDCardUtils.encryptIDCard(idCard);
                //根据身份证查询人员信息
                InstrucationPerson person = personMapper.selectInstrucationPersonByIdCard("", encryptedIDCard);
                if (person == null) {
                    person = new InstrucationPerson();
                    //库中没有该人员信息
                    String name = jsonObject.getString("name");
                    person.setPersonName(name);
                    person.setPersonPhone(jsonObject.getString("phone"));
                }
                person.setIdCard(idCard);
                personList.add(person);
            }
        }
        return AjaxResult.success(personList);
    }
}
