package com.ruoyi.instruction.domain.rspVo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.common.annotation.Excel;
import lombok.Data;
import org.apache.poi.ss.usermodel.IndexedColors;

import java.util.Date;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/11/8 9:34
 * 市级维稳指令导出实体类
 */
@Data
public class CityInstructionExportRspVo {

    /**
     * 指令id
     */
    private Long id;

    @Excel(name = "指令名称", headerBackgroundColor = IndexedColors.GREY_25_PERCENT)
    private String instructionTitle;


    @Excel(name = "办理单位", headerBackgroundColor = IndexedColors.GREY_25_PERCENT)
    private String receiveDept;


    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "交办时间", headerBackgroundColor = IndexedColors.GREY_25_PERCENT,width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date assignTime;


    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "审核时间", headerBackgroundColor = IndexedColors.GREY_25_PERCENT, width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date auditTime;


    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "接收时间", headerBackgroundColor = IndexedColors.GREY_25_PERCENT, width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date receiveTime;


    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "反馈时间", headerBackgroundColor = IndexedColors.GREY_25_PERCENT, width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date feedbackTime;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "办理期限", headerBackgroundColor = IndexedColors.GREY_25_PERCENT, width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date handleTime;

    @Excel(name = "反馈要求", headerBackgroundColor = IndexedColors.GREY_25_PERCENT)
    private String feedback;

    /**
     * 指令id
     */
    private Long instructionId;

    /**
     * 反馈部门
     */
    private String feedbackDept;



}
