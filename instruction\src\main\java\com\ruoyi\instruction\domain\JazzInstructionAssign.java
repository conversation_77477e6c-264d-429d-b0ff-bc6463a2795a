package com.ruoyi.instruction.domain;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.util.Date;

/**
 * 指令交办对象 t_instruction_assign
 * 
 * <AUTHOR>
 * @date 2022-12-19
 */
public class JazzInstructionAssign extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 交办表主键 */
    private Long id;

    /** 交办时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "交办时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date assignTime;

    /** 交办部门 */
    @Excel(name = "交办部门")
    private String assignDept;

    /** 交办人员 */
    @Excel(name = "交办人员")
    private String assignPerson;

    /** 指令id */
    @Excel(name = "指令id")
    private Long instructionId;

    /** 交办状态1：正常 9：删除 */
    @Excel(name = "交办状态1：正常 9：删除")
    private String status;

    public void setId(Long id) 
    {
        this.id = id;
    }

    public Long getId() 
    {
        return id;
    }
    public void setAssignTime(Date assignTime) 
    {
        this.assignTime = assignTime;
    }

    public Date getAssignTime() 
    {
        return assignTime;
    }
    public void setAssignDept(String assignDept) 
    {
        this.assignDept = assignDept;
    }

    public String getAssignDept() 
    {
        return assignDept;
    }
    public void setAssignPerson(String assignPerson) 
    {
        this.assignPerson = assignPerson;
    }

    public String getAssignPerson() 
    {
        return assignPerson;
    }
    public void setInstructionId(Long instructionId) 
    {
        this.instructionId = instructionId;
    }

    public Long getInstructionId() 
    {
        return instructionId;
    }
    public void setStatus(String status) 
    {
        this.status = status;
    }

    public String getStatus() 
    {
        return status;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("assignTime", getAssignTime())
            .append("assignDept", getAssignDept())
            .append("assignPerson", getAssignPerson())
            .append("instructionId", getInstructionId())
            .append("createBy", getCreateBy())
            .append("createTime", getCreateTime())
            .append("status", getStatus())
            .toString();
    }
}
