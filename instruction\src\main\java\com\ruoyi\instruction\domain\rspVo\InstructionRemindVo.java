package com.ruoyi.instruction.domain.rspVo;

import lombok.Data;

import java.util.Date;

/**
 * 指令待提醒返回类
 * <AUTHOR>
 * @version 1.0
 * @date 2023/6/20 15:40
 */
@Data
public class InstructionRemindVo {

    /**
     * 指令id
     */
    private Long id;

    /**
     * 指令标题
     */
    private String instructionTitle;

    /**
     * 办理期限
     */
    private Date handleTime;

    /**
     * 接收单位
     */
    private String receiveUnit;

    /**
     * 接收单位个数
     */
    private Integer receiveUnitCount;

    /**
     * 创建指令部门id
     */
    private Long createDeptId;

    /**
     * 已经接收部门名称
     */
    private String isReceiveDept;

    /**
     * 已经接收部门个数
     */
    private Integer isReceiveCount;


    /**
     * 转交部门
     */
    private String transferDept;

    /**
     * 转交部门个数
     */
    private Integer transferDeptCount;

    /**
     * 反馈部门
     */
    private String feedbackDept;


    /**
     * 反馈部门个数
     */
    private Integer feedbackDeptCount;

    /**
     * 县市区反馈部门个数
     */
    private String countyFeedbackDept;

    /**
     * 县市区反馈部门个数
     */
    private Integer countyFeedbackDeptCount;

    /**
     * 已过时间
     */
    private Integer minutesDiff;


}
