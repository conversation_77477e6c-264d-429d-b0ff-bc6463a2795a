package com.ruoyi.instruction.mapper;

import java.util.List;
import com.ruoyi.instruction.domain.ThreeSenses;

/**
 * 三感三率Mapper接口
 * 
 * <AUTHOR>
 * @date 2024-04-02
 */
public interface ThreeSensesMapper 
{
    /**
     * 查询三感三率
     * 
     * @param id 三感三率主键
     * @return 三感三率
     */
    public ThreeSenses selectThreeSensesById(Long id);

    /**
     * 查询三感三率列表
     * 
     * @param threeSenses 三感三率
     * @return 三感三率集合
     */
    public List<ThreeSenses> selectThreeSensesList(ThreeSenses threeSenses);

    /**
     * 新增三感三率
     * 
     * @param threeSenses 三感三率
     * @return 结果
     */
    public int insertThreeSenses(ThreeSenses threeSenses);

    /**
     * 修改三感三率
     * 
     * @param threeSenses 三感三率
     * @return 结果
     */
    public int updateThreeSenses(ThreeSenses threeSenses);

    /**
     * 删除三感三率
     * 
     * @param id 三感三率主键
     * @return 结果
     */
    public int deleteThreeSensesById(Long id);

    /**
     * 批量删除三感三率
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteThreeSensesByIds(Long[] ids);
}
