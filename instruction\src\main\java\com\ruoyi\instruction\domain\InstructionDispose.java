package com.ruoyi.instruction.domain;

import lombok.Data;
import lombok.EqualsAndHashCode;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

import java.util.List;

/**
 * 指令处置对象 t_instruction_dispose
 *
 * <AUTHOR>
 * @date 2022-12-19
 */
@Data
public class InstructionDispose extends BaseEntity {

    private static final long serialVersionUID = 1L;

    /**
     * 处置表id
     */
    private Long id;

    /**
     * 处置部门
     */
    @Excel(name = "处置部门")
    private String disposeDept;

    /**
     * 处置情况反馈
     */
    @Excel(name = "处置情况反馈")
    private String disposeFeedback;

    /**
     * 是否办结 1：已办结  2：未办结
     */
    @Excel(name = "是否办结 1：已办结  2：未办结")
    private Integer isEnd;

    /**
     * 处置人员
     */
    @Excel(name = "处置人员")
    private String disposeBy;

    /**
     * 转接表id
     */
    @Excel(name = "转接表id")
    private Long transferId;

    /**
     * 文件ids
     */
    @Excel(name = "文件ids")
    private String fileIds;

    /** 指令id */
    @Excel(name = "指令id")
    private Long instructionId;

    /** 1:正常 9：删除 */
    @Excel(name = "1:正常 9：删除")
    private Integer status;

    /**
     * 反馈列表
     */
    private InstructionFeedback instructionFeedback;


}
