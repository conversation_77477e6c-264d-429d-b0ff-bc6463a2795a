package com.ruoyi.instruction.controller;

import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;
import javax.servlet.http.HttpServletResponse;

import com.ruoyi.common.constant.Constants;
import com.ruoyi.common.constant.InstructionRolesConstants;
import com.ruoyi.common.core.domain.entity.SysDept;
import com.ruoyi.common.core.domain.entity.SysRole;
import com.ruoyi.common.core.domain.entity.SysUser;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.instruction.domain.RiskFeedback;
import com.ruoyi.instruction.mapper.RiskFeedbackMapper;
import com.ruoyi.instruction.service.impl.InstructionInfoServiceImpl;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.instruction.domain.RiskAssign;
import com.ruoyi.instruction.service.IRiskAssignService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 风险交办单页面Controller
 *
 * <AUTHOR>
 * @date 2025-02-26
 */
@RestController
@RequestMapping("/risk/assign")
public class RiskAssignController extends BaseController
{
    @Autowired
    private IRiskAssignService riskAssignService;

    @Autowired
    private RiskFeedbackMapper riskFeedbackMapper;


    /**
     * 查询风险交办单页面列表
     */
    // @PreAuthorize("@ss.hasPermi('risk:assign:list')")
    @GetMapping("/list")
    public TableDataInfo list(RiskAssign riskAssign)
    {

        SysUser user = SecurityUtils.getLoginUser().getUser();
        List<SysRole> roles = user.getRoles();
        List<String> roleList = roles.stream().map(SysRole::getRoleKey).collect(Collectors.toList());
        if (roleList.contains(InstructionRolesConstants.RISK_RECEIVE_ROLE)){
            //判断当前用户属于哪个县市区或市级职能部门
            SysDept dept = user.getDept();
            dept = InstructionInfoServiceImpl.getCountyDeptInfo(dept);
            riskAssign.setReceiveUnit(String.valueOf(dept.getDeptId()));
            riskAssign.setType(1);
        }
        startPage();
        List<RiskAssign> list = riskAssignService.selectRiskAssignList(riskAssign);
        list.stream().forEach(item -> {
            if (roleList.contains(InstructionRolesConstants.RISK_RECEIVE_ROLE)) {
                if (item.getCurrentStatus().equals("无需反馈")) {
                    item.setOperateType(4);
                } else {
                    item.setOperateType(3);
                }
            }else if (roleList.contains(InstructionRolesConstants.RISK_ASSIGN_ROLE)){
                item.setOperateType(1);
                if (item.getCurrentStatus().equals("待发送") ) {
                    item.setOperateType(2);
                }
            }
        });

        return getDataTable(list);
    }

    /**
     * 导出风险交办单页面列表
     */
    // @PreAuthorize("@ss.hasPermi('risk:assign:export')")
    @Log(title = "风险交办单页面", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, RiskAssign riskAssign)
    {
        List<RiskAssign> list = riskAssignService.selectRiskAssignList(riskAssign);
        ExcelUtil<RiskAssign> util = new ExcelUtil<RiskAssign>(RiskAssign.class);
        util.exportExcel(response, list, "风险交办单页面数据");
    }

    /**
     * 获取风险交办单页面详细信息
     */
    // @PreAuthorize("@ss.hasPermi('risk:assign:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        SysUser user = SecurityUtils.getLoginUser().getUser();
        List<SysRole> roles = user.getRoles();
        List<String> roleList = roles.stream().map(SysRole::getRoleKey).collect(Collectors.toList());
        RiskAssign riskAssign = riskAssignService.selectRiskAssignById(id);
        RiskFeedback riskFeedback = new RiskFeedback();
        riskFeedback.setRiskAssignId(id);
        if (roleList.contains(InstructionRolesConstants.RISK_RECEIVE_ROLE)){
            //判断当前用户属于哪个县市区或市级职能部门
            SysDept dept = user.getDept();
            dept = InstructionInfoServiceImpl.getCountyDeptInfo(dept);
            riskFeedback.setFeedbackDeptId(dept.getDeptId());
        }
        List<RiskFeedback> riskFeedbacks = riskFeedbackMapper.selectRiskFeedbackList(riskFeedback);
        riskAssign.setRiskFeedbackList(riskFeedbacks);
        return success(riskAssign);
    }

    /**
     * 新增风险交办单页面
     */
    // @PreAuthorize("@ss.hasPermi('risk:assign:add')")
    @Log(title = "风险交办单页面", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody RiskAssign riskAssign)
    {
        SysUser user = SecurityUtils.getLoginUser().getUser();
        String nickName = user.getNickName();
        riskAssign.setCreateBy(nickName);
        riskAssign.setCreateTime(new Date());
        riskAssign.setCreateDeptId(user.getDeptId());
        return toAjax(riskAssignService.insertRiskAssign(riskAssign));
    }

    /**
     * 修改风险交办单页面
     */
    // @PreAuthorize("@ss.hasPermi('risk:assign:edit')")
    @Log(title = "风险交办单页面", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody RiskAssign riskAssign)
    {
        return toAjax(riskAssignService.updateRiskAssign(riskAssign));
    }

    /**
     * 删除风险交办单页面
     */
    // @PreAuthorize("@ss.hasPermi('risk:assign:remove')")
    @Log(title = "风险交办单页面", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(riskAssignService.deleteRiskAssignByIds(ids));
    }

    /**
     * 发送提醒
     * @param riskAssign
     * @return
     */
    @GetMapping("/sendMsg")
    public AjaxResult sendMsg(RiskAssign riskAssign)
    {
        riskAssign.setSendTime(new Date());
        if (riskAssign.getIsFeedback() == 1) {
            //需反馈
            riskAssign.setCurrentStatus("待反馈");
        } else if (riskAssign.getIsFeedback() == 2) {
            //无需反馈
            riskAssign.setCurrentStatus("已完结");
        }
        return toAjax(riskAssignService.updateRiskAssign(riskAssign));
    }
}
