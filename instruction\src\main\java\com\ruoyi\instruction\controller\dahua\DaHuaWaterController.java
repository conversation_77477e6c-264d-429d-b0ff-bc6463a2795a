package com.ruoyi.instruction.controller.dahua;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.redis.RedisCache;
import com.ruoyi.instruction.domain.DahuaVideo;
import com.ruoyi.instruction.domain.KeyValueEntity;
import com.ruoyi.instruction.service.CameraSerive;
import com.ruoyi.instruction.service.IDahuaVideoService;
import com.ruoyi.instruction.task.WaterVideoTask;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/8/4 11:12
 */
@RestController
@RequestMapping("/system/dahuaWater")
public class DaHuaWaterController extends BaseController {
    private static final Logger log = LoggerFactory.getLogger(DaHuaWaterController.class);
    @Autowired
    CameraSerive cameraService;

    @Autowired
    private IDahuaVideoService dahuaVideoService;

    @Autowired
    private RedisCache redisCache;

    /**
     * 获取摄像头列表
     */
    @GetMapping("/list")
    public AjaxResult list() {
        String cacheObject = redisCache.getCacheObject(WaterVideoTask.DAHUA_ListCAMERA);
        if (StringUtils.isEmpty(cacheObject)) {
            List<KeyValueEntity> keyValueEntities = cameraService.listCamera();
            redisCache.setCacheObject(WaterVideoTask.DAHUA_ListCAMERA, JSON.toJSONString(keyValueEntities));
            return AjaxResult.success(keyValueEntities);
        }
        List<KeyValueEntity> list = JSONArray.parseArray(cacheObject, KeyValueEntity.class);
        for (KeyValueEntity entity : list) {
            DahuaVideo dahuaVideo = new DahuaVideo();
            dahuaVideo.setDkey(String.valueOf(entity.getKey()));
            List<DahuaVideo> list1 = dahuaVideoService.selectDahuaVideoList(dahuaVideo);
            if (list1.size() == 0) {
                dahuaVideo.setDvalue(String.valueOf(entity.getValue()));
                dahuaVideo.setEsX(entity.getEsX());
                dahuaVideo.setEsY(entity.getEsY());
                dahuaVideo.setOrgname(entity.getOrgName());
                dahuaVideoService.insertDahuaVideo(dahuaVideo);
            }
        }
        return AjaxResult.success(list);
    }


    /**
     * 获取摄像头列表
     */
    @GetMapping("/getList")
    public AjaxResult getList() {
        //删除大华视频缓存
        redisCache.deleteObject(WaterVideoTask.DAHUA_ListCAMERA);
        List<KeyValueEntity> keyValueEntities = cameraService.listCamera();
        redisCache.setCacheObject(WaterVideoTask.DAHUA_ListCAMERA, JSON.toJSONString(keyValueEntities));
        for (KeyValueEntity entity : keyValueEntities) {
            DahuaVideo dahuaVideo = new DahuaVideo();
            dahuaVideo.setDkey(String.valueOf(entity.getKey()));
            List<DahuaVideo> list1 = dahuaVideoService.selectDahuaVideoList(dahuaVideo);
            if (list1.size() == 0) {
                dahuaVideo.setDvalue(String.valueOf(entity.getValue()));
                dahuaVideo.setEsX(entity.getEsX());
                dahuaVideo.setEsY(entity.getEsY());
                dahuaVideo.setOrgname(entity.getOrgName());
                dahuaVideoService.insertDahuaVideo(dahuaVideo);
            }
        }
        return AjaxResult.success(keyValueEntities);
    }

    /**
     * 获取详情
     */
    @GetMapping("/details")
    public AjaxResult details(String id) {
        List<KeyValueEntity> keyValueEntities = cameraService.details(id);
        return AjaxResult.success(keyValueEntities);
    }


}
