package com.ruoyi.instruction.domain;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.common.annotation.Excel;
import lombok.Data;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 调度纪要对象 t_dispatch_minutes
 * 
 * <AUTHOR>
 * @date 2023-09-18
 */
@Data
public class TDispatchMinutes extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** $column.columnComment */
    private Long id;

    /** 时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date dispatchTime;

    /** 内容 */
    @Excel(name = "内容")
    private String content;


}
