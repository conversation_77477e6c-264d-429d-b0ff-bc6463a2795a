package com.ruoyi.instruction.mapper;

import java.util.List;
import java.util.Map;

import com.ruoyi.instruction.domain.PetitionInfo;
import com.ruoyi.instruction.domain.PetitionPerson;
import org.apache.ibatis.annotations.Param;

/**
 * 信访信息Mapper接口
 *
 * <AUTHOR>
 * @date 2024-04-28
 */
public interface PetitionInfoMapper
{
    /**
     * 查询信访信息
     *
     * @param id 信访信息主键
     * @return 信访信息
     */
    public PetitionInfo selectPetitionInfoById(Long id);

    /**
     * 查询信访信息列表
     *
     * @param petitionInfo 信访信息
     * @return 信访信息集合
     */
    public List<PetitionInfo> selectPetitionInfoList(PetitionInfo petitionInfo);

    /**
     * 新增信访信息
     *
     * @param petitionInfo 信访信息
     * @return 结果
     */
    public int insertPetitionInfo(PetitionInfo petitionInfo);

    /**
     * 修改信访信息
     *
     * @param petitionInfo 信访信息
     * @return 结果
     */
    public int updatePetitionInfo(PetitionInfo petitionInfo);

    /**
     * 删除信访信息
     *
     * @param id 信访信息主键
     * @return 结果
     */
    public int deletePetitionInfoById(Long id);

    /**
     * 批量删除信访信息
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deletePetitionInfoByIds(Long[] ids);

    /**
     * 查询信访类型
     * @return
     */
    List<Map<Integer, String>> selectTypeList();


    /**
     * 批量新增
     * @param insertList
     */
    void batchInsertPetitionInfo(List<PetitionInfo> insertList);

    /**
     * 获取事项目的
     * @return
     */
    List<String> getEventGoalList();

    /**
     * 查询规定时间内赴省进京人员
     * @param personNames
     * @param years
     * @return
     */
    List<String> findPersonNames(@Param("personNames") List<String> personNames,@Param("years") Integer years);


    /**
     * 查询当年赴省进京人员个数
     * @return
     */
    List<PetitionInfo> findProvinceCapitalCurrentYear();

    /**
     * 查询近3年进京赴省人员
     * @return
     */
    List<PetitionInfo> findProvinceCapitalThreeYear();

    /**
     * 查询出人员赴省进京人员信息
     * @return
     */
    List<PetitionInfo> findProvinceCapital();

}
