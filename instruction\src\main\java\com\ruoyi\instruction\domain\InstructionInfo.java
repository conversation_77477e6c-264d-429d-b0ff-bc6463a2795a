package com.ruoyi.instruction.domain;

import java.util.Date;
import java.util.List;
import java.util.Map;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.common.core.page.TableDataInfo;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 指令基本信息对象 t_instruction_info
 *
 * <AUTHOR>
 * @date 2022-12-19
 */
@Data
public class InstructionInfo extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /**
     * 指令主键
     */
    private Long id;

    /**
     * 指令编码（自动生成唯一uuid）
     */
    @Excel(name = "指令编码", readConverterExp = "自动生成唯一uuid")
    private String instructionCode;

    /**
     * 指令标题
     */
    @Excel(name = "指令标题")
    private String instructionTitle;

    /**
     * 办理期限
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "办理期限", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date handleTime;

    /**
     * 接收单位
     */
    @Excel(name = "接收单位")
    private String receiveUnit;

    /**
     * 县市区接收部门
     */
    private String receiveStr;

    /**
     * 紧急程度
     */
    @Excel(name = "紧急程度")
    private String emergencyDegree;

    /**
     * 关联群体id
     */
    @Excel(name = "关联群体id")
    private Long groupId;

    /**
     * 关联群体名称
     */
    private String groupName;

    /**
     * 群体类型
     */
    @Excel(name = "群体类型")
    private String type;

    /**
     * 事件类型
     */
    private String typeName;

    /**
     * 信息来源
     */
    @Excel(name = "信息来源")
    private String sourceInfo;

    /**
     * 指令内容
     */
    @Excel(name = "指令内容")
    private String instructionContent;

    /**
     * 基本情况
     */
    @Excel(name = "基本情况")
    private String baseInfo;

    /**
     * 创建者
     */
    @Excel(name = "创建者")
    private String creatorBy;

    /**
     * 状态 1:正常  9：删除
     */
    @Excel(name = "状态 1:正常  9：删除")
    private String status;

    /**
     * 指令状态 1:交办 2:暂存
     */
    @Excel(name = "指令状态 1:交办 2:暂存")
    private String instructionStatus;

    /**
     * 反馈要求
     */
    @Excel(name = "反馈要求")
    private String feedback;

    /**
     * 人员ids
     */
    @Excel(name = "人员ids")
    private String personIds;

    /**
     * 人员信息
     */
    private List<InstrucationPerson> personList;

    /**
     * 接收单位
     */
    private String[] unit;

    /**
     * 页码
     */
    private Integer start;

    /**
     * 每页显示条数
     */
    private Integer end;

    private Long typeId;

    /**
     * 事件id(用于判断创建指令的同时是否创建事件信息)
     */
    private Long eventId;

    /**
     * 流程，不传或者0为查全部,1交办，2接收，3处置，4反馈，5销号 6未处理 7已处理 8县市区反馈 9已下发
     */
    private Integer process;

    /**
     * 排序类型，0为默认排序。1交办正序，2交办倒叙，3接收正序，4接收倒序，5处置正序，6处置倒序，7反馈正序，8反馈倒序，9销号正序，10销号倒序 11：县市区反馈正序  12：县市区反馈降序
     */
    private Integer sortType;


    /**
     * 指令创建者部门id
     */
    private Long createDeptId;

    /**
     * 牵头人员ids
     */
    private String leadPersonIds;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date assignTime;

    /**
     * 上访类型1：到市  2：赴省  3：进京
     */
    private String petitionType;

    /**
     * 推送时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date pushTime;

    /**
     * 信息类别（从字典中获取）
     */
    private String infoCategory;

    /**
     * 页面类型 1：查看市级指令页面 2：查看县市区指令页面
     */
    private Integer pageType;
    /**
     * 交办开始时间
     */
    private  Date assignStartTime;
    /**
     * 交办结束时间
     */
    private  Date assignEndTime;
    /**
     * 时间搜索（1本人2本周，3本月）
     */
    private Integer dateType;

    /** 是否发布 1：发布 2：未发布 */
    private String isRelease;

    /** 情指id */
    private String qzid;

    /** 公安预警id */
    private Integer gonganEventId;

    /** 部门id */
    @Excel(name = "部门id")
    private String bmid;

    /** 明查暗访id */
    @Excel(name = "明查暗访id")
    private String mcafId;

    /** 文件ids */
    private String fileIds;

    /**
     * 是否为批量
     */
    private Integer isBatch;

    /** 网格直报id */
    @Excel(name = "网格直报id")
    private String wgzbId;

    /** 指令类型 1：维稳 2：平安 3：基层 */
    private Integer instructionType;

    /** 指令创建id */
    private Long instructionCreateDeptId;

    /**
     * 接收单位集合
     */
    private List<Map<String,Object>> receiveUnitMap;

    /** 暗访任务名称 */
    private String missionName;

    /** 暗访点位名称 */
    private String companyName;

    /** 县市区 */
    private String county;

    /** 乡镇街道 */
    private String town;

    /** 点位详细信息 */
    private String companyAddress;

    /** 是否审核 0:待审核1：审核 2：未审核 */
    private String isAudit;

    /** 审核人员 */
    private String auditPerson;

    /** 审核时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date auditTime;

    /**
     * 过程所属环节
     */
    private String processType;

    /** 是否销号 1:销号  2：未销号 */
    private Integer instrucationIsEnd;

    /** 反馈部门 */
    private String feedbackDept;

    /** 销号时间 */
    private Date endTime;

    /** 创建者id */
    private Long creatorId;

    /**
     * 部门协同字段
     */
    private List<Map<String,Object>> departmentMap;

    /**
     * 部门协同str
     */
    private String departmentStr;

    /** 审核提醒次数 */
    private Integer auditRemindCount;

    /** 审核提醒时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date auditRemindTime;

    /** 市外人员数 */
    private Integer outsidePerson;

    /** 行为类型，1扬言极端，2煽动串联，3集聚维权，4个人极端，5信访 */
    private Integer eventProperties;

    /** 纠纷所在地 */
    private String mzxPlace;

    /** 纠纷性质 */
    private String mzxNature;

    /** 纠纷人员关系 */
    private String mzxRelation;

    /** 纠纷发生时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date mzxOccurTime;

    /** 纠纷化解责任单位 */
    private String mzxDutyDept;

    /** 纠纷类型 */
    private String mzxType;

    /** 纠纷上报来源 */
    private String mzxSourceInfo;

    /**
     * mzxId
     */
    private String mzxId;

    /**
     * 关联人数
     */
    private Integer personCount;

    /** 备用统计数字段 */
    private Integer spareNum;


    /** 工作督查id */
    private Long workId;

    /**
     * 失控失联id
     */
    private Long deployControlId;

    /** 失控失联关联群体id */
    private String skslGroupIds;

    /** 失控失联事件类型 */
    private String skslType;

    /** 下步措施 */
    private String nextSteps;

    /** 微政预警事件id */
    @Excel(name = "微政预警事件id")
    private String wzEventId;

    /** 微政事件类型id */
    @Excel(name = "微政事件类型id")
    private String wzEvntType;

    /**
     * 稳控状态
     */
    private String wkStatus;

    /**
     * 我的关注 1:是关注 0:非关注
     */
    private Integer myFollow;

    /**
     * 延期类型 1:延期待审核 2:延期已审核
     */
    private Integer delayType;

    /**
     * 接收不力
     */
    private Integer receiveNot;

    /**
     * 反馈不力
     */
    private Integer feedbackNot;

    /**
     * 处置部门
     */
    private String transferDept;

    /**
     * 案件类型 优秀案例/反面案例
     */
    private String caseType;

    /**
     * 案例理由
     */
    private String caseReason;

    /**
     * 案件发布时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date caseTime;

    /** 化解状态 */
    private String resolveStatus;

    /** 化解状态查询条件 */
    private String resolveStatusQuery;

    /**
     * 是否已移交至维稳预警：0-否，1-是
     */
    private Integer isTransferred;

    /**
     * 是否可移交
     */
    private boolean canBeTransferred;

    /**
     * 移交id
     */
    private Long transferId;
}
