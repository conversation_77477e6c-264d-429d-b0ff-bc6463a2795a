package com.ruoyi.instruction.domain;

import lombok.Data;

/**
 * 风险排查传入类VO
 *
 * <AUTHOR>
 * @date 2025/3/5
 */
@Data
public class RiskCheckVo extends RiskCheck{
    /**
     * 风险数量
     */
    private Integer levelNum;
    /**
     * 排查类型
     */
    private String checkName;
    /**
     * 排查数量
     */
    private Integer checkNum;
    /**
     * 区域数量
     */
    private Integer areaNum;
    /**
     * 区域率
     */
    private String areaRate;
    /**
     * 化解类型
     */
    private String dissolutionType;
    /**
     * 化解数量
     */
    private Integer dissolutionNum;
    /**
     * 化解率
     */
    private String dissolutionRate;

}
