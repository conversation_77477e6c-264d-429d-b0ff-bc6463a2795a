package com.ruoyi.instruction.mapper;

import java.util.Date;
import java.util.List;
import java.util.Map;

import com.ruoyi.instruction.domain.InstructionReceive;
import com.ruoyi.instruction.domain.rspVo.CityInstructionExportRspVo;
import lombok.Data;
import org.apache.ibatis.annotations.Param;
import org.springframework.security.core.parameters.P;

/**
 * 指令接收Mapper接口
 *
 * <AUTHOR>
 * @date 2022-12-19
 */
public interface InstructionReceiveMapper
{
    /**
     * 查询指令接收
     *
     * @param id 指令接收主键
     * @return 指令接收
     */
    public InstructionReceive selectInstructionReceiveById(Long id);

    /**
     * 查询指令接收列表
     *
     * @param instructionReceive 指令接收
     * @return 指令接收集合
     */
    public List<InstructionReceive> selectInstructionReceiveList(InstructionReceive instructionReceive);

    /**
     * 新增指令接收
     *
     * @param instructionReceive 指令接收
     * @return 结果
     */
    public int insertInstructionReceive(InstructionReceive instructionReceive);

    /**
     * 修改指令接收
     *
     * @param instructionReceive 指令接收
     * @return 结果
     */
    public int updateInstructionReceive(InstructionReceive instructionReceive);

    /**
     * 删除指令接收
     *
     * @param id 指令接收主键
     * @return 结果
     */
    public int deleteInstructionReceiveById(Long id);

    /**
     * 批量删除指令接收
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteInstructionReceiveByIds(Long[] ids);

    /**
     * 根据指令id判断是否已经接收
     * @param id
     * @return
     */
    Date selectReceiveByInstructionId(Long id);

    /**
     * 查询出已经完成交办的单位
     * @param id
     * @return
     */
    List<String> findDisposeUnit(@Param("id") Long id);

    /**
     * 查询指令已反馈的部门
     * @param id
     * @return
     */
    List<String> findFeedBackUnit(@Param("id") Long id);

    /**
     * 查询接收最晚时间
     * @param id
     * @return
     */
    Date findReceiveTime(@Param("id") Long id,@Param("unitCount") Integer unitCount);

    /**
     * 根据指令id查询已接收单位
     * @param id
     * @return
     */
    String selectReceiveUnitByInstructionId(@Param("id") Long id);

    /**
     * 根据指令id删除接收记录
     * @param id
     */
    void deleteInstructionReceiveByInstructionId(@Param("id") Long id);

    /**
     * 通过接收id查询接收记录
     * @param feedBackIds
     * @return
     */
    List<InstructionReceive> findByIds(@Param("idList") List<Long> feedBackIds);

    /**
     * 未接收记录
     * @return
     */
    List<Map<String, Object>> findExpirationReminder();

    /**
     * 根据参数查询接收记录
     * @param params
     * @return
     */
    List<CityInstructionExportRspVo> selectCityReceiveInfo(@Param("params") Map<String, Object> params);

    /**
     * 根据部门id和指令id删除接收记录
     * @param reduce2
     * @param id
     */
    void deleteByDeptIdsAndInstructionId(@Param("list") List<Long> reduce2,@Param("instructionId")  Long id);

    /**
     * 根据指令id删除部门协同接收记录
     * @param id
     */
    void deleteMzxReceiveByInstructionId(@Param("instructionId") Long id);

    /**
     * 查询出未接收记录(MZX)
     * @return
     */
    List<Map<String, Object>> findNoReceiveList();

    /**
     * 查询出未反馈记录
     * @return
     */
    List<Map<String, Object>> findCountyFeedbackWorkNotice();

    /**
     * 查询出未接收记录(WeiWen)
     * @return
     */
    List<Map<String, Object>> findWeiWenNoReceiveList();
}
