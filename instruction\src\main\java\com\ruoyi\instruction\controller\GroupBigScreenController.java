package com.ruoyi.instruction.controller;

import com.ruoyi.common.constant.Constants;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.domain.entity.SysDept;
import com.ruoyi.common.core.domain.entity.SysRole;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.instruction.domain.InstrucationPerson;
import com.ruoyi.instruction.domain.InstructionEvent;
import com.ruoyi.instruction.domain.InstructionGroup;
import com.ruoyi.instruction.domain.rspVo.BigScreenGroupDetailsVo;
import com.ruoyi.instruction.domain.rspVo.BigScreenGroupStatisticalResultsVo;
import com.ruoyi.instruction.domain.rspVo.BigScreenJazzCommonVo;
import com.ruoyi.instruction.domain.rspVo.GroupDataRspVo;
import com.ruoyi.instruction.mapper.InstrucationPersonMapper;
import com.ruoyi.instruction.service.IInstrucationPersonService;
import com.ruoyi.instruction.service.IInstructionEndService;
import com.ruoyi.instruction.service.IInstructionEventService;
import com.ruoyi.instruction.service.IInstructionGroupService;
import com.ruoyi.instruction.service.impl.InstructionInfoServiceImpl;
import com.ruoyi.system.service.ISysDeptService;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 群体驾驶舱接口
 * <AUTHOR>
 * @version 1.0
 * @date 2023/4/17 14:48
 * 群体工作台controller
 */
@RestController
@RequestMapping("/bigScreen/group")
public class GroupBigScreenController extends BaseController {

    @Autowired
    private IInstructionGroupService groupService;

    @Autowired
    private IInstrucationPersonService iInstrucationPersonService;

    @Autowired
    private IInstructionEndService iInstructionEndService;

    @Resource
    private InstrucationPersonMapper instrucationPersonMapper;
    @Autowired
    private  IInstrucationPersonService instrucationPersonService;

    @Autowired
    private IInstructionEventService instructionEventService;


    /**
     * 根据年份统计重点群体信息
     * @return
     */
    @GetMapping("/getGroupDataByYear")
    public AjaxResult getGroupDataByYear(@RequestParam("date") Integer date){
        InstructionGroup instructionGroup=new InstructionGroup();
        HashMap hashMap=new HashMap();
        String yearFirst = DateUtils.parseDateToStr(DateUtils.YYYY_MM_DD, DateUtils.getYearFirst(date));
        String yearFirst1 = DateUtils.parseDateToStr(DateUtils.YYYY_MM_DD, DateUtils.getYearLast(date));
        hashMap.put("beginTime",yearFirst);
        hashMap.put("endTime",yearFirst1);
        instructionGroup.setParams(hashMap);
        BigScreenGroupStatisticalResultsVo bigScreenGroupStatisticalResultsVo=new BigScreenGroupStatisticalResultsVo();
        List<InstructionGroup> instructionGroups = groupService.selectInstructionGroupListYear(instructionGroup);
        Integer personnelNum=0;
        Integer eventNum=0;
        Set<String> collect1=new HashSet<>();
        if (!CollectionUtils.isEmpty(instructionGroups)) {
            for (InstructionGroup group : instructionGroups) {
                //所有人员ids
                group.setPersonCount(0);

                if (group.getAllPersonIds().length() > 0) {
                    Set<String> collect = Arrays.stream(group.getAllPersonIds().split(",")).collect(Collectors.toSet());
                    List<String> collect2 = Arrays.stream(collect.toArray()).map(x -> x.toString()).collect(Collectors.toList());
                    int count = instrucationPersonService.findPeronCountById(collect2);

//                    long[] longs = Arrays.stream(collect.toArray()).mapToLong(x -> Long.parseLong(x.toString())).toArray();
//                    List<InstrucationPerson> instrucationPeople = instrucationPersonMapper.selectInstrucationPersonByIds(longs, null, null);
                    group.setPersonCount(count);
                    collect1.addAll(collect);
                }
                personnelNum=personnelNum+group.getPersonCount();
                eventNum=eventNum+group.getEventCount();
            }
        }
        List<Long> ids =collect1.stream().filter(x->!x.equals("")).map(s -> Long.parseLong(s.toString()))
                .collect(Collectors.toList());
//        Long[] ids = collect1.toArray(new Long[]{});
        bigScreenGroupStatisticalResultsVo.setInstructionGroups(instructionGroups);
        bigScreenGroupStatisticalResultsVo.setPersonnelNum(personnelNum);
        bigScreenGroupStatisticalResultsVo.setEventNum(eventNum);
        if (CollectionUtils.isEmpty(ids)){
            bigScreenGroupStatisticalResultsVo.setRegionNum(0);
        }else {
            List<String> list = iInstrucationPersonService.selectDutyPlace(ids);
            int size = list.size();
            if (list.contains("")){
                size=size-1;
            }
            if (list.contains(null)){
                size=size-1;
            }
            bigScreenGroupStatisticalResultsVo.setRegionNum(size);
        }
        return AjaxResult.success(bigScreenGroupStatisticalResultsVo);
    }


    /**
     * 根据id获取重点群体信息
     * @return
     */
    @GetMapping("/getGroupDetailsById")
    public AjaxResult getGroupDetailsById(InstructionGroup instructionGroup){
        BigScreenGroupDetailsVo bigScreenGroupDetailsVo=new BigScreenGroupDetailsVo();
        List<InstructionGroup> instructionGroups = groupService.selectInstructionGroupList(instructionGroup);
        if (CollectionUtils.isEmpty(instructionGroups)){
            AjaxResult.success(bigScreenGroupDetailsVo);
        }else {
            InstructionGroup instructionGroup1 = instructionGroups.get(0);
            bigScreenGroupDetailsVo.setGroupName(instructionGroup1.getGroupName());
            instructionGroup1.setPersonCount(0);
            if (instructionGroup1.getAllPersonIds().length() > 0||instructionGroup1.getPersonIds().length()>0) {
                String finallyPersonIds = instructionGroup1.getAllPersonIds()+","+instructionGroup1.getPersonIds();
                List<String> collect1 = Arrays.stream(finallyPersonIds.split(",")).collect(Collectors.toList());
                //查询人员数量
                int count11 = instrucationPersonService.findPeronCountById(collect1);
                instructionGroup1.setPersonCount(count11);
                List<Long> ids = Arrays.stream(collect1.toArray()).filter(x->!x.equals("")).map(s -> Long.parseLong(s.toString()))
                        .collect(Collectors.toList());
                List<String> list = iInstrucationPersonService.selectDutyPlace(ids);
                int size = list.size();
                if (list.contains("")){
                    size=size-1;
                }
                if (list.contains(null)){
                    size=size-1;
                }
                bigScreenGroupDetailsVo.setRegionNum(size);
            }else {
                bigScreenGroupDetailsVo.setRegionNum(0);
            }
            BeanUtils.copyProperties(instructionGroup1,bigScreenGroupDetailsVo);
            Integer integer = iInstructionEndService.selectIsEndByGroupId(instructionGroup.getId(),bigScreenGroupDetailsVo);
        }
        return AjaxResult.success(bigScreenGroupDetailsVo);
    }

    /**
     * 根据id获取重点人员列表
     * @return
     */
    @GetMapping("/getPersonById")
    public TableDataInfo getPersonById(InstructionGroup instructionGroup){
        List<InstructionGroup> instructionGroups = groupService.selectInstructionGroupList(instructionGroup);
        if (CollectionUtils.isEmpty(instructionGroups)){
            return getDataTable(new ArrayList<>());
        }else {
            InstructionGroup instructionGroup1 = instructionGroups.get(0);
            if (instructionGroup1.getAllPersonIds().length() > 0) {
                Set<String> collect = Arrays.stream(instructionGroup1.getAllPersonIds().split(",")).collect(Collectors.toSet());
                instructionGroup1.setPersonCount(collect.size());
                // String[] longs = Arrays.stream(collect.toArray()).mapToLong(x -> Long.parseLong(x.toString())).toArray();
                String[] longs = collect.toArray(new String[collect.size()]);
                startPage();
                List<InstrucationPerson> instrucationPeople = instrucationPersonMapper.selectInstrucationPersonByIds(longs, null, null);
                for (InstrucationPerson i:instrucationPeople){
                    if (StringUtils.isEmpty(i.getHousePlace())){
                        if (StringUtils.isEmpty(i.getCurrentPlace())){
                            i.setHousePlace(i.getDutyPlace());
                        }else {
                            i.setHousePlace(i.getCurrentPlace());
                        }
                    }
                }
                return getDataTable(instrucationPeople);
            }
        }

        return getDataTable(new ArrayList<>());

    }


    /**
     * 根据id获取设计区县列表
     * @return
     */
    @GetMapping("/getDutyPlaceById")
    public AjaxResult getDutyPlaceById(InstructionGroup instructionGroup){
        List<HashMap> hashMaps=new ArrayList<>();
        List<InstructionGroup> instructionGroups = groupService.selectInstructionGroupList(instructionGroup);
        if (CollectionUtils.isEmpty(instructionGroups)){
            return AjaxResult.success(hashMaps);
        }else {
            InstructionGroup instructionGroup1 = instructionGroups.get(0);
            if (instructionGroup1.getAllPersonIds().length() > 0) {
                Set<String> collect = Arrays.stream(instructionGroup1.getAllPersonIds().split(",")).collect(Collectors.toSet());
                instructionGroup1.setPersonCount(collect.size());
                List<Long> ids = Arrays.stream(collect.toArray()).filter(x->!x.equals("")).map(s -> Long.parseLong(s.toString()))
                        .collect(Collectors.toList());
                List<BigScreenJazzCommonVo> list = iInstrucationPersonService.countDutyPlace(ids);
                String s1 = instructionEventService.selectDutyUnitByGroupId(instructionGroup.getId());
                List<String> collect1 = Arrays.stream(s1.split(",")).collect(Collectors.toList());
                Map<String, Integer> collect2 = collect1.stream().map(s->s.substring(0,3)).collect(Collectors.toMap(Function.identity(), s -> 1, Integer::sum));
                if (!CollectionUtils.isEmpty(list)) {
                    for (BigScreenJazzCommonVo s:list){
                        HashMap h=new HashMap();
                        h.put("dutyPlace",s.getName());
                        h.put("peopleNum",s.num);
                        Integer integer = collect2.get(s.getName());
                        h.put("eventNum",integer==null?0:integer);
                        hashMaps.add(h);
                    }
                }
            }
        }
        return AjaxResult.success(hashMaps);
    }

    /**
     * 根据群体id查询关联事件集合
     * @param instructionEvent
     * @return
     */
    @GetMapping("/getEventListByGroupId")
    public TableDataInfo getEventListByGroupId(InstructionEvent instructionEvent) {
        startPage();
        List<InstructionEvent> eventListByGroupId = instructionEventService.getEventListByGroupId(instructionEvent.getId());
        return getDataTable(eventListByGroupId);
    }

    /**
     * 根据id获取重点群体每月事件数
     * @return
     */
    @GetMapping("/getGroupEventNum")
    public AjaxResult getGroupEventNum(InstructionGroup instructionGroup){
        List groupEventNum = instructionEventService.getGroupEventNum(instructionGroup);
        return  AjaxResult.success(groupEventNum);
    }
}
