package com.ruoyi.instruction.domain.reqVo;

import com.ruoyi.common.core.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class InspectionReportParam extends BaseEntity implements Cloneable{
    private static final long serialVersionUID = 1L;

    /**
     * 报告类型
     */
    private String reportType;
    /**
     * 任务名称
     */
    private String missionName;
    /**
     * 县市区
     */
    private String county;
    /**
     * 乡镇街道
     */
    private String town;
    /**
     * 点位类别
     */
    private String inspectionUnit;
    /**
     * 问题类别
     */
    private String category;

    //浅拷贝
    @Override
    public InspectionReportParam clone() {
        try {
            InspectionReportParam clone = (InspectionReportParam) super.clone();
            // TODO: copy mutable state here, so the clone can't change the internals of the original
            return clone;
        } catch (CloneNotSupportedException e) {
            throw new AssertionError();
        }
    }
}
