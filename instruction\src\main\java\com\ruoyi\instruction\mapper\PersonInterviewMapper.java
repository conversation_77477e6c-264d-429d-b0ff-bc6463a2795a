package com.ruoyi.instruction.mapper;

import java.util.List;
import com.ruoyi.instruction.domain.PersonInterview;
import org.apache.ibatis.annotations.Param;

/**
 * 人员走访记录Mapper接口
 *
 * <AUTHOR>
 * @date 2024-01-05
 */
public interface PersonInterviewMapper
{
    /**
     * 查询人员走访记录
     *
     * @param id 人员走访记录主键
     * @return 人员走访记录
     */
    public PersonInterview selectPersonInterviewById(Long id);

    /**
     * 查询人员走访记录列表
     *
     * @param personInterview 人员走访记录
     * @return 人员走访记录集合
     */
    public List<PersonInterview> selectPersonInterviewList(PersonInterview personInterview);

    /**
     * 新增人员走访记录
     *
     * @param personInterview 人员走访记录
     * @return 结果
     */
    public int insertPersonInterview(PersonInterview personInterview);

    /**
     * 修改人员走访记录
     *
     * @param personInterview 人员走访记录
     * @return 结果
     */
    public int updatePersonInterview(PersonInterview personInterview);

    /**
     * 删除人员走访记录
     *
     * @param id 人员走访记录主键
     * @return 结果
     */
    public int deletePersonInterviewById(Long id);

    /**
     * 批量删除人员走访记录
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deletePersonInterviewByIds(Long[] ids);

    /**
     * 根据人员id查询走访次数
     * @param personId
     * @return
     */
    Long findInterViewCountByPersonId(@Param("personId") Long personId);
}
