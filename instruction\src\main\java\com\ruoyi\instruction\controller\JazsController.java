package com.ruoyi.instruction.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;

import com.ruoyi.instruction.domain.JazsImport;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.instruction.domain.Jazs;
import com.ruoyi.instruction.service.IJazsService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;
import org.springframework.web.multipart.MultipartFile;

/**
 * 金安指数Controller
 *
 * <AUTHOR>
 * @date 2023-08-09
 */
@RestController
@RequestMapping("/system/jazs")
public class JazsController extends BaseController
{
    @Autowired
    private IJazsService jazsService;

    /**
     * 查询金安指数列表
     */
    @PreAuthorize("@ss.hasPermi('system:jazs:list')")
    @GetMapping("/list")
    public TableDataInfo list(Jazs jazs)
    {
        startPage();
        List<Jazs> list = jazsService.selectJazsList(jazs);
        return getDataTable(list);
    }

    /**
     * 导出金安指数列表
     */
    @PreAuthorize("@ss.hasPermi('system:jazs:export')")
    @Log(title = "金安指数", businessType = BusinessType.EXPORT)
    @GetMapping("/export")
    public void export(String year, String county, String street, String queryMode, @RequestParam(name = "month", required = false) String rankName, String rankType, HttpServletResponse response) throws Exception {
        if ("month".equals(rankName)) {
            rankName = null;
        }
        List<Jazs> list = jazsService.selectJazsNewList(year, county, street, queryMode, rankName, rankType);
        System.out.println("导出数据条数：" + (list == null ? "null" : list.size()));
        ExcelUtil<Jazs> util = new ExcelUtil<Jazs>(Jazs.class);
        util.exportExcel(response, list, "金安指数数据");
    }

    /**
     * 获取金安指数详细信息
     */
    @PreAuthorize("@ss.hasPermi('system:jazs:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(jazsService.selectJazsById(id));
    }

    /**
     * 新增金安指数
     */
    @PreAuthorize("@ss.hasPermi('system:jazs:add')")
    @Log(title = "金安指数", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody Jazs jazs)
    {
        return toAjax(jazsService.insertJazs(jazs));
    }

    /**
     * 修改金安指数
     */
    @PreAuthorize("@ss.hasPermi('system:jazs:edit')")
    @Log(title = "金安指数", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody Jazs jazs)
    {
        return toAjax(jazsService.updateJazs(jazs));
    }

    /**
     * 删除金安指数
     */
    @PreAuthorize("@ss.hasPermi('system:jazs:remove')")
    @Log(title = "金安指数", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(jazsService.deleteJazsByIds(ids));
    }

    /**
     * 下载金安指数导入模板
     *
     * @param response
     */
    @PostMapping("/importTemplate")
    public void importTemplate(HttpServletResponse response) {
        ExcelUtil<JazsImport> util = new ExcelUtil<JazsImport>(JazsImport.class);
        util.importTemplateExcel(response, "金安指数导入模板");
    }

    /**
     * 导入金安指数数据
     */
    @Log(title = "金安指数", businessType = BusinessType.IMPORT)
    @PostMapping("/importJazsData")
    public AjaxResult importJazsData(MultipartFile file) throws Exception {
        ExcelUtil<JazsImport> util = new ExcelUtil<JazsImport>(JazsImport.class);
        List<JazsImport> jazsList = util.importExcel(file.getInputStream());
        //存入数据
        String msg = jazsService.importJazs(jazsList);
        return AjaxResult.success(msg);
    }

    /**
     * 查询金安指数列表
     */
//    @PreAuthorize("@ss.hasPermi('system:jazs:newlist')")
    @GetMapping("/newlist")
    public TableDataInfo newList(String year, String county, String street, @RequestParam(required = false) String queryMode, @RequestParam(name = "month", required = false) String rankName, String rankType)
    {
        startPage();
        List<Jazs> list = jazsService.selectJazsNewList(year, county, street, queryMode, rankName, rankType);
        return getDataTable(list);
    }
}
