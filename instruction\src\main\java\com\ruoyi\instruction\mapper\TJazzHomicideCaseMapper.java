package com.ruoyi.instruction.mapper;

import java.util.List;

import com.ruoyi.instruction.domain.rspVo.HomicideCaseVo;
import org.apache.ibatis.annotations.Mapper;
import com.ruoyi.instruction.domain.TJazzHomicideCase;
import org.apache.ibatis.annotations.Param;

/**
 * 社会治安-命案分析Mapper接口
 * 
 * <AUTHOR>
 * @date 2023-07-19
 */
@Mapper
public interface TJazzHomicideCaseMapper
{
    /**
     * 查询社会治安-命案分析
     * 
     * @param id 社会治安-命案分析ID
     * @return 社会治安-命案分析
     */
    public TJazzHomicideCase selectTJazzHomicideCaseById(Long id);

    /**
     * 查询社会治安-命案分析列表
     * 
     * @param tJazzHomicideCase 社会治安-命案分析
     * @return 社会治安-命案分析集合
     */
    public List<TJazzHomicideCase> selectTJazzHomicideCaseList(TJazzHomicideCase tJazzHomicideCase);

    /**
     * 新增社会治安-命案分析
     * 
     * @param tJazzHomicideCase 社会治安-命案分析
     * @return 结果
     */
    public int insertTJazzHomicideCase(TJazzHomicideCase tJazzHomicideCase);

    /**
     * 修改社会治安-命案分析
     * 
     * @param tJazzHomicideCase 社会治安-命案分析
     * @return 结果
     */
    public int updateTJazzHomicideCase(TJazzHomicideCase tJazzHomicideCase);

    /**
     * 删除社会治安-命案分析
     * 
     * @param id 社会治安-命案分析ID
     * @return 结果
     */
    public int deleteTJazzHomicideCaseById(Long id);

    /**
     * 批量删除社会治安-命案分析
     * 
     * @param ids 需要删除的数据ID
     * @return 结果
     */
    public int deleteTJazzHomicideCaseByIds(Long[] ids);

    /**
     * 命案分析
     * @param type
     * @return
     */
    List<HomicideCaseVo> homicideCasesAnalysis(@Param("type") Integer type);
}
