package com.ruoyi.instruction.controller;

import cn.hutool.core.date.DateUtil;
import com.ruoyi.common.constant.Constants;
import com.ruoyi.common.constant.InstructionRolesConstants;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.domain.entity.SysDept;
import com.ruoyi.common.core.domain.entity.SysRole;
import com.ruoyi.common.core.domain.entity.SysUser;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.exception.GlobalException;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.DeptUtils;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.examineAir.service.IExamineService;
import com.ruoyi.examineAir.vo.BigScreenExamineVo;
import com.ruoyi.examineAir.vo.BigScreenPageRegionStatisticsVo;
import com.ruoyi.instruction.domain.*;
import com.ruoyi.instruction.domain.rspVo.*;
import com.ruoyi.instruction.mapper.DispatchMinutesMapper;
import com.ruoyi.instruction.mapper.InstructionEventMapper;
import com.ruoyi.instruction.service.*;
import com.ruoyi.instruction.service.impl.InstructionInfoServiceImpl;
import com.ruoyi.system.mapper.SysDeptMapper;
import com.ruoyi.system.service.ISysDeptService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR> @version 1.0
 * @date 2
 * 维稳驾驶舱首页
 */
@RestController
@RequestMapping("/bigScreen/page")
public class BigScreenPageController extends BaseController {

    @Autowired
    private IInstructionEventService instructionEventService;

    @Autowired
    private InstructionEventMapper instructionEventMapper;

    @Autowired
    private IInstrucationPersonService iInstrucationPersonService;
    @Autowired
    private IExamineService examineService;

    @Autowired
    private IInstructionEndService instructionEndService;
    @Autowired
    private IInstructionAssignService instructionAssignService;
    @Autowired
    private IInstructionInfoService instructionInfoService;

    @Autowired
    InstructionInfoServiceImpl infoServicen;

    @Autowired
    private IInstructionGroupService instructionGroupService;
    @Autowired
    private ISysDeptService sysDeptService;
    @Autowired
    private  IDutyPersonnelService dutyPersonnelService;
    @Autowired
    private IDutyInformationService dutyInformationService;
    @Resource
    private SysDeptMapper sysDeptMapper;
    @Autowired
    private DispatchMinutesMapper dispatchMinutesMapper;

    @Autowired
    private INetworkYqService networkYqService;


    /**
     * 查询事件基本信息列表
     */
    @GetMapping("/eventList")
    public AjaxResult list(InstructionEvent instructionEvent) {
        String dept = getDept();
        instructionEvent.setDutyUnit(dept);
        List<InstructionEvent> list = instructionEventService.selectInstructionEventList(instructionEvent);
        if (!CollectionUtils.isEmpty(list)) {
            for (InstructionEvent i : list) {
                if (i.getGroupId() == null) {
                    i.setIsHeartGroup(false);
                } else {
                    InstructionGroup instructionGroup = instructionGroupService.selectInstructionGroupById(i.getGroupId());
                    if (instructionGroup != null && instructionGroup.getControlLevel().equals("3")) {
                        i.setIsHeartGroup(true);
                    } else {
                        i.setIsHeartGroup(false);
                    }
                }
            }
        }
        return AjaxResult.success(list);
    }

    /**
     * 获取事件基本信息详细信息
     */
    @GetMapping(value = "/event/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id) {
        return instructionEventService.selectInstructionEventById(id, null, "");
    }

    /**
     * 地图的市区统计
     *
     * @param type 0重点人员，1重大事件，2执行评价（考核晾晒）
     * @return
     */
    @GetMapping(value = "/map/regionStatistics")
    public AjaxResult regionStatistics(Integer type) {
        List<BigScreenPageRegionStatisticsVo> bigScreenPageRegionStatisticsVoList = new ArrayList<>();
        if (type == 0) {
            List<Map<String, Integer>> areaPersonCount = iInstrucationPersonService.getAreaPersonCount();
            if (!CollectionUtils.isEmpty(areaPersonCount)) {
                for (Map m : areaPersonCount) {
                    BigScreenPageRegionStatisticsVo bigScreenPageRegionStatisticsVo = new BigScreenPageRegionStatisticsVo();
                    bigScreenPageRegionStatisticsVo.setDutyPlace(m.get("dutyPlace").toString());
                    bigScreenPageRegionStatisticsVo.setCount(m.get("count").toString());
                    bigScreenPageRegionStatisticsVoList.add(bigScreenPageRegionStatisticsVo);
                }
                List<BigScreenPageRegionStatisticsVo> sort = sort(bigScreenPageRegionStatisticsVoList, type);
//                List<BigScreenPageRegionStatisticsVo> collect = bigScreenPageRegionStatisticsVoList.stream().sorted(
//                        (o1, o2) -> {
//                            if (o1.getCount().compareTo(o2.getCount()) > 0) {
//                                return -1;
//                            } else  {
//                                return 1;
//                            }
//                        }
//                ).collect(Collectors.toList());
                return AjaxResult.success(sort);
            }
            return AjaxResult.success(null);
        } else if (type == 1) {
            return AjaxResult.success(instructionEventService.countArea());
        } else if (type == 2) {
            List<BigScreenPageRegionStatisticsVo> newAreaCount = examineService.getNewAreaCount();
            if (CollectionUtils.isEmpty(newAreaCount)) {
                List<BigScreenPageRegionStatisticsVo> init = init();
                return AjaxResult.success(init);
            }
            List<BigScreenPageRegionStatisticsVo> sort = sort(newAreaCount, type);
            return AjaxResult.success(sort);
        }
        return AjaxResult.success(null);
    }

    /**
     * 区域重点人员详情
     *
     * @param instrucationPerson
     * @return
     */
    @GetMapping(value = "/map/regionPerson")
    public TableDataInfo regionPerson(InstrucationPerson instrucationPerson) {
        startPage();
        List<InstrucationPerson> instrucationPeople = iInstrucationPersonService.selectInstrucationPersonList(instrucationPerson);
        if (!CollectionUtils.isEmpty(instrucationPeople)) {
            for (InstrucationPerson p : instrucationPeople) {
                p.setHousePlace(p.getDutyPlace());
                String s = iInstrucationPersonService.selectGroupNameById(p.getId().toString());
                p.setGroupName(s);
            }

        }
        return getDataTable(instrucationPeople);
    }

    /**
     * 区域重大事件详情
     *
     * @param area
     * @return
     */
    @GetMapping(value = "/map/regionEvent")
    public AjaxResult regionEvent(String area) {
        InstructionEvent instructionEvent = new InstructionEvent();
        instructionEvent.setDutyUnit(area);
        instructionEvent.setStatus("1");
        instructionEvent.setIsRelease("1");
        List<InstructionEvent> instructionEvents = instructionEventService.selectInstructionEventList(instructionEvent);
        for (InstructionEvent i : instructionEvents) {
            if (i.getPetitionType() == null) {
                i.setPetitionTypeName("无");
                continue;
            }
            //1：到市 2：赴省 3：进京
            switch (i.getPetitionType()) {
                case "1":
                    i.setPetitionTypeName("到市");
                    break;
                case "2":
                    i.setPetitionTypeName("赴省");
                    break;
                case "3":
                    i.setPetitionTypeName("进京");
                    break;
                default:
                    i.setPetitionTypeName("无");
                    break;
            }
        }
        return AjaxResult.success(instructionEvents);
    }

    /**
     * 区域考核晾晒
     *
     * @param area
     * @return
     */
    @GetMapping(value = "/map/regionExamine")
    public AjaxResult regionExamine(String area) {
        BigScreenExamineVo examineVo = examineService.regionExamine(area);
        return AjaxResult.success(examineVo);
    }

    /**
     * 当日交办
     *@param type 查询时间段，1：当天，2：本周，3：本月，4：本年
     * @dataType 1全部，0或者空查金安稳的
     * @return
     */
    @GetMapping(value = "/map/todayAssigned")
    public AjaxResult todayAssigned(Date dt,Integer type,Integer dataType) {
        BigScreenPageTodayAssignVo bigScreenPageTodayAssignVo = new BigScreenPageTodayAssignVo();
        String date="";
        if (dt!=null){
            date= DateUtils.dateTime(dt);
        }else {
            Date timeStart = DateUtils.getTimeStart(type == null ? 1 : type);
            if (timeStart==null){
                throw new GlobalException("参数异常");
            }
            date =DateUtils.dateFormat(type,timeStart);
        }
        if (dataType==null){
            dataType = 1;
        }
//        String dept = getDept();
        String dept =null;
        SysUser user = SecurityUtils.getLoginUser().getUser();
        List<String> roleList = user.getRoles().stream().map(SysRole::getRoleKey).collect(Collectors.toList());
        if (roleList.contains(InstructionRolesConstants.COUNTY_COCKPIT)) {
            dept = DeptUtils.getDutyPlaceByDept(user.getDept());
        }
        List<InstructionAssignVo> instructionAssignVos = instructionAssignService.selectList(date, dept,type,dataType, 202L);
        List<InstructionEndVo> instructionEndVos = instructionEndService.selectList(date, dept,type,dataType);
        bigScreenPageTodayAssignVo.setAssignNum(CollectionUtils.isEmpty(instructionAssignVos) ? 0 : instructionAssignVos.size());
        bigScreenPageTodayAssignVo.setEndNum(CollectionUtils.isEmpty(instructionEndVos) ? 0 : instructionEndVos.size());
        InstructionInfo instructionInfo = new InstructionInfo();
//        instructionInfo.setAssignStartTime(DateUtil.beginOfDay(dt==null?new Date():dt));
        instructionInfo.setAssignStartTime(DateUtils.getTimeStart(type));
        instructionInfo.setAssignEndTime(dt==null?new Date():DateUtil.endOfDay(dt));
        instructionInfo.setReceiveUnit(dept);
        instructionInfo.setInstructionType(dataType!=null&&dataType==1?null:1);
        List<InstructionInfoRspVo> instructionInfoRspVoList = instructionInfoService.instructionListNoPower(instructionInfo);
        bigScreenPageTodayAssignVo.setList(instructionInfoRspVoList);
        return AjaxResult.success(bigScreenPageTodayAssignVo);
    }

    /**
     * 当日已交办列表
     * @param type 查询时间段，1：当天，2：本周，3：本月，4：本年
     *
     * @return
     */
    @GetMapping(value = "/map/todayAssignedDetails")
    public AjaxResult todayAssignedDetails(Date dt,Integer type,Integer dataType,@RequestParam(name = "createDeptId", required = false) Long createDeptId) {
        String date="";
        if (dt!=null){
            date= DateUtils.dateTime(dt);
        }else {
            Date timeStart = DateUtils.getTimeStart(type == null ? 1 : type);
            if (timeStart==null){
                throw new GlobalException("参数异常");
            }
            date =DateUtils.dateFormat(type,timeStart);
        }
//        String date = DateUtils.getDate();
//        String dept = getDept();
        String dept =null;
        if (createDeptId==null){
            createDeptId = 202L;
        }
        List<InstructionAssignVo> instructionAssignVos = instructionAssignService.selectList(date, dept,type,dataType,createDeptId);
        return AjaxResult.success(instructionAssignVos);
    }

    /**
     * 当日已销号列表
     *
     * @return
     */
    @GetMapping(value = "/map/todayEndDetails")
    public AjaxResult todayEndDetails(Date dt,Integer type,Integer dataType) {
        String date="";
        if (dt!=null){
            date= DateUtils.dateTime(dt);
        }else {
            Date timeStart = DateUtils.getTimeStart(type == null ? 1 : type);
            if (timeStart==null){
                throw new GlobalException("参数异常");
            }
            date =DateUtils.dateFormat(type,timeStart);
        }
//        String date = DateUtils.getDate();
//        String dept = getDept();
        String dept =null;
        List<InstructionEndVo> instructionEndVos = instructionEndService.selectList(date, dept,type,dataType);
        return AjaxResult.success(instructionEndVos);
    }

    /**
     * 指令列表(没有权限)
     *
     * @param instructionInfo
     * @return
     */
    @GetMapping("/instructionListNoPower")
    public TableDataInfo instructionListNoPower(InstructionInfo instructionInfo) {
        if (instructionInfo.getDateType() == null || instructionInfo.getStart() == null || instructionInfo.getEnd() == null) {
            throw new GlobalException("传参错误");
        }
        if (instructionInfo.getReceiveUnit().equals("金华市")) {
            instructionInfo.setReceiveUnit(null);
        }
        if (instructionInfo.getDateType() == 1) {
            instructionInfo.setAssignStartTime(DateUtil.beginOfDay(new Date()));
            instructionInfo.setAssignEndTime(new Date());
        } else if (instructionInfo.getDateType() == 2) {
            Calendar c = Calendar.getInstance();
            c.set(Calendar.DAY_OF_WEEK, Calendar.MONDAY);
            c.set(Calendar.HOUR_OF_DAY, 0);
            c.set(Calendar.MINUTE, 0);
            c.set(Calendar.SECOND, 0);
            c.set(Calendar.MILLISECOND, 0);
            instructionInfo.setAssignStartTime(c.getTime());
            instructionInfo.setAssignEndTime(new Date());
        } else if (instructionInfo.getDateType() == 3) {
            Calendar cal = Calendar.getInstance();
            cal.setTime(new Date());
            cal.set(Calendar.DAY_OF_MONTH, 1);
            cal.set(Calendar.HOUR_OF_DAY, 0);
            cal.set(Calendar.MINUTE, 0);
            cal.set(Calendar.SECOND, 0);
            cal.set(Calendar.MILLISECOND, 0);
            instructionInfo.setAssignStartTime(cal.getTime());
            instructionInfo.setAssignEndTime(new Date());
        }else if (instructionInfo.getDateType() == 4) {
            //查询累计
            Calendar cal = Calendar.getInstance();
            cal.set(2023, Calendar.JANUARY, 1, 0, 0, 0);
            cal.set(Calendar.MILLISECOND, 0);
            instructionInfo.setAssignStartTime(cal.getTime());
            instructionInfo.setAssignEndTime(new Date());
        }
        String dept = getDept();
        instructionInfo.setReceiveUnit(dept);
        instructionInfo.setInstructionType(1);
        List<InstructionInfoRspVo> instructionInfoRspVoList = instructionInfoService.instructionListNoPower(instructionInfo);
        //按照assignTime 降序排序
        instructionInfoRspVoList.sort(Comparator.comparing(InstructionInfoRspVo::getAssignTime).reversed());
        TableDataInfo tableDataInfo = new TableDataInfo();
        int start = instructionInfo.getEnd() * (instructionInfo.getStart() - 1);
        List<InstructionInfoRspVo> collect1 = instructionInfoRspVoList.stream().skip(start).limit(instructionInfo.getEnd()).collect(Collectors.toList());
//        //添加记录
        tableDataInfo.setRows(collect1);
        tableDataInfo.setTotal(instructionInfoRspVoList.size());
        tableDataInfo.setMsg("操作成功");
        tableDataInfo.setCode(200);
        return tableDataInfo;
    }

    public List<BigScreenPageRegionStatisticsVo> sort(List<BigScreenPageRegionStatisticsVo> collect, Integer type) {
        List<BigScreenPageRegionStatisticsVo> collect1 = new ArrayList<>();
        if (type == 2) {
            collect1 = collect.stream().sorted(
                    (o1, o2) -> {
                        if (new BigDecimal(o1.getCount()).compareTo(new BigDecimal(o2.getCount())) == 1) {
                            return -1;
                        } else {
                            return 1;
                        }
                    }
            ).collect(Collectors.toList());
        } else {
            collect1 = collect.stream().sorted(
                    (o1, o2) -> {
                        if (Integer.parseInt(o1.getCount()) - Integer.parseInt(o2.getCount()) > 0) {
                            return -1;
                        } else {
                            return 1;
                        }
                    }
            ).collect(Collectors.toList());
        }
        for (int i = 0; i < collect1.size(); i++) {
            if (i < 3) {
                collect1.get(i).setLevel(1);
            } else if (i > 6) {
                collect1.get(i).setLevel(3);
            } else {
                collect1.get(i).setLevel(2);
            }
        }
        return collect1;
    }

    /**
     * 执行评分获取初始化数据
     *
     * @return
     */
    public List<BigScreenPageRegionStatisticsVo> init() {
        List<BigScreenPageRegionStatisticsVo> list = new ArrayList<>();
        BigScreenPageRegionStatisticsVo b1 = new BigScreenPageRegionStatisticsVo();
        b1.setDutyPlace("婺城区");
        b1.setLevel(1);
        list.add(b1);
        BigScreenPageRegionStatisticsVo b2 = new BigScreenPageRegionStatisticsVo();
        b2.setDutyPlace("兰溪市");
        b2.setLevel(1);
        list.add(b2);
        BigScreenPageRegionStatisticsVo b3 = new BigScreenPageRegionStatisticsVo();
        b3.setDutyPlace("金东区");
        b3.setLevel(1);
        list.add(b3);
        BigScreenPageRegionStatisticsVo b4 = new BigScreenPageRegionStatisticsVo();
        b4.setDutyPlace("义乌市");
        b4.setLevel(1);
        list.add(b4);
        BigScreenPageRegionStatisticsVo b5 = new BigScreenPageRegionStatisticsVo();
        b5.setDutyPlace("武义县");
        b5.setLevel(1);
        list.add(b5);
        BigScreenPageRegionStatisticsVo b6 = new BigScreenPageRegionStatisticsVo();
        b6.setDutyPlace("永康市");
        b6.setLevel(1);
        list.add(b6);
        BigScreenPageRegionStatisticsVo b7 = new BigScreenPageRegionStatisticsVo();
        b7.setDutyPlace("东阳市");
        b7.setLevel(1);
        list.add(b7);
        BigScreenPageRegionStatisticsVo b8 = new BigScreenPageRegionStatisticsVo();
        b8.setDutyPlace("磐安县");
        b8.setLevel(1);
        list.add(b8);
        BigScreenPageRegionStatisticsVo b9 = new BigScreenPageRegionStatisticsVo();
        b9.setDutyPlace("浦江县");
        b9.setLevel(1);
        list.add(b9);
        BigScreenPageRegionStatisticsVo b10 = new BigScreenPageRegionStatisticsVo();
        b10.setDutyPlace("开发区");
        b10.setLevel(1);
        list.add(b10);
        return list;
    }


    public String getDept() {
        Long deptId = SecurityUtils.getDeptId();
        List<SysRole> roles = SecurityUtils.getLoginUser().getUser().getRoles();
        List<String> roleList = roles.stream().map(SysRole::getRoleKey).collect(Collectors.toList());
        if (roleList.contains("cityInstruction")) {
            deptId = Constants.JINHUA_CITY_DEPT_ID;
        } else if (roleList.contains("countyInstruction")) {
            SysDept dept = SecurityUtils.getLoginUser().getUser().getDept();
            dept = infoServicen.getCountyDeptInfo(dept);
            deptId = dept.getDeptId();
        }
        SysDept sysDept = sysDeptService.selectDeptForInstructionById(deptId);
        if (sysDept != null && sysDept.getDeptName().contains("政法")) {
            if (sysDept.getDeptName().contains("金华市委政法委")) {
                return null;
            } else {
                return sysDept.getDeptName();
            }
        } else {
            return "未知";
        }
    }

    /**
     * 获取亚运赛事
     *
     * @return
     */
    @GetMapping("/getGameEvent")
    public AjaxResult getGameEvent() {
        List<Map<String, Object>> mapList = instructionAssignService.getGameEvent();
        Map<Object, List<Map<String, Object>>> event_date = mapList.stream().collect(Collectors.groupingBy(map -> map.get("event_date"), LinkedHashMap::new, Collectors.toList()));
        List<Map<Object, Object>> finallyMap = new ArrayList<>();
        for (Object s : event_date.keySet()) {
            List<Map<String, Object>> mapList1 = event_date.get(s);
            Map<Object, Object> map = new HashMap<>();
            map.put("name", s);
            map.put("list", mapList1);
            finallyMap.add(map);
        }
        return AjaxResult.success(finallyMap);
    }

    /**
     * 获取每日会商
     * @param date
     * @return
     */
    @GetMapping("/getDayMeet")
    public AjaxResult getDayMeet(String date){
        List<Map<String,Object>> mapList = instructionEndService.getDayMeet(date);
        return AjaxResult.success(mapList);
    }

    /**
     * 获取日督查
     * @param date
     * @return
     */
    @GetMapping("/getDayView")
    public AjaxResult getDayView(String date){
        Map<String,Object> map = instructionEndService.getDayView(date);
        if (map==null){
            return AjaxResult.success(new HashMap<>());
        }
        return AjaxResult.success(map);
    }

    /**
     * 获取日调度数据
     * @return
     */
    @GetMapping("/getDayDispatch")
    public AjaxResult getDayDispatch(String workJob){
        List<Map<String,Object>> mapList = instructionEndService.getDayDispatch(workJob);
        // Map<Object, List<Map<String, Object>>> scene = mapList.stream().collect(Collectors.groupingBy(map -> map.get("workJob"), LinkedHashMap::new, Collectors.toList()));
        // List<Map<Object, Object>> finallyMap = new ArrayList<>();
        // for (Object s : scene.keySet()) {
        //     List<Map<String, Object>> mapList1 = scene.get(s);
        //     Map<Object, Object> map = new HashMap<>();
        //     map.put("flag", false);
        //     map.put("name", s);
        //     map.put("list", mapList1);
        //     finallyMap.add(map);
        // }
        return AjaxResult.success(mapList);
    }
    /**
     * 驾驶舱值班列表
     * @param area  区域
     * @param type 查询类型，1市直部门，2县市区指挥中心，3乡镇指挥中心
     * @return
     */
    @GetMapping("/dutyList")
    public AjaxResult dutyList(String area,Integer type){
        if(StringUtils.isEmpty(area)){
            Long deptId = SecurityUtils.getDeptId();
            SysDept sysDept = sysDeptMapper.selectDeptById(deptId);
//            AreaDept byDept = AreaDept.getByDept(sysDept.getDeptName());
//            if (!StringUtils.isEmpty(byDept.getArea())&&!"金华市".equals(byDept.getArea())){
//                area=byDept.getArea();
//            }
            String dutyPlaceByDept = DeptUtils.getDutyPlaceByDept1(sysDept);
            if (!StringUtils.isEmpty(dutyPlaceByDept)&&!dutyPlaceByDept.equals("金华市")){
                area=dutyPlaceByDept;
            }
        }
        List<DutyPersonnel> list =dutyPersonnelService.dutyListNew(area);
        return AjaxResult.success(list);
    }

    /**
     * 今日值班
     * @param area  区域
     * @return
     */
    @GetMapping("/dutyToday")
    public AjaxResult dutyList(String area){
        if(StringUtils.isEmpty(area)){
            Long deptId = SecurityUtils.getDeptId();
            SysDept sysDept = sysDeptMapper.selectDeptById(deptId);
//            AreaDept byDept = AreaDept.getByDept(sysDept.getDeptName());
//            if (!StringUtils.isEmpty(byDept.getArea())){
//                area=byDept.getArea();
//            }
            String dutyPlaceByDept = DeptUtils.getDutyPlaceByDept1(sysDept);
            if (!StringUtils.isEmpty(dutyPlaceByDept)){
                area=dutyPlaceByDept;
            }
        }
        DutyInformationVo dutyInformationVo = dutyInformationService.dutyToday(area);
        DutyRiskLevelVo dutyRiskLevelVo = dutyInformationService.selectRiskLevel(area);
        dutyInformationVo.setLevel(dutyRiskLevelVo.getLevel());
        return AjaxResult.success(dutyInformationVo);
    }
    /**
     * 今日值班本周列表
     * @param area  区域
     * @return
     */
    @GetMapping("/dutyTodayWeek")
    public AjaxResult dutyTodayWeek(String area,Date startTime,Date endTime){
        if(StringUtils.isEmpty(area)){
            Long deptId = SecurityUtils.getDeptId();
            SysDept sysDept = sysDeptMapper.selectDeptById(deptId);
//            AreaDept byDept = AreaDept.getByDept(sysDept.getDeptName());
//            if (!StringUtils.isEmpty(byDept.getArea())){
//                area=byDept.getArea();
//            }
            String dutyPlaceByDept = DeptUtils.getDutyPlaceByDept1(sysDept);
            if (!StringUtils.isEmpty(dutyPlaceByDept)){
                area=dutyPlaceByDept;
            }
        }
        List<DutyInformationVo> dutyInformationVo = dutyInformationService.dutyTodayWeek(area,startTime,endTime);
        return AjaxResult.success(dutyInformationVo);
    }

    /**
     * 今日值班判断是否是金华市
     * @return
     */
    @GetMapping("/isJh")
    public AjaxResult isJh(){
        boolean b=false;
        Long deptId = SecurityUtils.getDeptId();
        SysDept sysDept = sysDeptMapper.selectDeptById(deptId);
//        AreaDept byDept = AreaDept.getByDept(sysDept.getDeptName());
//        if (!StringUtils.isEmpty(byDept.getArea())) {
//            if ("金华市".equals(  byDept.getArea())){
//                b=true;
//            }
//        }
        String dutyPlaceByDept = DeptUtils.getDutyPlaceByDept1(sysDept);
        if (!StringUtils.isEmpty(dutyPlaceByDept)) {
            if ("金华市".equals( dutyPlaceByDept)){
                b=true;
            }
        }
        return  AjaxResult.success(b);
    }
    /**
     * 今日值班判断是否是金华市
     * @return
     */
    @GetMapping("/ddjy")
    public AjaxResult ddjy(Date date){
        List<HashMap> hashMaps = dispatchMinutesMapper.selectByTime(date);
        return  AjaxResult.success(hashMaps);
    }

    /**
     * 获取群体列表数据
     * @param group
     * @return
     */
    @GetMapping("/getGroupDataForBigScreen")
    public AjaxResult getGroupDataForBigScreen(InstructionGroup group){
        List<Map<String,Object>> mapList = instructionGroupService.getGroupDataForBigScreen(group);
        return AjaxResult.success(mapList);
    }

    /**
     * 获取网络舆情
     * @param networkYq
     * @return
     */
    @GetMapping("/getNetWorkList")
    public AjaxResult getNetWorkList(NetworkYq networkYq)
    {
        List<NetworkYq> list = networkYqService.selectNetworkYqList(networkYq);
        return AjaxResult.success(list);
    }

    /**
     * 根据人员id获取相关联事件
     * @param id
     * @return
     */
    @GetMapping("/getPersonEventList/{id}")
    public AjaxResult getPersonEventList(@PathVariable Long id){
        List<InstructionEvent> eventList = instructionEventMapper.findByPersonId(id);
        return AjaxResult.success(eventList);
    }

}
