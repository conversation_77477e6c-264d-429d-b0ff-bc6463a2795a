package com.ruoyi.instruction.mapper;

import java.util.List;
import com.ruoyi.instruction.domain.HandleDelay;

/**
 * 办理期限延期表Mapper接口
 *
 * <AUTHOR>
 * @date 2025-01-02
 */
public interface HandleDelayMapper
{
    /**
     * 查询办理期限延期表
     *
     * @param id 办理期限延期表主键
     * @return 办理期限延期表
     */
    public HandleDelay selectHandleDelayById(Long id);

    /**
     * 查询办理期限延期表列表
     *
     * @param handleDelay 办理期限延期表
     * @return 办理期限延期表集合
     */
    public List<HandleDelay> selectHandleDelayList(HandleDelay handleDelay);

    /**
     * 新增办理期限延期表
     *
     * @param handleDelay 办理期限延期表
     * @return 结果
     */
    public int insertHandleDelay(HandleDelay handleDelay);

    /**
     * 修改办理期限延期表
     *
     * @param handleDelay 办理期限延期表
     * @return 结果
     */
    public int updateHandleDelay(HandleDelay handleDelay);

    /**
     * 删除办理期限延期表
     *
     * @param id 办理期限延期表主键
     * @return 结果
     */
    public int deleteHandleDelayById(Long id);

    /**
     * 批量删除办理期限延期表
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteHandleDelayByIds(Long[] ids);


    /**
     * 获取指令列表
     * @return
     */
    List<HandleDelay> getInstructionList();

}
