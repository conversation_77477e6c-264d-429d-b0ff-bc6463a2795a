package com.ruoyi.instruction.controller;

import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.instruction.domain.InstructionCase;
import com.ruoyi.instruction.service.IInstructionCaseService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 【经典案例】Controller
 *
 * <AUTHOR>
 * @date 2023-05-12
 */
@RestController
@RequestMapping("/instruction/case")
public class InstructionCaseController extends BaseController
{
    @Autowired
    private IInstructionCaseService instructionCaseService;

    /**
     * 查询【经典案例】列表
     */
    @PreAuthorize("@ss.hasPermi('instruction:case:list')")
    @GetMapping("/list")
    public TableDataInfo list(InstructionCase instructionCase)
    {
        startPage();
        List<InstructionCase> list = instructionCaseService.selectInstructionCaseList(instructionCase);
        return getDataTable(list);
    }

    /**
     * 导出【经典案例】excel表
     */
    @PreAuthorize("@ss.hasPermi('instruction:case:export')")
    @Log(title = "【导出】", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, InstructionCase instructionCase)
    {
        List<InstructionCase> list = instructionCaseService.selectInstructionCaseList(instructionCase);
        ExcelUtil<InstructionCase> util = new ExcelUtil<InstructionCase>(InstructionCase.class);
        util.exportExcel(response, list, "【经典案例】数据");
    }

    /**
     * 获取【经典案例】详细信息
     */
    @PreAuthorize("@ss.hasPermi('instruction:case:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(instructionCaseService.selectInstructionCaseById(id));
    }

    /**
     * 新增【经典案例】
     */
    @PreAuthorize("@ss.hasPermi('instruction:case:add')")
    @Log(title = "【新增】", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody InstructionCase instructionCase)
    {
        return toAjax(instructionCaseService.insertInstructionCase(instructionCase));
    }

    /**
     * 修改【经典案例】
     */
    @PreAuthorize("@ss.hasPermi('instruction:case:edit')")
    @Log(title = "【更新】", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody InstructionCase instructionCase)
    {
        return toAjax(instructionCaseService.updateInstructionCase(instructionCase));
    }

    /**
     * 删除【经典案例】
     */
    @PreAuthorize("@ss.hasPermi('instruction:case:remove')")
    @Log(title = "【删除】", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(instructionCaseService.deleteInstructionCaseByIds(ids));
    }
}
