package com.ruoyi.instruction.domain;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;
import com.ruoyi.common.utils.DateUtils;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 【请填写功能名称】对象 t_instruction_eventpost
 *
 * <AUTHOR>
 * @date 2023-07-21
 */
@Data
public class InstructionEventpost extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    private String id;

    /**
     * 事件主键
     */
    private String eventId;

    /**
     * 事件编号
     */
    private String serialNumber;


    /**
     * 指挥平台分类 附录3.1
     */
    private String platformType;

    /**
     * 经度
     */
    private BigDecimal longitude;

    /**
     * 纬度
     */
    private BigDecimal latitude;

    /**
     * 事件标题
     */
    @Excel(name = "事件标题")
    private String subject;

    /**
     * 县市区
     */
    @Excel(name = "县市区")
    private String county;

    /**
     * 乡镇街道
     */
    @Excel(name = "乡镇街道")
    private String town;

    /**
     * 上报行政区划名称
     */
    @Excel(name = "网格")
    private String reportAdName;


    /**
     * 事发时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "事发时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm")
    private Date occurDateTime;


    /**
     * 事件类型（三级分类），匹配1.6中类型的code字段
     */
    @Excel(name = "事发类型")
    private String eventType;

    /**
     * 事发时间
     */
    private Long occurDate;

    /**
     * 事发地点
     */
    @Excel(name = "事发地点")
    private String occurLocation;

    /**
     * 事件内容
     */
    @Excel(name = "事件描述")
    private String content;

    /**
     * 涉事人数
     */
    @Excel(name = "涉事人数")
    private Long relatepeopleCount;

    /**
     * 上报附件 ，多个逗号隔开
     */
    @Excel(name = "上报附件 ，多个逗号隔开")
    private String reportFiles;

    /**
     * 紧急程度(1紧急;2非紧急)
     */
    @Excel(name = "紧急程度", readConverterExp = "1=紧急,2=非紧急")
    private String emergencyLevel;

    /**
     * 是否重点场所（0否，1是）
     */
    @Excel(name = "是否重点场所", readConverterExp = "0=否,1是")
    private String keyPlace;

    /**
     * 上报时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "上报时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm")
    private Date reportDateTime;

    /**
     * 上报人姓名
     */
    private String reportUserName;

    /**
     * 上报人主键（浙政钉用户主键）
     */
    private String reportUserId;

    /**
     * 上报人联系电话
     */
    private String reportContact;

    /**
     * 上报组织主键
     */
    private String reportOrgId;

    /**
     * 上报组织名称
     */
    private String reportOrgName;

    /**
     * 上报行政区划编码
     */
    private String reportAdCode;


    /**
     * 上报时间
     */
    private Long reportDate;

    /**
     * 编码
     */
    private String code;

    /**
     * 1存在 9 删除
     */
    private String status;


    /**
     * 处置类型 1：已处置  2：未处置
     */
    private Long dealType;

    /**
     * 1:交办 2：放置
     */
    private Long disposeType;

    /**
     * 指令、事件id
     */
    private Long infoId;


    /**
     * 文件ids
     */
    private String fileIds;

    /**
     * 二级事件名称
     */
    private String secondName;


}
