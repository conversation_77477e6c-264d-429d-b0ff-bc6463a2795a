package com.ruoyi.instruction.controller;

import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.net.URLEncoder;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;
import javax.servlet.http.HttpServletResponse;

import cn.hutool.core.io.resource.ClassPathResource;
import com.deepoove.poi.XWPFTemplate;
import com.deepoove.poi.data.*;
import com.deepoove.poi.data.style.TableStyle;
import com.ruoyi.common.constant.Constants;
import com.ruoyi.common.core.domain.entity.SysDept;
import com.ruoyi.common.core.domain.entity.SysRole;
import com.ruoyi.common.core.domain.entity.SysUser;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.instruction.domain.*;
import com.ruoyi.instruction.domain.rspVo.*;
import com.ruoyi.instruction.mapper.DutyPersonnelMapper;
import com.ruoyi.instruction.mapper.InstructionEventMapper;
import com.ruoyi.instruction.mapper.InstructionGroupMapper;
import com.ruoyi.instruction.service.IIndicatorTypeService;
import com.ruoyi.instruction.service.IInstructionInfoService;
import com.ruoyi.system.service.impl.SysDeptServiceImpl;
import fr.opensagres.xdocreport.document.IXDocReport;
import fr.opensagres.xdocreport.document.registry.XDocReportRegistry;
import fr.opensagres.xdocreport.template.IContext;
import fr.opensagres.xdocreport.template.TemplateEngineKind;
import fr.opensagres.xdocreport.template.formatter.FieldsMetadata;
import org.apache.poi.xwpf.usermodel.XWPFTable;
import org.springframework.beans.BeanUtils;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ResourceUtils;
import org.springframework.web.bind.annotation.*;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.instruction.service.IInstructionEventService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;
import org.springframework.web.multipart.MultipartFile;

/**
 * 事件基本信息Controller
 *
 * <AUTHOR>
 * @date 2022-12-16
 */
@RestController
@RequestMapping("/instruction/event")
public class InstructionEventController extends BaseController {

    @Autowired
    private IInstructionEventService instructionEventService;

    @Autowired
    private IIndicatorTypeService indicatorTypeService;

    @Autowired
    private IInstructionInfoService instructionInfoService;

    @Autowired
    private SysDeptServiceImpl deptService;

    @Autowired
    private InstructionEventMapper eventMapper;

    @Autowired
    private DutyPersonnelMapper dutyPersonnelMapper;

    @Autowired
    private InstructionGroupMapper groupMapper;

    /**
     * 查询事件基本信息列表
     */
    @PreAuthorize("@ss.hasPermi('instruction:event:list')")
    @GetMapping("/list")
    public TableDataInfo list(InstructionEvent instructionEvent) {
        Long deptId = SecurityUtils.getDeptId();
        SysDept dept = SecurityUtils.getLoginUser().getUser().getDept();
        List<SysRole> roles = SecurityUtils.getLoginUser().getUser().getRoles();
        List<String> roleList = roles.stream().map(SysRole::getRoleKey).collect(Collectors.toList());
        if (roleList.contains("cityInstruction")) {
            deptId = Constants.JINHUA_CITY_DEPT_ID;
        } else if (roleList.contains("countyInstruction")) {
            dept = deptService.getCountyDeptInfo(dept);
            deptId = dept.getDeptId();
        }
        List<InstructionEvent> list = new ArrayList<>();
        instructionEvent.setCreateDeptId(deptId);
        if ((deptId == 202 && instructionEvent.getPageType() != null && instructionEvent.getPageType() == 2)) {
            if (instructionEvent.getDutyUnit()!=null&&!instructionEvent.getDutyUnit().equals("")){
                Long dutyId = getDeptIdByDutyPlace(instructionEvent.getDutyUnit());
                instructionEvent.setDutyUnit(null);
                instructionEvent.setCreateDeptId(dutyId);
            }else {
                //访问市级创建页面
                instructionEvent.setCreateDeptId(10L);
            }

        } else if (deptId != 202 && instructionEvent.getPageType() != null && instructionEvent.getPageType() == 1) {
            //县市区访问市级事件页面
            instructionEvent.setDutyUnit(dept.getDeptName());
            instructionEvent.setCreateDeptId(202L);
        }
        //查询类型及其下级的
        if (instructionEvent.getType() != null) {
            String type = instructionEvent.getType();
            String[] typeIds = type.split(",");
            // List<Long> typeIds = indicatorTypeService.findChildenType(instructionEvent.getType());
            // typeIds.add(Long.valueOf(instructionEvent.getType()));
            Map<String, Object> params = instructionEvent.getParams();
            params.put("typeIds", typeIds);
            instructionEvent.setParams(params);
        }


        startPage();
        list = instructionEventService.selectInstructionEventList(instructionEvent);
        if (list != null && list.size() > 0) {
            //查询事件类型
            List<IndicatorType> indicatorTypes = indicatorTypeService.selectIndicatorTypeList(new IndicatorType());
            //查询群体
            List<GroupRspVo> groupList = groupMapper.getNewContactList(new InstructionGroup());
            for (InstructionEvent event : list) {
                if (event.getType() != null && !event.getType().equals("")) {
                    Long type = Long.valueOf(event.getType());
                    IndicatorType indicatorType1 = indicatorTypes.stream().filter(indicatorType -> indicatorType.getId().equals(type)).findFirst().orElse(null);
                    if (indicatorType1 != null) {
                        event.setTypeName(indicatorType1.getTypeName());
                    }
                }
                if (event.getGroupId() != null && !event.getGroupId().equals("")) {
                    Long groupId = Long.valueOf(event.getGroupId());
                    GroupRspVo group1 = groupList.stream().filter(group -> group.getId().equals(groupId)).findFirst().orElse(null);
                    if (group1 != null) {
                        event.setGroupName(group1.getGroupName());
                    }
                }
            }
        }
        return getDataTable(list);
    }

    /**
     * 根据部门名称查询部门id
     * @param dutyUnit
     * @return
     */
    private Long getDeptIdByDutyPlace(final String dutyUnit) {
        Long deptId = null;
        if (dutyUnit.equals("磐安县委政法委")){
            deptId = 213L;
        }else if (dutyUnit.equals("兰溪市委政法委")){
            deptId = 214L;
        }else if (dutyUnit.equals("东阳市委政法委")){
            deptId = 215L;
        }else if (dutyUnit.equals("义乌市委政法委")){
            deptId = 216L;
        }else if (dutyUnit.equals("浦江县委政法委")){
            deptId = 217L;
        }else if (dutyUnit.equals("永康市委政法委")){
            deptId = 218L;
        }else if (dutyUnit.equals("金东区政法委")){
            deptId = 219L;
        }else if (dutyUnit.equals("婺城区政法委")){
            deptId = 220L;
        }else if (dutyUnit.equals("开发区政法办")){
            deptId = 221L;
        }else if (dutyUnit.equals("武义县委政法委")){
            deptId = 262L;
        }
        return deptId;
    }

    /**
     * 导出事件基本信息列表
     */
    @PreAuthorize("@ss.hasPermi('instruction:event:export')")
    @Log(title = "事件基本信息", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, InstructionEvent instructionEvent) {
        Long deptId = SecurityUtils.getDeptId();
        instructionEvent.setCreateDeptId(deptId);
        if (deptId == 202 && instructionEvent.getPageType() != null && instructionEvent.getPageType() == 2) {
            if (instructionEvent.getDutyUnit()!=null&&!instructionEvent.getDutyUnit().equals("")){
                Long dutyId = getDeptIdByDutyPlace(instructionEvent.getDutyUnit());
                instructionEvent.setDutyUnit(null);
                instructionEvent.setCreateDeptId(dutyId);
            }else {
                //访问市级创建页面
                instructionEvent.setCreateDeptId(10L);
            }

        } else if (deptId != 202 && instructionEvent.getPageType() != null && instructionEvent.getPageType() == 1) {
            //县市区访问市级事件页面
            instructionEvent.setCreateDeptId(202L);
        }
        //查询类型及其下级的
        if (instructionEvent.getType() != null) {
            List<Long> typeIds = indicatorTypeService.findChildenType(instructionEvent.getType());
            typeIds.add(Long.valueOf(instructionEvent.getType()));
            Map<String, Object> params = instructionEvent.getParams();
            params.put("typeIds", typeIds);
            instructionEvent.setParams(params);
        }
        List<InstructionEvent> list = instructionEventService.selectInstructionEventList(instructionEvent);
        if (list != null && list.size() > 0) {
            //查询事件类型
            List<IndicatorType> indicatorTypes = indicatorTypeService.selectIndicatorTypeList(new IndicatorType());
            //查询群体
            List<GroupRspVo> groupList = groupMapper.getNewContactList(new InstructionGroup());
            for (InstructionEvent event : list) {
                if (event.getType() != null && !event.getType().equals("")) {
                    Long type = Long.valueOf(event.getType());
                    IndicatorType indicatorType1 = indicatorTypes.stream().filter(indicatorType -> indicatorType.getId().equals(type)).findFirst().orElse(null);
                    if (indicatorType1 != null) {
                        event.setTypeName(indicatorType1.getTypeName());
                    }
                }
                if (event.getGroupId() != null && !event.getGroupId().equals("")) {
                    Long groupId = Long.valueOf(event.getGroupId());
                    GroupRspVo group1 = groupList.stream().filter(group -> group.getId().equals(groupId)).findFirst().orElse(null);
                    if (group1 != null) {
                        event.setGroupName(group1.getGroupName());
                    }
                }
                event.setPersonCount(event.getPersonCount()+"+("+event.getOutsidePerson()+")");
            }
        }
        ExcelUtil<InstructionEvent> util = new ExcelUtil<InstructionEvent>(InstructionEvent.class);
        util.exportEasyExcel(response, list, "sheet1");
    }

    /**
     * 事件分析报告新
     *
     * @throws Exception
     */
    @GetMapping("/analysisReport")
    public void analysisReportNew(@RequestParam("typeId") Long typeId, Date startTime, Date endTime,String area,String infoSource,Integer eventProperties, String petitionType, HttpServletResponse response) throws Exception {
        EventAnalysisReportVo analysisReportGropVo = instructionEventService.analysisReportNew(typeId, startTime, endTime,area,infoSource, eventProperties, petitionType);
        EventAnalysisReportNewVo eventAnalysisReportNewVo=new EventAnalysisReportNewVo();
        BeanUtils.copyProperties(analysisReportGropVo,eventAnalysisReportNewVo);
//        eventAnalysisReportNewVo.setCreateTime( analysisReportGropVo.getCreateTime());
//        eventAnalysisReportNewVo.setTypeName(analysisReportGropVo.getTypeName());
//        eventAnalysisReportNewVo.setEventCount(analysisReportGropVo.getEventCount());
//        eventAnalysisReportNewVo.setGroupCount(analysisReportGropVo.getGroupCount());
//        eventAnalysisReportNewVo.setRespPersonCount(analysisReportGropVo.getRespPersonCount());
//        eventAnalysisReportNewVo.setRegionCount( analysisReportGropVo.getRegionCount() == null ? 0 : analysisReportGropVo.getRegionCount());
//        eventAnalysisReportNewVo.setRegionDetails(StringUtils.isEmpty(analysisReportGropVo.getRegionDetails()) ? "" : analysisReportGropVo.getRegionDetails());
//        eventAnalysisReportNewVo.setPersonNum(analysisReportGropVo.getPersonNum());
//        eventAnalysisReportNewVo.setGroupDetails(analysisReportGropVo.getGroupDetails());
//        eventAnalysisReportNewVo.setEventPropertiesDetails( analysisReportGropVo.getEventPropertiesDetails());
        MergeCellRule.MergeCellRuleBuilder mergeCellRuleBuilder = MergeCellRule.builder();

        List<String> list = dutyPersonnelMapper.selectAreaSort();
        //设置表头
        RowRenderData header = Rows.of("涉及属地", "异动类型情况", "异动类型情况","异动类型情况","异动类型情况").bgColor("F2F2F2").center()
                .textColor("7F7f7F").textFontFamily("Hei").textFontSize(9).create();
        RowRenderData header1 = Rows.of("涉及属地", "异动类型", "异动群体","异动次数","异动人数").bgColor("F2F2F2").center()
                .textColor("7F7f7F").textFontFamily("Hei").textFontSize(9).create();
        //设置样式颜色尺寸等等
        TableStyle.BorderStyle borderStyle = new TableStyle.BorderStyle();
        borderStyle.setColor("A6A6A6");
        borderStyle.setSize(4);
        borderStyle.setType(XWPFTable.XWPFBorderType.SINGLE);
        List<EventAnalysisReportEventVo> eventList = analysisReportGropVo.getEventList();
        HashMap<String,HashMap<String,HashMap<String,EventAnalysisDetailsRspVo>>> areaMap=new HashMap<>();
//        HashMap<String,List<EventAnalysisDetailsRspVo>> typeMap=new HashMap<>();
        HashMap<String, Integer> areaEventNum = analysisReportGropVo.getAreaEventNum();
        if (!CollectionUtils.isEmpty(eventList)){
            for (EventAnalysisReportEventVo e:eventList){
                String[] split = e.getRegion().split(",");
                for (int i=0;i<split.length;i++){
                    EventAnalysisDetailsRspVo ev=new EventAnalysisDetailsRspVo();
                    BeanUtils.copyProperties(e,ev);
                    ev.setGroupName( org.apache.commons.lang3.StringUtils.isBlank(ev.getGroupName())||ev.getGroupName().trim().equals("")?"其他":ev.getGroupName());

                    String trim = split[i].trim().equals("市")?"金华市": split[i].trim();
                    HashMap<String,HashMap<String,EventAnalysisDetailsRspVo>> hashMap = areaMap.get(trim);
                    if (CollectionUtils.isEmpty(hashMap)){
                        hashMap=new HashMap<>();
                        HashMap hashMap1=new HashMap();
                        EventAnalysisDetailsRspVo gg=new EventAnalysisDetailsRspVo();
                        gg.setEventNum(1);
                        Integer integer = areaEventNum.get(trim + "_" + e.getId());
                        gg.setRespPersonNum(integer==null?0:integer);
                        hashMap1.put(ev.getGroupName(),gg);

                        hashMap.put(e.getTypeName(),hashMap1);
                        areaMap.put(trim,hashMap);
                    }else {
                        HashMap<String, EventAnalysisDetailsRspVo> hashMap1 = hashMap.get(e.getTypeName());
                        if (CollectionUtils.isEmpty(hashMap1)){
                            hashMap1=new HashMap<>();
                        }
                        EventAnalysisDetailsRspVo eventAnalysisDetailsRspVo = hashMap1.get(ev.getGroupName());
                        if (eventAnalysisDetailsRspVo==null){
                            eventAnalysisDetailsRspVo=new EventAnalysisDetailsRspVo();
                            eventAnalysisDetailsRspVo.setEventNum(1);
                            Integer integer = areaEventNum.get(trim + "_" + e.getId());
                            eventAnalysisDetailsRspVo.setRespPersonNum(integer==null?0:integer);
                        }else {
                            eventAnalysisDetailsRspVo.setEventNum(eventAnalysisDetailsRspVo.getEventNum()+1);
                            Integer integer = areaEventNum.get(trim + "_" + e.getId());
                            eventAnalysisDetailsRspVo.setRespPersonNum(integer==null?eventAnalysisDetailsRspVo.getRespPersonNum():eventAnalysisDetailsRspVo.getRespPersonNum()+integer);
                        }
                        hashMap1.put(ev.getGroupName(),eventAnalysisDetailsRspVo);
                        hashMap.put(e.getTypeName(),hashMap1);
                        areaMap.put(trim,hashMap);
                    }
                }

            }
        }
        Tables.TableBuilder tableBuilder = Tables.ofA4MediumWidth().addRow(header).addRow(header1);
        int i=1;
        int start=1;
        int startT=1;
        for (String a:list){
            HashMap<String,HashMap<String,EventAnalysisDetailsRspVo>> value1  = areaMap.get(a);
            if (CollectionUtils.isEmpty(value1)){
                continue;
            }
            start=i+1;
            for(String key1 : value1.keySet()){
                startT=i+1;
                HashMap<String,EventAnalysisDetailsRspVo> eventAnalysisDetailsRspVos = value1.get(key1);
                Integer totalNum=0;
                Integer totalEventNum=0;
                for(String key2 : eventAnalysisDetailsRspVos.keySet()){
                    EventAnalysisDetailsRspVo eventAnalysisDetailsRspVo = eventAnalysisDetailsRspVos.get(key2);
                    i=i+1;
                    totalNum=totalNum+eventAnalysisDetailsRspVo.getRespPersonNum();
                    totalEventNum=totalEventNum+eventAnalysisDetailsRspVo.getEventNum();
                    RowRenderData row = Rows.of(a, key1,key2,eventAnalysisDetailsRspVo.getEventNum()+"",eventAnalysisDetailsRspVo.getRespPersonNum()+"").center().create();

                    tableBuilder.addRow(row);
                }
                i=i+1;
                RowRenderData row = Rows.of(a, key1, "合计",totalEventNum+"",totalNum+"").center().create();
                tableBuilder.addRow(row);
                mergeCellRuleBuilder.map(MergeCellRule.Grid.of(startT, 1), MergeCellRule.Grid.of(i, 1));
//                mergeCellRuleBuilder.map(MergeCellRule.Grid.of(i, 3), MergeCellRule.Grid.of(i, 4));
            }
            mergeCellRuleBuilder.map(MergeCellRule.Grid.of(start, 0), MergeCellRule.Grid.of(i, 0));
        }
//        areaMap.forEach((key, value) ->{
//            HashMap<String,List<EventAnalysisDetailsRspVo>> value1 = value;
//            for(String key1 : value1.keySet()){
//                List<EventAnalysisDetailsRspVo> eventAnalysisDetailsRspVos = value1.get(key1);
//                Integer totalNum=0;
//                for (EventAnalysisDetailsRspVo e:eventAnalysisDetailsRspVos){
//                    totalNum=totalNum+e.getRespPersonNum();
//                    RowRenderData row = Rows.of(key, key1, e.getGroupName(),e.getEventTitle(),e.getRespPersonNum()+"",e.getCreateTime(),e.getIsSiteVisits(),e.getSiteVisitsCount()+"",e.getSiteVisitsType(),e.getEventProperties()).center().create();
//                    tableBuilder.addRow(row);
//                }
//                RowRenderData row = Rows.of(key, key1, "合计",totalNum+"",totalNum+""+"",totalNum+"",totalNum+"",totalNum+""+"",totalNum+"",totalNum+"").center().create();
//                tableBuilder.addRow(row);
//            }
//        });
        TableRenderData tableRenderData =tableBuilder.border(borderStyle).center().create();
//                .create();
//        TableRenderData tableRenderData = Tables.ofA4MediumWidth().addRow(header)
//                .addRow(header1)
//                .border(borderStyle).center()
//                .create();
        /**
         * 设置表格合并规则
         * 1.起始行 MergeCellRule.Grid.of(i, j) i: 行 j: 列
         * 2.结束行 MergeCellRule.Grid.of(i, j) i: 行 j: 列
         */
        try {
            mergeCellRuleBuilder.map(MergeCellRule.Grid.of(0, 1), MergeCellRule.Grid.of(0, 4));
            mergeCellRuleBuilder.map(MergeCellRule.Grid.of(0, 0), MergeCellRule.Grid.of(1, 0));
        }catch (Exception e){
            e.printStackTrace();
        }
        tableRenderData.setMergeRule(mergeCellRuleBuilder.build());
        eventAnalysisReportNewVo.setRenderData(tableRenderData);

        RowRenderData header3 = Rows.of("县市区", "异动次数", "异动人次").bgColor("F2F2F2").center()
                .textColor("7F7f7F").textFontFamily("Hei").textFontSize(9).create();
        Tables.TableBuilder tableBuilder1 = Tables.ofA4MediumWidth().addRow(header3);
        HashMap<String, AreaEventAnalysisReportVo> areaEvent = analysisReportGropVo.getAreaEvent();
        List<Integer> pnum=new ArrayList<>();
        List<Integer> cnum=new ArrayList<>();
        list.add("无身份人员");
        for (String a:list){
            AreaEventAnalysisReportVo areaEventAnalysisReportVo = areaEvent.get(a);
            if (areaEventAnalysisReportVo==null){
                pnum.add(0);
                cnum.add(0);
                RowRenderData row = Rows.of(a, "0", "0").center().create();
                tableBuilder1.addRow(row);
            }else {
                pnum.add(areaEventAnalysisReportVo.getPersonNum());
                cnum.add(areaEventAnalysisReportVo.getEventCount());
                RowRenderData row = Rows.of(a, areaEventAnalysisReportVo.getEventCount()+"", areaEventAnalysisReportVo.getPersonNum()+"").center().create();
                tableBuilder1.addRow(row);
            }
        }
        TableRenderData tableRenderData1 =tableBuilder1.border(borderStyle).center().create();
        eventAnalysisReportNewVo.setRenderData1(tableRenderData1);

        ChartMultiSeriesRenderData chart = Charts
                .ofMultiSeries("异动分析",list.toArray(new String[0]))
                .addSeries("异动次数", cnum.toArray(new Integer[cnum.size()]))
                .addSeries("异动人数", pnum.toArray(new Integer[pnum.size()]))
                .create();

        eventAnalysisReportNewVo.setBarChart(chart);
        InputStream ins = null;
        OutputStream out = null;
        try {
            ClassPathResource tempFileResource = new ClassPathResource("/word/事件分析报告模板新.docx");
            ins = tempFileResource.getStream();
            XWPFTemplate template = XWPFTemplate.compile(ins).render(eventAnalysisReportNewVo);
            response.setCharacterEncoding("utf-8");
            response.setContentType("application/msword");
            String fileName = "事件分析报告.docx";
            response.setHeader("Content-Disposition", "attachment;filename="
                    .concat(String.valueOf(URLEncoder.encode(fileName, "UTF-8"))));
            template.write(response.getOutputStream());
//            template.writeToFile("C:\\Users\\<USER>\\Desktop\\2.docx");
        } catch (Exception e) {
            logger.info("生成word发生异常", e);
        } finally {
            try {
                if (ins != null) {
                    ins.close();
                }
                if (out != null) {
                    out.close();
                }
            } catch (IOException e) {
                logger.info("文件流关闭失败", e);
            }
        }
//
//
//        //此处的userInfo是word中命名的列表名
//        context.put("events", analysisReportGropVo.getEventList());
//        File file = ResourceUtils.getFile("C:\\Users\\<USER>\\Desktop\\事件分析报告模板新.docx");
//        XWPFTemplate template = XWPFTemplate.compile(file).render(eventAnalysisReportNewVo);
//        template.writeToFile("C:\\Users\\<USER>\\Desktop\\2.docx");




    }

    /**
     * 获取事件基本信息详细信息
     */
    @PreAuthorize("@ss.hasPermi('instruction:event:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id,@RequestParam(name = "type",required = false) Integer type,@RequestParam(name = "menuName",required = false) String menuName) {
        return instructionEventService.selectInstructionEventById(id,type,menuName);
    }

    /**
     * 新增事件基本信息
     */
    @PreAuthorize("@ss.hasPermi('instruction:event:add')")
    @Log(title = "事件基本信息", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody InstructionEvent instructionEvent) {
        return instructionEventService.insertInstructionEvent(instructionEvent);
    }

    /**
     * 修改事件基本信息
     */
    @PreAuthorize("@ss.hasPermi('instruction:event:edit')")
    @Log(title = "事件基本信息", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody InstructionEvent instructionEvent) {
        return instructionEventService.updateInstructionEvent(instructionEvent);
    }

    @Log(title = "事件基本信息", businessType = BusinessType.UPDATE)
    @PutMapping("/editReleaseStatus")
    public AjaxResult editRelaseStatus(@RequestBody InstructionEvent instructionEvent) {
        int i = eventMapper.updateInstructionEvent(instructionEvent);
        if (i>0){
            return AjaxResult.success();
        }
        return AjaxResult.error("更新状态失败,请联系管理员");
    }



    /**
     * 删除事件基本信息
     */
    @PreAuthorize("@ss.hasPermi('instruction:event:remove')")
    @Log(title = "事件基本信息", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long ids) {
        return toAjax(instructionEventService.deleteInstructionEventById(ids));
    }

    /**
     * 通过事件id生成指令
     *
     * @param id
     * @return
     */
    @GetMapping("/addInstruction/{id}")
    public AjaxResult addInstruction(@PathVariable("id") Long id) {
        return toAjax(instructionEventService.addInstruction(id));
    }


    /**
     * 根据群体id查询关联事件集合
     *
     * @param id
     * @return
     */
    @GetMapping("/getEventListByGroupId/{id}")
    public AjaxResult getEventListByGroupId(@PathVariable("id") Long id) {
        return AjaxResult.success(instructionEventService.getEventListByGroupId(id));
    }

    /**
     * 下载导入模板
     */
    @PostMapping("/importTemplate")
    public void importTemplate(HttpServletResponse response) {
        ExcelUtil<InstructionEvent> util = new ExcelUtil<InstructionEvent>(InstructionEvent.class);
        util.importTemplateExcel(response, "事件导入模板");
    }


    /**
     * 导入事件数据
     */
    @Log(title = "导入事件数据", businessType = BusinessType.IMPORT)
    @PostMapping("/importData")
    public AjaxResult importData(MultipartFile file) throws Exception {
        ExcelUtil<InstructionEvent> util = new ExcelUtil<InstructionEvent>(InstructionEvent.class);
        List<InstructionEvent> list = util.importExcel(file.getInputStream());
        //获取操作用户
        String operName = getUsername();
        //存入数据
        String msg = instructionEventService.importData(list, operName);
        return AjaxResult.success(msg);
    }


    /**
     * 导入多张sheet数据
     */
    @Log(title = "导入事件数据", businessType = BusinessType.IMPORT)
    @PostMapping("/importMoreSheetData")
    public AjaxResult importMoreSheetData(MultipartFile file) throws Exception {
        String msg = "";
        // 获取文件名
        if (file == null) {
            return AjaxResult.error("文件不能为空");
        }
        String fileName = file.getOriginalFilename();
        if (StringUtils.isEmpty(fileName)) {
            return AjaxResult.error("文件名不能为空");
        }
        // 获取文件后缀
        String prefix = fileName.substring(fileName.lastIndexOf("."));
        if (!prefix.toLowerCase().contains("xls") && !prefix.toLowerCase().contains("xlsx")) {
            return AjaxResult.error("文件格式异常，请上传Excel文件格式");
        }
        //由于2003和2007的版本所使用的接口不一样，所以这里统一用Workbook做兼容
        boolean isExcel2003 = prefix.toLowerCase().endsWith("xls") ? true : false;
        InputStream inputStream = null;
        try {
            inputStream = file.getInputStream();
            msg = instructionEventService.uploadEventAndPerson(inputStream, isExcel2003);
        } catch (Exception e) {
            return AjaxResult.error("上传失败,原因如下：" + e.getMessage());
        } finally {
            if (inputStream != null) {
                inputStream.close();
            }
        }

        return AjaxResult.success(msg);
    }

    /**
     * 未关联群体事件 同步更新群体id
     *
     * @return
     */
    @GetMapping("/synchronizationGroupId")
    public AjaxResult synchronizationGroupId() {
        int row = instructionEventService.synchronizationGroupId();
        return toAjax(row);
    }

    /**
     * 事件分析报告
     *
     * @param typeId    事件类型id
     * @param startTime 搜索事件开始事件
     * @param endTime   搜索事件结束事件
     * @throws Exception
     */
//    @GetMapping("/analysisReport")
    public void analysisReport(@RequestParam("typeId") Long typeId, Date startTime, Date endTime,String area, HttpServletResponse response) throws Exception {
        EventAnalysisReportVo analysisReportGropVo = instructionEventService.analysisReport(typeId, startTime, endTime,area);
        InputStream ins = null;
        OutputStream out = null;
        try {
            //获取Word模板，模板存放路径
            ClassPathResource tempFileResource = new ClassPathResource("/word/事件分析报告模板.docx");
            ins = tempFileResource.getStream();
            //注册xdocreport实例并加载FreeMarker模板引擎
            IXDocReport report = XDocReportRegistry.getRegistry().loadReport(ins, TemplateEngineKind.Freemarker);
            //创建xdocreport上下文对象，用于存放具体数据
            IContext context = report.createContext();


            //创建要替换的文本变量
            context.put("createTime", analysisReportGropVo.getCreateTime());
            context.put("typeName", analysisReportGropVo.getTypeName());
            context.put("eventCount", analysisReportGropVo.getEventCount());
            context.put("groupCount", analysisReportGropVo.getGroupCount());
            context.put("respPersonCount", analysisReportGropVo.getRespPersonCount());
            context.put("regionCount", analysisReportGropVo.getRegionCount() == null ? 0 : analysisReportGropVo.getRegionCount());
            context.put("regionDetails", StringUtils.isEmpty(analysisReportGropVo.getRegionDetails()) ? "" : analysisReportGropVo.getRegionDetails());
            context.put("personNum", analysisReportGropVo.getPersonNum());
            context.put("groupDetails", analysisReportGropVo.getGroupDetails());
            context.put("eventPropertiesDetails", analysisReportGropVo.getEventPropertiesDetails());


            //此处的userInfo是word中命名的列表名
            context.put("events", analysisReportGropVo.getEventList());
            FieldsMetadata fm = report.createFieldsMetadata();
            //Word模板中的表格数据对应的集合类型
            fm.load("events", EventAnalysisReportEventVo.class, true);
            response.setCharacterEncoding("utf-8");
            response.setContentType("application/msword");
            String fileName = "事件分析报告.docx";
            response.setHeader("Content-Disposition", "attachment;filename="
                    .concat(String.valueOf(URLEncoder.encode(fileName, "UTF-8"))));
            report.process(context, response.getOutputStream());

        } catch (Exception e) {
            logger.info("生成word发生异常", e);
        } finally {
            try {
                if (ins != null) {
                    ins.close();
                }
                if (out != null) {
                    out.close();
                }
            } catch (IOException e) {
                logger.info("文件流关闭失败", e);
            }
        }

    }


}
