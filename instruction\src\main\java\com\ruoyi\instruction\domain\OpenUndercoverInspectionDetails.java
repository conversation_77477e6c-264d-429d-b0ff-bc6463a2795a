package com.ruoyi.instruction.domain;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.common.annotation.Excels;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;
import org.apache.poi.ss.usermodel.IndexedColors;

import java.util.Date;

/**
 * 暗访督察详情下层列对象 t_open_undercover_inspection_details
 *
 * <AUTHOR>
 * @date 2023-08-08
 */
@Data
public class OpenUndercoverInspectionDetails extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /**  */
    private Long id;

    /**
     * 点位
     */
    @Excels({
            @Excel(name = "任务名称", targetAttr = "missionName", headerBackgroundColor = IndexedColors.GREY_25_PERCENT),
            @Excel(name = "县（市、区）", targetAttr = "county", headerBackgroundColor = IndexedColors.GREY_25_PERCENT),
            @Excel(name = "乡镇（街道）", targetAttr = "town", headerBackgroundColor = IndexedColors.GREY_25_PERCENT),
            @Excel(name = "检查单位名称", targetAttr = "companyName", headerBackgroundColor = IndexedColors.GREY_25_PERCENT),
            @Excel(name = "详细地址", targetAttr = "companyAddress", headerBackgroundColor = IndexedColors.GREY_25_PERCENT),
            @Excel(name = "检查单位类别", targetAttr = "companyCategory", headerBackgroundColor = IndexedColors.GREY_25_PERCENT),
            @Excel(name = "数据源部门", targetAttr = "dataSource", headerBackgroundColor = IndexedColors.GREY_25_PERCENT),
            @Excel(name = "检查层级", targetAttr = "inspectionLevel", headerBackgroundColor = IndexedColors.GREY_25_PERCENT),
            @Excel(name = "检查时间", targetAttr = "checkSubmitDate", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss", headerBackgroundColor = IndexedColors.GREY_25_PERCENT),
            @Excel(name = "检查人员", targetAttr = "checkerName", headerBackgroundColor = IndexedColors.GREY_25_PERCENT)

    })
    private TOpenUndercoverInspection openUndercoverInspection;

    /** 暗访单项名称（指令内容） */
    @Excel(name = "存在问题",headerBackgroundColor = IndexedColors.GREY_25_PERCENT)
    private String checkItemName;

    /** 单项暗访结果说明（基本情况） */
    @Excel(name = "问题备注",headerBackgroundColor = IndexedColors.GREY_25_PERCENT)
    private String checkComment;

    /** 暗访单项整改期限 */
    @Excel(name = "整改期限",headerBackgroundColor = IndexedColors.GREY_25_PERCENT)
    private Integer checkItemDayLimit;


    /** 单项问题领域（事件类型） */
    @Excel(name = "问题类别",headerBackgroundColor = IndexedColors.GREY_25_PERCENT)
    private String checkItemDomain;

    /** 问题严重程度 */
    @Excel(name = "风险等级",headerBackgroundColor = IndexedColors.GREY_25_PERCENT)
    private String checkItemLevel;


    /** 单项暗访提交附件（多个用','分隔） */
    @Excel(name = "单项暗访提交附件",headerBackgroundColor = IndexedColors.GREY_25_PERCENT)
    private String checkImgs;


    /** 暗访单项id */
    private Long checkItemId;



    /** 单项暗访结果（通过；不通过） */
    @Excel(name = "单项暗访结果",headerBackgroundColor = IndexedColors.GREY_25_PERCENT)
    private String checkResult;

    /** 暗访人员姓名 */
    private String checkerName;

    /** 暗访人员手机号(脱敏后) */
    private String checkerPhone;

    /** 编码（唯一） */
    private String uniqueNo;

    /** 暗访自增id */
    private Long inspectionId;


    /** 文件ids */
    private String fileIds;

    /** 事件类型 */
    private String type;

    private Date handleTime;


    /**
     * 处置类型 未处理、已放置、已交办、已接收、已处置、已反馈、已销号
     */
    private String dealType;

    /**
     * 1:交办 2：放置
     */
    private Long disposeType;

    /**
     * 指令、事件id
     */
    private Long infoId;

    /** 反馈时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "整改时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss",headerBackgroundColor = IndexedColors.GREY_25_PERCENT)
    private Date feedbackTime;

    /** 反馈情况 */
    @Excel(name = "整改情况",headerBackgroundColor = IndexedColors.GREY_25_PERCENT)
    private String feedbackSituation;

    /** 处置环节 */
    private String dealStep;

    /**
     * 处理文件ids
     */
    private String dealFiles;


    /**
     * 预估完成时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "预估完成时间", width = 30, dateFormat = "yyyy-MM-dd",headerBackgroundColor = IndexedColors.GREY_25_PERCENT)
    private Date forecastTime;


    /**
     * 整改结果
     */
    @Excel(name = "整改结果",headerBackgroundColor = IndexedColors.GREY_25_PERCENT)
    private String dealResult;


    /**
     * 处置人
     */
    @Excel(name = "整改人",headerBackgroundColor = IndexedColors.GREY_25_PERCENT)
    private String dealPerson;

    /**
     * 指令id
     */
    private Long instructionId;


}
