<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.examineAir.mapper.ExamineDetailsMapper">

    <resultMap type="ExamineDetails" id="ExamineDetailsResult">
            <result property="id" column="id"/>
            <result property="area" column="area"/>
            <result property="totalScore" column="total_score"/>
            <result property="tExamineId" column="t_examine_id"/>
            <result property="score1" column="score1"/>
            <result property="score2" column="score2"/>
            <result property="score3" column="score3"/>
            <result property="score4" column="score4"/>
            <result property="score5" column="score5"/>
            <result property="score6" column="score6"/>
            <result property="score7" column="score7"/>
            <result property="score8" column="score8"/>
            <result property="score9" column="score9"/>
            <result property="score10" column="score10"/>
            <result property="score11" column="score11"/>
            <result property="score12" column="score12"/>
            <result property="score13" column="score13"/>
            <result property="score14" column="score14"/>
            <result property="score15" column="score15"/>
    </resultMap>

    <sql id="selectExamineDetailsVo">
        select  @rownum := @rownum+1 AS score1,id, area, total_score, t_examine_id, score2, score3, score4, score5, score6, score7, score8, score9, score10, score11, score12, score13, score14, score15
        from t_examine_details,(SELECT @rownum:=0)r
    </sql>

    <select id="selectExamineDetailsList" parameterType="ExamineDetails" resultMap="ExamineDetailsResult">
        <include refid="selectExamineDetailsVo"/>
        <where>
            <if test="area != null  and area != ''">and area = #{area}</if>
            <if test="totalScore != null ">and total_score = #{totalScore}</if>
            <if test="tExamineId != null ">and t_examine_id = #{tExamineId}</if>
            <if test="score1 != null ">and score1 = #{score1}</if>
            <if test="score2 != null ">and score2 = #{score2}</if>
            <if test="score3 != null ">and score3 = #{score3}</if>
            <if test="score4 != null ">and score4 = #{score4}</if>
            <if test="score5 != null ">and score5 = #{score5}</if>
            <if test="score6 != null ">and score6 = #{score6}</if>
            <if test="score7 != null ">and score7 = #{score7}</if>
            <if test="score8 != null ">and score8 = #{score8}</if>
            <if test="score9 != null ">and score9 = #{score9}</if>
            <if test="score10 != null ">and score10 = #{score10}</if>
            <if test="score11 != null ">and score11 = #{score11}</if>
            <if test="score12 != null ">and score12 = #{score12}</if>
            <if test="score13 != null ">and score13 = #{score13}</if>
            <if test="score14 != null ">and score14 = #{score14}</if>
            <if test="score15 != null ">and score15 = #{score15}</if>
        </where>
        order by total_score desc
    </select>

    <select id="selectExamineDetailsById" parameterType="Long"
            resultMap="ExamineDetailsResult">
            <include refid="selectExamineDetailsVo"/>
            where  id = #{id}
    </select>

    <insert id="insertExamineDetails" parameterType="ExamineDetails" useGeneratedKeys="true"
            keyProperty="id">
        insert into t_examine_details
        <trim prefix="(" suffix=")" suffixOverrides=",">
                    <if test="area != null">area,</if>
                    <if test="totalScore != null">total_score,</if>
                    <if test="tExamineId != null">t_examine_id,</if>
                    <if test="score1 != null">score1,</if>
                    <if test="score2 != null">score2,</if>
                    <if test="score3 != null">score3,</if>
                    <if test="score4 != null">score4,</if>
                    <if test="score5 != null">score5,</if>
                    <if test="score6 != null">score6,</if>
                    <if test="score7 != null">score7,</if>
                    <if test="score8 != null">score8,</if>
                    <if test="score9 != null">score9,</if>
                    <if test="score10 != null">score10,</if>
                    <if test="score11 != null">score11,</if>
                    <if test="score12 != null">score12,</if>
                    <if test="score13 != null">score13,</if>
                    <if test="score14 != null">score14,</if>
                    <if test="score15 != null">score15,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
                    <if test="area != null">#{area},</if>
                    <if test="totalScore != null">#{totalScore},</if>
                    <if test="tExamineId != null">#{tExamineId},</if>
                    <if test="score1 != null">#{score1},</if>
                    <if test="score2 != null">#{score2},</if>
                    <if test="score3 != null">#{score3},</if>
                    <if test="score4 != null">#{score4},</if>
                    <if test="score5 != null">#{score5},</if>
                    <if test="score6 != null">#{score6},</if>
                    <if test="score7 != null">#{score7},</if>
                    <if test="score8 != null">#{score8},</if>
                    <if test="score9 != null">#{score9},</if>
                    <if test="score10 != null">#{score10},</if>
                    <if test="score11 != null">#{score11},</if>
                    <if test="score12 != null">#{score12},</if>
                    <if test="score13 != null">#{score13},</if>
                    <if test="score14 != null">#{score14},</if>
                    <if test="score15 != null">#{score15},</if>
        </trim>
    </insert>

    <update id="updateExamineDetails" parameterType="ExamineDetails">
        update t_examine_details
        <trim prefix="SET" suffixOverrides=",">
                    <if test="area != null">area = #{area},</if>
                    <if test="totalScore != null">total_score = #{totalScore},</if>
                    <if test="tExamineId != null">t_examine_id = #{tExamineId},</if>
                    <if test="score1 != null">score1 = #{score1},</if>
                    <if test="score2 != null">score2 = #{score2},</if>
                    <if test="score3 != null">score3 = #{score3},</if>
                    <if test="score4 != null">score4 = #{score4},</if>
                    <if test="score5 != null">score5 = #{score5},</if>
                    <if test="score6 != null">score6 = #{score6},</if>
                    <if test="score7 != null">score7 = #{score7},</if>
                    <if test="score8 != null">score8 = #{score8},</if>
                    <if test="score9 != null">score9 = #{score9},</if>
                    <if test="score10 != null">score10 = #{score10},</if>
                    <if test="score11 != null">score11 = #{score11},</if>
                    <if test="score12 != null">score12 = #{score12},</if>
                    <if test="score13 != null">score13 = #{score13},</if>
                    <if test="score14 != null">score14 = #{score14},</if>
                    <if test="score15 != null">score15 = #{score15},</if>
        </trim>
        where id = #{id}
    </update>

    <update id="deleteExamineDetailsById" parameterType="Long">
        update t_examine_details set status = 9
        where id = #{id}
    </update>

    <update id="deleteExamineDetailsByIds" parameterType="String">
        update t_examine_details set status = 9 where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>
</mapper>