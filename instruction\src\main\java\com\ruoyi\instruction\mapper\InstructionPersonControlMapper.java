package com.ruoyi.instruction.mapper;

import java.util.List;

import com.ruoyi.instruction.domain.InstructionPersonControl;
import com.ruoyi.instruction.domain.rspVo.InstructionPersonControlExportVo;
import org.apache.ibatis.annotations.Param;

/**
 * 重点人员-管控人员信息Mapper接口
 *
 * <AUTHOR>
 * @date 2023-03-21
 */
public interface InstructionPersonControlMapper {
    /**
     * 查询重点人员-管控人员信息
     *
     * @param id 重点人员-管控人员信息主键
     * @return 重点人员-管控人员信息
     */
    public InstructionPersonControl selectInstructionPersonControlById(Long id);

    /**
     * 查询重点人员-管控人员信息列表
     *
     * @param instructionPersonControl 重点人员-管控人员信息
     * @return 重点人员-管控人员信息集合
     */
    public List<InstructionPersonControl> selectInstructionPersonControlList(InstructionPersonControl instructionPersonControl);

    /**
     * 新增重点人员-管控人员信息
     *
     * @param instructionPersonControl 重点人员-管控人员信息
     * @return 结果
     */
    public int insertInstructionPersonControl(InstructionPersonControl instructionPersonControl);

    /**
     * 修改重点人员-管控人员信息
     *
     * @param instructionPersonControl 重点人员-管控人员信息
     * @return 结果
     */
    public int updateInstructionPersonControl(InstructionPersonControl instructionPersonControl);

    /**
     * 删除重点人员-管控人员信息
     *
     * @param id 重点人员-管控人员信息主键
     * @return 结果
     */
    public int deleteInstructionPersonControlById(Long id);

    /**
     * 批量删除重点人员-管控人员信息
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteInstructionPersonControlByIds(Long[] ids);

    /**
     * 根据人员id修改责任人状态
     *
     * @param personId
     */
    void updateDutyerStatus(@Param("person_id") Long personId);

    /**
     * 查询未关联人员五包一人员信息
     *
     * @return
     */
    List<String> selectIdCard();

    /**
     * 根据五包一人员关联人员id
     *
     * @param s
     * @param id
     */
    void updateByIdCard(@Param("idCard") String s, @Param("id") Long id);

    /**
     * 查询五包一重复人员
     * @return
     */
    String findRepeatPerson();

    /**
     * 处理五包一管控人员
     * @param split
     */
    void dealRepeatPerson(String[] split);

    /**
     * 查询重复组长id
     * @return
     */
    String findLeadPerson();

    /**
     * 处理多个组长问题
     * @param leadIds
     */
    void dealLeadPerson(String[] leadIds);

    /**
     * 根据关联人员id查询管控人员信息
     * @param collect
     * @return
     */
    List<InstructionPersonControlExportVo> selectPersonConttroByIds(@Param("list") List<Long> collect);
}
