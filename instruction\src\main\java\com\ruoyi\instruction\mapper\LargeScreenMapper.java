package com.ruoyi.instruction.mapper;

import org.apache.ibatis.annotations.Param;

import java.util.HashMap;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/3/8 13:58
 */
public interface LargeScreenMapper {

    /**
     * 获取人员类别
     *
     * @param typeId
     * @return
     */
    List<HashMap> getPersonType(@Param("typeId") Integer typeId);

    /**
     * 获取事件关联人员ids
     *
     * @return
     */
    String getEventPersonIds();

    /**
     * 根据人员id查询人员分布
     *
     * @param personIds
     * @return
     */
    List<HashMap> getPersonnelDistribution(Long[] personIds);

    /**
     * 获取人员类型数量
     * @param typeId
     * @return
     */
    Integer getPersonTypeCount(@Param("typeId") Integer typeId);

    /**
     * 获取人员类别
     * @param typeId
     * @return
     */
    String getPersonTypeNew(@Param("typeId") int typeId);
}
