package com.ruoyi.instruction.controller;

import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.exception.GlobalException;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.instruction.domain.InstructionRealtimeinfo;
import com.ruoyi.instruction.service.IInstructionRealtimeinfoService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 【实时信息】Controller
 *
 * <AUTHOR>
 * @date 2023-05-12
 */
@RestController
@RequestMapping("/instruction/realtimeinfo")
public class InstructionRealtimeinfoController extends BaseController
{
    @Autowired
    private IInstructionRealtimeinfoService instructionRealtimeinfoService;

    /**
     * 查询【实时信息】列表
     */
    @PreAuthorize("@ss.hasPermi('instruction:realtimeinfo:list')")
    @GetMapping("/list")
    public TableDataInfo list(InstructionRealtimeinfo instructionRealtimeinfo)
    {
        startPage();
        List<InstructionRealtimeinfo> list = instructionRealtimeinfoService.selectInstructionRealtimeinfoList(instructionRealtimeinfo);
        return getDataTable(list);
    }

    /**
     * 导出【实时信息】列表
     */
    @PreAuthorize("@ss.hasPermi('instruction:realtimeinfo:export')")
    @Log(title = "【导出】", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, InstructionRealtimeinfo instructionRealtimeinfo)
    {
        List<InstructionRealtimeinfo> list = instructionRealtimeinfoService.selectInstructionRealtimeinfoList(instructionRealtimeinfo);
        ExcelUtil<InstructionRealtimeinfo> util = new ExcelUtil<InstructionRealtimeinfo>(InstructionRealtimeinfo.class);
        util.exportExcel(response, list, "【实时信息】数据");
    }

    /**
     * 获取【实时信息】详细信息
     */
    @PreAuthorize("@ss.hasPermi('instruction:realtimeinfo:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(instructionRealtimeinfoService.selectInstructionRealtimeinfoById(id));
    }

    /**
     * 新增【实时信息】
     */
    @PreAuthorize("@ss.hasPermi('instruction:realtimeinfo:add')")
    @Log(title = "【新增】", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody InstructionRealtimeinfo instructionRealtimeinfo)
    {
        if (StringUtils.isEmpty(instructionRealtimeinfo.getInfoTitle())||instructionRealtimeinfo.getInfoTitle().length()>5){
            throw  new GlobalException("标题不能为空或者长度大于5");
        }
        return toAjax(instructionRealtimeinfoService.insertInstructionRealtimeinfo(instructionRealtimeinfo));
    }

    /**
     * 修改【实时信息】
     */
    @PreAuthorize("@ss.hasPermi('instruction:realtimeinfo:edit')")
    @Log(title = "【更新】", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody InstructionRealtimeinfo instructionRealtimeinfo)
    {
        if (StringUtils.isEmpty(instructionRealtimeinfo.getInfoTitle())||instructionRealtimeinfo.getInfoTitle().length()>5){
            throw  new GlobalException("标题不能为空或者长度大于5");
        }
        return toAjax(instructionRealtimeinfoService.updateInstructionRealtimeinfo(instructionRealtimeinfo));
    }

    /**
     * 删除【实时信息】
     */
    @PreAuthorize("@ss.hasPermi('instruction:realtimeinfo:remove')")
    @Log(title = "【删除】", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(instructionRealtimeinfoService.deleteInstructionRealtimeinfoByIds(ids));
    }
}
