package com.ruoyi.instruction.domain;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

import java.util.Date;

/**
 * 风险排查主对象 t_risk
 *
 * <AUTHOR>
 * @date 2023-12-25
 */
@Data
public class Risk extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键id */
    private Long id;

    /** 风险排查名称 */
    @Excel(name = "风险排查名称")
    private String riskName;

    /** 排查个数 */
    @Excel(name = "排查个数")
    private Long riskNum;

    /** 社会治安个数 */
    private Long securityNum;

    /** 社会矛盾个数 */
    private Long contradictionNum;

    /** 公共安全个数 */
    private Long safeNum;

    /** 网络安全 */
    private Long networkNum;

    /** 化解率 */
    @Excel(name = "化解率")
    private String dissolutionRate;

    /** 类型 1：常规  2：问题楼盘 */
    @Excel(name = "类型",readConverterExp = "1=常规,2=问题楼盘")
    private Long riskType;

    /** 状态 1：正常  9：删除 */
    private String status;

    /** 化解数 */
    @Excel(name = "化解数")
    private Long dissolutionNum;

    /** 涉及数 */
    @Excel(name = "涉及数")
    private Long referNum;

    /** 涉众投资 */
    @Excel(name = "涉众金融投资领域")
    private Long sztzNum;

    /** 问题楼盘 */
    @Excel(name = "房地产领域")
    private Long wtlpNum;


    /** 欠薪 */
    @Excel(name = "劳动关系领域")
    private Long qxNum;


    /** 新业态 */
    @Excel(name = "新业态新就业群体")
    private Long xytNum;


    /** 涉法涉诉 */
    @Excel(name = "涉法涉诉")
    private Long sfssNum;

    /** 拆迁安置 */
    @Excel(name = "拆迁安置")
    private Long cqazNum;

    /** 政策待遇 */
    @Excel(name = "政策待遇")
    private Long zcdyNum;


    /** 教育类 */
    @Excel(name = "教育领域")
    private Long jylNum;


    /** 生态环境 */
    @Excel(name = "生态环境")
    private Long sthjNum;


    /** 医疗 */
    @Excel(name = "医疗纠纷")
    private Long ylNum;


    /** 生产安全 */
    @Excel(name = "生产安全")
    private Long scaqNum;


    /** 涉政类 */
    @Excel(name = "涉政治类")
    private Long szlNum;


    /** 网络安全 */
    @Excel(name = "网络安全")
    private Long wlaqNum;


    /** 其他 */
    private Long qtNum;

    /** 其他-其他 */
    @Excel(name = "其他-其他")
    private Long newQtNum;



    /** 实体商铺 */
    private Long stspNum;

    /**
     * 年度
     */
    private String yearStr;

    /** 检查开始时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "检查开始时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date checkStartTime;

    /** 检查结束时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "检查结束时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date checkEndTime;

}
