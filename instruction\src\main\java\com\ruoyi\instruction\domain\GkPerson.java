package com.ruoyi.instruction.domain;

import java.util.Date;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;
import org.apache.poi.ss.usermodel.IndexedColors;

/**
 * 管控人员对象 t_gk_person
 *
 * <AUTHOR>
 * @date 2023-06-01
 */
@Data
public class GkPerson extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /**
     * 布控人员id
     */
    private Long id;

    /**
     * 姓名
     */
    @Excel(name = "姓名", headerColor = IndexedColors.RED, headerBackgroundColor = IndexedColors.WHITE)
    private String xm;

    /**
     * 身份证号
     */
    @Excel(name = "身份证号", headerColor = IndexedColors.RED)
    private String sfzh;

    /**
     * 人员类别
     */
    @Excel(name = "大类", headerColor = IndexedColors.RED, readConverterExp = "01=极端倾向,02=涉警信访,03=信访人员,05=涉恐涉疆,07=涉众群体,08=网上重点,11=退役信访,13=民师信访", combo = "极端倾向,涉警信访,信访人员,涉恐涉疆,涉众群体,网上重点,退役信访,民师信访")
    private String rylb;

    /**
     * 关联群体id
     */
    @Excel(name = "小类", readConverterExp = "21=豫皖村镇银行群体,22=山东芊桦群体,23=福七群体,24=义乌晟大网络科技有限公司投资受损群体,25=埃塞俄比亚群体,26=民师群体,27=退役军人群体,28=丰网速运群体", combo = "豫皖村镇银行群体,山东芊桦群体,福七群体,义乌晟大网络科技有限公司投资受损群体,埃塞俄比亚群体,民师群体,退役军人群体,丰网速运群体")
    private String groupIds;


    /**
     * 联系电话
     */
    @Excel(name = "重点人员手机号码")
    private String lxdh;

    /**
     * 现住地详址
     */
    @Excel(name = "现住地详址")
    private String xzdxz;

    /**
     * 户籍地区划
     */
    private String hjdqh;

    /**
     * 户籍地详址
     */
    private String hjdxz;


    /**
     * 主管部门
     */
    private String zgbm;


    /**
     * 布控天数
     */
    @Excel(name = "布控天数", type = Excel.Type.EXPORT)
    private Integer bkts;

    /**
     * 布控结果接收单位
     */
    @Excel(name = "布控结果接收单位", readConverterExp = "330700000000321600000000000A=金华市委政法委,3307020000004216000000000000=婺城区政法委,3307030000004216000000010002=金东区政法委,3307230000004216000000010002=武义县委政法委,3307260000004216000000000000=浦江县委政法委,3307270000004293000000000000=磐安县委政法委,330700000000321600000000000D=义乌市委政法委,330700000000321600000000000E=永康市委政法委,330700000000321600000000000B=兰溪市委政法委,330700000000321600000000000C=东阳市委政法委,330700000000321600000000000F=开发区政法办", combo = "金华市委政法委,婺城区政法委,金东区政法委,武义县委政法委,浦江县委政法委,磐安县委政法委,义乌市委政法委,永康市委政法委,兰溪市委政法委,东阳市委政法委,开发区政法办")
    private String bkjgjsdw;

    /**
     * 申请人
     */
    private String sqr;

    /**
     * 申请单位
     */
    private String sqdw;


    /**
     * 申请人身份证号
     */
    @Excel(name = "申请人身份证号")
    private String sqrsfzh;

    /**
     * 申请人联系电话
     */
    @Excel(name = "申请人手机号码")
    private String sqrlxdh;

    /**
     * 布控理由
     */
    @Excel(name = "布控事由", headerColor = IndexedColors.RED)
    private String bkly;

    /**
     * 车牌号
     */
    @Excel(name = "车牌号(多个采用英文逗号隔开)", width = 50)
    private String carLicense;

    /**
     * 申请人单位名称
     */
    private String sqrdwmc;

    /**
     * 1:正常 9:异常
     */
    private String status;

    /**
     * 管控状态 1：正在管控 2：未管控
     */
    private String gkStatus;

    /**
     * 文件ids
     */
    private String fileIds;

    /**
     * 布控编号
     */
    private String lkzlbh;

    /**
     * 撤控人
     */
    private String ckr;

    /**
     * 撤控人身份证号
     */
    private String ckrsfzh;

    /**
     * 撤控人单位名称
     */
    private String ckdw;

    /**
     * 撤控理由
     */
    private String ckly;

    /**
     * 撤控时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date ckTime;


    /**
     * 人员id
     */
    private Long personId;

    /**
     * 申请时间
     */
    private String sqsj;

    /**
     * 布控开始时间
     */
    private String bkkssj;

    /**
     * 布控结束时间
     */
    private String bkjzsj;

    /**
     * 审核人
     */
    private String shrxm;

    /**
     * 审核人身份证号
     */
    private String shrsfzh;

    /**
     * 审核单位
     */
    private String shdw;

    /**
     * 审核状态 0：未审核 1:审核通过 2:审核不通过
     */
    private String shzt;

    /**
     * 审核意见
     */
    private String shyj;

    /**
     * 审核时间
     */
    private String shsj;

    /**
     * 有效性 1：有效 0：无效
     */
    private String yxx;

    /**
     * 布控来源 00：政法 02:信访
     */
    private String bkjz;

    /**
     * 布控范围 330000:全省 330700：全市
     */
    private String bkfw;

    /**
     * 处置要求：1：拦截劝返 2：核查稳定 3：跟踪关注
     */
    private String czyq;

    /**
     * 指令类型11：布控  13：撤控
     */
    private String lkzllx;

    /**
     * 布控接收人
     */
    private String bkjsr;

    /**
     * 布控接收单位id
     */
    private Long bkjsdw;

    /** 驳回字段 */
    private String bhxx;

    /** 取消布控 */
    private String qxbk;

    /**
     * 人员ids
     */
    private String ids;

    /** 创建部门id */
    private Long createDeptId;
}
