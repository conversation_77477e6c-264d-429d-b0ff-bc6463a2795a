package com.ruoyi.instruction.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.instruction.domain.DeployControl;
import com.ruoyi.instruction.service.IDeployControlService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 失联失控信息Controller
 * 
 * <AUTHOR>
 * @date 2024-11-11
 */
@RestController
@RequestMapping("/deploy/control")
public class DeployControlController extends BaseController
{
    @Autowired
    private IDeployControlService deployControlService;

    /**
     * 查询失联失控信息列表
     */
    @PreAuthorize("@ss.hasPermi('deploy:control:list')")
    @GetMapping("/list")
    public TableDataInfo list(DeployControl deployControl)
    {
        startPage();
        List<DeployControl> list = deployControlService.selectDeployControlList(deployControl);
        return getDataTable(list);
    }

    /**
     * 导出失联失控信息列表
     */
    @PreAuthorize("@ss.hasPermi('deploy:control:export')")
    @Log(title = "失联失控信息", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, DeployControl deployControl)
    {
        List<DeployControl> list = deployControlService.selectDeployControlList(deployControl);
        ExcelUtil<DeployControl> util = new ExcelUtil<DeployControl>(DeployControl.class);
        util.exportExcel(response, list, "失联失控信息数据");
    }

    /**
     * 获取失联失控信息详细信息
     */
    @PreAuthorize("@ss.hasPermi('deploy:control:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(deployControlService.selectDeployControlById(id));
    }

    /**
     * 新增失联失控信息
     */
    @PreAuthorize("@ss.hasPermi('deploy:control:add')")
    @Log(title = "失联失控信息", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody DeployControl deployControl)
    {
        return toAjax(deployControlService.insertDeployControl(deployControl));
    }

    /**
     * 修改失联失控信息
     */
    @PreAuthorize("@ss.hasPermi('deploy:control:edit')")
    @Log(title = "失联失控信息", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody DeployControl deployControl)
    {
        return toAjax(deployControlService.updateDeployControl(deployControl));
    }

    /**
     * 删除失联失控信息
     */
    @PreAuthorize("@ss.hasPermi('deploy:control:remove')")
    @Log(title = "失联失控信息", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(deployControlService.deleteDeployControlByIds(ids));
    }
}
