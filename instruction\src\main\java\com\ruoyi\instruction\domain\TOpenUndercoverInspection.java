package com.ruoyi.instruction.domain;

import java.util.Date;
import java.util.List;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.annotation.Excels;
import com.ruoyi.common.utils.StringUtils;
import lombok.Data;
import com.ruoyi.common.core.domain.BaseEntity;
import org.apache.poi.ss.usermodel.IndexedColors;

/**
 * 暗访督察列对象 t_open_undercover_inspection
 *
 * <AUTHOR>
 * @date 2023-07-20
 */
@Data
public class TOpenUndercoverInspection extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /**
     * $column.columnComment
     */
    private Long id;

    private Integer num;
    /**
     * 暗访任务名称
     */
    @Excel(name = "任务名称", needMerge = true, headerBackgroundColor = IndexedColors.GREY_25_PERCENT)
    private String missionName;
    /**
     * 区县
     */
    @Excel(name = "县（市、区）", needMerge = true, headerBackgroundColor = IndexedColors.GREY_25_PERCENT)
    private String county;

    /**
     * 乡镇
     */
    @Excel(name = "乡镇（街道）", needMerge = true, headerBackgroundColor = IndexedColors.GREY_25_PERCENT)
    private String town;

    /**
     * 企业店招名称
     */
    @Excel(name = "检查单位名称", needMerge = true, headerBackgroundColor = IndexedColors.GREY_25_PERCENT)
    private String companyName;

    /**
     * 企业经营地址
     */
    @Excel(name = "详细地址", needMerge = true, headerBackgroundColor = IndexedColors.GREY_25_PERCENT)
    private String companyAddress;

    /**
     * 企业所属行业
     */
    @Excel(name = "检查单位类别", needMerge = true, headerBackgroundColor = IndexedColors.GREY_25_PERCENT)
    private String companyCategory;

    /**
     * 数据源部门
     */
    @Excel(name = "数据源部门", needMerge = true, headerBackgroundColor = IndexedColors.GREY_25_PERCENT)
    private String dataSource;

    private String checkList;

    /**
     * 检查层级
     */
    @Excel(name = "检查层级", needMerge = true, headerBackgroundColor = IndexedColors.GREY_25_PERCENT)
    private String inspectionLevel;

    /**
     * 暗访提交时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "检查时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss", needMerge = true, headerBackgroundColor = IndexedColors.GREY_25_PERCENT)
    private Date checkSubmitDate;

    /**
     * 暗访人员姓名
     */
    @Excel(name = "检查人员", needMerge = true, headerBackgroundColor = IndexedColors.GREY_25_PERCENT)
    private String checkerName;

    // @Excel(name = "问题详情", headerBackgroundColor = IndexedColors.GREY_25_PERCENT)
    private List<OpenUndercoverInspectionDetails> replyDetailList;

    /**
     * 暗访单项名称（指令内容）
     */
    private String checkItemName;

    private String checkItemDayLimitDay;

    /**
     * 单项暗访结果说明（基本情况）
     */
    private String checkComment;

    /**
     * 单项暗访结果（通过；不通过）
     */
    private String checkResult;

    /**
     * 单项问题领域（事件类型）
     */
    private String checkItemDomain;


    /**
     * 问题严重程度
     */
    private String checkItemLevel;


    /**
     * 检查单位
     */
    private String checkerUnit;

    /**
     * 编码（唯一）
     */
    private String uniqueNo;

    /**
     * 企业id
     */
    private Long companyId;


    /**
     * 企业统一社会信用代码
     */
    private String companyCode;


    /**
     * 企业法人代表
     */
    private String companyCorp;

    /**
     * 企业法人联系方式
     */
    private String companyCorpPhone;

    /**
     * 企业执照名称
     */
    private String companyLicenseName;

    /**
     * 企业执照照片
     */
    private String companyLicenseImg;

    /**
     * 企业行政编码
     */
    private String companyAreaCode;

    /**
     * 暗访人员手机号(脱敏后)
     */
    private String checkerPhone;

    /**
     * 暗访任务id
     */
    private Long missionId;


    /**
     * 暗访任务开始时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date missionStartTime;

    /**
     * 暗访任务结束时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date missionEndTime;

    /**
     * 暗访记录id
     */
    private Long checkReplyId;

    /**
     * 暗访记录标题(事件标题)
     */
    private String checkReplyTitle;


    /**
     * 暗访单项id
     */
    private Long checkItemId;


    /**
     * 暗访单项整改期限
     */
    private Integer checkItemDayLimit;


    /**
     * 单项暗访提交附件（多个用','分隔）
     */
    private String checkImgs;

    /**
     * 接收单位
     */
    private String receiveUnit;

    /**
     * 办理期限
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date handleTime;

    /**
     * 反馈要求
     */
    private String feedback;

    /**
     * 紧急程度
     */
    private String emergencyDegree;

    /**
     * 事件类型id
     */
    private String type;

    private Long inspectionId;
    /**
     * 基本情况
     */
    private String baseInfo;
    /**
     * 文件id
     */
    private String fileIds;

    public String getBaseInfo() {
        return checkItemName + (StringUtils.isEmpty(checkComment) ? "" : checkComment);
    }

    public void setBaseInfo(String baseInfo) {
        this.baseInfo = baseInfo;
    }

    /**
     * 处置类型 未处理、已放置、已交办、已接收、已处置、已反馈、已销号
     */
    private String dealType;

    /**
     * 1:交办 2：放置
     */
    private Long disposeType;

    /**
     * 指令、事件id
     */
    private Long infoId;

    /**
     * inspectionIds集合
     */
    private List<String> inspectionIds;

    private  String missionAreaCode;
    private  String missionAreaCodeName;

    /**
     * 页码类型 1：市级  2：县市区
     */
    private String viewType;

    /**
     * 整改结果
     */
    private String dealResult;


}
