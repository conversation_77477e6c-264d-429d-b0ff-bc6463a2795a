package com.ruoyi.instruction.domain;

/**
 * <AUTHOR>
 * @date 2023-05-10
 * 重大事件左下角的点击地区获取重大事件清单的板块
 */
public class InstructionEventInfos {
    /**
     * id
     */
    private int id;
    /**
     * 事件名称
     */
    private String eventName;
    /**
     * 事件类型
     */
    private  String eventType;
    /**
     *信息类型
     */
    private String infoType;
    /**
     *上访类型
     */
    private String petitionType;
    /**
     *涉事人员
     */
    private String involvedPeople;
    /**
     * 时间
     */
    private String eventTime;


    public String getEventType() {
        return eventType;
    }

    public void setEventType(String eventType) {
        this.eventType = eventType;
    }

    public int getId() {
        return id;
    }

    public void setId(int id) {
        this.id = id;
    }

    public String getEventName() {
        return eventName;
    }

    public void setEventName(String eventName) {
        this.eventName = eventName;
    }

    public String getInfoType() {
        return infoType;
    }

    public void setInfoType(String infoType) {
        this.infoType = infoType;
    }

    public String getPetitionType() {
        return petitionType;
    }

    public void setPetitionType(String petitionType) {
        this.petitionType = petitionType;
    }

    public String getInvolvedPeople() {
        return involvedPeople;
    }

    public void setInvolvedPeople(String involvedPeople) {
        this.involvedPeople = involvedPeople;
    }

    public String getEventTime() {
        return eventTime;
    }

    public void setEventTime(String eventTime) {
        this.eventTime = eventTime;
    }
}
