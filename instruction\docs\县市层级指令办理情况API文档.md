# 县市层级指令办理情况API文档

## 概述

县市层级指令办理情况模块提供了对金华市下属各县市区指令办理情况的统计和查询功能，主要包括应接收未接收和应处置未处置两个核心业务场景。

## 接口列表

### 1. 获取县市层级指令办理情况统计数据

**接口地址：** `GET /instruction/cityHandling/county/data`

**功能描述：** 获取县市层级应接收未接收和应处置未处置的统计数据，县级部门和乡镇街道分开统计。

**请求参数：**
- `countyName`（可选）：县市区名称，如"东阳市"、"义乌市"等，不传则查询所有县市区
- `startTime`（可选）：开始时间，格式：yyyy-MM-dd，按交办时间筛选
- `endTime`（可选）：结束时间，格式：yyyy-MM-dd，按交办时间筛选

**响应示例：**
```json
{
  "code": 200,
  "msg": "操作成功",
  "data": {
    "noReceiveCountyDeptCount": 5,
    "noReceiveTownDeptCount": 12,
    "noReceiveCountyInstructionCount": 8,
    "noReceiveTownInstructionCount": 15,
    "noDisposeCountyDeptCount": 3,
    "noDisposeTownDeptCount": 7,
    "noDisposeCountyInstructionCount": 6,
    "noDisposeTownInstructionCount": 10
  }
}
```

**字段说明：**
- `noReceiveCountyDeptCount`：应接收未接收的县级部门数量
- `noReceiveTownDeptCount`：应接收未接收的乡镇街道数量
- `noReceiveCountyInstructionCount`：应接收未接收的县级部门指令数量
- `noReceiveTownInstructionCount`：应接收未接收的乡镇街道指令数量
- `noDisposeCountyDeptCount`：应处置未处置的县级部门数量
- `noDisposeTownDeptCount`：应处置未处置的乡镇街道数量
- `noDisposeCountyInstructionCount`：应处置未处置的县级部门指令数量
- `noDisposeTownInstructionCount`：应处置未处置的乡镇街道指令数量

### 2. 获取县市层级指令办理情况详细列表

**接口地址：** `GET /instruction/cityHandling/county/list`

**功能描述：** 获取县市层级指令办理情况的详细列表数据。

**请求参数：**
- `listType`（必填）：列表类型
  - `noReceive`：应接收未接收
  - `noDispose`：应处置未处置
- `deptType`（必填）：部门类型
  - `county`：县级部门
  - `town`：乡镇街道
- `countyName`（可选）：县市区名称
- `startTime`（可选）：开始时间，格式：yyyy-MM-dd
- `endTime`（可选）：结束时间，格式：yyyy-MM-dd

**响应示例：**
```json
{
  "code": 200,
  "msg": "操作成功",
  "data": [
    {
      "id": 12345,
      "instructionTitle": "关于加强安全生产工作的指令",
      "emergencyDegree": "紧急",
      "instructionType": "双排双办",
      "assignTime": "2025-01-15T10:30:00",
      "handleTime": "2025-01-20T18:00:00",
      "deptName": "东阳市",
      "deptType": "县级部门",
      "unitType": "主办单位",
      "receiveTime": null,
      "lastFeedbackTime": "2025-01-18T14:30:00",
      "overdueDays": 2,
      "countyName": "东阳市"
    }
  ]
}
```

### 3. 导出县市层级指令办理情况

**接口地址：** `POST /instruction/cityHandling/county/export`

**功能描述：** 导出县市层级指令办理情况数据到Excel文件，包含4个工作表。

**请求参数：**
- `countyName`（可选）：县市区名称
- `startTime`（可选）：开始时间，格式：yyyy-MM-dd
- `endTime`（可选）：结束时间，格式：yyyy-MM-dd

**响应：** Excel文件下载

**工作表说明：**
1. **应接收未接收-县级部门**：县级部门应接收未接收的指令详情
2. **应接收未接收-乡镇街道**：乡镇街道应接收未接收的指令详情
3. **应处置未处置-县级部门**：县级部门应处置未处置的指令详情
4. **应处置未处置-乡镇街道**：乡镇街道应处置未处置的指令详情

## 业务逻辑说明

### 应接收未接收判断条件
1. 指令状态为有效（status = 1）
2. 创建部门为金华市（create_dept_id = 202）
3. 指令类型为双排双办或预警研判（instruction_type IN (3, 6)）
4. 接收记录状态为有效但接收时间为空（receive_time IS NULL）
5. 根据紧急程度判断是否超时：
   - 特急：超过2小时
   - 紧急：超过12小时
   - 一般：超过24小时

### 应处置未处置判断条件
1. 指令状态为有效（status = 1）
2. 创建部门为金华市（create_dept_id = 202）
3. 指令类型为双排双办或预警研判（instruction_type IN (3, 6)）
4. 存在反馈记录但反馈未结束（feedback_is_end = 2）
5. 取最新的反馈记录进行判断

### 部门类型区分
- **县级部门**：磐安县、兰溪市、东阳市、义乌市、浦江县、永康市、金东区、婺城区、开发区、武义县
- **乡镇街道**：除县级部门外的其他部门

## 注意事项

1. 所有时间筛选均按照指令的交办时间（assign_time）进行
2. 县市区筛选支持模糊匹配，如传入"东阳市"会匹配所有以"东阳市"开头的部门
3. 统计数据使用DISTINCT去重，确保同一指令不会被重复计算
4. 导出功能会生成包含多个工作表的Excel文件，便于分类查看数据
