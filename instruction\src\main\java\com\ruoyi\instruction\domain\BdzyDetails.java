package com.ruoyi.instruction.domain;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 八大战役详情对象 t_bdzy_details
 * 
 * <AUTHOR>
 * @date 2023-08-31
 */
public class BdzyDetails extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** $column.columnComment */
    private Long id;

    /** 八大战役详情id */
    @Excel(name = "八大战役详情id")
    private Long bdzyId;

    /** 标题 */
    @Excel(name = "标题")
    private String title;

    /** 描述 */
    @Excel(name = "描述")
    private String describe;

    /** 0未完成，1已完成 */
    @Excel(name = "0未完成，1已完成")
    private Long status;

    /** 期限 */
    @JsonFormat(pattern = "MM月dd日")
    @Excel(name = "期限", width = 30, dateFormat = "yyyy-MM-dd")
    private Date deadline;

    /** $column.columnComment */
    @Excel(name = "${comment}", readConverterExp = "$column.readConverterExp()")
    private Date cTime;

    /** $column.columnComment */
    @Excel(name = "${comment}", readConverterExp = "$column.readConverterExp()")
    private Date uTime;

    public void setId(Long id) 
    {
        this.id = id;
    }

    public Long getId() 
    {
        return id;
    }
    public void setBdzyId(Long bdzyId) 
    {
        this.bdzyId = bdzyId;
    }

    public Long getBdzyId() 
    {
        return bdzyId;
    }
    public void setTitle(String title) 
    {
        this.title = title;
    }

    public String getTitle() 
    {
        return title;
    }
    public void setDescribe(String describe) 
    {
        this.describe = describe;
    }

    public String getDescribe() 
    {
        return describe;
    }
    public void setStatus(Long status) 
    {
        this.status = status;
    }

    public Long getStatus() 
    {
        return status;
    }
    public void setDeadline(Date deadline) 
    {
        this.deadline = deadline;
    }

    public Date getDeadline() 
    {
        return deadline;
    }
    public void setcTime(Date cTime) 
    {
        this.cTime = cTime;
    }

    public Date getcTime() 
    {
        return cTime;
    }
    public void setuTime(Date uTime) 
    {
        this.uTime = uTime;
    }

    public Date getuTime() 
    {
        return uTime;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("bdzyId", getBdzyId())
            .append("title", getTitle())
            .append("describe", getDescribe())
            .append("status", getStatus())
            .append("deadline", getDeadline())
            .append("cTime", getcTime())
            .append("uTime", getuTime())
            .toString();
    }
}
