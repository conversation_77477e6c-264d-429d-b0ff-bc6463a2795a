package com.ruoyi.instruction.controller;

import java.util.*;
import java.util.stream.Collectors;
import javax.servlet.http.HttpServletResponse;

import cn.hutool.core.util.StrUtil;
import com.alibaba.excel.EasyExcel;
import com.ruoyi.common.constant.Constants;
import com.ruoyi.common.constant.InstructionRolesConstants;
import com.ruoyi.common.core.domain.entity.SysDept;
import com.ruoyi.common.core.domain.entity.SysRole;
import com.ruoyi.common.core.domain.entity.SysUser;
import com.ruoyi.common.exception.GlobalException;
import com.ruoyi.common.utils.*;
import com.ruoyi.instruction.domain.*;
import com.ruoyi.instruction.domain.rspVo.*;
import com.ruoyi.instruction.mapper.GkPersonMapper;
import com.ruoyi.instruction.mapper.InstrucationPersonMapper;
import com.ruoyi.instruction.mapper.InstructionPersonControlMapper;
import com.ruoyi.instruction.mapper.PersonWarnMapper;
import com.ruoyi.instruction.service.*;
import com.ruoyi.system.mapper.SysDeptMapper;
import com.ruoyi.system.service.impl.SysDeptServiceImpl;
import org.springframework.beans.BeanUtils;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;
import org.springframework.web.multipart.MultipartFile;

/**
 * 指令关联人员信息Controller
 *
 * <AUTHOR>
 * @date 2022-12-15
 */
@RestController
@RequestMapping("/instruction/person")
public class InstrucationPersonController extends BaseController {

    @Autowired
    private IInstructionGroupService instructionGroupService;

    @Autowired
    private IInstrucationPersonService instrucationPersonService;

    @Autowired
    private InstrucationPersonMapper personMapper;

    @Autowired
    private InstructionPersonControlMapper personControlMapper;

    @Autowired
    private GkPersonMapper gkPersonMapper;

    @Autowired
    private SysDeptMapper sysDeptMapper;

    @Autowired
    private IInstructionEventService instructionEventService;

    @Autowired
    private PersonWarnMapper personWarnMapper;

    @Autowired
    private ISensitiveRecordService sensitiveRecordService;

    /**
     * 获取各等级分组数
     *
     * @return
     */
    @GetMapping("/getLevelCount")
    public AjaxResult getLevelCount(InstrucationPerson instrucationPerson) {
        //判断当前账号属于哪个等级 市级、县市区、乡镇街道
        //进行是市级、县市区、乡镇街道账号判断
        SysDept dept = SecurityUtils.getLoginUser().getUser().getDept();
        Long parentId = dept.getParentId();
        if (!parentId.equals(Constants.JINHUA_CITY_DEPT_ID) && !dept.getDeptId().equals(Constants.JINHUA_CITY_DEPT_ID)) {
            //市级账号
            SysDept deptParent = sysDeptMapper.selectDeptById(parentId);
            if (parentId == 213L || parentId == 214L || parentId == 215L || parentId == 216L || parentId == 217L || parentId == 218L || parentId == 219L || parentId == 220L || parentId == 221L || parentId == 262L) {
                //县市区
                String deptName = deptParent.getDeptName();
                String replace = deptName.replace("委政法委", "").replace("政法委", "").replace("政法办", "");
                instrucationPerson.setDutyPlace(replace);
            } else {
                //乡镇街道
                instrucationPerson.setDutyPlace(deptParent.getDeptName());
                instrucationPerson.setpTown(dept.getDeptName());
            }
        }
        if (instrucationPerson.getIdCard() != null && !instrucationPerson.getIdCard().equals("")) {
            String s = instrucationPerson.getIdCard().toLowerCase();
            String encryptIDCard = IDCardUtils.encryptIDCard(s);
            instrucationPerson.setIdCard(encryptIDCard);
        }
        List<Map<String, Object>> mapList = personMapper.getLevelCount(instrucationPerson);
        return AjaxResult.success(mapList);
    }

    /**
     * 获取县市区统计
     *
     * @return
     */
    @GetMapping("/getCountyCount")
    public AjaxResult getCountyCount(InstrucationPerson instrucationPerson) {
        if (instrucationPerson.getIdCard() != null && !instrucationPerson.getIdCard().equals("")) {
            String s = instrucationPerson.getIdCard().toLowerCase();
            String encryptIDCard = IDCardUtils.encryptIDCard(s);
            instrucationPerson.setIdCard(encryptIDCard);
        }
        List<Map<String, Object>> mapList = personMapper.getCountyCount(instrucationPerson);
        return AjaxResult.success(mapList);
    }

    /**
     * 获取县市区管控等级统计
     * @param instrucationPerson
     * @return
     */
    @GetMapping("/getCountyLevelStatistics")
    public AjaxResult getCountyCountByDept(InstrucationPerson instrucationPerson) {
        List<Map<String, Object>> mapList = personMapper.getCountyLevelStatistics(instrucationPerson);
        return AjaxResult.success(mapList);
    }

    /**
     * 查询指令关联人员信息列表
     */
    @PreAuthorize("@ss.hasPermi('instruction:person:list')")
    @GetMapping("/list")
    public TableDataInfo list(InstrucationPerson instrucationPerson) {
        Map<String, Object> params = instrucationPerson.getParams();
        //判断用户所属那个部门
        List<SysRole> roles = SecurityUtils.getLoginUser().getUser().getRoles();
        List<String> roleList = roles.stream().map(SysRole::getRoleKey).collect(Collectors.toList());
        if (!roleList.contains(InstructionRolesConstants.CITY_INSTRUCTION)) {
            //查询用户父级部门所在县市区 县市区仅能查看自己所属区域的人员
            SysDept dept = SecurityUtils.getLoginUser().getUser().getDept();
            String dutyPlace = DeptUtils.getDutyPlaceByDept(dept);
            instrucationPerson.setDutyPlace(dutyPlace);
            if (dept.getParentId() == 213L || dept.getParentId() == 214L || dept.getParentId() == 215L || dept.getParentId() == 216L || dept.getParentId() == 217L || dept.getParentId() == 218L || dept.getParentId() == 219L || dept.getParentId() == 220L || dept.getParentId() == 221L || dept.getParentId() == 262L) {

            }else {
                instrucationPerson.setpTown(dept.getDeptName());
            }
        }
        List<InstrucationPerson> list = new ArrayList<>();
        //查询1:五包一 、2:管控
        if (instrucationPerson.getControlStrategy() != null && instrucationPerson.getControlStrategy().equals("2")) {
            //查询已管控人员ids
            List<Long> ids = gkPersonMapper.findPersonIds();
            params.put("gkPersonIds", ids);
            instrucationPerson.setParams(params);
            instrucationPerson.setControlStrategy(null);
        }
        //根据关联群体查询
        if (instrucationPerson.getGroupId() != null) {
            //查询群体关联人员
            String personIds = instructionGroupService.selectPersonIdsByGroupId(instrucationPerson.getGroupId());
            if (personIds != null && personIds.length() > 0) {
                String[] split = personIds.split(",");
                if (split.length > 0) {
                    List<Long> ids = new ArrayList<>();
                    for (String s : split) {
                        if (!s.equals("")) {
                            ids.add(Long.parseLong(s.trim()));
                        }
                    }
                    params.put("ids", ids);
                    instrucationPerson.setParams(params);
                    instrucationPerson.setGroupId(null);
                }
            } else {
                return getDataTable(list);
            }
        }
        if (instrucationPerson.getIdCard() != null && !instrucationPerson.getIdCard().equals("")) {
            String s = instrucationPerson.getIdCard().toLowerCase();
            String encryptIDCard = IDCardUtils.encryptIDCard(s);
            instrucationPerson.setIdCard(encryptIDCard);
        }
        startPage();
        list = instrucationPersonService.selectInstrucationPersonList(instrucationPerson);
        for (InstrucationPerson person : list) {
            if (person.getPersonPhone()!=null){
                String personPhone = person.getPersonPhone();
                person.setPersonPhone(IDCardUtils.maskSensitiveInfo(personPhone));
            }

        }
        return getDataTable(list);
    }


    /**
     * 查询指令关联关注关注人员信息列表
     */
    @PreAuthorize("@ss.hasPermi('instruction:person:list')")
    @GetMapping("/followList")
    public TableDataInfo followList(InstrucationPerson instrucationPerson) {
        List<InstrucationPerson> list = new ArrayList<>();
        //根据关联群体查询
        if (instrucationPerson.getGroupName() != null && instrucationPerson.getGroupName() != "") {
            //查询群体关联人员
            String personIds = instructionGroupService.selectPersonIdsByGroupName(instrucationPerson.getGroupName());
            if (personIds != null && personIds.length() > 0) {
                String[] split = personIds.split(",");
                if (split.length > 0) {
                    List<Long> collect = Arrays.stream(split).map(s -> Long.parseLong(s.trim())).collect(Collectors.toList());
                    Map<String, Object> params = instrucationPerson.getParams();
                    params.put("ids", collect);
                    instrucationPerson.setParams(params);
                }
            } else {
                return getDataTable(list);
            }
        }
        instrucationPerson.setPeopleType(2);
        startPage();
        list = instrucationPersonService.selectPersonByName(instrucationPerson);
        for (InstrucationPerson person : list) {
            String personPhone = person.getPersonPhone();
            person.setPersonPhone(IDCardUtils.maskSensitiveInfo(personPhone));
        }
        return getDataTable(list);
    }

    /**
     * 导出指令关联人员信息列表
     */
    // @PreAuthorize("@ss.hasPermi('instruction:person:export')")
    @Log(title = "指令关联人员信息", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, InstrucationPerson instrucationPerson) {
        List<InstrucationPerson> list = new ArrayList<>();
        List<Long> ids = new ArrayList<>();
        Map<String, Object> params = instrucationPerson.getParams();
        if (params != null && params.size() != 0 && !params.get("ids").equals("")) {
            String ids1 = (String) params.get("ids");
            String[] split = ids1.split(",");
            for (String s : split) {
                if (!s.equals("")) {
                    ids.add(Long.parseLong(s.trim()));
                }
            }
        }
        //查询1:五包一 、2:管控
        if (instrucationPerson.getControlStrategy() != null && instrucationPerson.getControlStrategy().equals("2")) {
            //查询已管控人员ids
            List<Long> gkPersonIds = gkPersonMapper.findPersonIds();
            params.put("gkPersonIds", gkPersonIds);
            instrucationPerson.setParams(params);
            instrucationPerson.setControlStrategy(null);
        }
        //根据关联群体查询
        if (instrucationPerson.getGroupId() != null) {
            //查询群体关联人员
            String personIds = instructionGroupService.selectPersonIdsByGroupId(instrucationPerson.getGroupId());
            if (personIds != null && personIds.length() > 0) {
                String[] split = personIds.split(",");
                if (split.length > 0) {
                    for (String s : split) {
                        if (!s.equals("")) {
                            ids.add(Long.parseLong(s.trim()));
                        }
                    }
                    instrucationPerson.setGroupId(null);
                }
            }
        }
        if (ids.size() > 0) {
            params.put("ids", ids);
            instrucationPerson.setParams(params);
        }

        //判断用户所属那个部门
        List<SysRole> roles = SecurityUtils.getLoginUser().getUser().getRoles();
        List<String> roleList = roles.stream().map(SysRole::getRoleKey).collect(Collectors.toList());
        if (!roleList.contains(InstructionRolesConstants.CITY_INSTRUCTION)) {
            //查询用户父级部门所在县市区 县市区仅能查看自己所属区域的人员
            SysDept dept = SecurityUtils.getLoginUser().getUser().getDept();
            String dutyPlace = DeptUtils.getDutyPlaceByDept(dept);
            instrucationPerson.setDutyPlace(dutyPlace);
            if (dept.getParentId() == 213L || dept.getParentId() == 214L || dept.getParentId() == 215L || dept.getParentId() == 216L || dept.getParentId() == 217L || dept.getParentId() == 218L || dept.getParentId() == 219L || dept.getParentId() == 220L || dept.getParentId() == 221L || dept.getParentId() == 262L) {

            }else {
                instrucationPerson.setpTown(dept.getDeptName());
            }
        }
        list = instrucationPersonService.selectInstrucationPersonListForExport(instrucationPerson);
        //查询五包一人员
        List<Long> collect = list.stream().map(InstrucationPerson::getId).collect(Collectors.toList());
        List<InstructionPersonControlExportVo> personControlExportVos = new ArrayList<>();
        if (collect.size() != 0) {
            personControlExportVos = personControlMapper.selectPersonConttroByIds(collect);
        }
        List<InstructionPersonExportVo> personRspVos = new ArrayList<>();
        int index = 1;
        for (InstrucationPerson person : list) {
            String personPhone = person.getPersonPhone();
            if (personPhone!=null&&!personPhone.equals("")){
                person.setPersonPhone(IDCardUtils.maskSensitiveInfo(personPhone));
            }

            List<InstructionPersonControlExportVo> personControlExportVoList = personControlExportVos.stream().filter(instructionPersonControlExportVo -> instructionPersonControlExportVo.getPersonId().equals(person.getId())).collect(Collectors.toList());
            InstructionPersonExportVo instructionPersonRspVo = new InstructionPersonExportVo();
            instructionPersonRspVo.setPersonControlList(personControlExportVoList);
            BeanUtils.copyProperties(person, instructionPersonRspVo);
            instructionPersonRspVo.setIndex(index++);
            personRspVos.add(instructionPersonRspVo);
        }
        ExcelUtil<InstructionPersonExportVo> util = new ExcelUtil<InstructionPersonExportVo>(InstructionPersonExportVo.class);
        util.exportExcel(response, personRspVos, "重点人员信息数据");
    }

    @Log(title = "指令关联人员信息无关联人员", businessType = BusinessType.EXPORT)
    @PostMapping("/exportNoGk")
    public void exportNoGk(HttpServletResponse response, InstrucationPerson instrucationPerson) {
        List<InstrucationPerson> list = new ArrayList<>();
        List<Long> ids = new ArrayList<>();
        Map<String, Object> params = instrucationPerson.getParams();
        if (params != null && params.size() != 0 && !params.get("ids").equals("")) {
            String ids1 = (String) params.get("ids");
            String[] split = ids1.split(",");
            for (String s : split) {
                if (!s.equals("")) {
                    ids.add(Long.parseLong(s.trim()));
                }
            }
        }
        //查询1:五包一 、2:管控
        if (instrucationPerson.getControlStrategy() != null && instrucationPerson.getControlStrategy().equals("2")) {
            //查询已管控人员ids
            List<Long> gkPersonIds = gkPersonMapper.findPersonIds();
            params.put("gkPersonIds", gkPersonIds);
            instrucationPerson.setParams(params);
            instrucationPerson.setControlStrategy(null);
        }
        //根据关联群体查询
        if (instrucationPerson.getGroupId() != null) {
            //查询群体关联人员
            String personIds = instructionGroupService.selectPersonIdsByGroupId(instrucationPerson.getGroupId());
            if (personIds != null && personIds.length() > 0) {
                String[] split = personIds.split(",");
                if (split.length > 0) {
                    for (String s : split) {
                        if (!s.equals("")) {
                            ids.add(Long.parseLong(s.trim()));
                        }
                    }
                    instrucationPerson.setGroupId(null);
                }
            }
        }
        if (ids.size() > 0) {
            params.put("ids", ids);
            instrucationPerson.setParams(params);
        }

        //判断用户所属那个部门
        List<SysRole> roles = SecurityUtils.getLoginUser().getUser().getRoles();
        List<String> roleList = roles.stream().map(SysRole::getRoleKey).collect(Collectors.toList());
        if (!roleList.contains(InstructionRolesConstants.CITY_INSTRUCTION)) {
            //查询用户父级部门所在县市区 县市区仅能查看自己所属区域的人员
            SysDept dept = SecurityUtils.getLoginUser().getUser().getDept();
            String dutyPlace = DeptUtils.getDutyPlaceByDept(dept);
            instrucationPerson.setDutyPlace(dutyPlace);
            if (dept.getParentId() == 213L || dept.getParentId() == 214L || dept.getParentId() == 215L || dept.getParentId() == 216L || dept.getParentId() == 217L || dept.getParentId() == 218L || dept.getParentId() == 219L || dept.getParentId() == 220L || dept.getParentId() == 221L || dept.getParentId() == 262L) {

            }else {
                instrucationPerson.setpTown(dept.getDeptName());
            }
        }
        list = instrucationPersonService.selectInstrucationPersonListForExport(instrucationPerson);
        int index = 1;
        List<InstructionPersonNoGkExportVo> personRspVos = new ArrayList<>();
        for (InstrucationPerson person : list) {
            InstructionPersonNoGkExportVo instructionPersonRspVo = new InstructionPersonNoGkExportVo();
            BeanUtils.copyProperties(person, instructionPersonRspVo);
            instructionPersonRspVo.setIndex(index++);
            personRspVos.add(instructionPersonRspVo);
        }
        ExcelUtil<InstructionPersonNoGkExportVo> util = new ExcelUtil<InstructionPersonNoGkExportVo>(InstructionPersonNoGkExportVo.class);
        util.exportExcel(response, personRspVos, "重点人员信息数据");
    }

    /**
     * 获取指令关联人员信息详细信息
     */
    @PreAuthorize("@ss.hasPermi('instruction:person:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id,@RequestParam(name = "type",required = false) Integer type,@RequestParam(name = "menuName",required = false) String menuName) {
        InstrucationPerson instrucationPerson = instrucationPersonService.selectInstrucationPersonById(id);
        if (type != null) {
            //查看信息脱敏
            SensitiveRecord sensitiveRecord = new SensitiveRecord();
            //当期用户信息
            SysUser user = SecurityUtils.getLoginUser().getUser();
            SysDept dept = user.getDept();
            SysDept parentDept = sysDeptMapper.selectDeptById(dept.getParentId());
            String deptName = parentDept.getDeptName().replace("委政法委", "").replace("政法委", "").replace("政法办", "");
            sensitiveRecord.setCreateDeptName(deptName + user.getDept().getDeptName());

            sensitiveRecord.setCreateBy(user.getNickName());
            sensitiveRecord.setCreateTime(new Date());
            sensitiveRecord.setCreateUserId(user.getUserId());
            sensitiveRecord.setCreateDeptId(user.getDeptId());
            sensitiveRecord.setType(1);
            sensitiveRecord.setPersonName(instrucationPerson.getPersonName());
            sensitiveRecord.setDutyPlace(instrucationPerson.getDutyPlace());
            sensitiveRecord.setDutyTown(instrucationPerson.getpTown());
            sensitiveRecord.setPersonId(instrucationPerson.getId());
            sensitiveRecord.setMenuName(menuName);
            sensitiveRecordService.insertSensitiveRecord(sensitiveRecord);
        }else {
            String idCard = instrucationPerson.getIdCard();
            if (idCard!=null&&!idCard.equals("")){
                instrucationPerson.setIdCard(IDCardUtils.maskSensitiveInfo(idCard));
            }
            String personPhone = instrucationPerson.getPersonPhone();
            if (personPhone != null&&!personPhone.equals("")){
                instrucationPerson.setPersonPhone(IDCardUtils.maskSensitiveInfo(personPhone));
            }
        }
        return AjaxResult.success(instrucationPerson);
    }


    /**
     * 新增指令关联人员信息
     */
    @PreAuthorize("@ss.hasPermi('instruction:person:add')")
    // @Log(title = "指令关联人员信息", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody InstrucationPerson instrucationPerson) {

        return instrucationPersonService.insertInstrucationPerson(instrucationPerson);
    }

    /**
     * 修改指令关联人员信息
     */
    @PreAuthorize("@ss.hasPermi('instruction:person:edit')")
    @Log(title = "指令关联人员信息", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody InstrucationPerson instrucationPerson) {
        return instrucationPersonService.updateInstrucationPerson(instrucationPerson);
    }

    /**
     * 删除指令关联人员信息
     */
    @PreAuthorize("@ss.hasPermi('instruction:person:remove')")
    @Log(title = "指令关联人员信息", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids) {
        return toAjax(instrucationPersonService.deleteInstrucationPersonByIds(ids));
    }


    /**
     * 下载人员导入模板
     *
     * @param response
     */
    @PostMapping("/importTemplate")
    public void importTemplate(HttpServletResponse response) {
        ExcelUtil<InstrucationPerson> util = new ExcelUtil<InstrucationPerson>(InstrucationPerson.class);
        util.importTemplateExcel(response, "人员导入模板");
    }

    /**
     * 下载重点人员导入模板
     *
     * @param response
     */
    @PostMapping("/importPersonTemplate")
    public void importPersonTemplate(HttpServletResponse response,int personType) {
        if (personType == 1){
            ExcelUtil<InstructionPersonRspVo> util = new ExcelUtil<>(InstructionPersonRspVo.class);
            util.importTemplateExcel(response, "关注人员导入模板");
        }else {
            ExcelUtil<InstructionPersonFollowRspVo> util = new ExcelUtil<>(InstructionPersonFollowRspVo.class);
            util.importTemplateExcel(response, "异动人员导入模板");
        }

    }

    /**
     * 导入重点人员数据
     * personType 1:关注人员 2:异动人员
     */
    @Log(title = "重点人员", businessType = BusinessType.IMPORT)
    @PostMapping("/importPersonData")
    public AjaxResult importPersonData(MultipartFile file, int personType) throws Exception {
        ExcelUtil<InstructionPersonImportRspVo> util = new ExcelUtil<InstructionPersonImportRspVo>(InstructionPersonImportRspVo.class);
        List<InstructionPersonImportRspVo> personList = util.importExcel(file.getInputStream());
        //获取操作用户
        String operName = getUsername();
        List<InstrucationPerson> finallyList = new ArrayList<>();
        for (InstructionPersonImportRspVo rspVo : personList) {
            InstrucationPerson person = new InstrucationPerson();
            BeanUtils.copyProperties(rspVo, person);
            if (rspVo.getPersonLevel() != null) {
                person.setpLevel(rspVo.getPersonLevel());
            }
            if (rspVo.getPersonType() != null) {
                person.setpType(rspVo.getPersonType());
            }
            if (rspVo.getPersonTown() != null) {
                person.setpTown(rspVo.getPersonTown());
            }
            finallyList.add(person);
        }
        //存入数据
        String msg = instrucationPersonService.importPerson(finallyList, operName, personType);
        return AjaxResult.success(msg);
    }


    @GetMapping("/findPersonByIdCard")
    public AjaxResult findPersonByIdCard(String idCard) {
        //通过证件号验证该用户是否已存在人员信息表中
        String s = idCard.toLowerCase();
        String encryptIDCard = IDCardUtils.encryptIDCard(s);
        InstrucationPerson person = personMapper.selectInstrucationPersonByIdCard("", encryptIDCard);
        if (person != null && person.getpLevel() != null) {
            return AjaxResult.error(person.getPersonName() + "已存在关注列表");
        } else if (person != null && person.getpLevel() == null) {
            return AjaxResult.error(person.getPersonName() + "已存在异动列表");
        }
        return AjaxResult.success();
    }


    @Log(title = "重点人员", businessType = BusinessType.IMPORT)
    @PostMapping("/importPersonControllerData")
    public AjaxResult importPersonControllerData(MultipartFile file, int personType) throws Exception {
        ExcelUtil<InstructionPersonControl> util = new ExcelUtil<InstructionPersonControl>(InstructionPersonControl.class);
        List<InstructionPersonControl> personList = util.importExcel(file.getInputStream());
        System.out.println(personList);
        return AjaxResult.success();
    }


    /**
     * 并返回导入数据集合 通过用户id查询
     */
    @Log(title = "人员信息导入", businessType = BusinessType.IMPORT)
    // @PreAuthorize("@ss.hasPermi('system:user:import')")
    @PostMapping("/importData")
    public AjaxResult importData(MultipartFile file) throws Exception {
        ExcelUtil<InstrucationPerson> util = new ExcelUtil<InstrucationPerson>(InstrucationPerson.class);
        List<InstrucationPerson> personList = util.importExcel(file.getInputStream());
        return AjaxResult.success(personList);
    }

    /**
     * 通过人员id查询人员集合
     */
    @GetMapping("/selectPersonListByIds")
    public TableDataInfo selectPersonListByIds(String ids, String personName, String leadIds, Integer type, Integer pageNum, Integer pageSize,@RequestParam(name = "infoType",required = false) Integer infoType,@RequestParam(name = "menuName",required = false) String menuName) {
        //查询关联人员信息
        return instrucationPersonService.selectInstrucationPersonByIds(ids, personName, leadIds, type, pageNum, pageSize,infoType,menuName);

    }

    /**
     * 根据人员姓名搜索人员库列表信息
     *
     * @param instrucationPerson
     * @return
     */
    @GetMapping("/selectPersonByName")
    public AjaxResult selectPersonByName(InstrucationPerson instrucationPerson) {
        List<InstrucationPerson> list = instrucationPersonService.selectPersonByNameNew(instrucationPerson);
        return AjaxResult.success(list);
    }

    @GetMapping("/selectPersonByNamePage")
    public TableDataInfo selectPersonByNamePage(InstrucationPerson instrucationPerson) {
        startPage();
        List<InstrucationPerson> list = instrucationPersonService.selectPersonByNameNew(instrucationPerson);
        return getDataTable(list);
    }

    /**
     * 查询地址薄接口
     *
     * @param type
     * @param code
     * @return
     */
    @GetMapping("/getPersonPlace")
    public AjaxResult getPersonPlace(Integer type, String code) {
        List<HashMap> maps = instrucationPersonService.getPersonPlace(type, code);
        return AjaxResult.success(maps);
    }


    /**
     * 修改人员类型
     */
    @PreAuthorize("@ss.hasPermi('instruction:person:edit')")
    @Log(title = "指令关联人员信息", businessType = BusinessType.UPDATE)
    @PutMapping("editPeopleStatus")
    public AjaxResult editPeopleStatus(@RequestBody InstrucationPerson instrucationPerson) {
        if (instrucationPerson == null || instrucationPerson.getId() == null) {
            throw new GlobalException("参数异常");
        }
        int i = personMapper.updateInstrucationPerson(instrucationPerson);
        if (i > 0) {
            return AjaxResult.success();
        }
        return AjaxResult.error("更改状态错误");
    }

    /**
     * 根据身份证查询用户
     *
     * @param idCard
     * @return
     */
    @GetMapping("/selectByIdCard")
    public AjaxResult selectByIdCard(String idCard) {
        List<InstrucationPerson> personList = instrucationPersonService.selectByIdCard(idCard);
        return AjaxResult.success(personList);
    }

    /**
     * 同步身份证号
     *
     * @return
     */
    @GetMapping("/tbIdCard")
    public AjaxResult tbIdCard() {
        instrucationPersonService.tbIdCard();
        return AjaxResult.success();
    }

    /**
     * 同步人员类型
     *
     * @param person
     * @return
     */
    @GetMapping("tbTypeName")
    public AjaxResult tbTypeName(InstrucationPerson person) {
        List<InstrucationPerson> personList = personMapper.selectInstrucationPersonList(person);
        personList.stream().forEach(person1 -> {
            String s = personMapper.selectTypeNameById(person1.getId());
            person1.setTypeName(s);
            personMapper.updateInstrucationPerson(person1);
        });
        return AjaxResult.success();
    }

    /**
     * 同步更新人员预警(首次，不考虑预警已存在)
     * @return
     */
    @GetMapping("tbPersonWarn")
    public AjaxResult tbPersonWarn() {
        //1、查询出所有人员id
        List<InstrucationPerson> personList = instrucationPersonService.selectInstrucationPersonList(new InstrucationPerson());
        //2、查询出所有事件信息
        List<InstructionEvent> eventList = instructionEventService.selectInstructionEventList(new InstructionEvent());
        //3、进行预警比较 遍历人员 判断属于种条件
        Set<Long> ids = new HashSet<>();
        Set<Long> orangeIds = new HashSet<>();
        Set<Long> yellowIds = new HashSet<>();
        List<PersonWarn> personWarnList = new ArrayList<>();
        //获取近三年前时间
        Date threeDate = DateUtils.getBeforeDate(3);
        //获取近一年前时间
        Date oneDate = DateUtils.getBeforeDate(1);
        //当年开始日期
        Date firstDateOfYear = DateUtils.getFirstDateOfYear();
        //1、事件中有关联群体且为挑头人员
        String groupIds = eventList.stream().filter(event -> event.getGroupId() != null && event.getLeadPersonIds() != null).map(InstructionEvent::getLeadPersonIds).collect(Collectors.joining(","));
        long[] longs = StrUtil.splitToLong(groupIds, ",");
        for (long id : longs) {
            //根据人员id进行查询个人信息
            if (id == -1) {
                continue;
            }
            InstrucationPerson instrucationPerson = personList.stream().filter(person -> person.getId() == id).findFirst().orElse(null);
            if (instrucationPerson == null) {
                continue;
            }
            if ((instrucationPerson.getpLevel() != null && instrucationPerson.getpLevel() == 3) || ids.contains(id)) {
                continue;
            }
            //用户当前管控等级不等于红且未存在预警信息
            ids.add(id);
            PersonWarn personWarn = new PersonWarn();
            personWarn.setPersonId(id);
            personWarn.setPersonName(instrucationPerson.getPersonName());
            personWarn.setNowLevel(instrucationPerson.getpLevel() != null ? instrucationPerson.getpLevel() : -1);
            personWarn.setApplyLevel(Constants.RED_LEVEL);
            personWarn.setDutyPlace(instrucationPerson.getDutyPlace());
            personWarn.setPersonTown(instrucationPerson.getpTown());
            personWarn.setAdjustReason("参与事件中有关联群体且为挑头人员");
            personWarnList.add(personWarn);

        }
        //2、近3年内进京访3次以上或赴省访5次以上-红、近3 年内进京访 2 次以上或赴省访3 次以上-橙
        Map<Long,Map<String, Integer>> map = new HashMap<>();
        List<InstructionEvent> eventList1 = eventList.stream()
                .filter(event -> event.getCreateTime().after(threeDate) && event.getPersonIds() != null && event.getPetitionType() != null && (event.getPetitionType().equals("2") || event.getPetitionType().equals("3")))
                .collect(Collectors.toList());
        for (InstructionEvent event : eventList1){
            String petitionType = event.getPetitionType();
            long[] longs1 = StrUtil.splitToLong(event.getPersonIds(), ",");
            for (long id : longs1) {
                //根据人员id进行查询个人信息
                if (id == -1) {
                    continue;
                }
                InstrucationPerson instrucationPerson = personList.stream().filter(person -> person.getId() == id).findFirst().orElse(null);
                if (instrucationPerson == null) {
                    continue;
                }
                if ((instrucationPerson.getpLevel() != null && instrucationPerson.getpLevel() == 3) || ids.contains(id)) {
                    continue;
                }
                Map<String, Integer> map1 = map.get(id);
                if (map1 == null) {
                    Map<String, Integer> map2 = new HashMap<>();
                    map2.put(petitionType, 1);
                    map.put(id, map2);
                }else {
                    Integer integer = map1.get(petitionType);
                    integer = (integer != null ? integer : 0);
                    map1.put(petitionType, integer + 1);
                    map.put(id, map1);
                }
            }
        }
        //遍历map 筛选出进京访3次以上或赴省访5次以上
        for (Map.Entry<Long, Map<String, Integer>> outerEntry : map.entrySet()) {
            long id = outerEntry.getKey();
            Map<String, Integer> innerMap = outerEntry.getValue();
            Integer goCapital = innerMap.get("3");
            Integer goProvince = innerMap.get("2");
            if (goCapital != null && goCapital >= 3 || goProvince != null && goProvince >= 5){
                InstrucationPerson instrucationPerson = personList.stream().filter(person -> person.getId() == id).findFirst().orElse(null);
                //用户当前管控等级不等于红且未存在预警信息
                ids.add(id);
                PersonWarn personWarn = new PersonWarn();
                personWarn.setPersonId(id);
                personWarn.setPersonName(instrucationPerson.getPersonName());
                personWarn.setNowLevel(instrucationPerson.getpLevel() != null ? instrucationPerson.getpLevel() : -1);
                personWarn.setApplyLevel(Constants.RED_LEVEL);
                personWarn.setDutyPlace(instrucationPerson.getDutyPlace());
                personWarn.setPersonTown(instrucationPerson.getpTown());
                personWarn.setAdjustReason("近 3 年内进京访 3 次以上或赴省访 5 次以上");
                personWarnList.add(personWarn);
            }else if (goCapital != null && goCapital >= 2 || goProvince != null && goProvince >= 3){
                if (ids.contains(id) || orangeIds.contains(id)) {
                    continue;
                }
                InstrucationPerson instrucationPerson = personList.stream().filter(person -> person.getId() == id).findFirst().orElse(null);
                if (instrucationPerson.getpLevel() != null && instrucationPerson.getpLevel() == 2) {
                    continue;
                }
                orangeIds.add(id);
                PersonWarn personWarn = new PersonWarn();
                personWarn.setPersonId(id);
                personWarn.setPersonName(instrucationPerson.getPersonName());
                personWarn.setNowLevel(instrucationPerson.getpLevel() != null ? instrucationPerson.getpLevel() : -1);
                personWarn.setApplyLevel(Constants.ORANGE_LEVEL);
                personWarn.setDutyPlace(instrucationPerson.getDutyPlace());
                personWarn.setPersonTown(instrucationPerson.getpTown());
                personWarn.setAdjustReason("近3 年内进京访 2 次以上或赴省访3 次以上");
                personWarnList.add(personWarn);
            }

        }
        //3、当年进京访2次以上或赴省访3次以上
        Map<Long,Map<String, Integer>> mapThree = new HashMap<>();
        List<InstructionEvent> eventListThree = eventList.stream()
                .filter(event -> event.getCreateTime().after(firstDateOfYear) && event.getPersonIds() != null && event.getPetitionType() != null && (event.getPetitionType().equals("2") || event.getPetitionType().equals("3")))
                .collect(Collectors.toList());
        for (InstructionEvent event : eventListThree){
            String petitionType = event.getPetitionType();
            long[] longs1 = StrUtil.splitToLong(event.getPersonIds(), ",");
            for (long id : longs1) {
                //根据人员id进行查询个人信息
                if (id == -1) {
                    continue;
                }
                InstrucationPerson instrucationPerson = personList.stream().filter(person -> person.getId() == id).findFirst().orElse(null);
                if (instrucationPerson == null) {
                    continue;
                }
                if ((instrucationPerson.getpLevel() != null && instrucationPerson.getpLevel() == 3) || ids.contains(id)) {
                    continue;
                }
                Map<String, Integer> map1 = mapThree.get(id);
                if (map1 == null) {
                    Map<String, Integer> map2 = new HashMap<>();
                    map2.put(petitionType, 1);
                    mapThree.put(id, map2);
                }else {
                    Integer integer = map1.get(petitionType);
                    integer = (integer != null ? integer : 0);
                    map1.put(petitionType, integer + 1);
                    mapThree.put(id, map1);
                }
            }
        }
        //遍历map 筛选出当年进京访 2 次以上或赴省访 3 次以上的人员-红 ,当年进京访 1次以上或赴省访 2 次以上的人员-橙,当年赴省访2次以上 - 黄
        for (Map.Entry<Long, Map<String, Integer>> outerEntry : mapThree.entrySet()) {
            long id = outerEntry.getKey();
            Map<String, Integer> innerMap = outerEntry.getValue();
            Integer goCapital = innerMap.get("3");
            Integer goProvince = innerMap.get("2");
            if (goCapital != null && goCapital >= 2 || goProvince != null && goProvince >= 3){
                InstrucationPerson instrucationPerson = personList.stream().filter(person -> person.getId() == id).findFirst().orElse(null);
                //用户当前管控等级不等于红且未存在预警信息
                ids.add(id);
                PersonWarn personWarn = new PersonWarn();
                personWarn.setPersonId(id);
                personWarn.setPersonName(instrucationPerson.getPersonName());
                personWarn.setNowLevel(instrucationPerson.getpLevel() != null ? instrucationPerson.getpLevel() : -1);
                personWarn.setApplyLevel(Constants.RED_LEVEL);
                personWarn.setDutyPlace(instrucationPerson.getDutyPlace());
                personWarn.setPersonTown(instrucationPerson.getpTown());
                personWarn.setAdjustReason("当年进京访 2 次以上或赴省访 3 次以上");
                personWarnList.add(personWarn);
            }else if (goCapital != null && goCapital >= 1 || goProvince != null && goProvince >= 2){
                if (ids.contains(id) || orangeIds.contains(id)) {
                    continue;
                }
                InstrucationPerson instrucationPerson = personList.stream().filter(person -> person.getId() == id).findFirst().orElse(null);
                if (instrucationPerson.getpLevel() != null && instrucationPerson.getpLevel() == 2) {
                    continue;
                }
                orangeIds.add(id);
                PersonWarn personWarn = new PersonWarn();
                personWarn.setPersonId(id);
                personWarn.setPersonName(instrucationPerson.getPersonName());
                personWarn.setNowLevel(instrucationPerson.getpLevel() != null ? instrucationPerson.getpLevel() : -1);
                personWarn.setApplyLevel(Constants.ORANGE_LEVEL);
                personWarn.setDutyPlace(instrucationPerson.getDutyPlace());
                personWarn.setPersonTown(instrucationPerson.getpTown());
                personWarn.setAdjustReason("当年进京访 1次以上或赴省访 2 次以上");
                personWarnList.add(personWarn);
            }else if (goProvince != null && goProvince >= 2){
                if (ids.contains(id) || orangeIds.contains(id)||yellowIds.contains(id)) {
                    continue;
                }
                InstrucationPerson instrucationPerson = personList.stream().filter(person -> person.getId() == id).findFirst().orElse(null);
                if (instrucationPerson.getpLevel() != null && instrucationPerson.getpLevel() >= 3) {
                    continue;
                }
                yellowIds.add(id);
                PersonWarn personWarn = new PersonWarn();
                personWarn.setPersonId(id);
                personWarn.setPersonName(instrucationPerson.getPersonName());
                personWarn.setNowLevel(instrucationPerson.getpLevel() != null ? instrucationPerson.getpLevel() : -1);
                personWarn.setApplyLevel(Constants.YELLOW_LEVEL);
                personWarn.setDutyPlace(instrucationPerson.getDutyPlace());
                personWarn.setPersonTown(instrucationPerson.getpTown());
                personWarn.setAdjustReason("当年赴省访2次以上");
                personWarnList.add(personWarn);
            }

        }
        //4、近1年内在网上挑头煽动 10 次以上的人员-红,近1年在网上挑头煽动 5 次以上的人员-橙,近 1年内在网上挑头煽动3 次以上的人员-黄
        Map<Long,Map<Integer, Integer>> mapFour = new HashMap<>();
        List<InstructionEvent> eventListFour = eventList.stream()
                .filter(event -> event.getCreateTime().after(oneDate) && event.getPersonIds() != null && (event.getEventProperties() == 2 || event.getEventProperties() == 1 || event.getEventProperties() == 4))
                .collect(Collectors.toList());
        for (InstructionEvent event : eventListFour){
            Integer eventProperties = event.getEventProperties();
            long[] longs1 = StrUtil.splitToLong(event.getPersonIds(), ",");
            for (long id : longs1) {
                //根据人员id进行查询个人信息
                if (id == -1) {
                    continue;
                }
                InstrucationPerson instrucationPerson = personList.stream().filter(person -> person.getId() == id).findFirst().orElse(null);
                if (instrucationPerson == null) {
                    continue;
                }
                if ((instrucationPerson.getpLevel() != null && instrucationPerson.getpLevel() == 3) || ids.contains(id)) {
                    continue;
                }
                Map<Integer, Integer> map1 = mapFour.get(id);
                if (map1 == null) {
                    Map<Integer, Integer> map2 = new HashMap<>();
                    map2.put(eventProperties, 1);
                    mapFour.put(id, map2);
                }else {
                    Integer integer = map1.get(eventProperties);
                    integer = (integer != null ? integer : 0);
                    map1.put(eventProperties, integer + 1);
                    mapFour.put(id, map1);
                }
            }
        }
        //遍历map 筛选出当年进京访 2 次以上或赴省访 3 次以上的人员-红 ,当年进京访 1次以上或赴省访 2 次以上的人员-橙,当年赴省访2次以上 - 黄
        for (Map.Entry<Long, Map<Integer, Integer>> outerEntry : mapFour.entrySet()) {
            long id = outerEntry.getKey();
            Map<Integer, Integer> innerMap = outerEntry.getValue();
            Integer OnlineIncitement = innerMap.get(2);
            Integer threaten = innerMap.get(1);
            Integer IndividualExtremes = innerMap.get(4);
            if (OnlineIncitement != null && OnlineIncitement >= 10 || IndividualExtremes != null){
                InstrucationPerson instrucationPerson = personList.stream().filter(person -> person.getId() == id).findFirst().orElse(null);
                //用户当前管控等级不等于红且未存在预警信息
                ids.add(id);
                PersonWarn personWarn = new PersonWarn();
                personWarn.setPersonId(id);
                personWarn.setPersonName(instrucationPerson.getPersonName());
                personWarn.setNowLevel(instrucationPerson.getpLevel() != null ? instrucationPerson.getpLevel() : -1);
                personWarn.setApplyLevel(Constants.RED_LEVEL);
                personWarn.setDutyPlace(instrucationPerson.getDutyPlace());
                personWarn.setPersonTown(instrucationPerson.getpTown());
                personWarn.setAdjustReason("近1年内在网上挑头煽动 10 次以上");
                if (IndividualExtremes!=null){
                    personWarn.setAdjustReason("近1年内曾有自焚、服毒、跳楼、自杀、自残等极端行为");
                }
                personWarnList.add(personWarn);
            }else if (OnlineIncitement != null && OnlineIncitement >= 5 || threaten != null){
                if (ids.contains(id) || orangeIds.contains(id)) {
                    continue;
                }
                InstrucationPerson instrucationPerson = personList.stream().filter(person -> person.getId() == id).findFirst().orElse(null);
                if (instrucationPerson.getpLevel() != null && instrucationPerson.getpLevel() == 2) {
                    continue;
                }
                orangeIds.add(id);
                PersonWarn personWarn = new PersonWarn();
                personWarn.setPersonId(id);
                personWarn.setPersonName(instrucationPerson.getPersonName());
                personWarn.setNowLevel(instrucationPerson.getpLevel() != null ? instrucationPerson.getpLevel() : -1);
                personWarn.setApplyLevel(Constants.ORANGE_LEVEL);
                personWarn.setDutyPlace(instrucationPerson.getDutyPlace());
                personWarn.setPersonTown(instrucationPerson.getpTown());
                personWarn.setAdjustReason("近1年在网上挑头煽动 5 次以上的人员");
                if (threaten!=null){
                    personWarn.setAdjustReason("近1年内扬实施自焚服毒、跳楼、自杀、自残、报复社会或个人等极端行为");
                }
                personWarnList.add(personWarn);
            }else if (OnlineIncitement != null && OnlineIncitement >= 3){
                if (ids.contains(id) || orangeIds.contains(id)||yellowIds.contains(id)) {
                    continue;
                }
                InstrucationPerson instrucationPerson = personList.stream().filter(person -> person.getId() == id).findFirst().orElse(null);
                if (instrucationPerson.getpLevel() != null && instrucationPerson.getpLevel() >= 3) {
                    continue;
                }
                yellowIds.add(id);
                PersonWarn personWarn = new PersonWarn();
                personWarn.setPersonId(id);
                personWarn.setPersonName(instrucationPerson.getPersonName());
                personWarn.setNowLevel(instrucationPerson.getpLevel() != null ? instrucationPerson.getpLevel() : -1);
                personWarn.setApplyLevel(Constants.YELLOW_LEVEL);
                personWarn.setDutyPlace(instrucationPerson.getDutyPlace());
                personWarn.setPersonTown(instrucationPerson.getpTown());
                personWarn.setAdjustReason("近 1年内在网上挑头煽动3 次以上的人员");
                personWarnList.add(personWarn);
            }

        }
        //批量插入人员预警数据
        personWarnMapper.batchSave(personWarnList);
        return AjaxResult.success(personWarnList).put("map", map.toString()).put("mapThree",mapThree.toString()).put("mapFour",mapFour.toString());
    }


}
