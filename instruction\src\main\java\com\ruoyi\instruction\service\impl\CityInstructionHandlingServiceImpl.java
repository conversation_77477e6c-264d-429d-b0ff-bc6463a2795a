package com.ruoyi.instruction.service.impl;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.ruoyi.common.constant.Constants;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.instruction.domain.InstructionInfo;
import com.ruoyi.instruction.domain.rspVo.*;
import com.ruoyi.instruction.mapper.CityInstructionHandlingMapper;
import com.ruoyi.instruction.mapper.InstructionInfoMapper;
import com.ruoyi.instruction.service.ICityInstructionHandlingService;
import com.ruoyi.instruction.service.IInstructionInfoService;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.math.BigDecimal;
import java.net.URLEncoder;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

/**
 * 市级指令办理情况服务实现
 *
 * <AUTHOR>
 * @date 2025-01-20
 */
@Service
public class CityInstructionHandlingServiceImpl implements ICityInstructionHandlingService {

    @Autowired
    private CityInstructionHandlingMapper cityInstructionHandlingMapper;
    @Autowired
    private InstructionInfoMapper infoMapper;
    @Autowired
    private IInstructionInfoService instructionInfoService;

    @Override
    public CityHandlingOverviewRsp getHandlingOverview(String startTime, String endTime, Long deptId, String deptName) {
        Map<String, Object> params = buildParams(startTime, endTime, deptId, deptName);

        CityHandlingOverviewRsp overview = new CityHandlingOverviewRsp();
        overview.setStartTime(startTime);
        overview.setEndTime(endTime);

        // 获取超时接收数量
        TableDataInfo tableDataInfo = getTimeoutReceiveListV2(startTime, endTime, 1);
        overview.setTimeoutReceiveCount(tableDataInfo.getTotal());

        // 获取超时处置数量
        tableDataInfo = getTimeoutDisposeListV2(startTime, endTime, 1);
        overview.setTimeoutDisposeCount(tableDataInfo.getTotal());


//        // 获取应处置未处置数据
//        Map<String, Object> unprocessedData = cityInstructionHandlingMapper.getUnprocessedData(params);
//        overview.setUnprocessedUnitCount((Long) unprocessedData.get("unitCount"));
//        overview.setUnprocessedDeptCount((Long) unprocessedData.get("deptCount"));

        // 获取待反馈数据
        Map<String, Object> pendingFeedbackData = cityInstructionHandlingMapper.getPendingFeedbackData(params);
        overview.setPendingFeedback7Days(((BigDecimal) pendingFeedbackData.get("days7")).longValue());
        overview.setPendingFeedback15Days(((BigDecimal) pendingFeedbackData.get("days15")).longValue());
        overview.setPendingFeedback30Days(((BigDecimal) pendingFeedbackData.get("days30")).longValue());

        return overview;
    }

    @Override
    public TableDataInfo getTimeoutDisposeListV2(String startTime, String endTime, Integer end) {
        InstructionInfo query = new InstructionInfo();
        query.setStart(1);
        query.setEnd(end);
        query.setSortType(0);
        query.setProcess(0);
        query.setPageType(1);
        query.setInstructionType(7);
        query.setFeedbackNot(1);

        Map<String, Object> params = new HashMap<>();
        params.put("beginAssignTime", startTime);
        params.put("endAssignTime", endTime);

        query.setParams(params);

        TableDataInfo tableDataInfo = instructionInfoService.mzxInstructionListNew(query);

        return tableDataInfo;
    }

    @Override
    public TableDataInfo getTimeoutReceiveListV2(String startTime, String endTime, Integer end) {
        InstructionInfo query = new InstructionInfo();
        query.setStart(1);
        query.setEnd(end);
        query.setSortType(0);
        query.setProcess(0);
        query.setPageType(1);
        query.setInstructionType(7);
        query.setReceiveNot(1);

        Map<String, Object> params = new HashMap<>();
        params.put("beginAssignTime", startTime);
        params.put("endAssignTime", endTime);

        query.setParams(params);

        TableDataInfo tableDataInfo = instructionInfoService.mzxInstructionListNew(query);

        return tableDataInfo;
    }

    @Override
    public CityHandlingTimeoutRsp getTimeoutData(String startTime, String endTime, Long deptId, String deptName) {
        Map<String, Object> params = buildParams(startTime, endTime, deptId, deptName);

        CityHandlingTimeoutRsp timeoutData = new CityHandlingTimeoutRsp();
        timeoutData.setStartTime(startTime);
        timeoutData.setEndTime(endTime);
        timeoutData.setCurrentArea(getCurrentArea(deptName));

        // 获取超时接收数量
        timeoutData.setTimeoutReceiveCount(cityInstructionHandlingMapper.getTimeoutReceiveCount(params));

        // 获取超时处置数量
        timeoutData.setTimeoutDisposeCount(cityInstructionHandlingMapper.getTimeoutDisposeCount(params));

        return timeoutData;
    }

    @Override
    public CityHandlingUnprocessedRsp getUnprocessedData(String startTime, String endTime, Long deptId, String deptName) {
        Map<String, Object> params = buildParams(startTime, endTime, deptId, deptName);

        CityHandlingUnprocessedRsp unprocessedData = new CityHandlingUnprocessedRsp();
        unprocessedData.setStartTime(startTime);
        unprocessedData.setEndTime(endTime);
        unprocessedData.setCurrentArea(getCurrentArea(deptName));

        // 获取应处置未处置数据
        Map<String, Object> data = cityInstructionHandlingMapper.getUnprocessedData(params);
        unprocessedData.setUnprocessedUnitCount((Integer) data.get("unitCount"));
        unprocessedData.setUnprocessedDeptCount((Integer) data.get("deptCount"));

        return unprocessedData;
    }

    @Override
    public Map<String, Object> getUnprocessedDataDetailed(String startTime, String endTime, Long deptId, String deptName) {
        Map<String, Object> params = buildParams(startTime, endTime, deptId, deptName);
        return cityInstructionHandlingMapper.getUnprocessedDataDetailed(params);
    }

    @Override
    public CityHandlingPendingFeedbackRsp getPendingFeedbackData(String startTime, String endTime, Long deptId, String deptName) {
        Map<String, Object> params = buildParams(startTime, endTime, deptId, deptName);

        CityHandlingPendingFeedbackRsp pendingFeedbackData = new CityHandlingPendingFeedbackRsp();
        pendingFeedbackData.setStartTime(startTime);
        pendingFeedbackData.setEndTime(endTime);
        pendingFeedbackData.setCurrentArea(getCurrentArea(deptName));

        // 获取待反馈数据
        Map<String, Object> data = cityInstructionHandlingMapper.getPendingFeedbackData(params);
        pendingFeedbackData.setPendingFeedback7Days((Integer) data.get("days7"));
        pendingFeedbackData.setPendingFeedback15Days((Integer) data.get("days15"));
        pendingFeedbackData.setPendingFeedback30Days((Integer) data.get("days30"));

        return pendingFeedbackData;
    }

    @Override
    public List<Map<String, Object>> getTimeoutReceiveList(String startTime, String endTime, Long deptId, String deptName) {
        Map<String, Object> params = buildParams(startTime, endTime, deptId, deptName);
        return cityInstructionHandlingMapper.getTimeoutReceiveList(params);
    }

    @Override
    public List<Map<String, Object>> getTimeoutDisposeList(String startTime, String endTime, Long deptId, String deptName) {
        Map<String, Object> params = buildParams(startTime, endTime, deptId, deptName);
        return cityInstructionHandlingMapper.getTimeoutDisposeList(params);
    }

    @Override
    public List<Map<String, Object>> getUnprocessedList(String startTime, String endTime, Long deptId, String deptName) {
        Map<String, Object> params = buildParams(startTime, endTime, deptId, deptName);
        return cityInstructionHandlingMapper.getUnprocessedList(params);
    }

    @Override
    public List<Map<String, Object>> getPendingFeedbackList(String emergencyType, String startTime, String endTime, Long deptId, String deptName) {
        Map<String, Object> params = buildParams(startTime, endTime, deptId, deptName);
        params.put("emergencyType", emergencyType);
        return cityInstructionHandlingMapper.getPendingFeedbackList(params);
    }

    /**
     * 构建查询参数
     */
    private Map<String, Object> buildParams(String startTime, String endTime, Long deptId, String deptName) {
        Map<String, Object> params = new HashMap<>();
        params.put("startTime", startTime);
        params.put("endTime", endTime);
        params.put("deptId", deptId);
        return params;
    }

    /**
     * 根据部门名称获取当前区域
     */
    private String getCurrentArea(String deptName) {
        if (deptName == null) {
            return null;
        }

        // 提取区域名称，如"东阳市政法委"提取"东阳市"
        if (deptName.contains("政法委")) {
            return deptName.replace("政法委", "");
        }

        return deptName;
    }

    // ==================== 县市层级指令办理情况 ====================

    @Override
    public Map<String, Object> getCountyNoReceiveStats(String countyName, String startTime, String endTime) {
        Map<String, Object> params = buildCountyParams(countyName, startTime, endTime);
        return cityInstructionHandlingMapper.getCountyNoReceiveStats(params);
    }

    @Override
    public Map<String, Object> getCountyDisposeAndReceive(Boolean cityFlag, Long deptId, String startTime, String endTime) {
        Map<String, Object> map = new HashMap<>();

        fillDeptReceive(cityFlag, deptId, startTime, endTime, map);

        fillHandleReceive(cityFlag, deptId, startTime, endTime, map);

        return map;
    }

    private void fillHandleReceive(Boolean cityFlag, Long deptId, String startTime, String endTime, Map<String, Object> map) {
        //获取应处置未处置数据
        //1、查询出市级双排双办、预警指令id
        List<Long> ids = infoMapper.getSpsbAndyjIds(cityFlag, deptId, startTime, endTime);
        //查询出详细数据
        List<ToFeedbackRsp> toFeedbackRspList = infoMapper.getToFeedbackList(cityFlag, ids);

        List<ToFeedbackRsp> deptRspList = toFeedbackRspList.stream()
                .filter(dept -> dept.getFeedbackDept().contains("政法委")
                        || dept.getFeedbackDept().contains("局"))
                .collect(Collectors.toList());
        // map.put("toFeedback", toFeedbackRspList);
        map.put("feedbackDeptCount", 0);
        map.put("feedbackDeptInstructionCount", 0);
        if (deptRspList.size() != 0) {
            long count = deptRspList.stream().filter(item -> item.getFeedbackTime() != null).map(ToFeedbackRsp::getId).distinct().count();
            long deptCount = deptRspList.stream().filter(item -> item.getFeedbackTime() != null).count();
            map.put("feedbackDeptCount", count);
            map.put("feedbackDeptInstructionCount", deptCount);
        }

        //获取应处置未处置数据
        //1、查询出市级双排双办、预警指令id
//        List<Long> townIds = infoMapper.getSpsbAndyjIds("镇街", deptId, startTime, endTime);
        //查询出详细数据
//        List<ToFeedbackRsp> toFeedbackTownRspList = infoMapper.getToFeedbackList(townIds);
        // map.put("toFeedback", toFeedbackRspList);

        List<ToFeedbackRsp> townRspList = toFeedbackRspList.stream()
                .filter(dept -> dept.getFeedbackDept().contains("街道")
                        || dept.getFeedbackDept().contains("镇")
                        || dept.getFeedbackDept().contains("乡"))
                .collect(Collectors.toList());

        map.put("feedbackTownCount", 0);
        map.put("feedbackTownInstructionCount", 0);
        if (townRspList.size() != 0) {
            long count = townRspList.stream().filter(item -> item.getFeedbackTime() != null).map(ToFeedbackRsp::getId).distinct().count();
            long townCount = townRspList.stream().filter(item -> item.getFeedbackTime() != null).count();
            map.put("feedbackTownCount", count);
            map.put("feedbackTownInstructionCount", townCount);
        }
    }

    private void fillDeptReceive(Boolean cityFlag, Long deptId, String startTime, String endTime, Map<String, Object> map) {
        List<ToReceiveRsp> deptList = infoMapper.getNoReceive(cityFlag, "部门", deptId, startTime, endTime);

        map.put("instructionDeptCount", 0);
        map.put("deptCount", 0);
        //获取应接收未接收数据
        if (deptList.size() > 0) {
            long count = deptList.stream().map(ToReceiveRsp::getId).distinct().count();
            map.put("instructionDeptCount", count);
            map.put("deptCount", deptList.size());
        }

        List<ToReceiveRsp> townList = infoMapper.getNoReceive(cityFlag, "部门", deptId, startTime, endTime);

        map.put("instructionTownCount", 0);
        map.put("townCount", 0);
        //获取应接收未接收数据
        if (townList.size() > 0) {
            long count = townList.stream().map(ToReceiveRsp::getId).distinct().count();
            map.put("instructionTownCount", count);
            map.put("townCount", townList.size());
        }
    }

    @Override
    public List<CountyInstructionHandlingRsp> getCountyNoReceiveList(String deptType, String countyName, String startTime, String endTime) {
        Map<String, Object> params = buildCountyParams(countyName, startTime, endTime);
        params.put("deptType", deptType);
        return cityInstructionHandlingMapper.getCountyNoReceiveList(params);
    }

    @Override
    public List<CountyInstructionHandlingRsp> getCountyNoDisposeList(String deptType, String countyName, String startTime, String endTime) {
        Map<String, Object> params = buildCountyParams(countyName, startTime, endTime);
        params.put("deptType", deptType);
        return cityInstructionHandlingMapper.getCountyNoDisposeList(params);
    }

    @Override
    public void exportHandlingData(HttpServletResponse response, String startTime, String endTime) throws IOException {
        response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
        response.setCharacterEncoding("utf-8");
        // 这里URLEncoder.encode可以防止中文乱码 当然和easyexcel没有关系
        String fileName = URLEncoder.encode("指令办理情况", "UTF-8").replaceAll("\\+", "%20");
        response.setHeader("Content-disposition", "attachment;filename*=utf-8''" + fileName + ".xlsx");
        ExcelWriter excelWriter = EasyExcel.write(response.getOutputStream()).build();
        //查询应处置未处置数据-部门
        //1、查询出市级双排双办、预警指令id
        List<Long> ids = infoMapper.getSpsbAndyjIds(true, null, startTime, endTime);
        //查询出详细数据
        List<ToFeedbackRsp> toFeedbackDeptRspList = infoMapper.getToFeedbackList(true, ids);

        List<ToFeedbackRsp> deptRspList = toFeedbackDeptRspList.stream()
                .filter(dept -> dept.getFeedbackDept().contains("政法委")
                        || dept.getFeedbackDept().contains("局"))
                .collect(Collectors.toList());

        List<ToFeedbackRsp> collect = deptRspList.stream().filter(item -> item.getFeedbackTime() != null).collect(Collectors.toList());
        WriteSheet feedbackDeptSheet = EasyExcel.writerSheet(1, "应处置未处置-部门").head(ToFeedbackRsp.class).build();
        excelWriter.write(collect, feedbackDeptSheet);

        //查询应处置未处置数据-镇街
        //1、查询出市级双排双办、预警指令id
        List<ToFeedbackRsp> townRspList = toFeedbackDeptRspList.stream()
                .filter(dept -> dept.getFeedbackDept().contains("街道")
                        || dept.getFeedbackDept().contains("镇")
                        || dept.getFeedbackDept().contains("乡"))
                .collect(Collectors.toList());

        //查询出详细数据
//        List<ToFeedbackRsp> toFeedbackRspTownList = infoMapper.getToFeedbackList(townRspList);
        List<ToFeedbackRsp> townCollect = townRspList.stream()
                .filter(item -> item.getFeedbackTime() != null)
                .collect(Collectors.toList());

        WriteSheet feedbackTownSheet = EasyExcel.writerSheet(2, "应处置未处置-镇街").head(ToFeedbackRsp.class).build();
        excelWriter.write(townCollect, feedbackTownSheet);
        // 关闭 ExcelWriter
        excelWriter.finish();
    }

    @Override
    public void exportCountyHandling(HttpServletResponse response, Long deptId, String startTime, String endTime) throws IOException {
        response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
        response.setCharacterEncoding("utf-8");
        // 这里URLEncoder.encode可以防止中文乱码 当然和easyexcel没有关系
        String fileName = URLEncoder.encode("指令办理情况", "UTF-8").replaceAll("\\+", "%20");
        response.setHeader("Content-disposition", "attachment;filename*=utf-8''" + fileName + ".xlsx");
        ExcelWriter excelWriter = EasyExcel.write(response.getOutputStream()).build();
        List<ToReceiveRsp> deptList = infoMapper.getNoReceive(false, "部门", deptId, startTime, endTime);
        WriteSheet deptAcceptSheet = EasyExcel.writerSheet(0, "应接收未接收-部门").head(ToReceiveRsp.class).build();
        excelWriter.write(deptList, deptAcceptSheet);

        List<ToReceiveRsp> townList = infoMapper.getNoReceive(false, "镇街", deptId, startTime, endTime);
        WriteSheet townAcceptSheet = EasyExcel.writerSheet(1, "应接收未接收-镇街").head(ToReceiveRsp.class).build();
        excelWriter.write(townList, townAcceptSheet);

        //查询应处置未处置数据-部门
        //1、查询出市级双排双办、预警指令id
        List<Long> ids = infoMapper.getSpsbAndyjIds(false, null, startTime, endTime);
        //查询出详细数据
        List<ToFeedbackRsp> toFeedbackDeptRspList = infoMapper.getToFeedbackList(false, ids);

        List<ToFeedbackRsp> deptRspList = toFeedbackDeptRspList.stream()
                .filter(dept -> dept.getFeedbackDept().contains("政法委")
                        || dept.getFeedbackDept().contains("局"))
                .collect(Collectors.toList());

        List<ToFeedbackRsp> collect = deptRspList.stream().filter(item -> item.getFeedbackTime() != null).collect(Collectors.toList());
        WriteSheet feedbackDeptSheet = EasyExcel.writerSheet(2, "应处置未处置-部门").head(ToFeedbackRsp.class).build();
        excelWriter.write(collect, feedbackDeptSheet);

        //查询应处置未处置数据-镇街
        //1、查询出市级双排双办、预警指令id
        List<ToFeedbackRsp> townRspList = toFeedbackDeptRspList.stream()
                .filter(dept -> dept.getFeedbackDept().contains("街道")
                        || dept.getFeedbackDept().contains("镇")
                        || dept.getFeedbackDept().contains("乡"))
                .collect(Collectors.toList());

        //查询出详细数据
//        List<ToFeedbackRsp> toFeedbackRspTownList = infoMapper.getToFeedbackList(townRspList);
        List<ToFeedbackRsp> townCollect = townRspList.stream()
                .filter(item -> item.getFeedbackTime() != null)
                .collect(Collectors.toList());

        WriteSheet feedbackTownSheet = EasyExcel.writerSheet(3, "应处置未处置-镇街").head(ToFeedbackRsp.class).build();
        excelWriter.write(townCollect, feedbackTownSheet);
        // 关闭 ExcelWriter
        excelWriter.finish();
    }

    /**
     * 构建县市层级查询参数
     */
    private Map<String, Object> buildCountyParams(String countyName, String startTime, String endTime) {
        Map<String, Object> params = new HashMap<>();
        params.put("countyName", countyName);
        params.put("startTime", startTime);
        params.put("endTime", endTime);
        return params;
    }
}
