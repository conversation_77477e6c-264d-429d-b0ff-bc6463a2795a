package com.ruoyi.instruction.mapper;

import com.ruoyi.instruction.domain.InstructionTrend;
import com.ruoyi.instruction.domain.JazzInstructionEvent;
import com.ruoyi.instruction.domain.rspVo.*;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.time.LocalDate;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 事件基本信息Mapper接口
 *
 * <AUTHOR>
 * @date 2022-12-16
 */
@Mapper
public interface JazzInstructionEventMapper {
    /**
     * 查询事件基本信息
     *
     * @param id 事件基本信息主键
     * @return 事件基本信息
     */
    public JazzInstructionEvent selectInstructionEventById(Long id);

    /**
     * 查询事件基本信息列表
     *
     * @param instructionEvent 事件基本信息
     * @return 事件基本信息集合
     */
    public List<JazzInstructionEvent> selectInstructionEventList(JazzInstructionEvent instructionEvent);

    /**
     * 新增事件基本信息
     *
     * @param instructionEvent 事件基本信息
     * @return 结果
     */
    public int insertInstructionEvent(JazzInstructionEvent instructionEvent);

    /**
     * 修改事件基本信息
     *
     * @param instructionEvent 事件基本信息
     * @return 结果
     */
    public int updateInstructionEvent(JazzInstructionEvent instructionEvent);

    /**
     * 删除事件基本信息
     *
     * @param id 事件基本信息主键
     * @return 结果
     */
    public int deleteInstructionEventById(Long id);

    /**
     * 批量删除事件基本信息
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteInstructionEventByIds(Long[] ids);

    /**
     * 根据人员id查询人员关联事件信息
     *
     * @param id
     * @return
     */
    List<JazzInstructionEvent> findByPersonId(@Param("id") Long id);

    /**
     * 根据群体id查询关联事件
     *
     * @param id
     * @return
     */
    List<JazzInstructionEvent> getEventListByGroupId(@Param("id") Long id);

    /**
     * 根据事件名称查询事件
     *
     * @param eventTitle
     * @return
     */
    JazzInstructionEvent findByEventTile(@Param("eventTitle") String eventTitle);

    /**
     * 根据事件名称、推送时间
     *
     * @param eventTitle
     * @param pushTime
     * @return
     */
    JazzInstructionEvent findByEventTileAndPushTime(@Param("eventTitle") String eventTitle, @Param("pushTime") Date pushTime);

    /**
     * 根据指令id查询指令对应事件
     *
     * @param infoId
     * @return
     */
    JazzInstructionEvent selectInstructionEventByInstrucationId(@Param("id") Long infoId);

    /**
     * 获取事件总数、重大事件数、区县事件数、市级事件数
     *
     * @return
     */
    EventDataRspVo getEventData(Map<String,Object> map);

    /**
     * 获取事件交办数
     * @return
     */
    List<EventAssignRspVo> getAssignData();

    /**
     * 获取信息类别
     * @return
     */
    List<Map<String, Integer>> getInfoCategory();

    /**
     * 获取事件类型
     * @return
     */
    List<Map<String, Integer>> getType();

    /**
     * 获取事件-基本情况
     * @return
     */
    String getEventBaseSituation();

    /**
     * 获取事件关联超过三次的群体名称
     * @return
     */
    List<String> getGroupName(@Param("groupCount")Integer groupCount);

    /**
     * 获取信息来源
     * @return
     */
    List<Map<String, Integer>> getInfoSource();

    /**
     * 获取事件多发区域排名
     * @return
     */
    List<Map<String, Integer>> getAreaRank();

    /**
     * 获取事件子类型数据
     * @param id
     * @return
     */
    List<Map<String, Integer>> getChildType(@Param("id") Integer id);

    /**
     * 获取大屏事件列表
     * @param date
     * @param nextDate
     * @return
     */
    List<BigScreenListEventRspVo> getBigScreenEventList(@Param("date") String date,@Param("nextDate")String nextDate);

    /**
     * 获取事件统计
     * @param date
     * @param nextDate
     * @return
     */
    BigScreenEventStatistics getStatistics(@Param("date") String date,@Param("nextDate")String nextDate);

    /**
     * 获取事件统计-县市区关联事件数
     * @param date
     * @param nextDate
     * @return
     */
    List<Map<String, Integer>> getCountyEventCount(@Param("date") String date, @Param("nextDate") String nextDate);

    /**
     * 根据id获取事件详情
     * @param id
     * @return
     */
    BigScreenEventInfo getInfoById(@Param("id") Long id);

    /**
     * 根据群组id指令id
     * @param groupId
     * @return
     */
    List<Long> selectInstructionIdByGroupId(@Param("groupId") Long groupId);

    /**
     * 根据群组id获取事件详情
     * @param groupId
     * @return
     */
    List<DisposalProcessEventVo> selectEventByGroupId(@Param("groupId") Long groupId);

    /**
     * 根据群组id获取事件详情,步骤为5时的列表
     * @param groupId
     * @return
     */
    List<DisposalProcessEventVo> selectEventByGroupIdForEnd(@Param("groupId") Long groupId);

    /**
     * 未关联群体事件 更新相关群体id
     * @return
     */
    int synchronizationGroupId();

    /**
     * 查询事件数量在时间范围内
     * @param date
     * @return
     */
    Integer getCountByTime(@Param("date") LocalDate date);
    /**
     * 查询所有事件的责任单位
     * @return
     */
    List<String> selectDutyUnit();

    String getPersonIdsByTime(@Param("date") LocalDate date);

    /**
     * 查询统计金安大数据-公共安全-趋势分析
     * <AUTHOR>
     */

    /**
     * 将GroupId置空
     * @param ids
     * @return
     */
    int updateGroupIdByGroupId(Long[] ids);
}
