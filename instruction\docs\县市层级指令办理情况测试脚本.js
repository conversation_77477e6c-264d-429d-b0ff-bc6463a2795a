/**
 * 县市层级指令办理情况功能测试脚本
 * 
 * 使用方法：
 * 1. 在浏览器开发者工具的Console中运行此脚本
 * 2. 或者在Node.js环境中安装axios后运行
 * 
 * 注意：请根据实际环境修改BASE_URL
 */

// 配置基础URL（请根据实际环境修改）
const BASE_URL = 'http://localhost:8080/instruction/cityHandling/county';

// 测试用例配置
const TEST_CASES = {
    // 统计数据测试
    stats: [
        {
            name: '查询所有县市区统计数据',
            params: {}
        },
        {
            name: '查询东阳市统计数据',
            params: { countyName: '东阳市' }
        },
        {
            name: '查询指定时间范围统计数据',
            params: { 
                startTime: '2025-01-01', 
                endTime: '2025-01-31' 
            }
        },
        {
            name: '查询义乌市指定时间范围统计数据',
            params: { 
                countyName: '义乌市',
                startTime: '2025-01-01', 
                endTime: '2025-01-31' 
            }
        }
    ],
    
    // 详细列表测试
    lists: [
        {
            name: '查询应接收未接收-县级部门列表',
            params: { 
                listType: 'noReceive', 
                deptType: 'county' 
            }
        },
        {
            name: '查询应接收未接收-乡镇街道列表',
            params: { 
                listType: 'noReceive', 
                deptType: 'town' 
            }
        },
        {
            name: '查询应处置未处置-县级部门列表',
            params: { 
                listType: 'noDispose', 
                deptType: 'county' 
            }
        },
        {
            name: '查询应处置未处置-乡镇街道列表',
            params: { 
                listType: 'noDispose', 
                deptType: 'town' 
            }
        },
        {
            name: '查询东阳市应接收未接收-县级部门列表',
            params: { 
                listType: 'noReceive', 
                deptType: 'county',
                countyName: '东阳市'
            }
        },
        {
            name: '查询指定时间范围应处置未处置-乡镇街道列表',
            params: { 
                listType: 'noDispose', 
                deptType: 'town',
                startTime: '2025-01-01',
                endTime: '2025-01-31'
            }
        }
    ]
};

// HTTP请求工具函数
async function makeRequest(url, params = {}) {
    try {
        // 如果在浏览器环境中使用fetch
        if (typeof fetch !== 'undefined') {
            const queryString = new URLSearchParams(params).toString();
            const fullUrl = queryString ? `${url}?${queryString}` : url;
            const response = await fetch(fullUrl);
            return await response.json();
        }
        
        // 如果在Node.js环境中使用axios
        if (typeof require !== 'undefined') {
            const axios = require('axios');
            const response = await axios.get(url, { params });
            return response.data;
        }
        
        throw new Error('No HTTP client available');
    } catch (error) {
        return {
            code: -1,
            msg: `请求失败: ${error.message}`,
            data: null
        };
    }
}

// 测试结果记录
const testResults = {
    passed: 0,
    failed: 0,
    total: 0,
    details: []
};

// 执行单个测试用例
async function runTestCase(testCase, endpoint) {
    console.log(`\n🧪 执行测试: ${testCase.name}`);
    console.log(`📋 参数:`, testCase.params);
    
    const startTime = Date.now();
    const result = await makeRequest(`${BASE_URL}${endpoint}`, testCase.params);
    const duration = Date.now() - startTime;
    
    testResults.total++;
    
    // 判断测试是否通过
    const passed = result.code === 200;
    if (passed) {
        testResults.passed++;
        console.log(`✅ 测试通过 (${duration}ms)`);
        console.log(`📊 返回数据:`, result.data);
    } else {
        testResults.failed++;
        console.log(`❌ 测试失败 (${duration}ms)`);
        console.log(`🚫 错误信息:`, result.msg);
    }
    
    // 记录测试详情
    testResults.details.push({
        name: testCase.name,
        passed,
        duration,
        params: testCase.params,
        result: result
    });
    
    return passed;
}

// 执行所有统计数据测试
async function runStatsTests() {
    console.log('\n🔍 开始执行统计数据测试...');
    console.log('=' .repeat(50));
    
    for (const testCase of TEST_CASES.stats) {
        await runTestCase(testCase, '/data');
        await new Promise(resolve => setTimeout(resolve, 100)); // 避免请求过快
    }
}

// 执行所有详细列表测试
async function runListTests() {
    console.log('\n📋 开始执行详细列表测试...');
    console.log('=' .repeat(50));
    
    for (const testCase of TEST_CASES.lists) {
        await runTestCase(testCase, '/list');
        await new Promise(resolve => setTimeout(resolve, 100)); // 避免请求过快
    }
}

// 测试导出功能
async function testExport() {
    console.log('\n📤 测试导出功能...');
    console.log('=' .repeat(50));
    
    try {
        const params = {
            countyName: '东阳市',
            startTime: '2025-01-01',
            endTime: '2025-01-31'
        };
        
        console.log(`📋 导出参数:`, params);
        
        // 注意：导出功能需要POST请求，这里只是模拟测试
        if (typeof fetch !== 'undefined') {
            const response = await fetch(`${BASE_URL}/export`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(params)
            });
            
            if (response.ok) {
                console.log('✅ 导出接口调用成功');
                console.log(`📄 响应类型: ${response.headers.get('content-type')}`);
            } else {
                console.log('❌ 导出接口调用失败');
                console.log(`🚫 状态码: ${response.status}`);
            }
        } else {
            console.log('ℹ️  导出功能测试需要在浏览器环境中进行');
        }
    } catch (error) {
        console.log(`❌ 导出测试失败: ${error.message}`);
    }
}

// 生成测试报告
function generateReport() {
    console.log('\n📊 测试报告');
    console.log('=' .repeat(50));
    console.log(`总测试数: ${testResults.total}`);
    console.log(`通过数: ${testResults.passed}`);
    console.log(`失败数: ${testResults.failed}`);
    console.log(`成功率: ${((testResults.passed / testResults.total) * 100).toFixed(2)}%`);
    
    if (testResults.failed > 0) {
        console.log('\n❌ 失败的测试用例:');
        testResults.details
            .filter(detail => !detail.passed)
            .forEach(detail => {
                console.log(`  - ${detail.name}: ${detail.result.msg}`);
            });
    }
    
    console.log('\n⏱️  性能统计:');
    const avgDuration = testResults.details.reduce((sum, detail) => sum + detail.duration, 0) / testResults.total;
    console.log(`平均响应时间: ${avgDuration.toFixed(2)}ms`);
    
    const maxDuration = Math.max(...testResults.details.map(detail => detail.duration));
    const slowestTest = testResults.details.find(detail => detail.duration === maxDuration);
    console.log(`最慢的测试: ${slowestTest.name} (${maxDuration}ms)`);
}

// 主测试函数
async function runAllTests() {
    console.log('🚀 开始执行县市层级指令办理情况功能测试');
    console.log(`🌐 测试环境: ${BASE_URL}`);
    console.log(`⏰ 开始时间: ${new Date().toLocaleString()}`);
    
    try {
        // 执行统计数据测试
        await runStatsTests();
        
        // 执行详细列表测试
        await runListTests();
        
        // 测试导出功能
        await testExport();
        
        // 生成测试报告
        generateReport();
        
    } catch (error) {
        console.error('❌ 测试执行过程中发生错误:', error);
    }
    
    console.log(`\n⏰ 结束时间: ${new Date().toLocaleString()}`);
    console.log('🎉 测试完成！');
}

// 数据验证函数
function validateStatsData(data) {
    const requiredFields = [
        'noReceiveCountyDeptCount',
        'noReceiveTownDeptCount',
        'noReceiveCountyInstructionCount',
        'noReceiveTownInstructionCount',
        'noDisposeCountyDeptCount',
        'noDisposeTownDeptCount',
        'noDisposeCountyInstructionCount',
        'noDisposeTownInstructionCount'
    ];

    return requiredFields.every(field =>
        data.hasOwnProperty(field) && typeof data[field] === 'number'
    );
}

function validateListData(data) {
    if (!Array.isArray(data)) return false;
    
    if (data.length === 0) return true; // 空数组也是有效的
    
    const requiredFields = ['id', 'instructionTitle', 'emergencyDegree', 'instructionType'];
    return data.every(item => 
        requiredFields.every(field => item.hasOwnProperty(field))
    );
}

// 如果在Node.js环境中直接运行
if (typeof module !== 'undefined' && module.exports) {
    module.exports = {
        runAllTests,
        runStatsTests,
        runListTests,
        testExport,
        validateStatsData,
        validateListData
    };
    
    // 如果直接运行此文件
    if (require.main === module) {
        runAllTests();
    }
}

// 如果在浏览器环境中，提供全局函数
if (typeof window !== 'undefined') {
    window.countyInstructionTest = {
        runAllTests,
        runStatsTests,
        runListTests,
        testExport
    };
    
    console.log('🔧 测试工具已加载，可以使用以下命令:');
    console.log('  - countyInstructionTest.runAllTests() // 运行所有测试');
    console.log('  - countyInstructionTest.runStatsTests() // 只运行统计测试');
    console.log('  - countyInstructionTest.runListTests() // 只运行列表测试');
    console.log('  - countyInstructionTest.testExport() // 测试导出功能');
}
