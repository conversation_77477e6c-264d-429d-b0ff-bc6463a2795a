package com.ruoyi.instruction.domain;

import java.util.Date;
import java.util.List;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 省级预警对象 t_province_yj
 * 
 * <AUTHOR>
 * @date 2023-09-21
 */
@Data
public class ProvinceYj extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键id */
    private Long id;

    /** 预警时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "预警时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date newsTime;

    /** 期数 */
    @Excel(name = "期数")
    private String newsNum;

    /** 标题 */
    @Excel(name = "标题")
    private String title;

    /** 概要 */
    @Excel(name = "概要")
    private String summary;

    /** 状态 1：正常 9：删除 */
    @Excel(name = "状态 1：正常 9：删除")
    private String status;

    private List<ProvinceYjDetails> detailsList;

    private String dateStr;


}
