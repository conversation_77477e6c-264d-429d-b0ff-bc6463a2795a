package com.ruoyi.instruction.domain;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 金华市行政区划编码对象 t_ad_code
 * 
 * <AUTHOR>
 * @date 2023-07-29
 */
public class AdCode extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** $column.columnComment */
    @Excel(name = "${comment}", readConverterExp = "$column.readConverterExp()")
    private String city;

    /** $column.columnComment */
    @Excel(name = "${comment}", readConverterExp = "$column.readConverterExp()")
    private String cityCode;

    /** $column.columnComment */
    @Excel(name = "${comment}", readConverterExp = "$column.readConverterExp()")
    private String county;

    /** $column.columnComment */
    @Excel(name = "${comment}", readConverterExp = "$column.readConverterExp()")
    private String countyCode;

    /** $column.columnComment */
    @Excel(name = "${comment}", readConverterExp = "$column.readConverterExp()")
    private String town;

    /** $column.columnComment */
    @Excel(name = "${comment}", readConverterExp = "$column.readConverterExp()")
    private String townCode;

    /** $column.columnComment */
    @Excel(name = "${comment}", readConverterExp = "$column.readConverterExp()")
    private String village;

    /** $column.columnComment */
    @Excel(name = "${comment}", readConverterExp = "$column.readConverterExp()")
    private String villageCode;

    /** $column.columnComment */
    @Excel(name = "${comment}", readConverterExp = "$column.readConverterExp()")
    private String grid;

    /** $column.columnComment */
    @Excel(name = "${comment}", readConverterExp = "$column.readConverterExp()")
    private String gridCode;

    public void setCity(String city) 
    {
        this.city = city;
    }

    public String getCity() 
    {
        return city;
    }
    public void setCityCode(String cityCode) 
    {
        this.cityCode = cityCode;
    }

    public String getCityCode() 
    {
        return cityCode;
    }
    public void setCounty(String county) 
    {
        this.county = county;
    }

    public String getCounty() 
    {
        return county;
    }
    public void setCountyCode(String countyCode) 
    {
        this.countyCode = countyCode;
    }

    public String getCountyCode() 
    {
        return countyCode;
    }
    public void setTown(String town) 
    {
        this.town = town;
    }

    public String getTown() 
    {
        return town;
    }
    public void setTownCode(String townCode) 
    {
        this.townCode = townCode;
    }

    public String getTownCode() 
    {
        return townCode;
    }
    public void setVillage(String village) 
    {
        this.village = village;
    }

    public String getVillage() 
    {
        return village;
    }
    public void setVillageCode(String villageCode) 
    {
        this.villageCode = villageCode;
    }

    public String getVillageCode() 
    {
        return villageCode;
    }
    public void setGrid(String grid) 
    {
        this.grid = grid;
    }

    public String getGrid() 
    {
        return grid;
    }
    public void setGridCode(String gridCode) 
    {
        this.gridCode = gridCode;
    }

    public String getGridCode() 
    {
        return gridCode;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("city", getCity())
            .append("cityCode", getCityCode())
            .append("county", getCounty())
            .append("countyCode", getCountyCode())
            .append("town", getTown())
            .append("townCode", getTownCode())
            .append("village", getVillage())
            .append("villageCode", getVillageCode())
            .append("grid", getGrid())
            .append("gridCode", getGridCode())
            .toString();
    }
}
