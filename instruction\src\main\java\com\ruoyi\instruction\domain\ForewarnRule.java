package com.ruoyi.instruction.domain;

import java.sql.Time;
import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 预警提醒规则对象 t_forewarn_rule
 *
 * <AUTHOR>
 * @date 2024-07-30
 */
@Data
public class ForewarnRule extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键id */
    private Long id;

    /** 预警类型 */
    @Excel(name = "预警类型")
    private String forewarnType;

    /** 预警名称 */
    @Excel(name = "预警名称")
    private String forewarnName;

    /** 启用模式 1：自动  2：手动 */
    @Excel(name = "启用模式 1：自动  2：手动")
    private String enableMode;

    /** 启用时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "启用时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date enableDate;

    /** 预警频次数 */
    @Excel(name = "预警频次数")
    private Integer forewarnNumber;

    /** 预警频次 1：时  2：周  3：月 */
    @Excel(name = "预警频次 1：时  2：天  3：周")
    private Integer forewarnFrequency;

    /** 时间范围开始时间 */
    @JsonFormat(pattern = "HH:mm:ss")
    @Excel(name = "时间范围开始时间", width = 30, dateFormat = "HH:mm:ss")
    private Time startTime;

    /** 时间范围结束时间 */
    @JsonFormat(pattern = "HH:mm:ss")
    @Excel(name = "时间范围结束时间", width = 30, dateFormat = "HH:mm:ss")
    private Time endTime;

    /** 预警规则 */
    @Excel(name = "预警规则")
    private String forewarnRule;

    /** 提醒模版 */
    @Excel(name = "提醒模版")
    private String remindTemplate;

    /** 启用状态 1：开  2：关 */
    @Excel(name = "启用状态 1：开  2：关")
    private String enableStatus;

}
