package com.ruoyi.examineAir.service;

import java.util.List;
import com.ruoyi.examineAir.domain.ExamineBillboard;

/**
 * 考核晾晒-红黄榜Service接口
 * 
 * <AUTHOR>
 * @date 2023-04-19
 */
public interface IExamineBillboardService 
{
    /**
     * 查询考核晾晒-红黄榜
     * 
     * @param id 考核晾晒-红黄榜主键
     * @return 考核晾晒-红黄榜
     */
    public ExamineBillboard selectExamineBillboardById(Long id);

    /**
     * 查询考核晾晒-红黄榜列表
     * 
     * @param examineBillboard 考核晾晒-红黄榜
     * @return 考核晾晒-红黄榜集合
     */
    public List<ExamineBillboard> selectExamineBillboardList(ExamineBillboard examineBillboard);

    /**
     * 新增考核晾晒-红黄榜
     * 
     * @param examineBillboard 考核晾晒-红黄榜
     * @return 结果
     */
    public int insertExamineBillboard(ExamineBillboard examineBillboard);

    /**
     * 修改考核晾晒-红黄榜
     * 
     * @param examineBillboard 考核晾晒-红黄榜
     * @return 结果
     */
    public int updateExamineBillboard(ExamineBillboard examineBillboard);

    /**
     * 批量删除考核晾晒-红黄榜
     * 
     * @param ids 需要删除的考核晾晒-红黄榜主键集合
     * @return 结果
     */
    public int deleteExamineBillboardByIds(Long[] ids);

    /**
     * 删除考核晾晒-红黄榜信息
     * 
     * @param id 考核晾晒-红黄榜主键
     * @return 结果
     */
    public int deleteExamineBillboardById(Long id);
}
