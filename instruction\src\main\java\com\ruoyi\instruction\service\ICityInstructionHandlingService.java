package com.ruoyi.instruction.service;

import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.instruction.domain.rspVo.CityHandlingOverviewRsp;
import com.ruoyi.instruction.domain.rspVo.CityHandlingTimeoutRsp;
import com.ruoyi.instruction.domain.rspVo.CityHandlingUnprocessedRsp;
import com.ruoyi.instruction.domain.rspVo.CityHandlingPendingFeedbackRsp;
import com.ruoyi.instruction.domain.rspVo.CountyInstructionHandlingRsp;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.util.List;
import java.util.Map;

/**
 * 市级指令办理情况服务接口
 *
 * <AUTHOR>
 * @date 2025-01-20
 */
public interface ICityInstructionHandlingService {

    /**
     * 获取市级指令办理情况概览
     *
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @param deptId 部门ID
     * @param deptName 部门名称
     * @return 办理情况概览
     */
    CityHandlingOverviewRsp getHandlingOverview(String startTime, String endTime, Long deptId, String deptName);

    TableDataInfo getTimeoutDisposeListV2(String startTime, String endTime, Integer end);

    TableDataInfo getTimeoutReceiveListV2(String startTime, String endTime, Integer end);

    /**
     * 获取超时接收和超时处置数据
     *
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @param deptId 部门ID
     * @param deptName 部门名称
     * @return 超时数据统计
     */
    CityHandlingTimeoutRsp getTimeoutData(String startTime, String endTime, Long deptId, String deptName);

    /**
     * 获取应处置未处置数据
     *
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @param deptId 部门ID
     * @param deptName 部门名称
     * @return 应处置未处置统计
     */
    CityHandlingUnprocessedRsp getUnprocessedData(String startTime, String endTime, Long deptId, String deptName);

    /**
     * 获取应处置未处置数据（区分部门和镇街）
     *
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @param deptId 部门ID
     * @param deptName 部门名称
     * @return 应处置未处置统计（区分县级部门和乡镇街道）
     */
    Map<String, Object> getUnprocessedDataDetailed(String startTime, String endTime, Long deptId, String deptName);

    /**
     * 获取待反馈数据（按紧急程度分类）
     *
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @param deptId 部门ID
     * @param deptName 部门名称
     * @return 待反馈数据统计
     */
    CityHandlingPendingFeedbackRsp getPendingFeedbackData(String startTime, String endTime, Long deptId, String deptName);

    /**
     * 获取超时接收指令列表
     *
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @param deptId 部门ID
     * @param deptName 部门名称
     * @return 超时接收指令列表
     */
    List<Map<String, Object>> getTimeoutReceiveList(String startTime, String endTime, Long deptId, String deptName);

    /**
     * 获取超时处置指令列表
     *
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @param deptId 部门ID
     * @param deptName 部门名称
     * @return 超时处置指令列表
     */
    List<Map<String, Object>> getTimeoutDisposeList(String startTime, String endTime, Long deptId, String deptName);

    /**
     * 获取应处置未处置指令列表
     *
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @param deptId 部门ID
     * @param deptName 部门名称
     * @return 应处置未处置指令列表
     */
    List<Map<String, Object>> getUnprocessedList(String startTime, String endTime, Long deptId, String deptName);

    /**
     * 获取待反馈指令列表（按紧急程度分类）
     *
     * @param emergencyType 紧急程度类型（7天/15天/30天）
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @param deptId 部门ID
     * @param deptName 部门名称
     * @return 待反馈指令列表
     */
    List<Map<String, Object>> getPendingFeedbackList(String emergencyType, String startTime, String endTime, Long deptId, String deptName);

    // ==================== 县市层级指令办理情况 ====================

    /**
     * 获取县市层级应接收未接收统计数据
     *
     * @param countyName 县市区名称（可选）
     * @param startTime 开始时间（按交办时间筛选）
     * @param endTime 结束时间（按交办时间筛选）
     * @return 应接收未接收统计数据
     */
    Map<String, Object> getCountyNoReceiveStats(String countyName, String startTime, String endTime);

    /**
     * 获取县市层级应处置未处置统计数据
     *
     * @param deptId 县市区名称（可选）
     * @param startTime 开始时间（按交办时间筛选）
     * @param endTime 结束时间（按交办时间筛选）
     * @return 应处置未处置统计数据
     */
    Map<String, Object> getCountyDisposeAndReceive(Boolean cityFlag, Long deptId, String startTime, String endTime);

    /**
     * 获取县市层级应接收未接收详细列表
     *
     * @param deptType 部门类型：county-县级部门，town-乡镇街道
     * @param countyName 县市区名称（可选）
     * @param startTime 开始时间（按交办时间筛选）
     * @param endTime 结束时间（按交办时间筛选）
     * @return 应接收未接收详细列表
     */
    List<CountyInstructionHandlingRsp> getCountyNoReceiveList(String deptType, String countyName, String startTime, String endTime);

    /**
     * 获取县市层级应处置未处置详细列表
     *
     * @param deptType 部门类型：county-县级部门，town-乡镇街道
     * @param countyName 县市区名称（可选）
     * @param startTime 开始时间（按交办时间筛选）
     * @param endTime 结束时间（按交办时间筛选）
     * @return 应处置未处置详细列表
     */
    List<CountyInstructionHandlingRsp> getCountyNoDisposeList(String deptType, String countyName, String startTime, String endTime);

    void exportHandlingData(HttpServletResponse response, String startTime, String endTime) throws IOException;

    void exportCountyHandling(HttpServletResponse response, Long deptId, String startTime, String endTime) throws IOException;
}
