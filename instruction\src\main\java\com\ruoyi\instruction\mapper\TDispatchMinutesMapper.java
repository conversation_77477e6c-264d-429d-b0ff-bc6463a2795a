package com.ruoyi.instruction.mapper;

import java.util.List;
import org.apache.ibatis.annotations.Mapper;
import com.ruoyi.instruction.domain.TDispatchMinutes;

/**
 * 调度纪要Mapper接口
 * 
 * <AUTHOR>
 * @date 2023-09-18
 */
@Mapper
public interface TDispatchMinutesMapper
{
    /**
     * 查询调度纪要
     * 
     * @param id 调度纪要ID
     * @return 调度纪要
     */
    public TDispatchMinutes selectTDispatchMinutesById(Long id);

    /**
     * 查询调度纪要列表
     * 
     * @param tDispatchMinutes 调度纪要
     * @return 调度纪要集合
     */
    public List<TDispatchMinutes> selectTDispatchMinutesList(TDispatchMinutes tDispatchMinutes);

    /**
     * 新增调度纪要
     * 
     * @param tDispatchMinutes 调度纪要
     * @return 结果
     */
    public int insertTDispatchMinutes(TDispatchMinutes tDispatchMinutes);

    /**
     * 修改调度纪要
     * 
     * @param tDispatchMinutes 调度纪要
     * @return 结果
     */
    public int updateTDispatchMinutes(TDispatchMinutes tDispatchMinutes);

    /**
     * 删除调度纪要
     * 
     * @param id 调度纪要ID
     * @return 结果
     */
    public int deleteTDispatchMinutesById(Long id);

    /**
     * 批量删除调度纪要
     * 
     * @param ids 需要删除的数据ID
     * @return 结果
     */
    public int deleteTDispatchMinutesByIds(Long[] ids);
}
