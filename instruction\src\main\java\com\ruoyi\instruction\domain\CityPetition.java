package com.ruoyi.instruction.domain;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;
import lombok.Data;

import java.util.Date;

@Data
public class CityPetition extends BaseEntity {

    private Long id;

    /** 问题属地 */
    @Excel(name = "问题属地",combo = {"婺城区","金东区","开发区","武义县","兰溪市","东阳市","磐安县","永康市","浦江县","义乌市"})
    private String problemPlace;

    /** 姓名 */
    @Excel(name = "姓名")
    private String personName;

    /** 身份证号码 */
    @Excel(name = "身份证号码")
    private String personCard;

    /** 手机号码 */
    @Excel(name = "手机号码")
    private String phone;

    /** 家庭地址 */
    @Excel(name = "家庭地址")
    private String address;

    /** 初重件 */
    @Excel(name = "初重件",combo = {"初件","重件"})
    private String eventType;

    /** 登记时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "登记时间(格式:yyyy-MM-dd)", width = 50, dateFormat = "yyyy-MM-dd")
    private Date gradeTime;

    private String gradeTimes;

    /** 内容分类 */
    @Excel(name = "内容分类")
    private String contentType;

    /** 二级分类 */
    @Excel(name = "二级分类")
    private String secondType;

    /** 诉求 */
    @Excel(name = "诉求")
    private String appeal;

    /** 接待情况 */
    @Excel(name = "接待情况")
    private String repeptionStatus;

    /** 批次 */
    @Excel(name = "批次")
    private String batch;

}
