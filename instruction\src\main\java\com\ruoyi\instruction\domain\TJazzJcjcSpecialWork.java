package com.ruoyi.instruction.domain;

import com.ruoyi.common.annotation.Excel;
import lombok.Data;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 基层基础-专项工作对象 t_jazz_jcjc_special_work
 * 
 * <AUTHOR>
 * @date 2023-07-17
 */
@Data
public class TJazzJcjcSpecialWork extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** $column.columnComment */
    private Long id;

    /** 应用级别 */
    @Excel(name = "应用级别")
    private String level;

    /** 重大应用名称 */
    @Excel(name = "重大应用名称")
    private String name;

    /** 协同单位 */
    @Excel(name = "协同单位")
    private String cooperativeUnit;

    /** 贯通计划 */
    @Excel(name = "贯通计划")
    private String connectionPlan;

    /** 年度计划 */
    @Excel(name = "年度计划")
    private String annualPlan;


}
