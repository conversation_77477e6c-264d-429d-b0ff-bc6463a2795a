package com.ruoyi.examineAir.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.examineAir.domain.ExamineBillboard;
import com.ruoyi.examineAir.service.IExamineBillboardService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 考核晾晒-红黄榜Controller
 * 
 * <AUTHOR>
 * @date 2023-04-19
 */
@RestController
@RequestMapping("/system/billboard")
public class ExamineBillboardController extends BaseController
{
    @Autowired
    private IExamineBillboardService examineBillboardService;

    /**
     * 查询考核晾晒-红黄榜列表
     */
//    @PreAuthorize("@ss.hasPermi('system:billboard:list')")
    @GetMapping("/list")
    public TableDataInfo list(ExamineBillboard examineBillboard)
    {
        startPage();
        List<ExamineBillboard> list = examineBillboardService.selectExamineBillboardList(examineBillboard);
        return getDataTable(list);
    }

    /**
     * 导出考核晾晒-红黄榜列表
     */
//    @PreAuthorize("@ss.hasPermi('system:billboard:export')")
    @Log(title = "考核晾晒-红黄榜", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, ExamineBillboard examineBillboard)
    {
        List<ExamineBillboard> list = examineBillboardService.selectExamineBillboardList(examineBillboard);
        ExcelUtil<ExamineBillboard> util = new ExcelUtil<ExamineBillboard>(ExamineBillboard.class);
        util.exportExcel(response, list, "考核晾晒-红黄榜数据");
    }

    /**
     * 获取考核晾晒-红黄榜详细信息
     */
//    @PreAuthorize("@ss.hasPermi('system:billboard:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(examineBillboardService.selectExamineBillboardById(id));
    }

    /**
     * 新增考核晾晒-红黄榜
     */
//    @PreAuthorize("@ss.hasPermi('system:billboard:add')")
    @Log(title = "考核晾晒-红黄榜", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody ExamineBillboard examineBillboard)
    {
        return toAjax(examineBillboardService.insertExamineBillboard(examineBillboard));
    }

    /**
     * 修改考核晾晒-红黄榜
     */
//    @PreAuthorize("@ss.hasPermi('system:billboard:edit')")
    @Log(title = "考核晾晒-红黄榜", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody ExamineBillboard examineBillboard)
    {
        return toAjax(examineBillboardService.updateExamineBillboard(examineBillboard));
    }

    /**
     * 删除考核晾晒-红黄榜
     */
//    @PreAuthorize("@ss.hasPermi('system:billboard:remove')")
    @Log(title = "考核晾晒-红黄榜", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(examineBillboardService.deleteExamineBillboardByIds(ids));
    }
}
