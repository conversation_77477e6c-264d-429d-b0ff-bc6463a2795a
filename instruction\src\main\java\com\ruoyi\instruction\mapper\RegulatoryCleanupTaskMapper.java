package com.ruoyi.instruction.mapper;

import java.util.List;
import com.ruoyi.instruction.domain.RegulatoryCleanupTask;

/**
 * 挂牌整治任务清单Mapper接口
 * 
 * <AUTHOR>
 * @date 2023-08-10
 */
public interface RegulatoryCleanupTaskMapper 
{
    /**
     * 查询挂牌整治任务清单
     * 
     * @param id 挂牌整治任务清单主键
     * @return 挂牌整治任务清单
     */
    public RegulatoryCleanupTask selectRegulatoryCleanupTaskById(Long id);

    /**
     * 查询挂牌整治任务清单列表
     * 
     * @param regulatoryCleanupTask 挂牌整治任务清单
     * @return 挂牌整治任务清单集合
     */
    public List<RegulatoryCleanupTask> selectRegulatoryCleanupTaskList(RegulatoryCleanupTask regulatoryCleanupTask);

    /**
     * 新增挂牌整治任务清单
     * 
     * @param regulatoryCleanupTask 挂牌整治任务清单
     * @return 结果
     */
    public int insertRegulatoryCleanupTask(RegulatoryCleanupTask regulatoryCleanupTask);

    /**
     * 修改挂牌整治任务清单
     * 
     * @param regulatoryCleanupTask 挂牌整治任务清单
     * @return 结果
     */
    public int updateRegulatoryCleanupTask(RegulatoryCleanupTask regulatoryCleanupTask);

    /**
     * 删除挂牌整治任务清单
     * 
     * @param id 挂牌整治任务清单主键
     * @return 结果
     */
    public int deleteRegulatoryCleanupTaskById(Long id);

    /**
     * 批量删除挂牌整治任务清单
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteRegulatoryCleanupTaskByIds(Long[] ids);
}
