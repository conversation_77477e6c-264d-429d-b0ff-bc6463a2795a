package com.ruoyi.instruction.controller;

import java.util.Date;
import java.util.List;
import javax.servlet.http.HttpServletResponse;

import com.ruoyi.common.core.domain.entity.SysDept;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.instruction.domain.Risk;
import com.ruoyi.instruction.domain.RiskAssign;
import com.ruoyi.instruction.service.impl.InstructionInfoServiceImpl;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.instruction.domain.RiskFeedback;
import com.ruoyi.instruction.service.IRiskFeedbackService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 风险交办-反馈Controller
 *
 * <AUTHOR>
 * @date 2025-02-24
 */
@RestController
@RequestMapping("/risk/feedback")
public class RiskFeedbackController extends BaseController
{
    @Autowired
    private IRiskFeedbackService riskFeedbackService;

    /**
     * 查询风险交办-反馈列表
     */
    // @PreAuthorize("@ss.hasPermi('risk:feedback:list')")
    @GetMapping("/list")
    public TableDataInfo list(RiskFeedback riskFeedback)
    {
        startPage();
        List<RiskFeedback> list = riskFeedbackService.selectRiskFeedbackList(riskFeedback);
        return getDataTable(list);
    }

    /**
     * 导出风险交办-反馈列表
     */
    // @PreAuthorize("@ss.hasPermi('risk:feedback:export')")
    @Log(title = "风险交办-反馈", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, RiskFeedback riskFeedback)
    {
        List<RiskFeedback> list = riskFeedbackService.selectRiskFeedbackList(riskFeedback);
        ExcelUtil<RiskFeedback> util = new ExcelUtil<RiskFeedback>(RiskFeedback.class);
        util.exportExcel(response, list, "风险交办-反馈数据");
    }

    /**
     * 获取风险交办-反馈详细信息
     */
    // @PreAuthorize("@ss.hasPermi('risk:feedback:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(riskFeedbackService.selectRiskFeedbackById(id));
    }

    /**
     * 新增风险交办-反馈
     */
    // @PreAuthorize("@ss.hasPermi('risk:feedback:add')")
    @Log(title = "风险交办-反馈", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody RiskFeedback riskFeedback)
    {
        SysDept dept = SecurityUtils.getLoginUser().getUser().getDept();
        dept = InstructionInfoServiceImpl.getCountyDeptInfo(dept);
        riskFeedback.setFeedbackDeptId(dept.getDeptId());
        riskFeedback.setFeedbackUnit(dept.getDeptName());
        return toAjax(riskFeedbackService.insertRiskFeedback(riskFeedback));
    }

    /**
     * 修改风险交办-反馈
     */
    // @PreAuthorize("@ss.hasPermi('risk:feedback:edit')")
    @Log(title = "风险交办-反馈", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody RiskFeedback riskFeedback)
    {
        if (riskFeedback.getIsPass()==2){
            riskFeedback.setPassTime(new Date());
        }
        return toAjax(riskFeedbackService.updateRiskFeedback(riskFeedback));
    }

    // @PreAuthorize("@ss.hasPermi('risk:feedback:edit')")
    @Log(title = "风险交办-退回", businessType = BusinessType.UPDATE)
    @PutMapping("/turnDown")
    public AjaxResult turnDown(@RequestBody RiskAssign riskAssign)
    {
        List<RiskFeedback> riskFeedbackList = riskAssign.getRiskFeedbackList();
        for (RiskFeedback riskFeedback : riskFeedbackList) {
            if (riskFeedback.getId() != null) {
                continue;
            }
            if (riskFeedback.getIsPass() == 2) {
                riskFeedback.setPassTime(new Date());
            }
            String feedbackDeptIds = riskFeedback.getFeedbackDeptIds();
            if (feedbackDeptIds == null || feedbackDeptIds.trim().isEmpty()) {
                // 如果输入为空或仅包含空白字符，直接返回或记录日志
                continue;
            }
            String[] split = feedbackDeptIds.split(",");
            //遍历split
            for (String s : split) {
                riskFeedback.setPassPerson(SecurityUtils.getUsername());
                riskFeedback.setFeedbackDeptId(Long.parseLong(s));
                riskFeedbackService.updateRiskFeedback(riskFeedback);
            }
        }
        return AjaxResult.success();
    }

    /**
     * 删除风险交办-反馈
     */
    // @PreAuthorize("@ss.hasPermi('risk:feedback:remove')")
    @Log(title = "风险交办-反馈", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(riskFeedbackService.deleteRiskFeedbackByIds(ids));
    }
}
