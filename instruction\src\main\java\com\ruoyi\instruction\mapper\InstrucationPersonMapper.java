package com.ruoyi.instruction.mapper;

import java.time.LocalDate;
import java.util.*;

import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.instruction.domain.InstrucationPerson;
import com.ruoyi.instruction.domain.InstructionGroup;
import com.ruoyi.instruction.domain.rspVo.BigScreenJazzCommonVo;
import com.ruoyi.instruction.domain.rspVo.GroupDataRspVo;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

/**
 * 指令关联人员信息Mapper接口
 *
 * <AUTHOR>
 * @date 2022-12-15
 */
@Repository
public interface InstrucationPersonMapper {
    /**
     * 查询指令关联人员信息
     *
     * @param id 指令关联人员信息主键
     * @return 指令关联人员信息
     */
    public InstrucationPerson selectInstrucationPersonById(Long id);

    /**
     * 查询指令关联人员信息列表
     *
     * @param instrucationPerson 指令关联人员信息
     * @return 指令关联人员信息集合
     */
    public List<InstrucationPerson> selectInstrucationPersonList(InstrucationPerson instrucationPerson);
    /**
     * 查询指令关联人员信息列表
     *
     * @param instrucationPerson 指令关联人员信息
     * @return 指令关联人员信息集合
     */
    public List<InstrucationPerson> selectPersonByName(InstrucationPerson instrucationPerson);
    /**
     * 查询指令关联人员信息列表
     *
     * @param instrucationPerson 指令关联人员信息
     * @return 指令关联人员信息集合
     */
    public List<InstrucationPerson> selectInstrucationPersonListForBigScreen(InstrucationPerson instrucationPerson);

    /**
     * 新增指令关联人员信息
     *
     * @param instrucationPerson 指令关联人员信息
     * @return 结果
     */
    @Log(title = "指令关联人员信息", businessType = BusinessType.INSERT)
    public int insertInstrucationPerson(InstrucationPerson instrucationPerson);

    /**
     * 修改指令关联人员信息
     *
     * @param instrucationPerson 指令关联人员信息
     * @return 结果
     */
    public int updateInstrucationPerson(InstrucationPerson instrucationPerson);

    /**
     * 删除指令关联人员信息
     *
     * @param id 指令关联人员信息主键
     * @return 结果
     */
    public int deleteInstrucationPersonById(Long id);

    /**
     * 批量删除指令关联人员信息
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteInstrucationPersonByIds(Long[] ids);

    /**
     * 通过身份证号查询用户
     * @param personName  人员姓名
     * @param idCard 身份证号
     * @return
     */
    InstrucationPerson selectInstrucationPersonByIdCard(@Param("personName") String personName, @Param("idCard") String idCard);

    /**
     * 通过人员ids以及名称查询人员
     * @param ids  人员id
     * @param personName 人员姓名
     * @param dutyPlace  责任所属地
     * @return
     */
    List<InstrucationPerson> selectInstrucationPersonByIds(@Param("ids") String[] ids, @Param("personName") String personName, @Param("dutyPlace") String dutyPlace);


    /**
     * 获取各省的信息
     *
     * @return
     */
    List<HashMap> getProvInfo();

    /**
     * 获取各市信息
     *
     * @param code
     * @return
     */
    List<HashMap> getCityByCode(@Param("code") String code);

    /**
     * 根据code查询县市区
     *
     * @param code
     * @return
     */
    List<HashMap> getCountryByCode(@Param("code") String code);

    /**
     * 通过人员id、人员姓名、身份证号查询人员信息
     *
     * @param id
     * @param personName
     * @param idCard
     * @return
     */
    List<InstrucationPerson> findPersonByParam(@Param("id") Long id, @Param("personName") String personName, @Param("idCard") String idCard);

    /**
     * 更新重点人员管控等级
     *
     * @param level
     * @param lowerPersonIds
     */
    void updatePersonControlLevel(@Param("level") String level, @Param("list") List<Long> lowerPersonIds);

    /**
     * 获取总人数、高、中、低人数
     * @param map
     * @return
     */
    GroupDataRspVo getPersonData(Map<String,Object> map);

    /**
     * 每日新增人数
     * @return
     */
    List<Map<String, Integer>> getAddPerson();

    /**
     * 获取区域重点人员数量
     * @return
     */
    List<Map<String, Integer>> getAreaPersonCount();

    /**
     * 人员关联事件排名
     * @param startTime
     * @param endTime
     * @return
     */
    List<Map<String, Object>> getEventCount(@Param("startTime")Date startTime,@Param("endTime")Date endTime );

    /**
     * 获取频繁重点人员
     * @return
     */
    List<Map<String, Integer>> getOftenPerson();

    /**
     * 查询人员的责任所在地，去重
     * @param lowerPersonIds
     * @return
     */
    List<String> selectDutyPlace(@Param("list") List<Long> lowerPersonIds);

    /**
     * 统计人员的责任所在地
     * @param lowerPersonIds
     * @return
     */
    List<BigScreenJazzCommonVo> countDutyPlace(@Param("list") List<Long> lowerPersonIds);

    /**
     * 根据人员ids查询数量
     * @param collect
     * @return
     */
    int findPersonCountById(@Param("list") List<String> collect);

    /**
     * 查询人员的群体
     * @param id
     * @return
     */
    List<String> selectGroupNameById(@Param("id") String id);

    /**
     * 查询人员关联群体数量
     * @param date
     * @param thresholdValue
     * @param thresholdRule
     * @return
     */
    List<Map<String, Object>> groupPersonCount(@Param("date") LocalDate date, @Param("thresholdValue") Integer thresholdValue, @Param("thresholdRule") String thresholdRule);

    /**
     * 根据事件类型统计人员数量，获取前10
     * @param instrucationPerson
     * @return
     */
    List<BigScreenJazzCommonVo> countPersonByEventTop10(InstrucationPerson instrucationPerson);

    /**
     * 根据人员id查询关联群体名称
     * @param id
     * @return
     */
    String findGroupNameByPersonId(@Param("id") Long id);

    /**
     * 根据人员查询
     * @param split
     * @param dutyPlace
     * @return
     */
    List<InstrucationPerson> selectPersonIds(@Param("ids") String[] split,@Param("dutyPlace") String dutyPlace);

    /**
     * 统计
     * @param split
     * @return
     */
    List<HashMap<String,Object>> selectPersonIdsGroup(@Param("ids") String[] split);

    /**
     * 根据人员ids查询人员
     * @param collect
     * @return
     */
    List<InstrucationPerson> findPersonDetailsById(@Param("list") List<String> collect);


    /**
     * 根据人名查询人员列表
     * @param instrucationPerson
     * @return
     */
    List<InstrucationPerson> selectPersonByNameNew(InstrucationPerson instrucationPerson);

    Integer getTotalPerson();

    /**
     * 根据人员ids查询
     * @param personIds
     * @return
     */
    List<InstrucationPerson> selectPersonByIds(String[] personIds);

    /**
     * 根据人员ids查询各县市区分布情况
     * @param ids
     * @return
     */
    List<Map<String, Object>> getGroupCountyDistribution(@Param("ids") List<String> ids);

    /**
     * 更新人员类型
     * @param personTypeIds
     */
    void updatePersonType(@Param("list") List<Long> personTypeIds,@Param("personType") int personType);

    /**
     * 导出
     * @param instrucationPerson
     * @return
     */
    List<InstrucationPerson> selectInstrucationPersonListForExport(InstrucationPerson instrucationPerson);

    /**
     * 查询人员信息（异动数、牵头数、事件类型）
     * @param ids
     * @return
     */
    List<InstrucationPerson> selectPersonData(@Param("list")List<Long> ids);

    /**
     * 根据人员id查询类型名称
     * @param id
     * @return
     */
    String selectTypeNameById(@Param("id") Long id);

    /**
     * 根据群体id查询群体相关数据
     * @param group
     * @return
     */
    List<Map<String, Object>> getGroupDataForYiDong(InstructionGroup group);

    /**
     * 查询各县市区人员总数
     * @return
     */
    List<Map<String, Object>> selectCountyPersonSum();

    /**
     * 根据人员id查询人员信息
     * @param idsSet
     * @return
     */
    List<InstrucationPerson> findGroupNameByIds(Set<Long> idsSet);

    /**
     * 批量更新用户关联群体
     * @param personList
     */
    void batchUpdatePersonGroupName(List<InstrucationPerson> personList);

    /**
     * 测试更新人员关联群体
     * @param personList
     */
    void testUpdatePersonGroupName(List<InstrucationPerson> personList);

    /**
     * 删除事件同步更新人员关联群体
     * @param personIds
     */
    void deleteEventUpdatePersonGroupName(List<String> personIds);

    /**
     * 获取人员等级统计
     * @return
     * @param instrucationPerson
     */
    List<Map<String, Object>> getLevelCount(final InstrucationPerson instrucationPerson);

    /**
     * 获取县市区等级统计
     * @return
     */
    List<Map<String, Object>> getCountyCount(final InstrucationPerson instrucationPerson);

    /**
     * 获取县市区等级统计
     * @param instrucationPerson
     * @return
     */
    List<Map<String, Object>> getCountyLevelStatistics(InstrucationPerson instrucationPerson);

    /**
     * 根据人员id及责任属地、乡镇街道查询人员
     * @param ids
     * @param personName
     * @param dutyPlace
     * @param deptName
     * @return
     */
    List<InstrucationPerson> selectInstrucationPersonByIdsAndDept(@Param("ids") String[] ids, @Param("personName") String personName, @Param("dutyPlace") String dutyPlace,@Param("deptName") String deptName);

    /**
     * 根据人员id更新走访记录
     * @param personId
     */
    void updateInterViewInfo(@Param("personId") Long personId);

    /**
     * 人员走访记录为空,更新走访记录及走访时间为null
     * @param personId
     */
    void updateInterViewInfoToNull(@Param("personId") Long personId);

    /**
     * 根据人员id和部门名称查询人员数量
     * @param collect
     * @param deptName
     * @return
     */
    int findPersonCountByIdAndDeptName(@Param("list") List<String> collect, @Param("deptName") String deptName);

    /**
     * 更新人员打击次数
     * @param idCard
     */
    void updatePersonStrikeCount(@Param("idCard") String idCard);

    /**
     * 更新人员库中赴省进京次数
     */
    void updateCalculateAndProvinceNum(List<InstrucationPerson> finallyList);


    /**
     * 查询人员赴省进京次数
     * @return
     */
    List<InstrucationPerson> findCalculateAndProvinceNum();

    /**
     * 查询新关注人员id
     * @return
     */
    List<Long> findIsFollowPersonId();

    /**
     * 根据人员id批量更新新关注人员
     * @param personIds
     */
    void updateIsFollowPerson(@Param("personIds") List<Long> personIds);

    /**
     * 根据条件查询需要预警人员信息
     * @param level
     * @param dateBefore
     * @param count
     * @return
     */
    List<InstrucationPerson> findForeWarnPerson(@Param("level") Integer level,@Param("dateBefore") Date dateBefore,@Param("count") Integer count);

    /**
     * 同步人员走访记录时间
     */
    void updatePersonInterviewTime();

    /**
     * 批量更新用户性别
     * @param personList
     */
    void batchUpdatePersonSex(List<InstrucationPerson> personList);

    /**
     * 查询当年附省进京人员
     * @return
     */
    List<InstrucationPerson> findProvinceCapitalYear();

    /**
     * 查询重点人员信息
     * @return
     */
    List<InstrucationPerson> findPLevelInfo();

    /**
     * 查询近3年赴省进京的人员信息
     * @return
     */
    List<InstrucationPerson> findProvinceCapitalThreeYear();

    /**
     * 查询近一年事件牵头人员ids
     * @return
     */
    String findEventPersonIdsYear();

    /**
     * 更新人员赴省进京次数
     * @param instrucationPerson
     */
    void updatePersonProvinceCapital(InstrucationPerson instrucationPerson);

    /**
     * 统计红橙黄人员数
     * @return
     */
    List<Map> getStatisticsOne();

    /**
     * 人员类型分组查询
     * @return
     */
    List<Map<String, Object>> getPersonType(@Param("dutyPlace") String countyName);

    /**
     * 根据条件获取重点人员异动情况
     * @param person
     * @return
     */
    List<InstrucationPerson> getPersonTransaction(InstrucationPerson person);

    /**
     * 获取县市区重点人员分布
     * @return
     */
    List<Map<String, Object>> getPersonCount();

    /**
     * 获取人员走访情况
     * @return
     */
    List<InstrucationPerson> selectPersonInterviewCondition(String dutyPlace);

    /**
     * 获取重点人员
     * @return
     */
    Long getImportanceCount();

    List<InstrucationPerson> selectImportancePersonByIds(String[] split);

    List<InstrucationPerson> selectYdPersonByIds(@Param("ids") String[] split, @Param("dutyPlace") String countyName);

    List<InstrucationPerson> groupPersonList(@Param("ids") String[] split, @Param("dutyPlace") String countyName);

    List<InstrucationPerson> findLxPersonEvent(@Param("ids") String[] split, @Param("deptId") final Long deptId, @Param("dutyPlace") final String countyName);
}
