package com.ruoyi.instruction.mapper;

import java.util.List;
import java.util.Map;

import com.ruoyi.instruction.domain.YjxzInfo;

/**
 * 应急小组名称Mapper接口
 *
 * <AUTHOR>
 * @date 2023-08-06
 */
public interface YjxzInfoMapper
{
    /**
     * 查询应急小组名称
     *
     * @param id 应急小组名称主键
     * @return 应急小组名称
     */
    public YjxzInfo selectYjxzInfoById(Long id);

    /**
     * 查询应急小组名称列表
     *
     * @param yjxzInfo 应急小组名称
     * @return 应急小组名称集合
     */
    public List<YjxzInfo> selectYjxzInfoList(YjxzInfo yjxzInfo);

    /**
     * 新增应急小组名称
     *
     * @param yjxzInfo 应急小组名称
     * @return 结果
     */
    public int insertYjxzInfo(YjxzInfo yjxzInfo);

    /**
     * 修改应急小组名称
     *
     * @param yjxzInfo 应急小组名称
     * @return 结果
     */
    public int updateYjxzInfo(YjxzInfo yjxzInfo);

    /**
     * 删除应急小组名称
     *
     * @param id 应急小组名称主键
     * @return 结果
     */
    public int deleteYjxzInfoById(Long id);

    /**
     * 批量删除应急小组名称
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteYjxzInfoByIds(Long[] ids);

    /**
     * 获取应急劝返小组信息
     * @return
     */
    List<Map<String, Object>> getYjqfInfo();

    List<Map<String,Object>> getBizType();
}
