package com.ruoyi.instruction.mapper;

import java.util.List;

import com.ruoyi.common.annotation.DataSource;
import com.ruoyi.common.enums.DataSourceType;
import com.ruoyi.instruction.domain.QbtbZwwRyyj;

/**
 * 人员预警Mapper接口
 *
 * <AUTHOR>
 * @date 2023-06-03
 */
public interface QbtbZwwRyyjMapper {
    /**
     * 查询人员预警
     *
     * @param nXh 人员预警主键
     * @return 人员预警
     */
    public QbtbZwwRyyj selectQbtbZwwRyyjByNXh(Long nXh);

    /**
     * 查询人员预警列表
     *
     * @param qbtbZwwRyyj 人员预警
     * @return 人员预警集合
     */
    public List<QbtbZwwRyyj> selectQbtbZwwRyyjList(QbtbZwwRyyj qbtbZwwRyyj);

    /**
     * 新增人员预警
     *
     * @param qbtbZwwRyyj 人员预警
     * @return 结果
     */
    public int insertQbtbZwwRyyj(QbtbZwwRyyj qbtbZwwRyyj);

    /**
     * 修改人员预警
     *
     * @param qbtbZwwRyyj 人员预警
     * @return 结果
     */
    public int updateQbtbZwwRyyj(QbtbZwwRyyj qbtbZwwRyyj);

    /**
     * 删除人员预警
     *
     * @param nXh 人员预警主键
     * @return 结果
     */
    public int deleteQbtbZwwRyyjByNXh(Long nXh);

    /**
     * 批量删除人员预警
     *
     * @param nXhs 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteQbtbZwwRyyjByNXhs(Long[] nXhs);
}
