package com.ruoyi.instruction.domain.rspVo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.Date;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/4/1 14:42
 * 风险排查详情导入错误信息
 */
@Data
public class RiskDetailsErrorVo extends BaseEntity {
    private static final long serialVersionUID = 1L;


    /** 标题 */
    @Excel(name = "标题(必填)")
    private String title;

    /** 排查类别大类 */
    @Excel(name = "排查类别(必填)"
            , readConverterExp = "51=涉众投资,52=问题楼盘,53=拆迁安置,54=政策待遇,58=实体商铺,59=教育领域,60=欠薪群体,61=生态环境,62=医疗纠纷,63=涉法涉诉,64=生产安全,65=涉政治类,66=网络安全,67=新业态,68=其他"
            ,combo = {"涉众投资","问题楼盘","拆迁安置","政策待遇","实体商铺","教育领域","欠薪群体","生态环境","医疗纠纷","涉法涉诉","生产安全","涉政治类","网络安全","新业态","其他"})
    private Long checkBig;

    /** 风险等级 1：重大  2：高  3：中  4：低 */
    @Excel(name = "风险等级(必填)",readConverterExp = "1=重大,2=高,3=中,4=低",combo = {"重大","高","中","低"})
    private String level;

    /** 责任单位 */
    @Excel(name = "属地(必填)")
    private String dutyUnit;

    /** 所属区域名称 */
    @Excel(name = "责任乡镇(必填)")
    private String areaName;

    /** 挂牌督办 */
    @Excel(name = "挂牌督办(非必填)")
    private String handel;

    /** 责任人 */
    @Excel(name = "责任人(必填)")
    private String dutyPerson;

    /** 化解情况 1: 未化解  2：已化解 */
    @Excel(name = "化解情况(必填)",readConverterExp = "1=未化解,2=已化解",combo = {"未化解","已化解"})
    private String dissolution;

    /** 化解时限 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "化解时限(必填 格式:yyyy-MM-dd )", width = 30, dateFormat = "yyyy-MM-dd")
    private Date dissolutionTime;

    /** 基本情况 */
    @Excel(name = "基本情况(必填)")
    private String basicSituation;

    /** 主要诉求 */
    @Excel(name = "主要诉求(必填)")
    private String mainApply;

    /** 正式化解措施 */
    @Excel(name = "化解措施(必填)",combo = {"建立工作专班","落实领导包案","明确化解期限","分步实施推进"})
    private String measure;

    @Excel(name = "错误信息")
    private String message;

}
