package com.ruoyi.instruction.domain;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 挂牌整治任务清单对象 t_regulatory_cleanup_task
 * 
 * <AUTHOR>
 * @date 2023-08-10
 */
public class RegulatoryCleanupTask extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 序号 */
    private Long id;

    /** 事项名称 */
    @Excel(name = "事项名称")
    private String itemName;

    /** 简要情况 */
    @Excel(name = "简要情况")
    private String briefDescription;

    /** 责任单位 */
    @Excel(name = "责任单位")
    private String responsibleUnit;

    /** 责任人 */
    @Excel(name = "责任人")
    private String responsiblePerson;

    /** 化解时限 */
    @Excel(name = "化解时限")
    private String resolutionDeadline;

    /** 备注 */
    @Excel(name = "备注")
    private String remarks;

    /** 化解情况 */
    @Excel(name = "化解情况")
    private String remediationStatus;

    /** 状态 1：正常  9：删除 */
    @Excel(name = "状态 1：正常  9：删除")
    private Integer status;

    public void setId(Long id) 
    {
        this.id = id;
    }

    public Long getId() 
    {
        return id;
    }
    public void setItemName(String itemName) 
    {
        this.itemName = itemName;
    }

    public String getItemName() 
    {
        return itemName;
    }
    public void setBriefDescription(String briefDescription) 
    {
        this.briefDescription = briefDescription;
    }

    public String getBriefDescription() 
    {
        return briefDescription;
    }
    public void setResponsibleUnit(String responsibleUnit) 
    {
        this.responsibleUnit = responsibleUnit;
    }

    public String getResponsibleUnit() 
    {
        return responsibleUnit;
    }
    public void setResponsiblePerson(String responsiblePerson) 
    {
        this.responsiblePerson = responsiblePerson;
    }

    public String getResponsiblePerson() 
    {
        return responsiblePerson;
    }
    public void setResolutionDeadline(String resolutionDeadline) 
    {
        this.resolutionDeadline = resolutionDeadline;
    }

    public String getResolutionDeadline() 
    {
        return resolutionDeadline;
    }
    public void setRemarks(String remarks) 
    {
        this.remarks = remarks;
    }

    public String getRemarks() 
    {
        return remarks;
    }
    public void setRemediationStatus(String remediationStatus) 
    {
        this.remediationStatus = remediationStatus;
    }

    public String getRemediationStatus() 
    {
        return remediationStatus;
    }
    public void setStatus(Integer status) 
    {
        this.status = status;
    }

    public Integer getStatus() 
    {
        return status;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("itemName", getItemName())
            .append("briefDescription", getBriefDescription())
            .append("responsibleUnit", getResponsibleUnit())
            .append("responsiblePerson", getResponsiblePerson())
            .append("resolutionDeadline", getResolutionDeadline())
            .append("remarks", getRemarks())
            .append("remediationStatus", getRemediationStatus())
            .append("status", getStatus())
            .toString();
    }
}
