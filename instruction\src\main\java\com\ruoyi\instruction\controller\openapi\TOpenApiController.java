package com.ruoyi.instruction.controller.openapi;

import java.util.*;
import java.util.stream.Collectors;

import com.ruoyi.common.core.domain.entity.SysUser;
import com.ruoyi.common.core.domain.model.LoginUser;
import com.ruoyi.common.exception.GlobalException;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.framework.web.service.SysPermissionService;
import com.ruoyi.framework.web.service.TokenService;
import com.ruoyi.instruction.domain.*;
import com.ruoyi.instruction.enums.AFReceiveUnit;
import com.ruoyi.instruction.mapper.IndicatorTypeMapper;
import com.ruoyi.instruction.mapper.OpenUndercoverInspectionDetailsMapper;
import com.ruoyi.instruction.service.IIndicatorTypeService;
import com.ruoyi.instruction.service.IInstructionFileService;
import com.ruoyi.instruction.service.ITOpenUndercoverInspectionService;
import com.ruoyi.system.mapper.SysUserMapper;
import com.ruoyi.system.service.ISysUserService;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.instruction.service.ITOpenApiService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import lombok.extern.slf4j.Slf4j;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 第三方对接配置Controller
 *
 * <AUTHOR>
 * @date 2023-07-20
 */
@RestController
@RequestMapping("/instruction/openApi")
@Slf4j
public class TOpenApiController extends BaseController {
    @Autowired
    private ITOpenApiService tOpenApiService;

    @Autowired
    private TokenService tokenService;
    @Autowired
    private ITOpenUndercoverInspectionService openUndercoverInspectionService;
    @Autowired
    private ISysUserService sysUserService;
    @Autowired
    private SysPermissionService permissionService;
    @Autowired
    private IndicatorTypeMapper indicatorTypeMapper;
    @Autowired
    private IInstructionFileService instructionFileService;
    @Autowired
    private OpenUndercoverInspectionDetailsMapper openUndercoverInspectionDetailsMapper;

    @PostMapping("getToken")
    public AjaxResult getToken(@RequestBody TOpenApi tOpenApi) {
        if (StringUtils.isEmpty(tOpenApi.getAppKey()) || StringUtils.isEmpty(tOpenApi.getAppSecret())) {
            throw new GlobalException("参数异常");
        }
        List<TOpenApi> tOpenApis = tOpenApiService.selectTOpenApiList(tOpenApi);
        if (CollectionUtils.isEmpty(tOpenApis)) {
            throw new GlobalException("异常码:100，请联系管理员");
        }
        TOpenApi tOpenApi1 = tOpenApis.get(0);
//        Set<String> collect = Arrays.stream(tOpenApi1.getPermissions().split(",")).collect(Collectors.toSet());
        Set<String> menuPermission = new HashSet<>();
        SysUser sysUser = new SysUser();
        sysUser.setUserId(Long.parseLong(tOpenApi1.getPermissions()));
        sysUser.setParams(new HashMap<>());
        System.out.println(sysUser == null);
        SysUser sysUsers = sysUserService.selectUserById(Long.parseLong(tOpenApi1.getPermissions()));
        menuPermission = permissionService.getMenuPermission(sysUsers);
//        if (!CollectionUtils.isEmpty(sysUsers)){
//             menuPermission = permissionService.getMenuPermission(sysUsers.get(0));
//        }
        if (CollectionUtils.isEmpty(menuPermission)) {
            return AjaxResult.error("异常码101,请联系管理员");
        }
        sysUser.setUserName(tOpenApi1.getName());
        LoginUser loginUser = new LoginUser(null, null, sysUser, menuPermission);
        // 生成token
        String token = tokenService.createToken(loginUser);
        HashMap hashMap = new HashMap();
        hashMap.put("token", token);
        return AjaxResult.success(hashMap);
    }

//    /**
//     * 上传暗访督察
//     * @param undercoverInspections
//     * @return
//     */
//    @PreAuthorize("@ss.hasPermi('openApi:uploadUndercoverInspection')")
//    @PostMapping("uploadUndercoverInspection")
//    public  AjaxResult uploadUndercoverInspection(@RequestBody List<TOpenUndercoverInspection> undercoverInspections ){
//        log.info("暗访督察上传数据："+undercoverInspections.toString());
//        int i =0;
//        if (!CollectionUtils.isEmpty(undercoverInspections)){
//            for (TOpenUndercoverInspection undercoverInspection:undercoverInspections){
//                undercoverInspection.setFeedback("期限内处置完毕");
//                undercoverInspection.setEmergencyDegree("一般");
//                undercoverInspection.setDataSource("市平安办");
//                undercoverInspection.setInspectionLevel("市本级");
//                if(!AFReceiveUnit.JINHUA.getAreaCode().equals(undercoverInspection.getCompanyAreaCode())){
//                    undercoverInspection.setTown(openUndercoverInspectionService.getTown(undercoverInspection.getCompanyAreaCode()));
//                }
//                if (!StringUtils.isEmpty(undercoverInspection.getCheckItemDomain())){
//                    String checkItemDomain = undercoverInspection.getCheckItemDomain().replace("社会治安安全","社会治安");
//                    IndicatorType indicatorType1=  indicatorTypeMapper.findByTypeName( checkItemDomain);
//                    if (indicatorType1!=null){
//                        undercoverInspection.setType(indicatorType1.getId()+"");
//                    }
//                }
//                String checkImgs = undercoverInspection.getCheckImgs();
//                if (!StringUtils.isEmpty(checkImgs)){
//                    String[] split = checkImgs.split(",");
//                    List<String> ids=new ArrayList<>();
//                    for (String s:split){
//                        InstructionFile instructionFile=new InstructionFile();
//                        instructionFile.setFileUrl("https://"+s.trim());
//                        int i1 = s.lastIndexOf("/");
//                        instructionFile.setFileName(s.substring(i1==-1?0:(i1+1)));
//                        instructionFileService.insertInstructionFile(instructionFile);
//                        ids.add(instructionFile.getId()+"");
//                    }
//                    String str = ids.stream().collect(Collectors.joining(","));
//                    undercoverInspection.setFileIds(str);
//                }
//                Calendar calendar = Calendar.getInstance();
//                calendar.setTime(undercoverInspection.getCheckSubmitDate());
//                calendar.add(Calendar.DAY_OF_YEAR,undercoverInspection.getCheckItemDayLimit());
//                Date futureDate = calendar.getTime();
//                undercoverInspection.setHandleTime(futureDate);
//                AFReceiveUnit byAreaCode = AFReceiveUnit.getByAreaCode(undercoverInspection.getCompanyAreaCode().substring(0,6)+"000000");
//                undercoverInspection.setReceiveUnit(byAreaCode==null?null:byAreaCode.getName());
//                undercoverInspection.setCounty(byAreaCode==null?null:byAreaCode.getArea());
//                TOpenUndercoverInspection tOpenUndercoverInspection = openUndercoverInspectionService.selectTOpenUndercoverInspectionByUniqueNo(undercoverInspection.getUniqueNo());
//
//                if (tOpenUndercoverInspection==null){
//                    int i1 = openUndercoverInspectionService.insertTOpenUndercoverInspection(undercoverInspection);
//                    i=i+i1;
//                }else {
//                    int i1 = openUndercoverInspectionService.updateTOpenUndercoverInspection(undercoverInspection);
//                    i=i+i1;
//                }
//
//            }
//        }
//        if (i==0){
//            return AjaxResult.error("上传失败");
//        }else {
//            return AjaxResult.success("上传成功");
//        }
//
//    }

    /**
     * 上传暗访督察
     *
     * @param undercoverInspections
     * @return
     */
    @PreAuthorize("@ss.hasPermi('openApi:uploadUndercoverInspection')")
    @PostMapping("uploadUndercoverInspection")
    @Transactional
    public AjaxResult uploadUndercoverInspection(@RequestBody TOpenUndercoverInspection undercoverInspections) {
        int i = 0;
        StringBuilder msg = new StringBuilder();
        undercoverInspections.setFeedback("期限内处置完毕");
        undercoverInspections.setEmergencyDegree("一般");
        undercoverInspections.setDataSource("市平安办");
        undercoverInspections.setInspectionLevel("市本级");
        AFReceiveUnit byAreaCode = AFReceiveUnit.getByAreaCode(undercoverInspections.getCompanyAreaCode().substring(0, 6) + "000000");
        undercoverInspections.setReceiveUnit(byAreaCode == null ? null : byAreaCode.getName());
        undercoverInspections.setCounty(byAreaCode == null ? null : byAreaCode.getArea());
        if (!AFReceiveUnit.JINHUA.getAreaCode().equals(undercoverInspections.getCompanyAreaCode())) {
            undercoverInspections.setTown(openUndercoverInspectionService.getTown(undercoverInspections.getCompanyAreaCode()));
        }
        if (!StringUtils.isEmpty(undercoverInspections.getMissionAreaCode())){
            undercoverInspections.setMissionAreaCodeName(openUndercoverInspectionService.getTown(undercoverInspections.getMissionAreaCode()));
            if (!"金华市".equals(undercoverInspections.getMissionAreaCodeName())){
                undercoverInspections.setReceiveUnit("["+undercoverInspections.getTown()+"]");
            }
        }
        TOpenUndercoverInspection tOpenUndercoverInspection = openUndercoverInspectionService.selectTOpenUndercoverInspectionByOpenId(undercoverInspections.getId());
        if (tOpenUndercoverInspection == null) {
            i = openUndercoverInspectionService.insertTOpenUndercoverInspection(undercoverInspections);
            msg.append("暗访上传成功");
        }else {
            msg.append("暗访id:"+undercoverInspections.getId()+"已存在");
            undercoverInspections.setInspectionId(tOpenUndercoverInspection.getInspectionId());
        }
        List<OpenUndercoverInspectionDetails> replyDetailList = undercoverInspections.getReplyDetailList();
        if (!CollectionUtils.isEmpty(replyDetailList)) {
            for (OpenUndercoverInspectionDetails o : replyDetailList) {
                OpenUndercoverInspectionDetails details = openUndercoverInspectionDetailsMapper.selectOpenUndercoverInspectionDetailsByUniqueNo(o.getUniqueNo());
                if (details != null) {
                    msg.append(",uniqueNo:" + o.getUniqueNo()+"已存在");
                    continue;
                }else {
                    msg.append(",uniqueNo:" + o.getUniqueNo()+"上传成功");
                }
                o.setInspectionId(undercoverInspections.getInspectionId());
                if (!StringUtils.isEmpty(o.getCheckItemDomain())) {
                    String checkItemDomain = o.getCheckItemDomain().replace("社会治安安全", "社会治安");
                    IndicatorType indicatorType1 = indicatorTypeMapper.findByTypeName(checkItemDomain);
                    if (indicatorType1 != null) {
                        o.setType(indicatorType1.getId() + "");
                    }
                }
                String checkImgs = o.getCheckImgs();
                if (!StringUtils.isEmpty(checkImgs)) {
                    String[] split = checkImgs.split(",");
                    List<String> ids = new ArrayList<>();
                    for (String s : split) {
                        InstructionFile instructionFile = new InstructionFile();
                        instructionFile.setFileUrl("https://" + s.trim());
                        int i1 = s.lastIndexOf("/");
                        instructionFile.setFileName(s.substring(i1 == -1 ? 0 : (i1 + 1)));
                        instructionFileService.insertInstructionFile(instructionFile);
                        ids.add(instructionFile.getId() + "");
                    }
                    String str = ids.stream().collect(Collectors.joining(","));
                    o.setFileIds(str);
                }
                Calendar calendar = Calendar.getInstance();
                calendar.setTime(undercoverInspections.getCheckSubmitDate());
                calendar.add(Calendar.DAY_OF_YEAR, o.getCheckItemDayLimit());
                Date futureDate = calendar.getTime();
                o.setHandleTime(futureDate);
                openUndercoverInspectionDetailsMapper.insertOpenUndercoverInspectionDetails(o);
            }
        }
        return AjaxResult.success(msg.toString());

    }
}
