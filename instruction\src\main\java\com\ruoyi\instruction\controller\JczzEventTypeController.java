package com.ruoyi.instruction.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.instruction.domain.JczzEventType;
import com.ruoyi.instruction.service.IJczzEventTypeService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 基层智治事件类型Controller
 * 
 * <AUTHOR>
 * @date 2023-08-03
 */
@RestController
@RequestMapping("/jczz/eventType")
public class JczzEventTypeController extends BaseController
{
    @Autowired
    private IJczzEventTypeService jczzEventTypeService;

    /**
     * 查询基层智治事件类型列表
     */
    @PreAuthorize("@ss.hasPermi('jczz:eventType:list')")
    @GetMapping("/list")
    public TableDataInfo list(JczzEventType jczzEventType)
    {
        startPage();
        List<JczzEventType> list = jczzEventTypeService.selectJczzEventTypeList(jczzEventType);
        return getDataTable(list);
    }

    /**
     * 导出基层智治事件类型列表
     */
    @PreAuthorize("@ss.hasPermi('jczz:eventType:export')")
    @Log(title = "基层智治事件类型", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, JczzEventType jczzEventType)
    {
        List<JczzEventType> list = jczzEventTypeService.selectJczzEventTypeList(jczzEventType);
        ExcelUtil<JczzEventType> util = new ExcelUtil<JczzEventType>(JczzEventType.class);
        util.exportExcel(response, list, "基层智治事件类型数据");
    }

    /**
     * 获取基层智治事件类型详细信息
     */
    @PreAuthorize("@ss.hasPermi('jczz:eventType:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(jczzEventTypeService.selectJczzEventTypeById(id));
    }

    /**
     * 新增基层智治事件类型
     */
    @PreAuthorize("@ss.hasPermi('jczz:eventType:add')")
    @Log(title = "基层智治事件类型", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody JczzEventType jczzEventType)
    {
        return toAjax(jczzEventTypeService.insertJczzEventType(jczzEventType));
    }

    /**
     * 修改基层智治事件类型
     */
    @PreAuthorize("@ss.hasPermi('jczz:eventType:edit')")
    @Log(title = "基层智治事件类型", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody JczzEventType jczzEventType)
    {
        return toAjax(jczzEventTypeService.updateJczzEventType(jczzEventType));
    }

    /**
     * 删除基层智治事件类型
     */
    @PreAuthorize("@ss.hasPermi('jczz:eventType:remove')")
    @Log(title = "基层智治事件类型", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(jczzEventTypeService.deleteJczzEventTypeByIds(ids));
    }
}
