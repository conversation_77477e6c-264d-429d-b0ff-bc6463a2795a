package com.ruoyi.instruction.controller;

import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.instruction.domain.InstructionGroup;
import com.ruoyi.instruction.domain.YjxzInfo;
import com.ruoyi.instruction.mapper.InstructionGroupMapper;
import com.ruoyi.instruction.service.IInstructionEventService;
import com.ruoyi.instruction.service.IInstructionGroupService;
import com.ruoyi.instruction.service.IInstructionInfoService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/8/16 10:18
 * 移动端controller
 */
@RestController
@RequestMapping("/system/yidong")
public class YiDongController {

    @Autowired
    private IInstructionEventService eventService;

    @Autowired
    private IInstructionInfoService instructionInfoService;

    @Autowired
    private IInstructionGroupService groupService;

    @Autowired
    private InstructionGroupMapper groupMapper;

    /**
     * 获取移动端预警事件数
     *
     * @return
     */
    @GetMapping("/getYdEventCount")
    public AjaxResult getYdEventCount() {
        Map<String,Long> mapList = eventService.getYdEventCount();
        return AjaxResult.success(mapList);
    }

    /**
     * 获取月度趋势
     * @return
     */
    @GetMapping("/getEventMonthlyTrend")
    public AjaxResult getEventMonthlyTrend(){
        List<Map<String,Object>> mapList = eventService.getEventMonthlyTrend();
        return AjaxResult.success(mapList);
    }

    /**
     * 获取县市区指令下达数
     * @return
     */
    @GetMapping("/getCountyInstructionCount")
    public AjaxResult getCountyInstructionCount(){
        List<Map<String,Object>> mapList = instructionInfoService.getCountyInstructionCount();
        return AjaxResult.success(mapList);
    }

    /**
     * 获取公安情指推送/网格直报统计数据
     * @param type 1:公安推送  2：网格直报 3：维稳事件
     * @return
     */
    @GetMapping("/getForeWarnStatistics/{type}")
    public AjaxResult getForeWarnStatistics(@PathVariable("type")Integer type){
        Map<String,Object> map = eventService.getForeWarnStatistics(type);
        return AjaxResult.success(map);
    }

    /**
     * 根据县市区名称查询统计数据
     * @param county
     * @return
     */
    @GetMapping("/getCountyStatistics")
    public AjaxResult  getCountyStatistics(@RequestParam("county")String county){
        Map<String,Object> map = instructionInfoService.getCountyStatistics(county);
        return AjaxResult.success(map);
    }

    /**
     * 获取县市区平均处置时长
     * @return
     */
    @GetMapping("/getCountyDealTime")
    public AjaxResult getCountyDealTime(){
        List<Map<String,Object>> mapList = instructionInfoService.getCountyDealTime();
        return AjaxResult.success(mapList);
    }

    /**
     * 获取群体县市区分布情况图表
     * @param group
     * @return
     */
    @GetMapping("/getGroupCountyDistribution")
    public AjaxResult getGroupCountyDistribution(InstructionGroup group){
        List<Map<String,Object>> mapList = groupService.getGroupCountyDistribution(group);
        //查询异动次数（去重）
        Long count = groupMapper.getGroupCount(group);
        return AjaxResult.success(mapList).put("eventCount",count);
    }

}
