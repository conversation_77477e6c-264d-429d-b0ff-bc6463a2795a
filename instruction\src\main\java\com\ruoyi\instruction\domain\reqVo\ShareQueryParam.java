package com.ruoyi.instruction.domain.reqVo;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/7/18 15:23
 */
@Data
public class ShareQueryParam {

    /**
     * 资源秘钥
     */
    private String resourceCode;

    /**
     * 资源编码
     */
    private String resourceSecret;

    /**
     * 租户编码
     */
    private String tenantCode;

    /**
     * 密文摘要
     */
    @JsonProperty("PasswdDigest")
    private String PasswdDigest;

    /**
     * uuid请求唯一号
     */
    @JsonProperty("Nonce")
    private String Nonce;

    /**
     * 当前时间 yyyy-MM-dd HH:mm:ss
     */
    @JsonProperty("Created")
    private String Created;

    /**
     * 页码
     */
    private String pageNum;

    /**
     * 页数
     */
    private String pageSize;

    /**
     * 查询参数
     * [{"column":"company","value":"123","type":"eq"},{"column":"company","value":"346","type":"like"}]
     */
    private String condition;

    /**
     * 排名类型 1：类型 2:区县
     */
    private String type;

    /**
     * 排名类型 1：年 2：月
     */
    private String searchTimeType;

    /**
     * 事件编号
     */
    private String searchEventNum;

    /**
     * 事件关联人
     */
    private String searchUsername;

    /**
     * 事件关联人联系电话
     */
    private String searchPhone;

    /**
     * 事件关键字模糊
     */
    private String searchDescKeyword;

    /**
     * 事件发生地
     */
    private String searchPlace;

    /**
     * 事件类型
     */
    private String searchEventType;

    /**
     * 事件所属区域
     */
    private String searchArea;

    /**
     * 事件来源
     */
    private String searchSource;

    /**
     * 事件开始时间 yyyy-MM-dd
     */
    private String searchOccStartTime;

    /**
     * 事件结束时间 yyyy-MM-dd
     */
    private String searchOccStopTime;

    /**
     * 页标 最小1
     */
    private Long page;

    /**
     * 分页大小
     */
    private Long size;




}
