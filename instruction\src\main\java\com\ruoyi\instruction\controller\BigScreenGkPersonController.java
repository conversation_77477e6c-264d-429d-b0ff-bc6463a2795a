package com.ruoyi.instruction.controller;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.alibaba.fastjson2.TypeReference;
import com.ruoyi.common.constant.CacheConstants;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.redis.RedisCache;
import com.ruoyi.common.exception.CustomException;
import com.ruoyi.common.exception.ServiceException;
import com.ruoyi.common.helper.DingtalkHelper;
import com.ruoyi.common.reqvo.DingTalkReceevers;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.common.utils.sm3.SM3Utils;
import com.ruoyi.common.utils.sm3.SymmetricEncoder;
import com.ruoyi.instruction.domain.*;
import com.ruoyi.instruction.domain.reqVo.DingGetAccountIdVo;
import com.ruoyi.instruction.domain.reqVo.SecretParam;
import com.ruoyi.instruction.domain.reqVo.ShareQueryParam;
import com.ruoyi.instruction.domain.rspVo.BigSceenCategoryDetailRspVo;
import com.ruoyi.instruction.domain.rspVo.BigSceenCategoryRspVo;
import com.ruoyi.instruction.domain.rspVo.BigScreenMcafVo;
import com.ruoyi.instruction.mapper.NetworkYqMapper;
import com.ruoyi.instruction.service.IGkPersonService;
import com.ruoyi.instruction.service.IInstructionEndService;
import com.ruoyi.instruction.service.IInstructionInfoService;
import com.ruoyi.instruction.service.ITOpenUndercoverInspectionService;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.compress.utils.Lists;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.client.RestTemplate;

import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 人员预警驾驶舱相关接口
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2023/7/13 10:37
 */
@RestController
@RequestMapping("/bigScreen/gkPerson")
public class BigScreenGkPersonController extends BaseController {

    private static final Logger log = LoggerFactory.getLogger(BigScreenGkPersonController.class);

    @Autowired
    private IGkPersonService gkPersonService;

    @Autowired
    private DingtalkHelper dingtalkHelper;

    @Autowired
    private RedisCache redisCache;

    @Autowired
    private ITOpenUndercoverInspectionService inspectionService;

    @Autowired
    private IInstructionInfoService instructionInfoService;

    @Autowired
    private IInstructionEndService instructionEndService;

    @Autowired
    private NetworkYqMapper networkYqMapperl;

    /**
     * 查询管控人员列表
     */
    @GetMapping("/list")
    public AjaxResult list(GkPerson gkPerson) {
        //查询已布控人员列表
        List<HashMap> list = gkPersonService.selectGkPersonListForBigScreen(gkPerson);
        return AjaxResult.success(list);
    }

    /**
     * 根据管控人员id获取预警信息
     *
     * @param id
     * @return
     */
    @GetMapping("/getYjByGkId/{id}")
    public AjaxResult getYjByGkId(@PathVariable("id") Long id) {
        List<QbtbZwwRyyj> list = gkPersonService.getYjByGkId(id);
        return AjaxResult.success(list);
    }

    /**
     * 浙政钉通话获取AccountId
     *
     * @param dingGetAccountIdVo
     * @return
     */
    @PostMapping("getAccountId")
    public AjaxResult getAccountId(@RequestBody DingGetAccountIdVo dingGetAccountIdVo) {
        List<DingTalkReceevers> byMobiles = dingtalkHelper.getByMobiles(dingGetAccountIdVo.getSource(), dingGetAccountIdVo.phones);
        List<String> collect = byMobiles.stream().map(x -> x.getAccountId()).collect(Collectors.toList());
        return AjaxResult.success(collect);
    }

    /**
     * 获取秘钥
     *
     * @return
     */
    @GetMapping("/getSecret")
    public AjaxResult getSecret() {
        String secret = "4ecd02d8-516f-4594-8a34-9552d9c2c83d"; //系统方提供
        String tentCode = "********************"; //系统方提供
        String password = "Edde3(de78"; //系统方提供
        String url = "http://10.45.178.221:8881/dataShare/getSecretKey";
        String sign = new SM3Utils().sm3(secret + "#" + tentCode + "#" + password);
        // log.info("接收到获取密钥key的请求内容为：请求地址->{},请求参数:secret->{},租户编码->{},密码->{},生成的签名->{}", url, secret, tentCode, password, sign);
        RestTemplate restTemplate = new RestTemplate();
        SecretParam secretParam = new SecretParam();
        secretParam.setResourceSecret(secret);
        secretParam.setSign(sign);
        secretParam.setTenantCode(tentCode);

        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);

        HttpEntity<SecretParam> request =
                new HttpEntity<SecretParam>(secretParam, headers);


        ResponseEntity<String> responseEntity = restTemplate.postForEntity(url, request, String.class);
        String responseData = responseEntity.getBody();

        //String responseData = sendPost(secretKeyUrl, secretParamStr, tentCode, null);
        // log.info("接收到获取密钥key的响应结果为：{}", responseData);

        return AjaxResult.success(responseData);
    }

    /**
     * 获取网信数据
     *
     * @return
     */
    @GetMapping("/getData")
    public String getData(int pageNo, int pagesize, int Type) {

        RestTemplate restTemplate = new RestTemplate();

        String tenantCode = "********************";
        String pwd = "Edde3(de78";
        String secretParam = "ce02f306-fc89-4399-b673-d51f0add8559";
        String resourceCode = "AP230720439251328530";
        String url = "http://10.45.178.221:8881/dataShare/query";
        String condition = "";
        if (Type == 1) {
            condition = "";
        } else if (Type == 2) {
            Date TodayDate = DateUtils.getNowDate();
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
            String dateString = sdf.format(TodayDate);
            condition = "[{\"column\":\"issue_time\",\"value\":" +
                    "\"" + dateString + "\"" +
                    ",\"type\":\"eq\"}]";
        }

        String pageNum = Integer.toString(pageNo);
        String pageSize = Integer.toString(pagesize);
        String nonce = UUID.randomUUID().toString();
        DateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        String created = sdf.format(new Date());
        // String responseData = "sRpjHFk3Lyu1CNBJL02c5gviG5fFChKbQ99T/fZNhPTAAfUbNqX4GGAo3wMMyL4o\r\n";
        String responseData = forGetSecretKey(tenantCode, pwd, secretParam);
        String code = JSONObject.parseObject(responseData).getString("code");
        if (!code.equals(String.valueOf(200))) {
            String msg = JSONObject.parseObject(responseData).getString("msg");
            throw new CustomException(msg);
        }
        JSONObject data = JSONObject.parseObject(responseData).getJSONObject("data");
        String resultData = data.getString("data");
        String secretKey = SymmetricEncoder.AESDncode(secretParam, resultData);
        // log.info("接收到获取密钥key为：{}", secretKey);

        String secret = nonce + "_" + created + "_" + secretKey;
        String PasswdDigest = new SM3Utils().sm3(secret);
        String pageNumS = "1";
        String pageSizeS = "500";
        if (!StringUtils.isEmpty(pageNum)) {
            pageNumS = pageNum;
        }
        if (!StringUtils.isEmpty(pageSize)) {
            pageSizeS = pageSize;
        }

        //String dataParam = "resourceCode=" + resourceCode + "&resourceSecret=" + secretParam + "&tenantCode=" + tenantCode + "&PasswdDigest=" + PasswdDigest + "&Nonce=" + nonce + "&Created=" + created + "&pageNum=" + pageNumS + "&pageSize=" + pageSizeS;
        // log.info("接收到共享查询请求地址：{},请求参数：appKey->{},tenantCode->{},passwdDigest->{},nonce->{},created->{},pageNum->{},pageSize->{}", url, secret, tenantCode, PasswdDigest, nonce, created, pageNumS, pageSizeS);

        ShareQueryParam shareQueryParam = new ShareQueryParam();
        shareQueryParam.setResourceCode(resourceCode);
        shareQueryParam.setResourceSecret(secretParam);
        shareQueryParam.setTenantCode(tenantCode);
        shareQueryParam.setPasswdDigest(PasswdDigest);
        shareQueryParam.setNonce(nonce);
        shareQueryParam.setCreated(created);
        shareQueryParam.setPageNum(pageNumS);
        shareQueryParam.setPageSize(pageSizeS);
        shareQueryParam.setCondition(condition);


        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        headers.set("tenantCode", tenantCode);
        headers.set("resCode", resourceCode);


        HttpEntity<ShareQueryParam> request =
                new HttpEntity<ShareQueryParam>(shareQueryParam, headers);


        ResponseEntity<String> responseEntity = restTemplate.postForEntity(url, request, String.class);

        String resData = responseEntity.getBody();
        // log.info("接收到共享查询的响应信息为：{}", resData);
        String resCode = JSONObject.parseObject(resData).getString("code");
        if (!resCode.equals("200")) {
            String msg = JSONObject.parseObject(resData).getString("msg");
            throw new CustomException(msg);
        } else {
            JSONObject resDataResult = JSONObject.parseObject(resData).getJSONObject("data");
            return resDataResult.getString("data");
        }

    }

    private String forGetSecretKey(final String tentCode, final String password, final String secret) {
        String url = "http://10.45.178.221:8881/dataShare/getSecretKey";
        String sign = new SM3Utils().sm3(secret + "#" + tentCode + "#" + password);
        // log.info("接收到获取密钥key的请求内容为：请求地址->{},请求参数:secret->{},租户编码->{},密码->{},生成的签名->{}", url, secret, tentCode, password, sign);
        RestTemplate restTemplate = new RestTemplate();
        SecretParam secretParam = new SecretParam();
        secretParam.setResourceSecret(secret);
        secretParam.setSign(sign);
        secretParam.setTenantCode(tentCode);

        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);

        HttpEntity<SecretParam> request =
                new HttpEntity<SecretParam>(secretParam, headers);

        ResponseEntity<String> responseEntity = restTemplate.postForEntity(url, request, String.class);
        String responseData = responseEntity.getBody();

        //String responseData = sendPost(secretKeyUrl, secretParamStr, tentCode, null);
        // log.info("接收到获取密钥key的响应结果为：{}", responseData);
        return responseData;
    }


    /**
     * 获取明查暗访数据
     *
     * @return
     */
    @GetMapping("/getMcafData")
    public AjaxResult getMcafData() {
        int pageNo = 1;
        int pagesize = 500;
        int Type = 1;
        String mcafData = redisCache.getCacheObject(CacheConstants.MCAF_LON_LAT);
        if (mcafData != null && !mcafData.equals("")) {
            List<Map<String, Object>> maps = JSON.parseObject(mcafData, new TypeReference<List<Map<String, Object>>>() {
            });
            return AjaxResult.success(maps);
        }
        RestTemplate restTemplate = new RestTemplate();

        String tenantCode = "********************";
        String pwd = "Edde3(de78";
        String secretParam = "3431a4cd-9394-4c6a-abb1-d0f8a1ad184b";
        String resourceCode = "DB230821881107097925";
        String url = "http://10.45.178.221:8881/dataShare/query";
        String condition = "";
        if (Type == 1) {
            condition = "";
        } else if (Type == 2) {
            Date TodayDate = DateUtils.getNowDate();
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
            String dateString = sdf.format(TodayDate);
            condition = "[{\"column\":\"issue_time\",\"value\":" +
                    "\"" + dateString + "\"" +
                    ",\"type\":\"eq\"}]";
        }

        String pageNum = Integer.toString(pageNo);
        String pageSize = Integer.toString(pagesize);
        String nonce = UUID.randomUUID().toString();
        DateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        String created = sdf.format(new Date());
        // String responseData = "sRpjHFk3Lyu1CNBJL02c5gviG5fFChKbQ99T/fZNhPTAAfUbNqX4GGAo3wMMyL4o\r\n";
        String responseData = forGetSecretKey(tenantCode, pwd, secretParam);
        String code = JSONObject.parseObject(responseData).getString("code");
        if (!code.equals(String.valueOf(200))) {
            String msg = JSONObject.parseObject(responseData).getString("msg");
            throw new CustomException(msg);
        }
        JSONObject data = JSONObject.parseObject(responseData).getJSONObject("data");
        String resultData = data.getString("data");
        String secretKey = SymmetricEncoder.AESDncode(secretParam, resultData);
        // log.info("接收到获取密钥key为：{}", secretKey);

        String secret = nonce + "_" + created + "_" + secretKey;
        String PasswdDigest = new SM3Utils().sm3(secret);
        String pageNumS = "1";
        String pageSizeS = "500";
        if (!StringUtils.isEmpty(pageNum)) {
            pageNumS = pageNum;
        }
        if (!StringUtils.isEmpty(pageSize)) {
            pageSizeS = pageSize;
        }

        //String dataParam = "resourceCode=" + resourceCode + "&resourceSecret=" + secretParam + "&tenantCode=" + tenantCode + "&PasswdDigest=" + PasswdDigest + "&Nonce=" + nonce + "&Created=" + created + "&pageNum=" + pageNumS + "&pageSize=" + pageSizeS;
        // log.info("接收到共享查询请求地址：{},请求参数：appKey->{},tenantCode->{},passwdDigest->{},nonce->{},created->{},pageNum->{},pageSize->{}", url, secret, tenantCode, PasswdDigest, nonce, created, pageNumS, pageSizeS);

        ShareQueryParam shareQueryParam = new ShareQueryParam();
        shareQueryParam.setResourceCode(resourceCode);
        shareQueryParam.setResourceSecret(secretParam);
        shareQueryParam.setTenantCode(tenantCode);
        shareQueryParam.setPasswdDigest(PasswdDigest);
        shareQueryParam.setNonce(nonce);
        shareQueryParam.setCreated(created);
        shareQueryParam.setPageNum(pageNumS);
        shareQueryParam.setPageSize(pageSizeS);
        shareQueryParam.setCondition(condition);


        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        headers.set("tenantCode", tenantCode);
        headers.set("resCode", resourceCode);


        HttpEntity<ShareQueryParam> request =
                new HttpEntity<ShareQueryParam>(shareQueryParam, headers);


        ResponseEntity<String> responseEntity = restTemplate.postForEntity(url, request, String.class);

        String resData = responseEntity.getBody();
        String resCode = JSONObject.parseObject(resData).getString("code");
        if (!resCode.equals("200")) {
            String msg = JSONObject.parseObject(resData).getString("msg");
            throw new CustomException(msg);
        } else {
            JSONObject resDataResult = JSONObject.parseObject(resData).getJSONObject("data");
            JSONObject dataResultString = JSONObject.parseObject(resDataResult.getString("data"));
            Integer total = Integer.valueOf(dataResultString.getString("total"));
            List<Map<String, Object>> mapList = (List<Map<String, Object>>) dataResultString.get("records");
            getMcafAllData(mapList, total, pageNo, pagesize);
            String finallyData = mapList.toString();
            redisCache.setCacheObject(CacheConstants.MCAF_LON_LAT, finallyData, 1, TimeUnit.DAYS);
            return AjaxResult.success(mapList);
        }

    }

    /**
     * 查询督察点位企业类型
     */
    @GetMapping("/category")
    public AjaxResult listCompanyCategory() {
        //查询已布控人员列表
        List<String> list = inspectionService.listCompanyCategory();

        int id = 0;

        List<BigSceenCategoryRspVo> categoryList = Lists.newArrayList();

        BigSceenCategoryRspVo checked = new BigSceenCategoryRspVo(id, "已检查");
        categoryList.add(checked);

        List<BigSceenCategoryDetailRspVo> detailList = Lists.newArrayList();
        for (String category : list) {
            detailList.add(new BigSceenCategoryDetailRspVo(++id, category));
        }
        checked.setList(detailList);

        BigSceenCategoryRspVo unchecked = new BigSceenCategoryRspVo(++id, "未检查");
        categoryList.add(unchecked);

        List<BigSceenCategoryDetailRspVo> uncheckedCategoryList = Lists.newArrayList();
        uncheckedCategoryList.add(new BigSceenCategoryDetailRspVo(++id, "其他"));
        unchecked.setList(uncheckedCategoryList);

        return AjaxResult.success(categoryList);
    }

    /**
     * 根据类型获取明查暗访数据
     *
     * @return
     */
    @GetMapping("/get_mcaf_by_category")
    public AjaxResult getMcafByCategory(String category) {
        List<BigScreenMcafVo> mcafVoList = Lists.newArrayList();
        List<Map<String, Object>> mapList;

        List<TOpenUndercoverInspection> inspectionList = inspectionService.listByCompanyCategory(category);
        if (CollectionUtils.isEmpty(inspectionList)) {
            throw new ServiceException("查询点位不存在");
        }

        String mcafData = redisCache.getCacheObject(CacheConstants.MCAF_LON_LAT + category);
        if (StringUtils.isNotBlank(mcafData)) {
            mapList = JSON.parseObject(mcafData, new TypeReference<List<Map<String, Object>>>() {
            });
        } else {
            List<Long> companyIdList = inspectionList.stream()
                    .map(TOpenUndercoverInspection::getCompanyId)
                    .collect(Collectors.toList());

            StringBuilder sb = new StringBuilder();
            sb.append("[{\"column\":\"id\",\"value\":\"");
            for (int i = 0; i < companyIdList.size(); i++) {
                Long companyId = companyIdList.get(i);

                sb.append(companyId);
                if (i < companyIdList.size() - 1) {
                    sb.append(",");
                }
            }
            sb.append("\",\"type\":\"in\"}]");
            String condition = sb.toString();
//            String condition = "[{\"column\":\"id\",\"value\":\"1679691710017761282\"," + "\"type\":\"eq\"}]";

            mapList = queryLocationByCondition(condition);

            if (CollectionUtils.isEmpty(mapList)) {
                return AjaxResult.success(mcafVoList);
            }

            redisCache.setCacheObject(CacheConstants.MCAF_LON_LAT + category, mapList.toString(), 1, TimeUnit.DAYS);
        }

        if (CollectionUtils.isEmpty(mapList)) {
            return AjaxResult.success();
        }

        Map<Long, Map<String, Object>> locationMap = mapList.stream()
                .collect(Collectors.toMap(map -> Long.valueOf(map.get("id") + ""),
                        Function.identity()));

        List<Long> inspectionIdList = inspectionList.stream()
                .map(TOpenUndercoverInspection::getInspectionId)
                .collect(Collectors.toList());

        List<OpenUndercoverInspectionDetails> inspectionDetailsList =
                inspectionService.listDetailsByInspectionId(inspectionIdList);

        Map<Long, List<OpenUndercoverInspectionDetails>> inspectionDetailMap = inspectionDetailsList.stream()
                .collect(Collectors.groupingBy(OpenUndercoverInspectionDetails::getInspectionId));

        for (TOpenUndercoverInspection inspection : inspectionList) {
            BigScreenMcafVo bigScreenMcafVo = new BigScreenMcafVo();
            bigScreenMcafVo.setId(inspection.getId());
            bigScreenMcafVo.setCompanyCategory(inspection.getCompanyCategory());
            bigScreenMcafVo.setCompanyAddress(inspection.getCompanyAddress());
            bigScreenMcafVo.setCompanyName(inspection.getCompanyName());

            String startTimeStr = DateUtils.parseDateToStr("yyyy-MM-dd", inspection.getMissionStartTime());
            bigScreenMcafVo.setMissionStartTime(startTimeStr);

            Map<String, Object> map = locationMap.get(inspection.getCompanyId());
            if (MapUtils.isEmpty(map)) {
                log.info("该点位经纬度未查询到:{}", inspection.getId());
                continue;
            }

            bigScreenMcafVo.setLat((String) map.get("lat"));
            bigScreenMcafVo.setLon((String) map.get("lon"));

            bigScreenMcafVo.setIssueCount(0);
            bigScreenMcafVo.setResolvedCount(0);

            List<OpenUndercoverInspectionDetails> inspectionDetailsList4One = inspectionDetailMap.get(inspection.getInspectionId());
            if (CollectionUtils.isEmpty(inspectionDetailsList4One)) {
                mcafVoList.add(bigScreenMcafVo);
                continue;
            }

            bigScreenMcafVo.setIssueCount(inspectionDetailsList4One.size());

            //查询是否已解决
            List<String> inspectionUniqueNoList = inspectionDetailsList4One.stream()
                    .map(OpenUndercoverInspectionDetails::getUniqueNo)
                    .filter(StringUtils::isNotBlank)
                    .collect(Collectors.toList());

            List<Long> instructionInfoList = instructionInfoService.listIdByMcafIds(inspectionUniqueNoList);

            if (CollectionUtils.isNotEmpty(instructionInfoList)) {
                int resolvedCount = instructionEndService.countByInstructionIds(instructionInfoList);

                bigScreenMcafVo.setResolvedCount(resolvedCount);
            }

            mcafVoList.add(bigScreenMcafVo);
        }

        return AjaxResult.success(mcafVoList);
    }


    private List<Map<String, Object>> queryLocationByCondition(String condition) {
        int pageNo = 1;
        int pagesize = 500;

        RestTemplate restTemplate = new RestTemplate();

        String tenantCode = "********************";
        String pwd = "Edde3(de78";
        String secretParam = "3431a4cd-9394-4c6a-abb1-d0f8a1ad184b";
        String resourceCode = "DB230821881107097925";
        String url = "http://10.45.178.221:8881/dataShare/query";

        String pageNum = Integer.toString(pageNo);
        String pageSize = Integer.toString(pagesize);
        String nonce = UUID.randomUUID().toString();
        DateFormat sdf = new SimpleDateFormat("yyyy-MM-dd hh:mm:ss");
        String created = sdf.format(new Date());
        // String responseData = "sRpjHFk3Lyu1CNBJL02c5gviG5fFChKbQ99T/fZNhPTAAfUbNqX4GGAo3wMMyL4o\r\n";
        String responseData = forGetSecretKey(tenantCode, pwd, secretParam);
        String code = JSONObject.parseObject(responseData).getString("code");
        if (!code.equals(String.valueOf(200))) {
            String msg = JSONObject.parseObject(responseData).getString("msg");
            throw new CustomException(msg);
        }
        JSONObject data = JSONObject.parseObject(responseData).getJSONObject("data");
        String resultData = data.getString("data");
        String secretKey = SymmetricEncoder.AESDncode(secretParam, resultData);
        // log.info("接收到获取密钥key为：{}", secretKey);

        String secret = nonce + "_" + created + "_" + secretKey;
        String PasswdDigest = new SM3Utils().sm3(secret);
        String pageNumS = "1";
        String pageSizeS = "500";
        if (!StringUtils.isEmpty(pageNum)) {
            pageNumS = pageNum;
        }
        if (!StringUtils.isEmpty(pageSize)) {
            pageSizeS = pageSize;
        }

        ShareQueryParam shareQueryParam = new ShareQueryParam();
        shareQueryParam.setResourceCode(resourceCode);
        shareQueryParam.setResourceSecret(secretParam);
        shareQueryParam.setTenantCode(tenantCode);
        shareQueryParam.setPasswdDigest(PasswdDigest);
        shareQueryParam.setNonce(nonce);
        shareQueryParam.setCreated(created);
        shareQueryParam.setPageNum(pageNumS);
        shareQueryParam.setPageSize(pageSizeS);
        shareQueryParam.setCondition(condition);

        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        headers.set("tenantCode", tenantCode);
        headers.set("resCode", resourceCode);

        HttpEntity<ShareQueryParam> request =
                new HttpEntity<ShareQueryParam>(shareQueryParam, headers);

        ResponseEntity<String> responseEntity = restTemplate.postForEntity(url, request, String.class);

        String resData = responseEntity.getBody();
        String resCode = JSONObject.parseObject(resData).getString("code");
        if (!resCode.equals("200")) {
            String msg = JSONObject.parseObject(resData).getString("msg");
            throw new CustomException(msg);
        } else {
            JSONObject resDataResult = JSONObject.parseObject(resData).getJSONObject("data");
            JSONObject dataResultString = JSONObject.parseObject(resDataResult.getString("data"));
            Integer total = Integer.valueOf(dataResultString.getString("total"));
            List<Map<String, Object>> mapList = (List<Map<String, Object>>) dataResultString.get("records");

            return mapList;
        }
    }

    /**
     * 获取明查暗访所有数据
     *
     * @param finallyMapList
     * @param total
     * @param pageNo
     * @param pagesize
     */
    private void getMcafAllData(final List<Map<String, Object>> finallyMapList, final Integer total, final Integer pageNo, final Integer pagesize) {
        int number = total / pagesize + (total % pagesize != 0 ? 1 : 0);
        for (int i = pageNo + 1; i <= number; i++) {
            RestTemplate restTemplate = new RestTemplate();

            String tenantCode = "********************";
            String pwd = "Edde3(de78";
            String secretParam = "3431a4cd-9394-4c6a-abb1-d0f8a1ad184b";
            String resourceCode = "DB230821881107097925";
            String url = "http://10.45.178.221:8881/dataShare/query";
            String condition = "";

            String pageNum = Integer.toString(i);
            String pageSize = Integer.toString(pagesize);
            String nonce = UUID.randomUUID().toString();
            DateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            String created = sdf.format(new Date());
            // String responseData = "sRpjHFk3Lyu1CNBJL02c5gviG5fFChKbQ99T/fZNhPTAAfUbNqX4GGAo3wMMyL4o\r\n";
            String responseData = forGetSecretKey(tenantCode, pwd, secretParam);
            String code = JSONObject.parseObject(responseData).getString("code");
            if (!code.equals(String.valueOf(200))) {
                String msg = JSONObject.parseObject(responseData).getString("msg");
                throw new CustomException(msg);
            }
            JSONObject data = JSONObject.parseObject(responseData).getJSONObject("data");
            String resultData = data.getString("data");
            String secretKey = SymmetricEncoder.AESDncode(secretParam, resultData);
            // log.info("接收到获取密钥key为：{}", secretKey);

            String secret = nonce + "_" + created + "_" + secretKey;
            String PasswdDigest = new SM3Utils().sm3(secret);
            String pageNumS = "1";
            String pageSizeS = "500";
            if (!StringUtils.isEmpty(pageNum)) {
                pageNumS = pageNum;
            }
            if (!StringUtils.isEmpty(pageSize)) {
                pageSizeS = pageSize;
            }

            //String dataParam = "resourceCode=" + resourceCode + "&resourceSecret=" + secretParam + "&tenantCode=" + tenantCode + "&PasswdDigest=" + PasswdDigest + "&Nonce=" + nonce + "&Created=" + created + "&pageNum=" + pageNumS + "&pageSize=" + pageSizeS;
            // log.info("接收到共享查询请求地址：{},请求参数：appKey->{},tenantCode->{},passwdDigest->{},nonce->{},created->{},pageNum->{},pageSize->{}", url, secret, tenantCode, PasswdDigest, nonce, created, pageNumS, pageSizeS);

            ShareQueryParam shareQueryParam = new ShareQueryParam();
            shareQueryParam.setResourceCode(resourceCode);
            shareQueryParam.setResourceSecret(secretParam);
            shareQueryParam.setTenantCode(tenantCode);
            shareQueryParam.setPasswdDigest(PasswdDigest);
            shareQueryParam.setNonce(nonce);
            shareQueryParam.setCreated(created);
            shareQueryParam.setPageNum(pageNumS);
            shareQueryParam.setPageSize(pageSizeS);
            shareQueryParam.setCondition(condition);


            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);
            headers.set("tenantCode", tenantCode);
            headers.set("resCode", resourceCode);


            HttpEntity<ShareQueryParam> request =
                    new HttpEntity<ShareQueryParam>(shareQueryParam, headers);


            ResponseEntity<String> responseEntity = restTemplate.postForEntity(url, request, String.class);

            String resData = responseEntity.getBody();
            String resCode = JSONObject.parseObject(resData).getString("code");
            if (!resCode.equals("200")) {
                String msg = JSONObject.parseObject(resData).getString("msg");
                throw new CustomException(msg);
            } else {
                JSONObject resDataResult = JSONObject.parseObject(resData).getJSONObject("data");
                JSONObject dataResultString = JSONObject.parseObject(resDataResult.getString("data"));
                List<Map<String, Object>> mapList = (List<Map<String, Object>>) dataResultString.get("records");
                finallyMapList.addAll(mapList);
            }
        }
    }

    /**
     * 获取网格直报事件数
     *
     * @return
     */
    @GetMapping("/getWgzbEvent")
    public String getWgzbEvent() {
        int pageNo = 1;
        int pagesize = 500;
        RestTemplate restTemplate = new RestTemplate();

        String tenantCode = "********************";
        String pwd = "Edde3(de78";
        String secretParam = "db1ecbec-d751-4e1b-963a-3380058eaf1a";
        String resourceCode = "EX230906011433480287";
        String url = "http://10.45.178.221:8881/dataShare/query";
        String condition = "";
        String pageNum = Integer.toString(pageNo);
        String pageSize = Integer.toString(pagesize);
        String nonce = UUID.randomUUID().toString();
        DateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        String created = sdf.format(new Date());
        // String responseData = "sRpjHFk3Lyu1CNBJL02c5gviG5fFChKbQ99T/fZNhPTAAfUbNqX4GGAo3wMMyL4o\r\n";
        String responseData = forGetSecretKey(tenantCode, pwd, secretParam);
        String code = JSONObject.parseObject(responseData).getString("code");
        if (!code.equals(String.valueOf(200))) {
            String msg = JSONObject.parseObject(responseData).getString("msg");
            throw new CustomException(msg);
        }
        JSONObject data = JSONObject.parseObject(responseData).getJSONObject("data");
        String resultData = data.getString("data");
        String secretKey = SymmetricEncoder.AESDncode(secretParam, resultData);
        // log.info("接收到获取密钥key为：{}", secretKey);

        String secret = nonce + "_" + created + "_" + secretKey;
        String PasswdDigest = new SM3Utils().sm3(secret);
        String pageNumS = "1";
        String pageSizeS = "500";
        if (!StringUtils.isEmpty(pageNum)) {
            pageNumS = pageNum;
        }
        if (!StringUtils.isEmpty(pageSize)) {
            pageSizeS = pageSize;
        }

        //String dataParam = "resourceCode=" + resourceCode + "&resourceSecret=" + secretParam + "&tenantCode=" + tenantCode + "&PasswdDigest=" + PasswdDigest + "&Nonce=" + nonce + "&Created=" + created + "&pageNum=" + pageNumS + "&pageSize=" + pageSizeS;
        // log.info("接收到共享查询请求地址：{},请求参数：appKey->{},tenantCode->{},passwdDigest->{},nonce->{},created->{},pageNum->{},pageSize->{}", url, secret, tenantCode, PasswdDigest, nonce, created, pageNumS, pageSizeS);

        ShareQueryParam shareQueryParam = new ShareQueryParam();
        shareQueryParam.setResourceCode(resourceCode);
        shareQueryParam.setResourceSecret(secretParam);
        shareQueryParam.setTenantCode(tenantCode);
        shareQueryParam.setPasswdDigest(PasswdDigest);
        shareQueryParam.setNonce(nonce);
        shareQueryParam.setCreated(created);
        shareQueryParam.setPageNum(pageNumS);
        shareQueryParam.setPageSize(pageSizeS);
        shareQueryParam.setCondition(condition);


        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        headers.set("tenantCode", tenantCode);
        headers.set("resCode", resourceCode);


        HttpEntity<ShareQueryParam> request =
                new HttpEntity<ShareQueryParam>(shareQueryParam, headers);


        ResponseEntity<String> responseEntity = restTemplate.postForEntity(url, request, String.class);

        String resData = responseEntity.getBody();
        // log.info("接收到共享查询的响应信息为：{}", resData);
        String resCode = JSONObject.parseObject(resData).getString("code");
        if (!resCode.equals("200")) {
            String msg = JSONObject.parseObject(resData).getString("msg");

            throw new CustomException(msg);
        } else {
            JSONObject resDataResult = JSONObject.parseObject(resData).getJSONObject("data");
            JSONObject data1 = resDataResult.getJSONObject("data");
            JSONArray records = data1.getJSONArray("records");
            //获取基层智治-事件表
            List<Map<String, Object>> map = gkPersonService.getJczzEventType();
            records.sort(Comparator.comparing(obj -> ((JSONObject) obj).getBigInteger("create_time")).reversed());
            for (int i = 0; i < records.size(); i++) {
                JSONObject jsonObject = records.getJSONObject(i);
                String create_time = jsonObject.getString("create_time");
                DateFormat sdf1 = new SimpleDateFormat("yyyy-MM-dd HH:mm");
                String sd = sdf1.format(new Date(Long.parseLong(create_time)));
                jsonObject.put("create_time", sd);
                String emergency_level = jsonObject.getString("emergency_level");
                if (emergency_level.equals("1")) {
                    jsonObject.put("emergency_level", "紧急");
                } else {
                    jsonObject.put("emergency_level", "非紧急");
                }
                String firstType = jsonObject.getString("first_type");
                for (Map<String, Object> m : map) {
                    String code1 = (String) m.get("code");
                    if (code1.equals(firstType)) {
                        jsonObject.put("first_type", m.get("name"));
                        break;
                    }
                }

            }
            return resDataResult.getString("data");
        }
    }

    /**
     * 根据id查询事件日志
     *
     * @param id
     * @return
     */
    @GetMapping("/getEventLogById")
    public JSONObject getEventLogById(String id) {
        RestTemplate restTemplate = new RestTemplate();

        String tenantCode = "********************";
        String pwd = "Edde3(de78";
        String secretParam = "072f8e9c-595a-42b8-857f-f68b77a587e9";
        String resourceCode = "EX230906684882403136";
        String url = "http://10.45.178.221:8881/dataShare/query";
        String condition = "";
        if (!id.equals("")) {
            condition = "[{\"column\":\"event_id\",\"value\":" +
                    "\"" + id + "\"" +
                    ",\"type\":\"eq\"}]";
        }
        String pageNum = "1";
        String pageSize = "10";
        String nonce = UUID.randomUUID().toString();
        DateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        String created = sdf.format(new Date());
        // String responseData = "sRpjHFk3Lyu1CNBJL02c5gviG5fFChKbQ99T/fZNhPTAAfUbNqX4GGAo3wMMyL4o\r\n";
        String responseData = forGetSecretKey(tenantCode, pwd, secretParam);
        String code = JSONObject.parseObject(responseData).getString("code");
        if (!code.equals(String.valueOf(200))) {
            String msg = JSONObject.parseObject(responseData).getString("msg");
            throw new CustomException(msg);
        }
        JSONObject data = JSONObject.parseObject(responseData).getJSONObject("data");
        String resultData = data.getString("data");
        String secretKey = SymmetricEncoder.AESDncode(secretParam, resultData);
        // log.info("接收到获取密钥key为：{}", secretKey);

        String secret = nonce + "_" + created + "_" + secretKey;
        String PasswdDigest = new SM3Utils().sm3(secret);
        String pageNumS = "1";
        String pageSizeS = "500";
        if (!StringUtils.isEmpty(pageNum)) {
            pageNumS = pageNum;
        }
        if (!StringUtils.isEmpty(pageSize)) {
            pageSizeS = pageSize;
        }

        //String dataParam = "resourceCode=" + resourceCode + "&resourceSecret=" + secretParam + "&tenantCode=" + tenantCode + "&PasswdDigest=" + PasswdDigest + "&Nonce=" + nonce + "&Created=" + created + "&pageNum=" + pageNumS + "&pageSize=" + pageSizeS;
        // log.info("接收到共享查询请求地址：{},请求参数：appKey->{},tenantCode->{},passwdDigest->{},nonce->{},created->{},pageNum->{},pageSize->{}", url, secret, tenantCode, PasswdDigest, nonce, created, pageNumS, pageSizeS);

        ShareQueryParam shareQueryParam = new ShareQueryParam();
        shareQueryParam.setResourceCode(resourceCode);
        shareQueryParam.setResourceSecret(secretParam);
        shareQueryParam.setTenantCode(tenantCode);
        shareQueryParam.setPasswdDigest(PasswdDigest);
        shareQueryParam.setNonce(nonce);
        shareQueryParam.setCreated(created);
        shareQueryParam.setPageNum(pageNumS);
        shareQueryParam.setPageSize(pageSizeS);
        shareQueryParam.setCondition(condition);


        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        headers.set("tenantCode", tenantCode);
        headers.set("resCode", resourceCode);


        HttpEntity<ShareQueryParam> request =
                new HttpEntity<ShareQueryParam>(shareQueryParam, headers);


        ResponseEntity<String> responseEntity = restTemplate.postForEntity(url, request, String.class);

        String resData = responseEntity.getBody();
        // log.info("接收到共享查询的响应信息为：{}", resData);
        String resCode = JSONObject.parseObject(resData).getString("code");
        if (!resCode.equals("200")) {
            String msg = JSONObject.parseObject(resData).getString("msg");
            throw new CustomException(msg);
        } else {
            JSONObject resDataResult = JSONObject.parseObject(resData).getJSONObject("data");
            JSONObject data1 = resDataResult.getJSONObject("data");
            JSONArray records = data1.getJSONArray("records");
            records.sort(Comparator.comparing(obj -> ((JSONObject) obj).getBigInteger("createtime")).reversed());
            for (int i = 0; i < records.size(); i++) {
                JSONObject jsonObject = records.getJSONObject(i);
                String create_time = jsonObject.getString("createtime");
                log.info("create_time:" + create_time);
                DateFormat sdf1 = new SimpleDateFormat("yyyy-MM-dd HH:mm");
                String sd = sdf1.format(new Date(Long.parseLong(create_time)));
                jsonObject.put("createtime", sd);
                String type = getEventType().get(jsonObject.getString("type"));
                jsonObject.put("type", type);
            }
            return data1;
        }
    }

    private static final Map<String, String> getEventType() {
        Map<String, String> map = new HashMap<>();
        map.put("10", "上报");
        map.put("20", "受理");
        map.put("30", "办理");
        map.put("40", "办结");
        map.put("50", "反馈");
        map.put("60", "回退");
        map.put("70", "上报人审查");
        map.put("80", "指派");
        map.put("81", "分拨");
        map.put("90", "督办");
        map.put("100", "催办");
        map.put("110", "结束");
        map.put("120", "协办");
        map.put("130", "下派");
        map.put("140", "退回");
        map.put("141", "核查回退");
        map.put("142", "分拨退回");
        map.put("150", "问责");
        map.put("160", "延期申请");
        map.put("170", "延期审核");
        map.put("180", "补充说明");
        map.put("210", "申请退回");
        map.put("220", "同意退回");
        map.put("230", "拒绝退回");
        map.put("330", "上报领导");
        map.put("340", "核实触动神经");
        map.put("350", "转触动神经");
        map.put("360", "转触动神经");
        map.put("480", "自动指派");
        return map;
    }
}
