package com.ruoyi.instruction.mapper;

import com.ruoyi.instruction.domain.PersonnelStrike;
import com.ruoyi.instruction.domain.rspVo.PersonnelStrikeRspVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface PersonnelStrikeMapper {

    List<PersonnelStrikeRspVo> selectPersonnelStrikeList(PersonnelStrike personnelStrike);

    void batchInsertPersonnelStrike(List<PersonnelStrike> insertList);

    int insertPersonnelStrike(PersonnelStrike personnelStrike);

    long findCountBySfzh(@Param("idCard") String idCard);
}
