package com.ruoyi.instruction.controller;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.alibaba.excel.write.metadata.WriteTable;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.instruction.domain.rspVo.RiskDetailJawRspVo;
import com.ruoyi.instruction.domain.rspVo.RiskStatisExportVo;
import com.ruoyi.instruction.mapper.InstructionInfoMapper;
import com.ruoyi.instruction.mapper.PersonAuditMapper;
import com.ruoyi.instruction.domain.PersonAudit;
import com.ruoyi.instruction.mapper.RiskDetailsMapper;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @Description: 金安稳工作台控制器
 * @Author: LiangLiangTian
 * @Date: 2025/01/16 16:52
 */
@RestController
@RequestMapping("/jaw/workbench")
public class JawWorkbenchController extends BaseController {

    @Autowired
    private InstructionInfoMapper infoMapper;

    @Autowired
    private PersonAuditMapper personAuditMapper;

    @Autowired
    private RiskDetailsMapper detailsMapper;

    /**
     * 获取金安稳待销号数据
     * @return 待销号数量统计结果
     */
    @GetMapping("/getPendingCloseCount")
    public AjaxResult getPendingCloseCount() {
        Integer count = infoMapper.getPendingCloseCount();
        return AjaxResult.success(count);
    }


    /**
     * 指令概况（支持时间快捷选项）
     * @param timeType 时间类型：today-当天, month-近30天, halfYear-近半年, year-近1年
     * @param startTime 自定义开始时间 (yyyy-MM-dd HH:mm:ss)
     * @param endTime 自定义结束时间 (yyyy-MM-dd HH:mm:ss)
     * @return 已交办、已销号、处置中数量统计
     */
    @GetMapping("/getInstructionOverviewWithTime")
    public AjaxResult getInstructionOverviewWithTime(
            @RequestParam(required = false) String timeType,
            @RequestParam(required = false) String startTime,
            @RequestParam(required = false) String endTime) {

        // 如果指定了时间类型，计算对应的时间范围
        if (timeType != null && !timeType.isEmpty()) {
            String[] timeRange = getTimeRange(timeType);
            startTime = timeRange[0];
            endTime = timeRange[1];
        }

        Map<String, Object> overview = infoMapper.getInstructionOverview(startTime, endTime);
        return AjaxResult.success(overview);
    }

    /**
     * 根据时间类型获取时间范围
     * @param timeType 时间类型
     * @return [开始时间, 结束时间]
     */
    private String[] getTimeRange(String timeType) {
        LocalDateTime now = LocalDateTime.now();
        LocalDateTime start;
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

        switch (timeType) {
            case "today":
                // 当天：00:00:00 到 23:59:59
                start = now.toLocalDate().atStartOfDay();
                return new String[]{
                    start.format(formatter),
                    now.toLocalDate().atTime(23, 59, 59).format(formatter)
                };
            case "month":
                // 近30天
                start = now.minusDays(30);
                break;
            case "halfYear":
                // 近半年
                start = now.minusMonths(6);
                break;
            case "year":
                // 近1年
                start = now.minusYears(1);
                break;
            default:
                return new String[]{null, null};
        }

        return new String[]{start.format(formatter), now.format(formatter)};
    }



    /**
     * 查询提醒时效统计（未按时接收和未按时反馈统计数据）
     * @param timeType 时间类型：today-当天, month-近30天, halfYear-近半年, year-近1年
     * @param startTime 自定义开始时间 (yyyy-MM-dd HH:mm:ss)
     * @param endTime 自定义结束时间 (yyyy-MM-dd HH:mm:ss)
     * @return 未按时接收和未按时反馈的部门数、指令数统计
     */
    @GetMapping("/getRemindTimeoutStatistics")
    public AjaxResult getRemindTimeoutStatistics(
            @RequestParam(required = false) String timeType,
            @RequestParam(required = false) String startTime,
            @RequestParam(required = false) String endTime) {

        // 如果指定了时间类型，计算对应的时间范围
        if (timeType != null && !timeType.isEmpty()) {
            String[] timeRange = getTimeRange(timeType);
            startTime = timeRange[0];
            endTime = timeRange[1];
        }

        Map<String, Object> statistics = infoMapper.getRemindTimeoutStatistics(startTime, endTime);
        return AjaxResult.success(statistics);
    }



    /**
     * 导出未按时接收、未按时反馈详情数据
     * @param timeType 时间类型：today-当天, month-近30天, halfYear-近半年, year-近1年
     * @param startTime 自定义开始时间 (yyyy-MM-dd HH:mm:ss)
     * @param endTime 自定义结束时间 (yyyy-MM-dd HH:mm:ss)
     * @param response HttpServletResponse
     */
    @GetMapping("/exportTimeoutDetails")
    public void exportTimeoutDetails(
            @RequestParam(required = false) String timeType,
            @RequestParam(required = false) String startTime,
            @RequestParam(required = false) String endTime,
            HttpServletResponse response) throws IOException {

        // 如果指定了时间类型，计算对应的时间范围
        if (timeType != null && !timeType.isEmpty()) {
            String[] timeRange = getTimeRange(timeType);
            startTime = timeRange[0];
            endTime = timeRange[1];
        }

        // 创建工作簿
        Workbook workbook = new XSSFWorkbook();

        // 创建样式
        CellStyle headerStyle = createHeaderStyle(workbook);
        CellStyle dataStyle = createDataStyle(workbook);

        // 创建Sheet1：统计汇总
        createStatisticsSheet(workbook, headerStyle, dataStyle, startTime, endTime);

        // 创建Sheet2：未按时接收明细
        createReceiveTimeoutSheet(workbook, headerStyle, dataStyle, startTime, endTime);

        // 创建Sheet3：未按时反馈明细
        createFeedbackTimeoutSheet(workbook, headerStyle, dataStyle, startTime, endTime);

        // 设置响应头
        String fileName = "指令时效统计_" + LocalDate.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd")) + ".xlsx";
        response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
        response.setHeader("Content-Disposition", "attachment; filename=" + URLEncoder.encode(fileName, "UTF-8"));

        // 输出到响应流
        workbook.write(response.getOutputStream());
        workbook.close();
    }

    /**
     * 创建表头样式
     */
    private CellStyle createHeaderStyle(Workbook workbook) {
        CellStyle style = workbook.createCellStyle();
        style.setFillForegroundColor(IndexedColors.GREY_25_PERCENT.getIndex());
        style.setFillPattern(FillPatternType.SOLID_FOREGROUND);
        style.setBorderBottom(BorderStyle.THIN);
        style.setBorderTop(BorderStyle.THIN);
        style.setBorderRight(BorderStyle.THIN);
        style.setBorderLeft(BorderStyle.THIN);
        style.setAlignment(HorizontalAlignment.CENTER);
        style.setVerticalAlignment(VerticalAlignment.CENTER);

        Font font = workbook.createFont();
        font.setBold(true);
        style.setFont(font);

        return style;
    }

    /**
     * 创建数据样式
     */
    private CellStyle createDataStyle(Workbook workbook) {
        CellStyle style = workbook.createCellStyle();
        style.setBorderBottom(BorderStyle.THIN);
        style.setBorderTop(BorderStyle.THIN);
        style.setBorderRight(BorderStyle.THIN);
        style.setBorderLeft(BorderStyle.THIN);
        style.setAlignment(HorizontalAlignment.LEFT);
        style.setVerticalAlignment(VerticalAlignment.CENTER);

        return style;
    }

    /**
     * 创建统计汇总Sheet
     */
    private void createStatisticsSheet(Workbook workbook, CellStyle headerStyle, CellStyle dataStyle, String startTime, String endTime) {
        Sheet sheet = workbook.createSheet("统计汇总");

        int rowNum = 0;

        // 未按时接收统计
        Row titleRow1 = sheet.createRow(rowNum++);
        Cell titleCell1 = titleRow1.createCell(0);
        titleCell1.setCellValue("未按时接收统计");
        titleCell1.setCellStyle(headerStyle);

        Row headerRow1 = sheet.createRow(rowNum++);
        String[] receiveHeaders = {"单位名称", "应接收指令数", "未按时接收指令数"};
        for (int i = 0; i < receiveHeaders.length; i++) {
            Cell cell = headerRow1.createCell(i);
            cell.setCellValue(receiveHeaders[i]);
            cell.setCellStyle(headerStyle);
        }

        // 获取未按时接收统计数据
        List<Map<String, Object>> receiveStats = infoMapper.getReceiveTimeoutByDept(startTime, endTime);
        for (Map<String, Object> stat : receiveStats) {
            Row dataRow = sheet.createRow(rowNum++);
            createCell(dataRow, 0, stat.get("deptName"), dataStyle);
            createCell(dataRow, 1, stat.get("totalCount"), dataStyle);
            createCell(dataRow, 2, stat.get("timeoutCount"), dataStyle);
        }

        rowNum += 2; // 空行间隔

        // 未按时反馈统计
        Row titleRow2 = sheet.createRow(rowNum++);
        Cell titleCell2 = titleRow2.createCell(0);
        titleCell2.setCellValue("未按时反馈统计");
        titleCell2.setCellStyle(headerStyle);

        Row headerRow2 = sheet.createRow(rowNum++);
        String[] feedbackHeaders = {"单位名称", "应反馈指令数", "未按时反馈指令数"};
        for (int i = 0; i < feedbackHeaders.length; i++) {
            Cell cell = headerRow2.createCell(i);
            cell.setCellValue(feedbackHeaders[i]);
            cell.setCellStyle(headerStyle);
        }

        // 获取未按时反馈统计数据
        List<Map<String, Object>> feedbackStats = infoMapper.getFeedbackTimeoutByDept(startTime, endTime);
        for (Map<String, Object> stat : feedbackStats) {
            Row dataRow = sheet.createRow(rowNum++);
            createCell(dataRow, 0, stat.get("deptName"), dataStyle);
            createCell(dataRow, 1, stat.get("totalCount"), dataStyle);
            createCell(dataRow, 2, stat.get("timeoutCount"), dataStyle);
        }

        // 自动调整列宽
        for (int i = 0; i < 3; i++) {
            sheet.autoSizeColumn(i);
        }
    }

    /**
     * 创建未按时接收明细Sheet
     */
    private void createReceiveTimeoutSheet(Workbook workbook, CellStyle headerStyle, CellStyle dataStyle, String startTime, String endTime) {

        final DateTimeFormatter DATE_TIME_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

        Sheet sheet = workbook.createSheet("未按时接收明细");

        // 创建表头
        Row headerRow = sheet.createRow(0);
        String[] headers = {"序号", "指令标题", "紧急程度", "交办时间", "办理期限", "接收不力单位", "接收时间"};
        for (int i = 0; i < headers.length; i++) {
            Cell cell = headerRow.createCell(i);
            cell.setCellValue(headers[i]);
            cell.setCellStyle(headerStyle);
        }

        // 获取数据并填充
        List<Map<String, Object>> dataList = infoMapper.getRemindTimeoutList(startTime, endTime);
        int rowNum = 1;
        for (Map<String, Object> data : dataList) {
            Row dataRow = sheet.createRow(rowNum);

            createCell(dataRow, 0, rowNum, dataStyle);
            createCell(dataRow, 1, data.get("instructionTitle"), dataStyle);
            createCell(dataRow, 2, data.get("emergencyDegree"), dataStyle);
            createCell(dataRow, 5, data.get("receiveDept"), dataStyle);

            // 2. 对日期时间列进行手动格式化并写入
            // 交办时间 (第4列，索引为3)
            Object assignTimeObj = data.get("assignTime");
            String formattedAssignTime = formatDateTime(assignTimeObj, DATE_TIME_FORMATTER);
            createCell(dataRow, 3, formattedAssignTime, dataStyle);

            // 办理期限 (第5列，索引为4)
            Object handleTimeObj = data.get("handleTime");
            String formattedHandleTime = formatDateTime(handleTimeObj, DATE_TIME_FORMATTER);
            createCell(dataRow, 4, formattedHandleTime, dataStyle);

            // 接收时间 (第7列，索引为6)
            Object receiveTimeObj = data.get("receiveTime");
            String formattedReceiveTime = formatDateTime(receiveTimeObj, DATE_TIME_FORMATTER);
            createCell(dataRow, 6, formattedReceiveTime, dataStyle);

            rowNum++;
        }

        // 自动调整列宽
        for (int i = 0; i < headers.length; i++) {
            sheet.autoSizeColumn(i);
        }
    }

    /**
     * 一个新的辅助方法，用于将日期时间对象（如 Date, Timestamp, LocalDateTime）格式化为指定的字符串。
     * @param dateTimeObj 从数据库Map中获取的日期时间对象
     * @param formatter   日期时间格式化器
     * @return 格式化后的字符串，如果对象为null则返回空字符串
     */
    private String formatDateTime(Object dateTimeObj, DateTimeFormatter formatter) {
        if (dateTimeObj == null) {
            return "";
        }

        LocalDateTime localDateTime;
        if (dateTimeObj instanceof LocalDateTime) {
            localDateTime = (LocalDateTime) dateTimeObj;
        } else if (dateTimeObj instanceof Date) { // java.sql.Timestamp 是 Date 的子类
            localDateTime = ((Date) dateTimeObj).toInstant()
                    .atZone(ZoneId.systemDefault())
                    .toLocalDateTime();
        } else {
            return String.valueOf(dateTimeObj);
        }

        return localDateTime.format(formatter);
    }

    /**
     * 创建未按时反馈明细Sheet
     */
    private void createFeedbackTimeoutSheet(Workbook workbook, CellStyle headerStyle, CellStyle dataStyle, String startTime, String endTime) {

        final DateTimeFormatter DATE_TIME_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

        Sheet sheet = workbook.createSheet("未按时反馈明细");

        // 创建表头
        Row headerRow = sheet.createRow(0);
        String[] headers = {"序号", "指令标题", "紧急程度", "交办时间", "办理期限", "接收单位", "反馈时间", "流转单位", "处置时间"};
        for (int i = 0; i < headers.length; i++) {
            Cell cell = headerRow.createCell(i);
            cell.setCellValue(headers[i]);
            cell.setCellStyle(headerStyle);
        }

        List<Map<String, Object>> dataList = infoMapper.getOvertimeFeedbackList(startTime, endTime);
        int rowNum = 1;
        for (Map<String, Object> data : dataList) {
            Row dataRow = sheet.createRow(rowNum);

            // 使用 createCell 写入非日期列
            createCell(dataRow, 0, rowNum, dataStyle);
            createCell(dataRow, 1, data.get("instructionTitle"), dataStyle);
            createCell(dataRow, 2, data.get("emergencyDegree"), dataStyle);
            createCell(dataRow, 5, data.get("receiveDept"), dataStyle);
            createCell(dataRow, 7, data.get("transferDept"), dataStyle);

            // 2. 对所有日期时间列进行手动格式化并写入
            // 交办时间 (第4列，索引为3)
            Object assignTimeObj = data.get("assignTime");
            String formattedAssignTime = formatDateTime(assignTimeObj, DATE_TIME_FORMATTER);
            createCell(dataRow, 3, formattedAssignTime, dataStyle);

            // 办理期限 (第5列，索引为4)
            Object handleTimeObj = data.get("handleTime");
            String formattedHandleTime = formatDateTime(handleTimeObj, DATE_TIME_FORMATTER);
            createCell(dataRow, 4, formattedHandleTime, dataStyle);

            // 反馈时间 (第7列，索引为6)
            Object feedbackTimeObj = data.get("feedbackTime");
            String formattedFeedbackTime = formatDateTime(feedbackTimeObj, DATE_TIME_FORMATTER);
            createCell(dataRow, 6, formattedFeedbackTime, dataStyle);

            createCell(dataRow, 8, formattedFeedbackTime, dataStyle);

            rowNum++;
        }

        // 自动调整列宽
        for (int i = 0; i < headers.length; i++) {
            sheet.autoSizeColumn(i);
        }
    }

    /**
     * 创建单元格并设置值
     */
    private void createCell(Row row, int column, Object value, CellStyle style) {
        Cell cell = row.createCell(column);
        if (value == null) {
            cell.setCellValue("-");
        } else if (value instanceof Number) {
            cell.setCellValue(((Number) value).doubleValue());
        } else {
            cell.setCellValue(value.toString());
        }
        cell.setCellStyle(style);
    }

    /**
     * 获取人员等级变化统计（近30天和近一年）
     * @return 包含近30天和近一年等级变化统计数据
     */
    @GetMapping("/getPersonLevelChangeStatistics1")
    public AjaxResult getPersonLevelChangeStatistics1() {

        // 简化数据获取逻辑 - 统一使用getStatistics获取当前状态
        PersonAudit currentAudit = new PersonAudit();
        List<Map<Object, Object>> currentData = personAuditMapper.getStatistics(currentAudit);

        // 为了保持接口兼容性，我们将当前数据复制到各个时间段
        // 实际业务中应该根据具体需求获取历史数据
        List<Map<Object, Object>> monthData = currentData;
        List<Map<Object, Object>> yearData = currentData;
        List<Map<Object, Object>> todayData = currentData;
        List<Map<Object, Object>> lastYearSameDayData = currentData;
        List<Map<Object, Object>> monthAgoDayData = currentData;

        // 直接基于当前数据构建结果
        List<Map<String, Object>> result = buildSimplifiedStatistics(currentData);

        return AjaxResult.success(result);
    }

    @GetMapping("/getPersonLevelChangeStatistics")
    public AjaxResult getPersonLevelChangeStatistics() {

        PersonAudit personAudit = new PersonAudit();
        //查询当天的数据
        List<Map<Object, Object>> maps = personAuditMapper.getStatistics(personAudit);

        LocalDateTime now = LocalDateTime.now();
        //30天前数据
        LocalDateTime oneMonthAgo = now.minusDays(30);
        //1年前数据
        LocalDateTime oneYearAgo = now.minusDays(365);

        Map<String, Object> params = personAudit.getParams();
        params.put("beginAuditTime", oneMonthAgo.format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
        params.put("endAuditTime", now.format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
        personAudit.setParams(params);
        //查询近30天数据
        List<Map<Object, Object>> oneMonthMaps = personAuditMapper.getStatisticsByDate(personAudit);
        //查询近1年数据
        params.put("beginAuditTime", oneYearAgo.format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
        params.put("endAuditTime", now.format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
        personAudit.setParams(params);
        List<Map<Object, Object>> oneYearMaps = personAuditMapper.getStatisticsByDate(personAudit);
        List<Map<Object, Object>> result = calcPersonLevelChange(maps, oneMonthMaps, oneYearMaps);
        return AjaxResult.success(result);
    }


    /**
     * 统计人员等级变化（返回endCount、历史startCount及正负差值、同比、环比百分比）
     *
     * @param currentList  当前数据
     * @param monthAgoList 30天前数据
     * @param yearAgoList  1年前数据
     * @return List<Map < Object, Object>>，每个map包含p_level、endCount、monthStart、yearStart、monthDiff、yearDiff、yearRate、monthRate
     */
    public List<Map<Object, Object>> calcPersonLevelChange(
            List<Map<Object, Object>> currentList,
            List<Map<Object, Object>> monthAgoList,
            List<Map<Object, Object>> yearAgoList) {

        // 历史数据转为p_level->startCount的Map
        Map<Integer, Integer> monthAgoMap = monthAgoList.stream()
                .collect(Collectors.toMap(
                        m -> m.get("p_level") == null ? 0 : ((Number) m.get("p_level")).intValue(),
                        m -> m.get("startCount") == null ? 0 : ((Number) m.get("startCount")).intValue(),
                        (a, b) -> a
                ));
        Map<Integer, Integer> yearAgoMap = yearAgoList.stream()
                .collect(Collectors.toMap(
                        m -> m.get("p_level") == null ? 0 : ((Number) m.get("p_level")).intValue(),
                        m -> m.get("startCount") == null ? 0 : ((Number) m.get("startCount")).intValue(),
                        (a, b) -> a
                ));

        List<Map<Object, Object>> result = new ArrayList<>();
        for (Map<Object, Object> cur : currentList) {
            Integer pLevel = cur.get("p_level") == null ? 0 : ((Number) cur.get("p_level")).intValue();
            int endCount = cur.get("endCount") == null ? 0 : ((Number) cur.get("endCount")).intValue();
            int monthStart = monthAgoMap.getOrDefault(pLevel, 0);
            int yearStart = yearAgoMap.getOrDefault(pLevel, 0);

            int monthDiff = endCount - monthStart;
            int yearDiff = endCount - yearStart;

            // 百分比计算，分母为当前值，保留一位小数，正负号
            String monthRate = endCount != 0
                    ? String.format("%+.1f%%", monthDiff * 100.0 / endCount)
                    : "0.0%";
            String yearRate = endCount != 0
                    ? String.format("%+.1f%%", yearDiff * 100.0 / endCount)
                    : "0.0%";

            Map<Object, Object> map = new HashMap<>();
            map.put("p_level", pLevel);
            map.put("endCount", endCount);
            map.put("monthCount", monthStart);
            map.put("yearCount", yearStart);
            map.put("monthDifference", monthDiff);
            map.put("yearDifference", yearDiff);
            map.put("monthOnMonthGrowthRate", monthRate);
            map.put("yearOnYearGrowthRate", yearRate);
            result.add(map);
        }
        return result;
    }


    /**
     * 创建特定日期范围的PersonAudit对象
     * @param daysAgo 多少天前开始
     * @param duration 持续天数
     * @return PersonAudit对象
     */
    private PersonAudit createPersonAuditWithSpecificDate(int daysAgo, int duration) {
        PersonAudit audit = new PersonAudit();

        // 设置type（触发getGradeChangeStatistics方法）
        audit.setType(1);

        // 计算时间范围
        LocalDateTime now = LocalDateTime.now();
        LocalDateTime startTime = now.minusDays(daysAgo);
        LocalDateTime endTime = startTime.plusDays(duration);

        // 设置时间参数
        Map<String, Object> params = new HashMap<>();
        params.put("beginAuditTime", startTime.format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
        params.put("endAuditTime", endTime.format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
        audit.setParams(params);

        return audit;
    }

    /**
     * 合并统计数据并计算同比环比
     * @param monthData 近30天数据
     * @param yearData 近一年数据
     * @param todayData 当天数据
     * @param lastYearSameDayData 去年同期数据
     * @param monthAgoDayData 30天前数据
     * @return 合并后的数据
     */
    private List<Map<String, Object>> mergeStatisticsDataWithComparison(
            List<Map<Object, Object>> monthData,
            List<Map<Object, Object>> yearData,
            List<Map<Object, Object>> todayData,
            List<Map<Object, Object>> lastYearSameDayData,
            List<Map<Object, Object>> monthAgoDayData) {

        List<Map<String, Object>> result = new ArrayList<>();

        // 将各个数据集转换为Map，以p_level为key
        Map<Object, Map<Object, Object>> yearDataMap = convertToLevelMap(yearData);
        Map<Object, Map<Object, Object>> todayDataMap = convertToLevelMap(todayData);
        Map<Object, Map<Object, Object>> lastYearSameDayDataMap = convertToLevelMap(lastYearSameDayData);
        Map<Object, Map<Object, Object>> monthAgoDayDataMap = convertToLevelMap(monthAgoDayData);

        // 收集所有可能的等级
        Set<Object> allLevels = new HashSet<>();
        if (monthData != null) {
            allLevels.addAll(monthData.stream()
                    .filter(item -> item != null && item.get("p_level") != null)
                    .map(item -> item.get("p_level"))
                    .collect(Collectors.toSet()));
        }
        allLevels.addAll(yearDataMap.keySet());
        allLevels.addAll(todayDataMap.keySet());
        allLevels.addAll(lastYearSameDayDataMap.keySet());
        allLevels.addAll(monthAgoDayDataMap.keySet());

        // 遍历所有等级，构建结果
        for (Object pLevel : allLevels) {
            if (pLevel == null) {
                continue; // 跳过null值
            }
            Map<String, Object> mergedItem = new HashMap<>();

            // 基本信息
            mergedItem.put("p_level", pLevel);

            // 获取各时间段的数据
            Map<Object, Object> monthItem = findByLevel(monthData, pLevel);
            Map<Object, Object> yearItem = yearDataMap.get(pLevel);
            Map<Object, Object> todayItem = todayDataMap.get(pLevel);
            Map<Object, Object> lastYearSameDayItem = lastYearSameDayDataMap.get(pLevel);
            Map<Object, Object> monthAgoDayItem = monthAgoDayDataMap.get(pLevel);

            // 基础数据
            Integer monthCount = getStartCount(monthItem);
            Integer yearCount = getStartCount(yearItem);
            Integer endCount = getEndCount(monthItem != null ? monthItem : yearItem);
            Integer todayCount = getStartCount(todayItem);
            Integer lastYearSameDayCount = getStartCount(lastYearSameDayItem);
            Integer monthAgoDayCount = getStartCount(monthAgoDayItem);

            mergedItem.put("monthCount", monthCount);
            mergedItem.put("yearCount", yearCount);
            mergedItem.put("endCount", endCount);

            // 计算近30天差值（endCount - monthCount）
            Integer monthDifference = endCount - monthCount;
            mergedItem.put("monthDifference", formatWithSign(monthDifference));

            // 计算近一年差值（endCount - yearCount）
            Integer yearDifference = endCount - yearCount;
            mergedItem.put("yearDifference", formatWithSign(yearDifference));

            // 计算同比（当天与去年同期）
            Integer yearOnYearComparison = todayCount - lastYearSameDayCount;
            mergedItem.put("yearOnYearComparison", formatWithSign(yearOnYearComparison));

            // 计算同比增长率（基于endCount）
            String yearOnYearGrowthRate = calculateGrowthRate(yearOnYearComparison, endCount);
            mergedItem.put("yearOnYearGrowthRate", yearOnYearGrowthRate);

            // 计算环比（当天与30天前）
            Integer monthOnMonthComparison = todayCount - monthAgoDayCount;
            mergedItem.put("monthOnMonthComparison", formatWithSign(monthOnMonthComparison));

            // 计算环比增长率（基于endCount）
            String monthOnMonthGrowthRate = calculateGrowthRate(monthOnMonthComparison, endCount);
            mergedItem.put("monthOnMonthGrowthRate", monthOnMonthGrowthRate);

            result.add(mergedItem);
        }

        // 按p_level排序
        result.sort((a, b) -> {
            Integer levelA = convertToInteger(a.get("p_level"));
            Integer levelB = convertToInteger(b.get("p_level"));
            return levelA.compareTo(levelB);
        });

        return result;
    }

    /**
     * 将数据列表转换为以p_level为key的Map
     */
    private Map<Object, Map<Object, Object>> convertToLevelMap(List<Map<Object, Object>> dataList) {
        Map<Object, Map<Object, Object>> result = new HashMap<>();
        if (dataList != null) {
            for (Map<Object, Object> item : dataList) {
                if (item != null && item.get("p_level") != null) {
                    result.put(item.get("p_level"), item);
                }
            }
        }
        return result;
    }

    /**
     * 根据等级查找数据项
     */
    private Map<Object, Object> findByLevel(List<Map<Object, Object>> dataList, Object pLevel) {
        if (dataList == null || pLevel == null) {
            return null;
        }
        return dataList.stream()
                .filter(item -> item != null && Objects.equals(item.get("p_level"), pLevel))
                .findFirst()
                .orElse(null);
    }

    /**
     * 安全获取startCount值
     */
    private Integer getStartCount(Map<Object, Object> item) {
        if (item == null || item.get("startCount") == null) {
            return 0;
        }
        return convertToInteger(item.get("startCount"));
    }

    /**
     * 安全获取endCount值
     */
    private Integer getEndCount(Map<Object, Object> item) {
        if (item == null || item.get("endCount") == null) {
            return 0;
        }
        return convertToInteger(item.get("endCount"));
    }

    /**
     * 格式化数字，保留符号
     */
    private String formatWithSign(Integer value) {
        if (value == null || value == 0) {
            return "0";
        }
        return value > 0 ? "+" + value : String.valueOf(value);
    }

    /**
     * 安全转换为Integer类型
     */
    private Integer convertToInteger(Object value) {
        if (value == null) {
            return 0;
        }
        if (value instanceof Long) {
            return ((Long) value).intValue();
        } else if (value instanceof Integer) {
            return (Integer) value;
        } else {
            return Integer.valueOf(value.toString());
        }
    }

    /**
     * 基于当前数据构建简化的统计结果
     */
    private List<Map<String, Object>> buildSimplifiedStatistics(List<Map<Object, Object>> currentData) {
        List<Map<String, Object>> result = new ArrayList<>();

        if (currentData != null) {
            for (Map<Object, Object> item : currentData) {
                if (item != null && item.get("p_level") != null) {
                    Map<String, Object> mergedItem = new HashMap<>();

                    // 基本信息
                    mergedItem.put("p_level", convertToInteger(item.get("p_level")));

                    // 基础数据 - 当前统计数据
                    Integer endCount = convertToInteger(item.get("endCount"));
                    Integer startCount = convertToInteger(item.get("startCount"));

                    mergedItem.put("monthCount", startCount);
                    mergedItem.put("yearCount", startCount);
                    mergedItem.put("endCount", endCount);

                    // 计算变化（暂时为示例数据）
                    Integer monthDifference = endCount - startCount;
                    Integer yearDifference = endCount - startCount;

                    mergedItem.put("monthDifference", formatWithSign(monthDifference));
                    mergedItem.put("yearDifference", formatWithSign(yearDifference));

                    // 同比环比（暂时为0）
                    mergedItem.put("yearOnYearComparison", "0");
                    mergedItem.put("monthOnMonthComparison", "0");

                    // 增长率
                    String yearOnYearGrowthRate = calculateGrowthRate(0, endCount);
                    String monthOnMonthGrowthRate = calculateGrowthRate(0, endCount);

                    mergedItem.put("yearOnYearGrowthRate", yearOnYearGrowthRate);
                    mergedItem.put("monthOnMonthGrowthRate", monthOnMonthGrowthRate);

                    result.add(mergedItem);
                }
            }
        }

        // 按p_level排序
        result.sort((a, b) -> {
            Integer levelA = (Integer) a.get("p_level");
            Integer levelB = (Integer) b.get("p_level");
            return levelA.compareTo(levelB);
        });

        return result;
    }

    /**
     * 计算增长率百分比
     * @param changeValue 变化值（绝对值）
     * @param baseValue 基数值（endCount）
     * @return 格式化的百分比字符串，保留一位小数
     */
    private String calculateGrowthRate(Integer changeValue, Integer baseValue) {
        // 如果基数为0或null，无法计算增长率
        if (baseValue == null || baseValue == 0) {
            return "N/A";
        }

        // 如果变化值为0或null，增长率为0
        if (changeValue == null || changeValue == 0) {
            return "0.0%";
        }

        // 计算增长率：(变化值 / 基数) * 100
        double growthRate = (changeValue.doubleValue() / baseValue.doubleValue()) * 100;

        // 格式化为保留一位小数的百分比，带符号
        if (growthRate > 0) {
            return String.format("+%.1f%%", growthRate);
        } else {
            return String.format("%.1f%%", growthRate);
        }
    }

    /**
     * 创建带时间范围的PersonAudit对象
     * @param days 天数
     * @param type 类型标识
     * @return PersonAudit对象
     */
    private PersonAudit createPersonAuditWithTimeRange(int days, String type) {
        PersonAudit audit = new PersonAudit();

        // 设置type（触发getGradeChangeStatistics方法）
        audit.setType(1); // 设置为非null值以触发getGradeChangeStatistics方法

        // 设置时间参数
        LocalDateTime now = LocalDateTime.now();
        LocalDateTime startTime = now.minusDays(days);
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

        Map<String, Object> params = new HashMap<>();
        params.put("beginAuditTime", startTime.format(formatter));
        params.put("endAuditTime", now.format(formatter));

        audit.setParams(params);

        return audit;
    }

    /**
     * 查询重点人员类型统计
     * @param pLevel 可选参数，人员等级筛选
     * @return 人员类型统计数据
     */
    @GetMapping("/getPersonTypeStatistics")
    public AjaxResult getPersonTypeStatistics(@RequestParam(required = false) Integer pLevel) {
        List<Map<String, Object>> statistics = infoMapper.getPersonTypeStatistics(pLevel);
        return AjaxResult.success(statistics);
    }

    /**
     * 查询近七天活跃次数超过指定次数的人员信息
     * @param days 天数，默认7天
     * @param minCount 最小活跃次数，默认2次
     * @return 活跃人员信息列表
     */
    @GetMapping("/getActivePersons")
    public AjaxResult getActivePersons(
            @RequestParam(defaultValue = "7") Integer days,
            @RequestParam(defaultValue = "2") Integer minCount) {
        try {
            // 1. 计算时间范围
            LocalDateTime endTime = LocalDateTime.now();
            LocalDateTime startTime = endTime.minusDays(days);
            String startTimeStr = startTime.format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));

            // 2. 查询近N天所有的person_ids (模拟 GROUP_CONCAT 查询)
            String personIdsConcat = getPersonIdsFromRecentEvents(startTimeStr);

            // 3. 统计每个人员ID的出现次数
            Map<String, Integer> personCountMap = calculatePersonMoveCount(personIdsConcat);

            // 4. 筛选出活跃次数超过指定次数的人员ID
            List<String> activePersonIds = personCountMap.entrySet().stream()
                    .filter(entry -> entry.getValue() >= minCount)
                    .map(Map.Entry::getKey)
                    .collect(Collectors.toList());

            if (activePersonIds.isEmpty()) {
                return AjaxResult.success(new ArrayList<>());
            }

            // 5. 根据人员ID查询人员详细信息
            List<Map<String, Object>> activePersons = getPersonInfoByIds(activePersonIds);

            // 6. 将活跃次数添加到人员信息中，并确保所有字段都存在
            List<Map<String, Object>> result = activePersons.stream()
                    .map(person -> {
                        Map<String, Object> personWithCount = new HashMap<>(person);
                        String personId = String.valueOf(person.get("id"));
                        Integer moveCount = personCountMap.getOrDefault(personId, 0);
                        personWithCount.put("moveCount", moveCount);

                        // 确保所有字段都存在，空值用空字符串替代
                        personWithCount.put("person_name", personWithCount.getOrDefault("person_name", ""));
                        personWithCount.put("duty_place", personWithCount.getOrDefault("duty_place", ""));
                        personWithCount.put("p_town", personWithCount.getOrDefault("p_town", ""));
                        personWithCount.put("group_name", personWithCount.getOrDefault("group_name", ""));

                        return personWithCount;
                    })
                    .sorted((a, b) -> {
                        // 按活跃次数降序排列
                        Integer countA = (Integer) a.get("moveCount");
                        Integer countB = (Integer) b.get("moveCount");
                        return countB.compareTo(countA);
                    })
                    .collect(Collectors.toList());

            return AjaxResult.success(result);

        } catch (Exception e) {
            logger.error("查询活跃人员失败", e);
            return AjaxResult.error("查询活跃人员失败: " + e.getMessage());
        }
    }

    /**
     * 查询近N天所有事件的person_ids (真实SQL查询)
     * @param startTimeStr 开始时间字符串
     * @return 所有person_ids用逗号连接的字符串
     */
    private String getPersonIdsFromRecentEvents(String startTimeStr) {
        try {
            // 直接使用SQL查询获取近N天的person_ids
            // SQL: SELECT GROUP_CONCAT(person_ids) FROM t_instruction_event
            // WHERE STATUS = 1 AND create_time > ? AND person_ids IS NOT NULL

            // 使用真实SQL查询获取近N天的person_ids
            String personIdsResult = infoMapper.getPersonIdsConcat(startTimeStr);
            return personIdsResult != null ? personIdsResult : "";

        } catch (Exception e) {
            logger.error("查询事件person_ids失败", e);
            return "";
        }
    }

    /**
     * 统计person_ids字符串中每个ID的出现次数
     * @param personIdsConcat 逗号分隔的person_ids字符串
     * @return 人员ID -> 出现次数的映射
     */
    private Map<String, Integer> calculatePersonMoveCount(String personIdsConcat) {
        Map<String, Integer> countMap = new HashMap<>();

        if (personIdsConcat == null || personIdsConcat.trim().isEmpty()) {
            return countMap;
        }

        // 按逗号分割所有person_ids
        String[] allPersonIds = personIdsConcat.split(",");

        for (String personId : allPersonIds) {
            String cleanId = personId.trim();
            // 确保是有效的数字ID
            if (!cleanId.isEmpty() && cleanId.matches("\\d+")) {
                countMap.put(cleanId, countMap.getOrDefault(cleanId, 0) + 1);
            }
        }

        return countMap;
    }

    /**
     * 根据人员ID列表查询人员详细信息 (真实SQL查询)
     * @param personIds 人员ID列表
     * @return 人员信息列表
     */
    private List<Map<String, Object>> getPersonInfoByIds(List<String> personIds) {
        try {
            // 使用真实SQL查询人员信息
            // SQL: SELECT id,person_name,duty_place,p_town,group_name
            // FROM t_instruction_person WHERE status = 1 AND id IN (?)

            if (personIds.isEmpty()) {
                return new ArrayList<>();
            }

            // 使用真实SQL查询人员信息
            List<Map<String, Object>> personInfoList = infoMapper.getPersonInfoByIds(personIds);
            return personInfoList != null ? personInfoList : new ArrayList<>();

        } catch (Exception e) {
            logger.error("查询人员信息失败", e);
            return new ArrayList<>();
        }
    }

    /**
     * 查询人员走访记录按等级分组统计
     * @param timeRange 时间范围筛选：month30-超过30天, day15to30-超过15-30天, day7to15-超过7-15天, day3to7-超过3-7天, day1to3-超过1-3天
     * @return 按人员等级分组的统计数据
     */
    @GetMapping("/getPersonVisitStatisticsByLevel")
    public AjaxResult getPersonVisitStatisticsByLevel(@RequestParam(required = false) String timeRange) {
        List<Map<String, Object>> statistics = infoMapper.getPersonVisitStatisticsByLevel(timeRange);
        return AjaxResult.success(statistics);
    }


    /**
     * 导出人员走访记录统计
     * @param timeRange 时间范围筛选：month30-超过30天, day15to30-超过15-30天, day7to15-超过7-15天, day3to7-超过3-7天, day1to3-超过1-3天
     * @param response HttpServletResponse
     */
    @GetMapping("/exportPersonVisitStatistics")
    public void exportPersonVisitStatistics(
            @RequestParam(required = false) String timeRange,
            HttpServletResponse response) throws IOException {

        // 创建工作簿
        Workbook workbook = new XSSFWorkbook();

        // 创建样式
        CellStyle headerStyle = createHeaderStyle(workbook);
        CellStyle dataStyle = createDataStyle(workbook);

        // 创建Sheet1：统计汇总
        createPersonVisitSummarySheet(workbook, headerStyle, dataStyle);

        // 创建Sheet2：详细列表
        createPersonVisitDetailSheet(workbook, headerStyle, dataStyle, timeRange);

        // 设置响应头
        String fileName = "人员走访记录统计_" + LocalDate.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd")) + ".xlsx";
        response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
        response.setHeader("Content-Disposition", "attachment; filename=" + URLEncoder.encode(fileName, "UTF-8"));

        // 输出到响应流
        workbook.write(response.getOutputStream());
        workbook.close();
    }

    /**
     * 创建人员走访统计汇总Sheet
     */
    private void createPersonVisitSummarySheet(Workbook workbook, CellStyle headerStyle, CellStyle dataStyle) {
        Sheet sheet = workbook.createSheet("统计汇总");

        // 创建表头
        Row headerRow = sheet.createRow(0);
        String[] headers = {"属地", "超过1个月", "超15-30天", "超7-15天", "超3-7天", "超1-3天"};
        for (int i = 0; i < headers.length; i++) {
            Cell cell = headerRow.createCell(i);
            cell.setCellValue(headers[i]);
            cell.setCellStyle(headerStyle);
        }

        // 获取统计汇总数据
        List<Map<String, Object>> summaryData = infoMapper.getPersonVisitStatisticsSummary();
        int rowNum = 1;
        for (Map<String, Object> data : summaryData) {
            Row dataRow = sheet.createRow(rowNum);
            createCell(dataRow, 0, data.get("dutyPlace"), dataStyle);
            createCell(dataRow, 1, data.get("month30Count"), dataStyle);
            createCell(dataRow, 2, data.get("day15to30Count"), dataStyle);
            createCell(dataRow, 3, data.get("day7to15Count"), dataStyle);
            createCell(dataRow, 4, data.get("day3to7Count"), dataStyle);
            createCell(dataRow, 5, data.get("day1to3Count"), dataStyle);
            rowNum++;
        }

        // 自动调整列宽
        for (int i = 0; i < headers.length; i++) {
            sheet.autoSizeColumn(i);
        }
    }

    /**
     * 创建人员走访详细列表Sheet
     */
    private void createPersonVisitDetailSheet(Workbook workbook, CellStyle headerStyle, CellStyle dataStyle, String timeRange) {
        Sheet sheet = workbook.createSheet("详细列表");

        // 创建表头
        Row headerRow = sheet.createRow(0);
        String[] headers = {"序号", "姓名", "属地", "责任乡镇", "管控等级", "走访要求", "最后一次走访时间", "未走访超过时间"};
        for (int i = 0; i < headers.length; i++) {
            Cell cell = headerRow.createCell(i);
            cell.setCellValue(headers[i]);
            cell.setCellStyle(headerStyle);
        }

        // 获取详细数据
        List<Map<String, Object>> detailData = infoMapper.getPersonVisitStatistics(timeRange);
        int rowNum = 1;
        for (Map<String, Object> data : detailData) {
            Row dataRow = sheet.createRow(rowNum);
            createCell(dataRow, 0, rowNum, dataStyle);
            createCell(dataRow, 1, data.get("personName"), dataStyle);
            createCell(dataRow, 2, data.get("dutyPlace"), dataStyle);
            createCell(dataRow, 3, data.get("pTown"), dataStyle);
            createCell(dataRow, 4, data.get("levelName"), dataStyle);
            createCell(dataRow, 5, data.get("visitRequirement"), dataStyle);
            createCell(dataRow, 6, data.get("lastVisitTime"), dataStyle);
            createCell(dataRow, 7, data.get("overduePeriod"), dataStyle);
            rowNum++;
        }

        // 自动调整列宽
        for (int i = 0; i < headers.length; i++) {
            sheet.autoSizeColumn(i);
        }
    }

    /**
     * 获取风险提醒统计
     * @param type
     * @return
     */
    @GetMapping("/getRiskRemindStatis")
    public AjaxResult getRiskRemindStatis(@RequestParam(required = false) String type) {
        List<RiskDetailJawRspVo> list = detailsMapper.getRiskRemindStatis(type);

        // 先按level分组计数
        Map<Integer, Long> levelCountMap = list.stream()
                .filter(item -> item.getLevel() != null)
                .collect(Collectors.groupingBy(RiskDetailJawRspVo::getLevel, Collectors.counting()));

        // 补齐1~4
        List<Map<String, Object>> result = new ArrayList<>();
        for (int level = 1; level <= 4; level++) {
            Map<String, Object> map = new HashMap<>();
            map.put("level", level);
            map.put("count", levelCountMap.getOrDefault(level, 0L));
            result.add(map);
        }

        return AjaxResult.success(result);
    }


    /**
     * 导出风险排查总表
     * @param response
     * @param type
     * @throws IOException
     */
    @PostMapping("/exportRiskRemind")
    public void exportRiskRemind(HttpServletResponse response, @RequestParam(required = false) String type) throws IOException {
        List<RiskDetailJawRspVo> list = detailsMapper.getRiskRemindStatis(type);
        List<RiskStatisExportVo> riskStatisExportVos = new ArrayList<>();
        String[] countyStrings = {"婺城区", "金东区", "兰溪市", "东阳市", "义乌市", "永康市", "浦江县", "武义县", "磐安县", "开发区"};
        for (String countyString : countyStrings){
            RiskStatisExportVo riskStatisExportVo = new RiskStatisExportVo();
            riskStatisExportVo.setUnitName(countyString);
            long moreThreeMonth = list.stream().filter(item -> item.getDutyUnit().equals(countyString)&&item.getFollowStatus().equals("超3个月未跟进")).count();
            long moreTwoMonth = list.stream().filter(item -> item.getDutyUnit().equals(countyString)&&item.getFollowStatus().equals("超2-3个月")).count();
            long moreOneMonth = list.stream().filter(item -> item.getDutyUnit().equals(countyString)&&item.getFollowStatus().equals("超1-2个月")).count();
            long oneMonth = list.stream().filter(item -> item.getDutyUnit().equals(countyString)&&item.getFollowStatus().equals("超半个月-1个月")).count();
            long moreOneWeek = list.stream().filter(item -> item.getDutyUnit().equals(countyString)&&item.getFollowStatus().equals("超1周-2周")).count();
            riskStatisExportVo.setMoreThreeMonthNum(moreThreeMonth);
            riskStatisExportVo.setMoreTwoMonthNum(moreTwoMonth);
            riskStatisExportVo.setMoreOneMonthNum(moreOneMonth);
            riskStatisExportVo.setOneWeekNum(moreOneWeek);
            riskStatisExportVo.setOneMonthNum(oneMonth);
            riskStatisExportVos.add(riskStatisExportVo);

        }

        try {
            response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
            response.setCharacterEncoding("utf-8");
            // 这里URLEncoder.encode可以防止中文乱码 当然和easyexcel没有关系
            String fileName = URLEncoder.encode("测试", "UTF-8").replaceAll("\\+", "%20");
            response.setHeader("Content-disposition", "attachment;filename*=utf-8''" + fileName + ".xlsx");
            ExcelWriter excelWriter = EasyExcel.write(response.getOutputStream()).build();

            // List<FeedbackPoorRsp> feedbackPoorRspList = infoMapper.getFeedbackPoor(params,1);

            //表格1:写入统计
            // // 把sheet设置为不需要头 不然会输出sheet的头 这样看起来第一个table 就有2个头了
            // WriteSheet threeSheet = EasyExcel.writerSheet("统计结果").needHead(Boolean.FALSE).build();
            // // 这里必须指定需要头，table 会继承sheet的配置，sheet配置了不需要，table 默认也是不需要
            // WriteTable writeTable0 = EasyExcel.writerTable(0).head(PoorTrackStatisRspVo.class).needHead(Boolean.TRUE).build();
            // // 第二个对象 读取对象的excel实体类中的标题
            // WriteTable writeTable1 = EasyExcel.writerTable(1).head(PoorFeedbackTrackStatisRspVo.class).needHead(Boolean.TRUE).build();
            // // 第一次写入会创建头
            // excelWriter.write(data(poorTrackList,params), threeSheet, writeTable0);
            // // 第二次写如也会创建头，然后在第一次的后面写入数据
            // excelWriter.write(getFeedbackData(feedbackPoorRspList, params), threeSheet, writeTable1);

            // 表格3:写入处置不力
            WriteSheet twoSheet = EasyExcel.writerSheet(1, "跟进情况统计").head(RiskStatisExportVo.class).build();
            excelWriter.write(riskStatisExportVos, twoSheet);

            //表格2:写入接收不力
            WriteSheet writeSheet = EasyExcel.writerSheet(0, "跟进明细").head(RiskDetailJawRspVo.class).build();
            excelWriter.write(list, writeSheet);


            // 关闭 ExcelWriter
            excelWriter.finish();
        } catch (UnsupportedEncodingException e) {
            // 处理编码异常
            System.err.println("编码错误: " + e.getMessage());
        }

    }

}
