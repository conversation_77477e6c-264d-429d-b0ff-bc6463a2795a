package com.ruoyi.instruction.controller;

import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.constant.InstructionRolesConstants;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.domain.entity.SysDept;
import com.ruoyi.common.core.domain.entity.SysRole;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.DeptUtils;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.instruction.domain.EasyCauseTrouble;
import com.ruoyi.instruction.domain.HealthCommission;
import com.ruoyi.instruction.domain.PublicSecurity;
import com.ruoyi.instruction.domain.TownShip;
import com.ruoyi.instruction.service.IEasyCauseTroubleService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 易肇事Controller
 *
 * <AUTHOR> Xu
 * @date 2024-06-26
 */
@RestController
@RequestMapping("/easycause/trouble")
public class EasyCauseTroubleController extends BaseController {
    @Autowired
    private IEasyCauseTroubleService easyCauseTroubleService;


    /**
     * 查询易肇事列表
     */
    @GetMapping("/list")
    public TableDataInfo list(EasyCauseTrouble easyCauseTrouble)
    {
        //县市区账号进行过滤
        List<SysRole> roles = SecurityUtils.getLoginUser().getUser().getRoles();
        List<String> roleList = roles.stream().map(SysRole::getRoleKey).collect(Collectors.toList());
        if (!roleList.contains(InstructionRolesConstants.CITY_INSTRUCTION)) {
            //查询用户父级部门所在县市区 县市区仅能查看自己所属区域的人员
            SysDept dept = SecurityUtils.getLoginUser().getUser().getDept();
            String dutyPlace = DeptUtils.getDutyPlaceByDept(dept);
            easyCauseTrouble.setCounty(dutyPlace);
            if (dept.getParentId() != 213L && dept.getParentId() != 214L && dept.getParentId() != 215L
                    && dept.getParentId() != 216L && dept.getParentId() != 217L && dept.getParentId() != 218L
                    && dept.getParentId() != 219L && dept.getParentId() != 220L && dept.getParentId() != 221L
                    && dept.getParentId() != 262L) {
                easyCauseTrouble.setStreet(dept.getDeptName());
            }
        }
        startPage();
        List<EasyCauseTrouble> list = easyCauseTroubleService.selectEasyCauseTroubleList(easyCauseTrouble);
        return getDataTable(list);
    }

    /**
     * 查询易肇事详情
     */
    @GetMapping("/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return AjaxResult.success(easyCauseTroubleService.selectEasyCauseTroubleById(id));
    }

    /**
     * 下载卫健委数据模板
     *
     * @param response
     */
    @PostMapping("/importHealthCommissionTemplate")
    public void importHealthCommissionTemplate(HttpServletResponse response) {
        ExcelUtil<HealthCommission> util = new ExcelUtil<HealthCommission>(HealthCommission.class);
        util.importTemplateExcel(response, "卫健委数据导入模板");
    }


    /**
     * 导入卫健委数据
     */
    @PostMapping("/importHealthCommissionData")
    public AjaxResult importHealthCommissionData(MultipartFile file) throws Exception {
        ExcelUtil<HealthCommission> util = new ExcelUtil<HealthCommission>(HealthCommission.class);
        List<HealthCommission> list = util.importExcel(file.getInputStream());
        //存入数据
        return easyCauseTroubleService.importHealthCommissionData(list);

    }



    /**
     * 下载公安数据模板
     *
     * @param response
     */
    @PostMapping("/importPublicSecurityTemplate")
    public void importPublicSecurityTemplate(HttpServletResponse response) {
        ExcelUtil<PublicSecurity> util = new ExcelUtil<PublicSecurity>(PublicSecurity.class);
        util.importTemplateExcel(response, "公安数据导入模板");
    }

    /**
     * 导入公安数据
     */
    @PostMapping("/importPublicSecurityData")
    public AjaxResult importPublicSecurityData(MultipartFile file) throws Exception {
        ExcelUtil<PublicSecurity> util = new ExcelUtil<PublicSecurity>(PublicSecurity.class);
        List<PublicSecurity> list = util.importExcel(file.getInputStream());
        //存入数据
        return easyCauseTroubleService.importPublicSecurityData(list);
    }

    /**
     * 下载乡镇模板
     */
    @PostMapping("/importTownshipTemplate")
    public void importTownshipTemplate(HttpServletResponse response) {
        ExcelUtil<TownShip> util = new ExcelUtil<TownShip>(TownShip.class);
        util.importTemplateExcel(response, "乡镇数据导入模板");
    }

    /**
     * 导入乡镇数据
     */
    @PostMapping("/importTownshipData")
    public AjaxResult importTownshipData(MultipartFile file) throws Exception {
        ExcelUtil<TownShip> util = new ExcelUtil<TownShip>(TownShip.class);
        List<TownShip> list = util.importExcel(file.getInputStream());
        //存入数据
        return easyCauseTroubleService.importTownshipData(list);
    }

    /**
     * 导出易肇事
     */
    @Log(title = "导出易肇事", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, @RequestBody EasyCauseTrouble easyCauseTrouble)
    {
        //县市区账号进行过滤
        List<SysRole> roles = SecurityUtils.getLoginUser().getUser().getRoles();
        List<String> roleList = roles.stream().map(SysRole::getRoleKey).collect(Collectors.toList());
        if (!roleList.contains(InstructionRolesConstants.CITY_INSTRUCTION)) {
            //查询用户父级部门所在县市区 县市区仅能查看自己所属区域的人员
            SysDept dept = SecurityUtils.getLoginUser().getUser().getDept();
            String dutyPlace = DeptUtils.getDutyPlaceByDept(dept);
            easyCauseTrouble.setCounty(dutyPlace);
            if (dept.getParentId() != 213L && dept.getParentId() != 214L && dept.getParentId() != 215L
                    && dept.getParentId() != 216L && dept.getParentId() != 217L && dept.getParentId() != 218L
                    && dept.getParentId() != 219L && dept.getParentId() != 220L && dept.getParentId() != 221L
                    && dept.getParentId() != 262L) {
                easyCauseTrouble.setStreet(dept.getDeptName());
            }
        }
        List<EasyCauseTrouble> list = easyCauseTroubleService.selectEasyCauseTroubleList(easyCauseTrouble);
        ExcelUtil<EasyCauseTrouble> util = new ExcelUtil<EasyCauseTrouble>(EasyCauseTrouble.class);
        util.exportExcel(response, list, "易肇事数据");
    }

    /**
     * 易肇事统计
     */
    @GetMapping("/stastic")
    public AjaxResult stastic()
    {
        EasyCauseTrouble easyCauseTrouble = new EasyCauseTrouble();
        //县市区账号进行过滤
        List<SysRole> roles = SecurityUtils.getLoginUser().getUser().getRoles();
        List<String> roleList = roles.stream().map(SysRole::getRoleKey).collect(Collectors.toList());
        if (!roleList.contains(InstructionRolesConstants.CITY_INSTRUCTION)) {
            //查询用户父级部门所在县市区 县市区仅能查看自己所属区域的人员
            SysDept dept = SecurityUtils.getLoginUser().getUser().getDept();
            String dutyPlace = DeptUtils.getDutyPlaceByDept(dept);
            easyCauseTrouble.setCounty(dutyPlace);
            if (dept.getParentId() != 213L && dept.getParentId() != 214L && dept.getParentId() != 215L
                    && dept.getParentId() != 216L && dept.getParentId() != 217L && dept.getParentId() != 218L
                    && dept.getParentId() != 219L && dept.getParentId() != 220L && dept.getParentId() != 221L
                    && dept.getParentId() != 262L) {
                easyCauseTrouble.setStreet(dept.getDeptName());
            }
        }
        return AjaxResult.success(easyCauseTroubleService.stastic(easyCauseTrouble));
    }


}
