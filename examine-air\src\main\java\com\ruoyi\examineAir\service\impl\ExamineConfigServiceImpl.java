package com.ruoyi.examineAir.service.impl;

import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.examineAir.mapper.ExamineConfigMapper;
import com.ruoyi.examineAir.domain.ExamineConfig;
import com.ruoyi.examineAir.service.IExamineConfigService;

/**
 * 考核晾晒动态配置信息Service业务层处理
 * 
 * <AUTHOR>
 * @date 2023-04-19
 */
@Service
public class ExamineConfigServiceImpl implements IExamineConfigService 
{
    @Autowired
    private ExamineConfigMapper examineConfigMapper;

    /**
     * 查询考核晾晒动态配置信息
     * 
     * @param id 考核晾晒动态配置信息主键
     * @return 考核晾晒动态配置信息
     */
    @Override
    public ExamineConfig selectExamineConfigById(Long id)
    {
        return examineConfigMapper.selectExamineConfigById(id);
    }

    /**
     * 查询考核晾晒动态配置信息列表
     * 
     * @param examineConfig 考核晾晒动态配置信息
     * @return 考核晾晒动态配置信息
     */
    @Override
    public List<ExamineConfig> selectExamineConfigList(ExamineConfig examineConfig)
    {
        return examineConfigMapper.selectExamineConfigList(examineConfig);
    }

    /**
     * 新增考核晾晒动态配置信息
     * 
     * @param examineConfig 考核晾晒动态配置信息
     * @return 结果
     */
    @Override
    public int insertExamineConfig(ExamineConfig examineConfig)
    {
        return examineConfigMapper.insertExamineConfig(examineConfig);
    }

    /**
     * 修改考核晾晒动态配置信息
     * 
     * @param examineConfig 考核晾晒动态配置信息
     * @return 结果
     */
    @Override
    public int updateExamineConfig(ExamineConfig examineConfig)
    {
        return examineConfigMapper.updateExamineConfig(examineConfig);
    }

    /**
     * 批量删除考核晾晒动态配置信息
     * 
     * @param ids 需要删除的考核晾晒动态配置信息主键
     * @return 结果
     */
    @Override
    public int deleteExamineConfigByIds(Long[] ids)
    {
        return examineConfigMapper.deleteExamineConfigByIds(ids);
    }

    /**
     * 删除考核晾晒动态配置信息信息
     * 
     * @param id 考核晾晒动态配置信息主键
     * @return 结果
     */
    @Override
    public int deleteExamineConfigById(Long id)
    {
        return examineConfigMapper.deleteExamineConfigById(id);
    }
}
