package com.ruoyi.instruction.mapper;

import java.util.List;
import com.ruoyi.instruction.domain.PetitionPerson;

/**
 * 信访人员库Mapper接口
 *
 * <AUTHOR>
 * @date 2024-04-29
 */
public interface PetitionPersonMapper
{
    /**
     * 查询信访人员库
     *
     * @param id 信访人员库主键
     * @return 信访人员库
     */
    public PetitionPerson selectPetitionPersonById(Long id);

    /**
     * 查询信访人员库列表
     *
     * @param petitionPerson 信访人员库
     * @return 信访人员库集合
     */
    public List<PetitionPerson> selectPetitionPersonList(PetitionPerson petitionPerson);

    /**
     * 新增信访人员库
     *
     * @param petitionPerson 信访人员库
     * @return 结果
     */
    public int insertPetitionPerson(PetitionPerson petitionPerson);

    /**
     * 修改信访人员库
     *
     * @param petitionPerson 信访人员库
     * @return 结果
     */
    public int updatePetitionPerson(PetitionPerson petitionPerson);

    /**
     * 删除信访人员库
     *
     * @param id 信访人员库主键
     * @return 结果
     */
    public int deletePetitionPersonById(Long id);

    /**
     * 批量删除信访人员库
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deletePetitionPersonByIds(Long[] ids);

    /**
     * 查询信访事件关联人员数
     * @return
     */
    List<PetitionPerson> findPersonInfo();

    /**
     * 批量新增人员
     * @param insertList
     */
    void batchInsertPetitionPerson(List<PetitionPerson> insertList);

    /**
     * 批量更新人员
     * @param updateList
     */
    void batchUpdatePetitionPerson(List<PetitionPerson> updateList);
}
