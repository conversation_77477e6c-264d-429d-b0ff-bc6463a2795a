package com.ruoyi.instruction.controller;

import java.io.*;
import java.net.URLEncoder;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;
import javax.servlet.http.HttpServletResponse;

import cn.hutool.core.io.resource.ClassPathResource;
import com.ruoyi.common.constant.Constants;
import com.ruoyi.common.core.domain.entity.SysDept;
import com.ruoyi.common.core.domain.entity.SysUser;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.instruction.domain.IndicatorType;
import com.ruoyi.instruction.domain.InstrucationPerson;
import com.ruoyi.instruction.domain.InstructionEvent;
import com.ruoyi.instruction.domain.rspVo.AnalysisReportEventVo;
import com.ruoyi.instruction.domain.rspVo.AnalysisReportGropVo;
import com.ruoyi.instruction.domain.rspVo.GroupRspVo;
import com.ruoyi.instruction.mapper.GroupDisposeMapper;
import com.ruoyi.instruction.mapper.InstructionEventMapper;
import com.ruoyi.instruction.service.IIndicatorTypeService;
import com.ruoyi.instruction.service.IInstrucationPersonService;
import com.ruoyi.instruction.service.IInstructionInfoService;
import com.ruoyi.instruction.service.impl.InstructionInfoServiceImpl;
import fr.opensagres.xdocreport.document.IXDocReport;
import fr.opensagres.xdocreport.document.registry.XDocReportRegistry;
import fr.opensagres.xdocreport.template.IContext;
import fr.opensagres.xdocreport.template.TemplateEngineKind;
import fr.opensagres.xdocreport.template.formatter.FieldsMetadata;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.instruction.domain.InstructionGroup;
import com.ruoyi.instruction.service.IInstructionGroupService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;
import org.springframework.web.multipart.MultipartFile;

/**
 * 群体基本信息Controller
 *
 * <AUTHOR>
 * @date 2022-12-16
 */
@RestController
@RequestMapping("/instruction/group")
public class InstructionGroupController extends BaseController {

    @Autowired
    private InstructionInfoServiceImpl instructionInfoService;

    @Autowired
    private IInstructionGroupService instructionGroupService;

    @Autowired
    private IIndicatorTypeService indicatorTypeService;

    @Autowired
    private IInstrucationPersonService personService;

    @Autowired
    private InstructionEventMapper eventMapper;

    /**
     * 查询群体基本信息列表
     */
    @PreAuthorize("@ss.hasPermi('instruction:group:list')")
    @GetMapping("/list")
    public TableDataInfo list(InstructionGroup instructionGroup) {
        if (instructionGroup.getTypeName() != null && !instructionGroup.getTypeName().isEmpty()) {
            IndicatorType indicatorType = new IndicatorType();
            indicatorType.setTypeName(instructionGroup.getTypeName());
            //根据类型名称查询
            List<IndicatorType> indicatorTypes = indicatorTypeService.selectIndicatorTypeList(indicatorType);
            List<Long> typeIds = indicatorTypes.stream().map(IndicatorType::getId).collect(Collectors.toList());
            if (typeIds.size() > 0) {
                Map<String, Object> params = instructionGroup.getParams();
                params.put("typeIds", typeIds);
                instructionGroup.setParams(params);
            }
        }

        startPage();
        List<InstructionGroup> list = instructionGroupService.selectInstructionGroupList(instructionGroup);
        if (list != null && list.size() > 0) {
            //判断用户是否为市级账号
            boolean flag = false;
            SysDept dept = SecurityUtils.getLoginUser().getUser().getDept();
            if (!dept.getDeptId().equals(Constants.JINHUA_CITY_DEPT_ID) && !dept.getParentId().equals(Constants.JINHUA_CITY_DEPT_ID)) {
                //当前账号为县市区账号
                flag = true;
                dept = instructionInfoService.getDeptIdByAncestors(dept);
            }

            //1、查询事件
            List<InstructionEvent> eventList = eventMapper.selectEventList();
            //2、查询类型
            List<IndicatorType> indicatorTypes = indicatorTypeService.selectIndicatorTypeList(new IndicatorType());
            SysDept finalDept = dept;
            for (InstructionGroup group : list) {
                if (group.getType() != null) {
                    indicatorTypes.stream().filter(type -> type.getId().equals(Long.valueOf(group.getType()))).findFirst().ifPresent(type -> group.setTypeName(type.getTypeName()));
                }
                group.setIsDeal(0);
                //查询关联县市区事件
                if (flag){
                    long count = eventList.stream().filter(event -> event.getDutyUnit() != null && event.getDutyUnit().contains(finalDept.getDeptName()) && event.getGroupId() != null && event.getGroupId().equals(group.getId())).count();
                    group.setEventCount((int) count);
                    if (group.getCreateDeptId().equals(dept.getDeptId())){
                        group.setIsDeal(1);
                    }
                }else {
                    group.setIsDeal(1);
                }
                //查询群体关联事件
                String finallyPersonIds = eventList.stream().filter(event -> event.getGroupId() != null && event.getGroupId().equals(group.getId())).map(InstructionEvent::getPersonIds).collect(Collectors.joining(","));
                //所有人员ids
                group.setPersonCount(0);
                if (group.getPersonIds() == null) {
                    group.setPersonIds("");
                }
                if (finallyPersonIds.length() > 0 || group.getPersonIds().length() > 0) {
                    String ids = finallyPersonIds + "," + group.getPersonIds();
                    group.setPersonIds(ids);
                    List<String> collect = Arrays.stream(ids.split(",")).collect(Collectors.toList());
                    //查询人员数量
                    int count = 0;
                    if (flag){
                        String deptName = dept.getDeptName();
                        String replace = deptName.replace("委政法委", "").replace("政法委", "").replace("政法办", "");
                        count = personService.findPeronCountByIdAndDeptName(collect,replace);
                    }else {
                        count = personService.findPeronCountById(collect);
                    }
                    group.setPersonCount(count);
                }
            }
        }
        return getDataTable(list);
    }


    @GetMapping("/listForWZ")
    public AjaxResult listForWZ(InstructionGroup instructionGroup) {

        List<InstructionGroup> list = instructionGroupService.selectInstructionGroupList(instructionGroup);
        if (list != null && list.size() > 0) {
            //1、查询事件
            List<InstructionEvent> eventList = eventMapper.selectEventList();
            for (InstructionGroup group : list) {
                //查询群体关联事件
                String finallyPersonIds = eventList.stream().filter(event -> event.getGroupId() != null && event.getGroupId().equals(group.getId())).map(InstructionEvent::getPersonIds).collect(Collectors.joining(","));
                //所有人员ids
                group.setPersonCount(0);
                if (group.getPersonIds() == null) {
                    group.setPersonIds("");
                }
                if (finallyPersonIds.length() > 0 || group.getPersonIds().length() > 0) {
                    String ids = finallyPersonIds + "," + group.getPersonIds();
                    group.setPersonIds(ids);
                    List<String> collect = Arrays.stream(ids.split(",")).collect(Collectors.toList());
                    //查询人员数量
                    int count = 0;
                        count = personService.findPeronCountById(collect);
                    group.setPersonCount(count);
                }
            }
        }
        return AjaxResult.success(list);
    }


    /**
     * 查询关联群体
     * @param instructionGroup
     * @return
     */
    @GetMapping("/contactList")
    public AjaxResult contactList(InstructionGroup instructionGroup) {
        List<InstructionGroup> list = instructionGroupService.selectInstructionGroupList(instructionGroup);
        return AjaxResult.success(list);
    }

    /**
     * 获取群体id 及群体名称
     * @param instructionGroup
     * @return
     */
    @GetMapping("/getNewContactList")
    public AjaxResult getNewContactList(InstructionGroup instructionGroup) {
        List<GroupRspVo> list = instructionGroupService.getNewContactList(instructionGroup);
        return AjaxResult.success(list);
    }


    /**
     * 导出群体基本信息列表
     */
    @PreAuthorize("@ss.hasPermi('instruction:group:export')")
    @Log(title = "群体基本信息", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, InstructionGroup instructionGroup) {
        List<InstructionGroup> list = instructionGroupService.selectInstructionGroupList(instructionGroup);
        if (list != null && list.size() > 0) {
            //判断用户是否为市级账号
            boolean flag = false;
            SysDept dept = SecurityUtils.getLoginUser().getUser().getDept();
            if (!dept.getDeptId().equals(Constants.JINHUA_CITY_DEPT_ID) && !dept.getParentId().equals(Constants.JINHUA_CITY_DEPT_ID)) {
                //当前账号为县市区账号
                flag = true;
                dept = instructionInfoService.getDeptIdByAncestors(dept);
            }
            //1、查询事件
            List<InstructionEvent> eventList = eventMapper.selectEventList();
            List<IndicatorType> indicatorTypes = indicatorTypeService.selectIndicatorTypeList(new IndicatorType());
            SysDept finalDept = dept;
            for (InstructionGroup group : list) {
                if (group.getType() != null) {
                    indicatorTypes.stream().filter(type -> type.getId().equals(Long.valueOf(group.getType()))).findFirst().ifPresent(type -> group.setTypeName(type.getTypeName()));
                }
                if (flag){
                    long count = eventList.stream().filter(event -> event.getDutyUnit() != null && event.getDutyUnit().contains(finalDept.getDeptName()) && event.getGroupId() != null && event.getGroupId().equals(group.getId())).count();
                    group.setEventCount((int) count);
                }
                //查询群体关联事件
                String finallyPersonIds = eventList.stream().filter(event -> event.getGroupId() != null && event.getGroupId().equals(group.getId())).map(InstructionEvent::getPersonIds).collect(Collectors.joining(","));
                //所有人员ids
                group.setPersonCount(0);
                if (group.getPersonIds() == null) {
                    group.setPersonIds("");
                }
                if (finallyPersonIds.length() > 0 || group.getPersonIds().length() > 0) {
                    String ids = finallyPersonIds + "," + group.getPersonIds();
                    group.setPersonIds(ids);
                    List<String> collect = Arrays.stream(ids.split(",")).collect(Collectors.toList());
                    //查询人员数量
                    int count = 0;
                    if (flag){
                        String deptName = dept.getDeptName();
                        String replace = deptName.replace("委政法委", "").replace("政法委", "").replace("政法办", "");
                        count = personService.findPeronCountByIdAndDeptName(collect,replace);
                    }else {
                        count = personService.findPeronCountById(collect);
                    }
                    group.setPersonCount(count);
                }
            }
        }
        ExcelUtil<InstructionGroup> util = new ExcelUtil<InstructionGroup>(InstructionGroup.class);
        util.exportExcel(response, list, "群体基本信息数据");
    }

    /**
     * 获取群体基本信息详细信息
     */
    @PreAuthorize("@ss.hasPermi('instruction:group:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id) {
        return success(instructionGroupService.selectInstructionGroupById(id));
    }

    /**
     * 新增群体基本信息
     */
    @PreAuthorize("@ss.hasPermi('instruction:group:add')")
    @Log(title = "群体基本信息", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody InstructionGroup instructionGroup) {
        return instructionGroupService.insertInstructionGroup(instructionGroup);
    }

    /**
     * 修改群体基本信息
     */
    @PreAuthorize("@ss.hasPermi('instruction:group:edit')")
    @Log(title = "群体基本信息", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody InstructionGroup instructionGroup) {
        return toAjax(instructionGroupService.updateInstructionGroup(instructionGroup));
    }

    /**
     * 删除群体基本信息
     */
    @PreAuthorize("@ss.hasPermi('instruction:group:remove')")
    @Log(title = "群体基本信息", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids) {
        return toAjax(instructionGroupService.deleteInstructionGroupByIds(ids));
    }

    /**
     * 查询群体基本信息列表
     */
    @GetMapping("/getGroupList")
    public AjaxResult getGroupList(InstructionGroup instructionGroup) {
        List<InstructionGroup> list = instructionGroupService.selectInstructionGroupList(instructionGroup);
        return AjaxResult.success(list);
    }

    /**
     * 下载群体导入模板
     * @param response
     */
    @PostMapping("/importTemplate")
    public void importTemplate(HttpServletResponse response) {
        ExcelUtil<InstructionGroup> util = new ExcelUtil<InstructionGroup>(InstructionGroup.class);
        util.importTemplateExcel(response, "群体导入模板");
    }



    /**
     * 导入事件数据
     */
    @Log(title = "导入事件数据", businessType = BusinessType.IMPORT)
    @PostMapping("/importData")
    public AjaxResult importData(MultipartFile file) throws Exception {
        ExcelUtil<InstructionGroup> util = new ExcelUtil<InstructionGroup>(InstructionGroup.class);
        List<InstructionGroup> list = util.importExcel(file.getInputStream());
        //获取操作用户
        String operName = getUsername();
        //存入数据
        String msg = instructionGroupService.importData(list, operName);
        return AjaxResult.success(msg);
    }
    /**
     * 分析报告
     */
    @GetMapping("/analysisReport")
    public void analysisReport(@RequestParam(required = false) Long id, Date startTime, Date endTime, HttpServletResponse response) throws Exception {
        AnalysisReportGropVo analysisReportGropVo=instructionGroupService.analysisReport(id,startTime,endTime);
        InputStream ins = null;
        OutputStream out = null;
        try {
            //获取Word模板，模板存放路径
            ClassPathResource tempFileResource = new ClassPathResource("/word/分析报告模板.docx");
            ins = tempFileResource.getStream();
            //注册xdocreport实例并加载FreeMarker模板引擎
            IXDocReport report = XDocReportRegistry.getRegistry().loadReport(ins, TemplateEngineKind.Freemarker);
            //创建xdocreport上下文对象，用于存放具体数据
            IContext context = report.createContext();


            //创建要替换的文本变量
            context.put("createTime", analysisReportGropVo.getCreateTime());
            context.put("groupName", analysisReportGropVo.getGroupName());
            context.put("eventCount", analysisReportGropVo.getEventCount());
            context.put("personCount", analysisReportGropVo.getPersonCount());
            context.put("leadPersonCount", analysisReportGropVo.getLeadPersonCount());
            context.put("respPersonCount", analysisReportGropVo.getRespPersonCount());
            context.put("siteVisitsCount", analysisReportGropVo.getSiteVisitsCount()==null?0:analysisReportGropVo.getSiteVisitsCount());
            context.put("siteVisitsDetails", StringUtils.isEmpty(analysisReportGropVo.getSiteVisitsDetails())?"":analysisReportGropVo.getSiteVisitsDetails());
            context.put("leadPersonDetails", StringUtils.isEmpty(analysisReportGropVo.getLeadPersonDetails())?"":analysisReportGropVo.getLeadPersonDetails());
            context.put("regionCount", analysisReportGropVo.getRegionCount()==null?0: analysisReportGropVo.getRegionCount());
            context.put("regionDetails",StringUtils.isEmpty( analysisReportGropVo.getRegionDetails())?"": analysisReportGropVo.getRegionDetails());
            context.put("personNum",analysisReportGropVo.getPersonNum());
            context.put("leadPersonNum",analysisReportGropVo.getLeadPersonNum());
            context.put("respPersonDetails",StringUtils.isEmpty(analysisReportGropVo.getRespPersonDetails())?"":analysisReportGropVo.getRespPersonDetails());
            context.put("eventPropertiesDetails",StringUtils.isEmpty(analysisReportGropVo.getEventPropertiesDetails())?"":analysisReportGropVo.getEventPropertiesDetails());
            context.put("respPersonNum",analysisReportGropVo.getRespPersonNum());
            context.put("outsidePersonDetails",analysisReportGropVo.getOutsidePersonDetails());


            //此处的userInfo是word中命名的列表名
            context.put("events",analysisReportGropVo.getEventList() );
            FieldsMetadata fm = report.createFieldsMetadata();
            //Word模板中的表格数据对应的集合类型
            fm.load("events", AnalysisReportEventVo.class, true);
            response.setCharacterEncoding("utf-8");
            response.setContentType("application/msword");
            String fileName = "分析报告.docx";
            response.setHeader("Content-Disposition", "attachment;filename="
                    .concat(String.valueOf(URLEncoder.encode(fileName, "UTF-8"))));
            report.process(context, response.getOutputStream());

        } catch (Exception e) {
            logger.info("生成word发生异常", e);
        } finally {
            try {
                if (ins != null){
                    ins.close();
                }
                if (out != null){
                    out.close();
                }
            } catch (IOException e) {
                logger.info("文件流关闭失败", e);
            }
        }

    }

    /**
     * 获取异动区域分布
     * @param instructionEvent
     * @return
     */
    @GetMapping("/getTransactionArea")
    public AjaxResult getTransactionArea(InstructionEvent instructionEvent) {
        //根据type查询日期
        String previousDate = DateUtils.getPreviousDateByType(instructionEvent.getType());
        Map<String, Object> params = instructionEvent.getParams();
        params.put("beginTime", previousDate);
        instructionEvent.setParams(params);
        List<InstructionEvent> eventList = eventMapper.selectInstructionEventList(instructionEvent);
        List<InstrucationPerson> personList = new ArrayList<>();
        HashMap<String, Integer> map = new HashMap<>();
        map.put("东阳市", 0);
        map.put("义乌市", 0);
        map.put("兰溪市", 0);
        map.put("婺城区", 0);
        map.put("开发区", 0);
        map.put("武义县", 0);
        map.put("永康市", 0);
        map.put("浦江县", 0);
        map.put("磐安县", 0);
        map.put("金东区", 0);
        if (eventList.size() != 0) {
            eventList.forEach(event -> {
                String dutyPlace = event.getDutyUnit();
                if (dutyPlace != null) {
                    if (dutyPlace.contains("东阳市")) {
                        map.put("东阳市", map.get("东阳市") + 1);
                    } else if (dutyPlace.contains("义乌市")) {
                        map.put("义乌市", map.get("义乌市") + 1);
                    } else if (dutyPlace.contains("兰溪市")) {
                        map.put("兰溪市", map.get("兰溪市") + 1);
                    } else if (dutyPlace.contains("婺城区")) {
                        map.put("婺城区", map.get("婺城区") + 1);
                    } else if (dutyPlace.contains("开发区")) {
                        map.put("开发区", map.get("开发区") + 1);
                    } else if (dutyPlace.contains("武义县")) {
                        map.put("武义县", map.get("武义县") + 1);
                    } else if (dutyPlace.contains("永康市")) {
                        map.put("永康市", map.get("永康市") + 1);
                    } else if (dutyPlace.contains("浦江县")) {
                        map.put("浦江县", map.get("浦江县") + 1);
                    } else if (dutyPlace.contains("磐安县")) {
                        map.put("磐安县", map.get("磐安县") + 1);
                    } else if (dutyPlace.contains("金东区")) {
                        map.put("金东区", map.get("金东区") + 1);
                    }
                }
            });


            //获取异动人员排名
            //使用stream把eventList中的personIds全部集合成一个String
            String personIds = eventList.stream().map(InstructionEvent::getPersonIds).collect(Collectors.joining(","));
            //筛选出personIds中次数出现前6的数据
            Map<String, Integer> numberCountMap = new HashMap<>();

            if (personIds != null && !personIds.isEmpty()) {
                String[] numberArray = personIds.split(",");
                for (String number : numberArray) {
                    number = number.trim();
                    // 去除前后空格
                    numberCountMap.put(number, numberCountMap.getOrDefault(number, 0) + 1);
                }
                // 按出现次数降序排序，并截取前十项
                List<Map.Entry<String, Integer>> sortedList = numberCountMap.entrySet()
                        .stream()
                        .sorted((entry1, entry2) -> entry2.getValue().compareTo(entry1.getValue()))
                        .limit(10)
                        .collect(Collectors.toList());

                // 将排序后的结果转换为Map
                Map<String, Integer> topTenMap = new LinkedHashMap<>();
                for (Map.Entry<String, Integer> entry : sortedList) {
                    topTenMap.put(entry.getKey(), entry.getValue());
                }
                //将topTenMap中的key集成成一个List<String>
                List<String> keyList = new ArrayList<>(topTenMap.keySet());
                InstrucationPerson person = new InstrucationPerson();
                person.getParams().put("ids", keyList);
                personList = personService.selectInstrucationPersonList(person);
                for (InstrucationPerson person1 : personList) {
                    Integer integer = topTenMap.get(person1.getId() + "");
                    person1.setEventNum(integer);
                }
                //按照人员eventNum降序排序
                personList.sort(Comparator.comparingInt(InstrucationPerson::getEventNum).reversed());
            }
        }
        //获取进京、赴省、来市人员
        List<Map<String, Integer>> list = new ArrayList<>();
        for (int i = 1; i <= 3; i++) {
            Map<String, Integer> map1 = new HashMap<>();
            map1.put("petitionType", i);
            map1.put("personCount", 0);
            map1.put("personNum", 0);
            if (eventList.size() != 0) {
                //筛选出符合条件的事件
                int finalI = i;
                String collect = eventList.stream().filter(event -> event.getPetitionType() != null && event.getPetitionType().equals(String.valueOf(finalI))).map(InstructionEvent::getPersonIds).collect(Collectors.joining(","));
                if (collect.length() > 0) {
                    String[] split = collect.split(",");
                    //将split去重
                    Set<String> set = new HashSet<>(Arrays.asList(split));
                    map1.put("personCount", set.size());
                    map1.put("personNum", split.length);
                }
            }
            list.add(map1);
        }

        return AjaxResult.success().put("personList", personList).put("capital", list).put("area",map);
    }

    /**
     * 获取异动情况
     * @param instructionEvent
     * @return
     */
    @GetMapping("/getTransactionSituation")
    public AjaxResult getTransactionSituation(InstructionEvent instructionEvent) {
        int onePersonNum = 0;
        int oneEventNum = 0;
        int twoPersonNum = 0;
        int twoEventNum = 0;
        //根据type查询日期
        String oneDate = DateUtils.getPreviousDateByType(instructionEvent.getType());
        String twoDate = "";
        if (instructionEvent.getType().equals("1")){
            //获取仅60天数据
            twoDate = DateUtils.getPreviousDateByType("4");
        }else if (instructionEvent.getType().equals("2")){
            //获取仅30天数据
            twoDate = DateUtils.getPreviousDateByType("3");
        }else if (instructionEvent.getType().equals("3")){
            //获取仅15天数据
            twoDate = DateUtils.getPreviousDateByType("4");
        }
        Map<String, Object> params = instructionEvent.getParams();
        params.put("beginTime", oneDate);
        instructionEvent.setParams(params);
        //查询当前异动人次
        List<InstructionEvent> eventList = eventMapper.selectInstructionEventList(instructionEvent);
        if (eventList.size() != 0) {
            String personIds = eventList.stream().filter(event -> event.getPersonIds() != null).map(InstructionEvent::getPersonIds).collect(Collectors.joining(","));
            System.out.println("personIds = " + personIds);
            String[] split = personIds.split(",");
            onePersonNum = split.length;
            oneEventNum = eventList.size();
        }
        //查询前段日期异动人次
        instructionEvent.getParams().put("beginTime", twoDate);
        instructionEvent.getParams().put("endTime", oneDate);
        List<InstructionEvent> eventList1 = eventMapper.selectInstructionEventList(instructionEvent);
        if (eventList1.size() != 0) {
            String personIds = eventList1.stream().filter(event -> event.getPersonIds() != null).map(InstructionEvent::getPersonIds).collect(Collectors.joining(","));
            String[] split = personIds.split(",");
            System.out.println("personIds1 = " + personIds);
            twoEventNum = eventList1.size();
            twoPersonNum = split.length;
        }
        Map<String, Object> eventMap = calculateGrowthRate(oneEventNum, twoEventNum);
        Map<String, Object> personMap = calculateGrowthRate(onePersonNum, twoPersonNum);

        return AjaxResult.success().put("eventMap", eventMap).put("personMap", personMap);
    }


    /**
     * 计算环比值并判断上升还是下降
     * @param currentValue 当前值
     * @param previousValue 前一个值
     * @return 包含环比值、趋势信息和变化量的Map
     */
    public Map<String, Object> calculateGrowthRate(int currentValue, int previousValue) {
        Map<String, Object> result = new HashMap<>();

        if (previousValue == 0) {
            result.put("growthRate", "-");
            result.put("trend", "");
            result.put("message", "前一个值为0，无法计算");
            result.put("changeAmount", "前一个值为0,无法计算");
        } else {
            double growthRate = Math.abs(((double) (currentValue - previousValue) / previousValue) * 100);
            growthRate = Double.parseDouble(String.format("%.2f", growthRate));
            result.put("growthRate", growthRate);

            int changeAmount = currentValue - previousValue;
            result.put("changeAmount", Math.abs(changeAmount));

            if (changeAmount > 0) {
                result.put("trend", "上升");
            } else if (changeAmount < 0) {
                result.put("trend", "下降");
            } else {
                result.put("trend", "持平");
            }
        }
        // 返回输入的两个值
        result.put("currentValue", currentValue);
        result.put("previousValue", previousValue);

        return result;
    }



}
