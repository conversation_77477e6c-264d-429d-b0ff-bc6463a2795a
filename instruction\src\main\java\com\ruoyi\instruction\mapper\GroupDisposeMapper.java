package com.ruoyi.instruction.mapper;

import java.util.List;
import com.ruoyi.instruction.domain.GroupDispose;

/**
 * 群体处置记录Mapper接口
 * 
 * <AUTHOR>
 * @date 2024-12-19
 */
public interface GroupDisposeMapper 
{
    /**
     * 查询群体处置记录
     * 
     * @param id 群体处置记录主键
     * @return 群体处置记录
     */
    public GroupDispose selectGroupDisposeById(Long id);

    /**
     * 查询群体处置记录列表
     * 
     * @param groupDispose 群体处置记录
     * @return 群体处置记录集合
     */
    public List<GroupDispose> selectGroupDisposeList(GroupDispose groupDispose);

    /**
     * 新增群体处置记录
     * 
     * @param groupDispose 群体处置记录
     * @return 结果
     */
    public int insertGroupDispose(GroupDispose groupDispose);

    /**
     * 修改群体处置记录
     * 
     * @param groupDispose 群体处置记录
     * @return 结果
     */
    public int updateGroupDispose(GroupDispose groupDispose);

    /**
     * 删除群体处置记录
     * 
     * @param id 群体处置记录主键
     * @return 结果
     */
    public int deleteGroupDisposeById(Long id);

    /**
     * 批量删除群体处置记录
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteGroupDisposeByIds(Long[] ids);
}
