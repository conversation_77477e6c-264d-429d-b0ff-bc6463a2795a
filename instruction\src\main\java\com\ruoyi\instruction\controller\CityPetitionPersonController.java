package com.ruoyi.instruction.controller;

import com.ruoyi.common.constant.InstructionRolesConstants;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.domain.entity.SysDept;
import com.ruoyi.common.core.domain.entity.SysRole;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.utils.DeptUtils;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.instruction.domain.CityPetition;
import com.ruoyi.instruction.domain.CityPetitionPerson;
import com.ruoyi.instruction.domain.PetitionPerson;
import com.ruoyi.instruction.mapper.CityPetitionMapper;
import com.ruoyi.instruction.service.ICityPetitionPersonService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 信访到市人员库Controller
 *
 * <AUTHOR>
 * @date 2024-05-28
 */
@RestController
@RequestMapping("/citypetition/person")
public class CityPetitionPersonController extends BaseController {
    @Autowired
    private ICityPetitionPersonService cityPetitionPersonService;
    @Autowired
    private CityPetitionMapper cityPetitionMapper;

    /**
     * 查询信访到市人员库列表
     */
    @GetMapping("/list")
    public TableDataInfo list(CityPetitionPerson cityPetitionPerson)
    {
        Map<String, Object> params = cityPetitionPerson.getParams();
        if (cityPetitionPerson.getContentType() != null && !cityPetitionPerson.getContentType().isEmpty()) {
            List<String> contentTypes = Arrays.asList(cityPetitionPerson.getContentType().split(","));
            params.put("contentTypes", contentTypes);
        }
        if (params.get("gradeBeginTime") != null && params.get("gradeEndTime") != null) {
            CityPetition cityPetition = new CityPetition();
            cityPetition.setParams(params);
            List<CityPetition> cityPetitionList = cityPetitionMapper.selectCityPetitionList(cityPetition);
            List<String> personNames = cityPetitionList.stream().map(CityPetition::getPersonName).collect(Collectors.toList());
            if (personNames.size() == 0) {
                return getDataTable(new ArrayList<>());
            }
            params.put("personNames", personNames);
        }
        cityPetitionPerson.setParams(params);
        //县市区账号进行过滤
        List<SysRole> roles = SecurityUtils.getLoginUser().getUser().getRoles();
        List<String> roleList = roles.stream().map(SysRole::getRoleKey).collect(Collectors.toList());
        if (!roleList.contains(InstructionRolesConstants.CITY_INSTRUCTION)) {
            //查询用户父级部门所在县市区 县市区仅能查看自己所属区域的人员
            SysDept dept = SecurityUtils.getLoginUser().getUser().getDept();
            String dutyPlace = DeptUtils.getDutyPlaceByDept(dept);
            cityPetitionPerson.setDutyPlace(dutyPlace);
        }
        startPage();
        List<CityPetitionPerson> list = cityPetitionPersonService.selectCityPetitionPersonList(cityPetitionPerson);
        return getDataTable(list);
    }

    /**
     * 获取统计数据
     * @param cityPetitionPerson
     * @return
     */
    @GetMapping("/getStatistics")
    public AjaxResult getStatistics(CityPetitionPerson cityPetitionPerson) {
        AjaxResult ajaxResult = new AjaxResult();
        Map<String, Object> params = cityPetitionPerson.getParams();
        if (cityPetitionPerson.getContentType() != null && !cityPetitionPerson.getContentType().isEmpty()) {
            List<String> contentTypes = Arrays.asList(cityPetitionPerson.getContentType().split(","));
            params.put("contentTypes", contentTypes);
        }
        if (params.get("gradeBeginTime") != null && params.get("gradeEndTime") != null) {
            CityPetition cityPetition = new CityPetition();
            cityPetition.setParams(params);
            List<CityPetition> citypetitionList = cityPetitionMapper.selectCityPetitionList(cityPetition);
            if (citypetitionList.size() > 0){
                List<String> personNames = citypetitionList.stream().map(CityPetition::getPersonName).collect(Collectors.toList());
                params.put("personNames", personNames);
            }else {
                // 创建一个HashMap实例
                Map<String, Integer> regionCounts = new HashMap<>();
                // 插入数据
                regionCounts.put("武义县", 0);
                regionCounts.put("义乌市", 0);
                regionCounts.put("永康市", 0);
                regionCounts.put("金东区", 0);
                regionCounts.put("开发区", 0);
                regionCounts.put("磐安县", 0);
                regionCounts.put("兰溪市", 0);
                regionCounts.put("婺城区", 0);
                regionCounts.put("东阳市", 0);
                regionCounts.put("浦江县", 0);

                ajaxResult.put("dutyPlaceCounts", regionCounts);
                ajaxResult.put("provinceNum", 0);
                ajaxResult.put("capitalNum", 0);
                ajaxResult.put("total", 0);
                return ajaxResult;
            }
        }
        cityPetitionPerson.setParams(params);
        //县市区账号进行过滤
        List<SysRole> roles = SecurityUtils.getLoginUser().getUser().getRoles();
        List<String> roleList = roles.stream().map(SysRole::getRoleKey).collect(Collectors.toList());
        if (!roleList.contains(InstructionRolesConstants.CITY_INSTRUCTION)) {
            //查询用户父级部门所在县市区 县市区仅能查看自己所属区域的人员
            SysDept dept = SecurityUtils.getLoginUser().getUser().getDept();
            String dutyPlace = DeptUtils.getDutyPlaceByDept(dept);
            cityPetitionPerson.setDutyPlace(dutyPlace);
        }
        List<CityPetitionPerson> list = cityPetitionPersonService.selectCityPetitionPersonList(cityPetitionPerson);
        if (list.size() > 0) {
            long cityNum = list.stream()
                    .mapToLong(CityPetitionPerson::getCityNum)
                    .sum();
            // 使用Stream API进行分组并计算每个dutyPlace的数量
            Map<String, Long> dutyPlaceCounts = list.stream()
                    .filter(person -> person.getDutyPlace() != null && !person.getDutyPlace().isEmpty())
                    .collect(Collectors.groupingBy(
                            CityPetitionPerson::getDutyPlace,
                            Collectors.counting()));
            ajaxResult.put("dutyPlaceCounts", dutyPlaceCounts);
            ajaxResult.put("cityNum", cityNum);
            ajaxResult.put("total", list.size());
        }else {
            // 创建一个HashMap实例
            Map<String, Integer> regionCounts = new HashMap<>();
            // 插入数据
            regionCounts.put("武义县", 0);
            regionCounts.put("义乌市", 0);
            regionCounts.put("永康市", 0);
            regionCounts.put("金东区", 0);
            regionCounts.put("开发区", 0);
            regionCounts.put("磐安县", 0);
            regionCounts.put("兰溪市", 0);
            regionCounts.put("婺城区", 0);
            regionCounts.put("东阳市", 0);
            regionCounts.put("浦江县", 0);

            ajaxResult.put("dutyPlaceCounts", regionCounts);
            ajaxResult.put("cityNum", 0);
            ajaxResult.put("total", 0);
        }
        return ajaxResult;
    }
}
