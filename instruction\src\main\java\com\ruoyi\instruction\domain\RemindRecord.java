package com.ruoyi.instruction.domain;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 工作通知记录对象 t_remind_record
 *
 * <AUTHOR>
 * @date 2024-04-10
 */
@Data
public class RemindRecord extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键 */
    private Long id;

    /** 事项名称 */
    @Excel(name = "事项名称")
    private String itemTitle;

    /** 事项类型 */
    @Excel(name = "事项类型")
    private String itemType;

    /** 指令类型 1：维稳 2：平安 3：mzx */
    @Excel(name = "指令类型 1：维稳 2：平安 3：mzx")
    private Integer instructionType;

    /** 提醒单位 */
    @Excel(name = "提醒单位")
    private String remindUnit;

    /** 交办时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm")
    @Excel(name = "交办时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date assignTime;

    /** 办理期限 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm")
    @Excel(name = "办理期限", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date handleTime;

    /** 触发时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm")
    @Excel(name = "触发时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date remindTime;

    /** 指令id */
    @Excel(name = "指令id")
    private Long instructionId;

    /** 状态 1：正常 9：删除 */
    @Excel(name = "状态 1：正常 9：删除")
    private String status;

    /** 接收部门id */
    @Excel(name = "接收部门id")
    private Long receiveId;

    /** 创建部门id */
    @Excel(name = "创建部门id")
    private Long createDeptId;

    /** 提醒信息 */
    @Excel(name = "提醒信息")
    private String remindInfo;

    /**
     * 总数
     */
    private Long aCount;

}
