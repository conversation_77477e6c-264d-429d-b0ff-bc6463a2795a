# 县市层级指令办理情况功能说明

## 功能概述

县市层级指令办理情况模块是在市级指令办理情况基础上新增的功能，专门用于统计和查询金华市下属各县市区的指令办理情况。该模块参考了金安治市级指令工作台的业务逻辑，实现了应接收未接收和应处置未处置两个核心功能。

## 主要特性

### 1. 数据范围
- **覆盖范围**：金华市下属所有县市区
- **县级部门**：磐安县、兰溪市、东阳市、义乌市、浦江县、永康市、金东区、婺城区、开发区、武义县
- **乡镇街道**：各县市区下属的乡镇街道部门
- **指令类型**：双排双办、预警研判指令

### 2. 核心功能

#### 应接收未接收
- **业务含义**：指令已交办给相关部门，但部门尚未确认接收
- **判断标准**：
  - 指令状态有效
  - 存在接收记录但接收时间为空
  - 根据紧急程度判断是否超时（特急2小时、紧急12小时、一般24小时）
- **统计维度**：县级部门数量、乡镇街道数量、涉及指令数量

#### 应处置未处置
- **业务含义**：指令已接收但处置反馈不及时或未完成
- **判断标准**：
  - 指令状态有效
  - 存在反馈记录但反馈未结束
  - 取最新反馈记录进行判断
- **统计维度**：县级部门数量、乡镇街道数量、涉及指令数量

### 3. 查询筛选

#### 时间筛选
- **筛选字段**：指令交办时间（assign_time）
- **格式要求**：yyyy-MM-dd
- **默认行为**：不传时间参数则查询全量数据

#### 区域筛选
- **筛选方式**：按县市区名称进行模糊匹配
- **支持范围**：金华市下属所有县市区
- **默认行为**：不传区域参数则查询所有县市区

#### 部门类型筛选
- **县级部门**：直接以县市区名称命名的部门
- **乡镇街道**：县市区下属的具体乡镇街道部门
- **应用场景**：详细列表查询和数据导出

## 技术实现

### 1. 架构设计
```
Controller层：CityInstructionHandlingController
├── 统计数据接口：/county/data
├── 详细列表接口：/county/list
└── 数据导出接口：/county/export

Service层：ICityInstructionHandlingService + CityInstructionHandlingServiceImpl
├── getCountyNoReceiveStats()：应接收未接收统计
├── getCountyNoDisposeStats()：应处置未处置统计
├── getCountyNoReceiveList()：应接收未接收列表
└── getCountyNoDisposeList()：应处置未处置列表

Mapper层：CityInstructionHandlingMapper + CityInstructionHandlingMapper.xml
├── getCountyNoReceiveStats：应接收未接收统计SQL
├── getCountyNoDisposeStats：应处置未处置统计SQL
├── getCountyNoReceiveList：应接收未接收列表SQL
└── getCountyNoDisposeList：应处置未处置列表SQL
```

### 2. 数据模型
```java
CountyInstructionHandlingRsp {
    Long id;                    // 指令ID
    String instructionTitle;    // 指令标题
    String emergencyDegree;     // 紧急程度
    String instructionType;     // 指令类型
    Date assignTime;            // 交办时间
    Date handleTime;            // 办理期限
    String deptName;            // 部门名称
    String deptType;            // 部门类型（县级部门/乡镇街道）
    String unitType;            // 单位类型（主办单位/协办单位）
    Date receiveTime;           // 接收时间
    Date lastFeedbackTime;      // 最新反馈时间
    Integer overdueDays;        // 超期天数
    String countyName;          // 所属县市区
}
```

### 3. 核心SQL逻辑

#### 应接收未接收统计
```sql
SELECT
    COUNT(DISTINCT CASE WHEN dept_level = 'county' THEN receive_dept END) as countyDeptCount,
    COUNT(DISTINCT CASE WHEN dept_level = 'town' THEN receive_dept END) as townDeptCount,
    COUNT(DISTINCT instruction_id) as instructionCount
FROM (
    -- 子查询：获取所有应接收未接收的记录
    SELECT DISTINCT
        a.id as instruction_id,
        b.receive_dept,
        CASE
            WHEN b.receive_dept IN ('磐安县', '兰溪市', ...) THEN 'county'
            ELSE 'town'
        END as dept_level
    FROM t_instruction_info a
    INNER JOIN t_instruction_receive b ON a.id = b.instrucation_id
    WHERE -- 应接收未接收的判断条件
) stats
```

#### 应处置未处置统计
```sql
-- 类似结构，但基于反馈表进行统计
-- 使用最新反馈记录进行判断
-- 条件：feedback_is_end = 2（反馈未结束）
```

## 使用场景

### 1. 日常监控
- **监控对象**：各县市区指令办理效率
- **关注指标**：应接收未接收、应处置未处置的部门和指令数量
- **预警机制**：超时指令的及时发现和处理

### 2. 数据分析
- **横向对比**：不同县市区之间的办理效率对比
- **纵向分析**：同一县市区不同时期的办理情况变化
- **分类统计**：县级部门与乡镇街道的办理情况差异

### 3. 考核评估
- **考核维度**：接收及时性、处置完成度
- **数据支撑**：为绩效考核提供量化数据
- **改进依据**：识别办理薄弱环节，制定改进措施

## 与市级功能的区别

| 对比项 | 市级指令办理情况 | 县市层级指令办理情况 |
|--------|------------------|---------------------|
| 数据范围 | 金华市整体统计 | 按县市区分别统计 |
| 部门分类 | 不区分部门层级 | 区分县级部门和乡镇街道 |
| 统计维度 | 指令数量为主 | 部门数量+指令数量 |
| 筛选条件 | 固定金华市范围 | 可按县市区筛选 |
| 业务逻辑 | 自定义统计逻辑 | 参考金安治工作台逻辑 |

## 注意事项

1. **数据一致性**：统计查询和详细列表查询使用相同的业务逻辑，确保数据一致性
2. **性能优化**：使用DISTINCT去重，避免重复统计同一指令
3. **时间精度**：应接收未接收按小时计算超时，应处置未处置按天计算超期
4. **部门识别**：通过部门名称前缀识别所属县市区，支持模糊匹配
5. **导出功能**：支持分工作表导出，便于分类查看和分析数据
