package com.ruoyi.examineAir.service.impl;

import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.examineAir.mapper.ExamineDetailsMapper;
import com.ruoyi.examineAir.domain.ExamineDetails;
import com.ruoyi.examineAir.service.IExamineDetailsService;

/**
 * 考核晾晒详情Service业务层处理
 * 
 * <AUTHOR>
 * @date 2023-04-19
 */
@Service
public class ExamineDetailsServiceImpl implements IExamineDetailsService 
{
    @Autowired
    private ExamineDetailsMapper examineDetailsMapper;

    /**
     * 查询考核晾晒详情
     * 
     * @param id 考核晾晒详情主键
     * @return 考核晾晒详情
     */
    @Override
    public ExamineDetails selectExamineDetailsById(Long id)
    {
        return examineDetailsMapper.selectExamineDetailsById(id);
    }

    /**
     * 查询考核晾晒详情列表
     * 
     * @param examineDetails 考核晾晒详情
     * @return 考核晾晒详情
     */
    @Override
    public List<ExamineDetails> selectExamineDetailsList(ExamineDetails examineDetails)
    {
        return examineDetailsMapper.selectExamineDetailsList(examineDetails);
    }

    /**
     * 新增考核晾晒详情
     * 
     * @param examineDetails 考核晾晒详情
     * @return 结果
     */
    @Override
    public int insertExamineDetails(ExamineDetails examineDetails)
    {
        return examineDetailsMapper.insertExamineDetails(examineDetails);
    }

    /**
     * 修改考核晾晒详情
     * 
     * @param examineDetails 考核晾晒详情
     * @return 结果
     */
    @Override
    public int updateExamineDetails(ExamineDetails examineDetails)
    {
        return examineDetailsMapper.updateExamineDetails(examineDetails);
    }

    /**
     * 批量删除考核晾晒详情
     * 
     * @param ids 需要删除的考核晾晒详情主键
     * @return 结果
     */
    @Override
    public int deleteExamineDetailsByIds(Long[] ids)
    {
        return examineDetailsMapper.deleteExamineDetailsByIds(ids);
    }

    /**
     * 删除考核晾晒详情信息
     * 
     * @param id 考核晾晒详情主键
     * @return 结果
     */
    @Override
    public int deleteExamineDetailsById(Long id)
    {
        return examineDetailsMapper.deleteExamineDetailsById(id);
    }
}
