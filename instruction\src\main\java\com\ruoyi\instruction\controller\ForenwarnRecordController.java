package com.ruoyi.instruction.controller;

import java.util.Arrays;
import java.util.List;
import javax.servlet.http.HttpServletResponse;

import com.ruoyi.common.core.domain.entity.SysDept;
import com.ruoyi.common.core.domain.entity.SysUser;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.system.mapper.SysDeptMapper;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.instruction.domain.ForenwarnRecord;
import com.ruoyi.instruction.service.IForenwarnRecordService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 预警提醒记录Controller
 *
 * <AUTHOR>
 * @date 2024-07-31
 */
@RestController
@RequestMapping("/system/record")
public class ForenwarnRecordController extends BaseController
{
    @Autowired
    private IForenwarnRecordService forenwarnRecordService;

    @Autowired
    private SysDeptMapper deptMapper;

    /**
     * 查询预警提醒记录列表
     */
    @PreAuthorize("@ss.hasPermi('system:record:list')")
    @GetMapping("/list")
    public TableDataInfo list(ForenwarnRecord forenwarnRecord)
    {
        SysUser user = SecurityUtils.getLoginUser().getUser();
        SysDept dept = user.getDept();
        List<Long> parentIds = Arrays.asList(204L, 205L, 206L, 207L, 208L, 209L, 210L, 211L, 212L, 261L);
        if (dept.getParentId() != 202 && dept.getDeptId() != 202 && !parentIds.contains(dept.getParentId())) {
            SysDept parentDept = deptMapper.selectDeptById(dept.getParentId());
            String deptName = parentDept.getDeptName().replace("委政法委", "").replace("政法委", "").replace("政法办", "");
            forenwarnRecord.setDutyPlace(deptName);
        } else if (parentIds.contains(dept.getParentId())) {
            SysDept parentDept = deptMapper.selectDeptById(dept.getParentId());
            String deptName = parentDept.getDeptName().replace("委政法委", "").replace("政法委", "").replace("政法办", "");
            forenwarnRecord.setDutyPlace(deptName);
            //乡镇街道
            forenwarnRecord.setpTown(dept.getDeptName());
        }
        startPage();
        List<ForenwarnRecord> list = forenwarnRecordService.selectForenwarnRecordList(forenwarnRecord);
        return getDataTable(list);
    }

    /**
     * 导出预警提醒记录列表
     */
    @PreAuthorize("@ss.hasPermi('system:record:export')")
    @Log(title = "预警提醒记录", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, ForenwarnRecord forenwarnRecord)
    {
        List<ForenwarnRecord> list = forenwarnRecordService.selectForenwarnRecordList(forenwarnRecord);
        ExcelUtil<ForenwarnRecord> util = new ExcelUtil<ForenwarnRecord>(ForenwarnRecord.class);
        util.exportExcel(response, list, "预警提醒记录数据");
    }

    /**
     * 获取预警提醒记录详细信息
     */
    @PreAuthorize("@ss.hasPermi('system:record:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(forenwarnRecordService.selectForenwarnRecordById(id));
    }

    /**
     * 新增预警提醒记录
     */
    @PreAuthorize("@ss.hasPermi('system:record:add')")
    @Log(title = "预警提醒记录", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody ForenwarnRecord forenwarnRecord)
    {
        return toAjax(forenwarnRecordService.insertForenwarnRecord(forenwarnRecord));
    }

    /**
     * 修改预警提醒记录
     */
    @PreAuthorize("@ss.hasPermi('system:record:edit')")
    @Log(title = "预警提醒记录", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody ForenwarnRecord forenwarnRecord)
    {
        return toAjax(forenwarnRecordService.updateForenwarnRecord(forenwarnRecord));
    }

    /**
     * 删除预警提醒记录
     */
    @PreAuthorize("@ss.hasPermi('system:record:remove')")
    @Log(title = "预警提醒记录", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(forenwarnRecordService.deleteForenwarnRecordByIds(ids));
    }
}
