package com.ruoyi.instruction.domain.rspVo;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/4/17 11:38
 * 事件交办返回信息
 */
@Data
public class EventAssignRspVo {

    /**
     * 事件交办事件
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date date;

    /**
     * 已经交办数目
     */
    private Integer hasCount;

    /**
     * 未交办数目
     */
    private Integer notCount;
}
