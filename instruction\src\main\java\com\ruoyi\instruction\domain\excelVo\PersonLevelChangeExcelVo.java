package com.ruoyi.instruction.domain.excelVo;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 重点人员等级变更ExcelVo
 * <AUTHOR>
 * @version 1.0
 * @date 2024/9/2 9:16
 */
@Data
@EqualsAndHashCode
public class PersonLevelChangeExcelVo {

    @ColumnWidth(20)
    @ExcelProperty("县市区域")
    private String county;

    @ExcelProperty({"黄色","初始数"})
    private Long initialNum1;

    @ExcelProperty({"黄色","新增数"})
    private Long addedNum1;

    @ExcelProperty({"黄色","销号数"})
    private Long cancelledNum1;

    @ExcelProperty({"橙色","初始值"})
    private Long initialNum2;

    @ExcelProperty({"橙色","新增数"})
    private Long addedNum2;

    @ExcelProperty({"橙色","降级数"})
    private Long downgradeNum2;

    @ExcelProperty({"橙色","销号数"})
    private Long cancelledNum2;

    @ExcelProperty({"红色","初始数"})
    private Long initialNum3;

    @ExcelProperty({"红色","新增数"})
    private Long addedNum3;

    @ExcelProperty({"红色","降级数"})
    private Long downgradeNum3;

    @ExcelProperty({"红色","销号数"})
    private Long cancelledNum3;

    @ExcelProperty({"总计","初始数"})
    private Long totalInitialNum;

    @ExcelProperty({"总计","新增数"})
    private Long totalAddedNum;

    @ExcelProperty({"总计","降级数"})
    private Long totalDowngradeNum;

    @ExcelProperty({"总计","销号数"})
    private Long totalCancelledNum;
}
