package com.ruoyi.instruction.domain;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * 指令基本信息对象 t_instruction_info
 *
 * <AUTHOR>
 * @date 2022-12-19
 */
@Data
public class JazzInstructionInfo extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /**
     * 指令主键
     */
    private Long id;

    /**
     * 指令编码（自动生成唯一uuid）
     */
    @Excel(name = "指令编码", readConverterExp = "自动生成唯一uuid")
    private String instructionCode;

    /**
     * 指令标题
     */
    @Excel(name = "指令标题")
    private String instructionTitle;

    /**
     * 办理期限
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "办理期限", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date handleTime;

    /**
     * 接收单位
     */
    @Excel(name = "接收单位")
    private String receiveUnit;

    /**
     * 紧急程度
     */
    @Excel(name = "紧急程度")
    private String emergencyDegree;

    /**
     * 关联群体id
     */
    @Excel(name = "关联群体id")
    private Long groupId;

    /**
     * 关联群体名称
     */
    private String groupName;

    /**
     * 群体类型
     */
    @Excel(name = "群体类型")
    private String type;

    /**
     * 信息来源
     */
    @Excel(name = "信息来源")
    private String sourceInfo;

    /**
     * 指令内容
     */
    @Excel(name = "指令内容")
    private String instructionContent;

    /**
     * 基本情况
     */
    @Excel(name = "基本情况")
    private String baseInfo;

    /**
     * 创建者
     */
    @Excel(name = "创建者")
    private String creatorBy;

    /**
     * 状态 1:正常  9：删除
     */
    @Excel(name = "状态 1:正常  9：删除")
    private String status;

    /**
     * 指令状态 1:交办 2:暂存
     */
    @Excel(name = "指令状态 1:交办 2:暂存")
    private String instructionStatus;

    /**
     * 反馈要求
     */
    @Excel(name = "反馈要求")
    private String feedback;

    /**
     * 人员ids
     */
    @Excel(name = "人员ids")
    private String personIds;

    /**
     * 人员信息
     */
    private List<InstrucationPerson> personList;

    /**
     * 接收单位
     */
    private String[] unit;

    /**
     * 页码
     */
    private Integer start;

    /**
     * 每页显示条数
     */
    private Integer end;

    private Long typeId;

    /**
     * 事件id(用于判断创建指令的同时是否创建事件信息)
     */
    private Long eventId;

    /**
     * 流程，不传或者0为查全部,1交办，2接收，3处置，4反馈，5销号 6未处理 7已处理
     */
    private Integer process;

    /**
     * 排序类型，0为默认排序。1交办正序，2交办倒叙，3接收正序，4接收倒序，5处置正序，6处置倒序，7反馈正序，8反馈倒序，9销号正序，10销号倒序
     */
    private Integer sortType;


    /**
     * 指令创建者部门id
     */
    private Long createDeptId;

    /**
     * 牵头人员ids
     */
    private String leadPersonIds;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date assignTime;

    /**
     * 上访类型1：到市  2：赴省  3：进京
     */
    private String petitionType;

    /**
     * 推送时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date pushTime;

    /**
     * 信息类别（从字典中获取）
     */
    private String infoCategory;

    /**
     * 页面类型 1：查看市级指令页面 2：查看县市区指令页面
     */
    private Integer pageType;
    /**
     * 交办开始时间
     */
    private  Date assignStartTime;
    /**
     * 交办结束时间
     */
    private  Date assignEndTime;
    /**
     * 时间搜索（1本人2本周，3本月）
     */
    private Integer dateType;

}