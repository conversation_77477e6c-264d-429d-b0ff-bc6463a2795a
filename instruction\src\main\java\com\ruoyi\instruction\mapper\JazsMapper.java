package com.ruoyi.instruction.mapper;

import java.util.List;
import java.util.Map;

import com.ruoyi.instruction.domain.Jazs;
import org.apache.ibatis.annotations.Param;

/**
 * 金安指数Mapper接口
 *
 * <AUTHOR>
 * @date 2023-08-09
 */
public interface JazsMapper
{
    /**
     * 查询金安指数
     *
     * @param id 金安指数主键
     * @return 金安指数
     */
    public Jazs selectJazsById(Long id);

    /**
     * 查询金安指数列表
     *
     * @param jazs 金安指数
     * @return 金安指数集合
     */
    public List<Jazs> selectJazsList(Jazs jazs);

    /**
     * 新增金安指数
     *
     * @param jazs 金安指数
     * @return 结果
     */
    public int insertJazs(Jazs jazs);

    /**
     * 修改金安指数
     *
     * @param jazs 金安指数
     * @return 结果
     */
    public int updateJazs(Jazs jazs);

    /**
     * 删除金安指数
     *
     * @param id 金安指数主键
     * @return 结果
     */
    public int deleteJazsById(Long id);

    /**
     * 批量删除金安指数
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteJazsByIds(Long[] ids);

    public List<Jazs> selectJazsNewList(@Param("year") String year, @Param("county") String county,@Param("street") String street,@Param("queryMode") String queryMode,@Param("rankName")String rankName,@Param("rankType") String rankType);

    /**
     * 获取金安指数排名前十
     * @param str
     * @return
     */
    List<Map<String, Object>> selectTownTop(@Param("str") String str);

    List<Map<String, Object>> selectPointClass(@Param("year") final String date);

    Jazs selectOneByBusinessKey(Jazs queryParam);
}
