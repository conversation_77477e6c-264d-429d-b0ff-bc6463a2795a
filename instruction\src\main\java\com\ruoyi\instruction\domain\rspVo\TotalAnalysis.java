package com.ruoyi.instruction.domain.rspVo;

import com.alibaba.excel.annotation.ExcelProperty;
import com.ruoyi.common.annotation.Excel;
import lombok.Data;

/**
 * 区县整体化解分析
 */
@Data
public class TotalAnalysis {
//    @Excel(name = "县市区域", needMerge = true)
    @ExcelProperty("县市区域")
    private String  countyName;

    @ExcelProperty("排查数")
//    @Excel(name = "排查数")
    private int  count;

    @ExcelProperty("化解数")
//    @Excel(name = "化解数")
    private int solvedCount;

//    @Excel(name = "化解率")
    @ExcelProperty("化解率")
    private String dissolutionRate;

//    @Excel(name = "未化解数")
    @ExcelProperty("未化解数")
    private int noSolvedCount;
//    @Excel(name = "超期未化解")
    @ExcelProperty("超期未化解")
    private int overTimeCount;


    public TotalAnalysis(String countyName) {
        this.countyName = countyName;
    }

    public TotalAnalysis(String countyName, int count, int solvedCount, String dissolutionRate, int noSolvedCount, int overTimeCount) {
        this.countyName = countyName;
        this.count = count;
        this.solvedCount = solvedCount;
        this.dissolutionRate = dissolutionRate;
        this.noSolvedCount = noSolvedCount;
        this.overTimeCount = overTimeCount;
    }
}
