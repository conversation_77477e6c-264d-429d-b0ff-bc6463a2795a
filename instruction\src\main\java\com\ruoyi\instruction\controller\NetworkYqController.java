package com.ruoyi.instruction.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.instruction.domain.NetworkYq;
import com.ruoyi.instruction.service.INetworkYqService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 网络舆情Controller
 * 
 * <AUTHOR>
 * @date 2023-09-14
 */
@RestController
@RequestMapping("/network/yq")
public class NetworkYqController extends BaseController
{
    @Autowired
    private INetworkYqService networkYqService;

    /**
     * 查询网络舆情列表
     */
    @PreAuthorize("@ss.hasPermi('network:yq:list')")
    @GetMapping("/list")
    public TableDataInfo list(NetworkYq networkYq)
    {
        startPage();
        List<NetworkYq> list = networkYqService.selectNetworkYqList(networkYq);
        return getDataTable(list);
    }



    /**
     * 导出网络舆情列表
     */
    @PreAuthorize("@ss.hasPermi('network:yq:export')")
    @Log(title = "网络舆情", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, NetworkYq networkYq)
    {
        List<NetworkYq> list = networkYqService.selectNetworkYqList(networkYq);
        ExcelUtil<NetworkYq> util = new ExcelUtil<NetworkYq>(NetworkYq.class);
        util.exportExcel(response, list, "网络舆情数据");
    }

    /**
     * 获取网络舆情详细信息
     */
    @PreAuthorize("@ss.hasPermi('network:yq:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") String id)
    {
        return success(networkYqService.selectNetworkYqById(id));
    }

    /**
     * 新增网络舆情
     */
    @PreAuthorize("@ss.hasPermi('network:yq:add')")
    @Log(title = "网络舆情", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody NetworkYq networkYq)
    {
        return toAjax(networkYqService.insertNetworkYq(networkYq));
    }

    /**
     * 修改网络舆情
     */
    @PreAuthorize("@ss.hasPermi('network:yq:edit')")
    @Log(title = "网络舆情", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody NetworkYq networkYq)
    {
        return toAjax(networkYqService.updateNetworkYq(networkYq));
    }

    /**
     * 删除网络舆情
     */
    @PreAuthorize("@ss.hasPermi('network:yq:remove')")
    @Log(title = "网络舆情", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable String[] ids)
    {
        return toAjax(networkYqService.deleteNetworkYqByIds(ids));
    }
}
