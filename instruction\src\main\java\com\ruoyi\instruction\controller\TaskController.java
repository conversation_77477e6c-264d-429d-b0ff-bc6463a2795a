package com.ruoyi.instruction.controller;

import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.utils.IDCardUtils;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.instruction.domain.InstrucationPerson;
import com.ruoyi.instruction.mapper.InstrucationPersonMapper;
import com.ruoyi.instruction.service.IInstrucationPersonService;
import com.ruoyi.instruction.service.ITaskService;
import com.ruoyi.instruction.task.WarningEventTask;
import org.apache.ibatis.annotations.Param;
import org.aspectj.weaver.loadtime.Aj;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 定时任务执行接口
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2023/5/5 10:46
 */
@RestController
@RequestMapping("/task")
public class TaskController extends BaseController {

    @Autowired
    private ITaskService taskService;

    @Autowired
    private InstrucationPersonMapper instrucationPersonMapper;

    /**
     * 生成事件数量进行判断
     * @param id
     * @return
     */
    @GetMapping("/eventCount/{id}")
    public AjaxResult eventCount(@PathVariable("id")Long id){
        return taskService.eventCount(id);
    }

    /**
     * 人员在事件中出现次数进行阈值告警
     * @param id
     * @return
     */
    @GetMapping("/eventPersonCount/{id}")
    public AjaxResult eventPersonCount(@PathVariable("id")Long id){
        return taskService.eventPersonCount(id);
    }

    /**
     *  区域事件
     * @param id
     * @return
     */
    @GetMapping("/areaEventCount/{id}")
    public AjaxResult areaEventCount(@PathVariable("id")Long id){
        return taskService.areaEventCount(id);
    }

    /**
     * 事件增速
     * @param id
     * @return
     */
    @GetMapping("/eventGrowthRate/{id}")
    public AjaxResult eventGrowthRate(@PathVariable("id")Long id){
        return taskService.eventGrowthRate(id);
    }

    /**
     * 人员在群体中出现次数，进行预警
     * @param id
     * @return
     */
    @GetMapping("/groupPersonCount/{id}")
    public AjaxResult groupPersonCount(@PathVariable("id")Long id){
        return taskService.groupPersonCount(id);
    }

    /**
     * 事件类型增速
     * @param id
     * @return
     */
    @GetMapping("eventTypeGrowthRate/{id}")
    public AjaxResult eventTypeGrowthRate(@PathVariable("id")Long id){
        return taskService.eventTypeGrowthRate(id);
    }

    /**
     * 区域事件类型增速
     * @param id
     * @return
     */
    @GetMapping("areaEventTypeGrowthRate/{id}")
    public AjaxResult areaEventTypeGrowthRate(@PathVariable("id")Long id){
        return taskService.areaEventTypeGrowthRate(id);
    }

    /**
     * 区域涉事人员增速
     * @param id
     * @return
     */
    @GetMapping("/areaPersonGrowthRate/{id}")
    public AjaxResult areaPersonGrowthRate(@PathVariable("id")Long id){

        return null;
    }


    @GetMapping("/getPerson")
    public AjaxResult getPerson(){

        return AjaxResult.success();
    }

    @GetMapping("/updatePersonIdCard")
    public AjaxResult updatePersonIdCard(InstrucationPerson person){
        startPage();

        List<InstrucationPerson> personList = instrucationPersonMapper.selectInstrucationPersonList(person);
        personList.stream().forEach(person1 -> {
            if (person1.getIdCard() != null && person1.getIdCard() != "") {
                //加密身份证号
                String encryptedIDCard = IDCardUtils.encryptIDCard(person1.getIdCard());
                person1.setIdCard(encryptedIDCard);
                instrucationPersonMapper.updateInstrucationPerson(person1);
            }
        });
        return AjaxResult.success(personList);
    }

}
