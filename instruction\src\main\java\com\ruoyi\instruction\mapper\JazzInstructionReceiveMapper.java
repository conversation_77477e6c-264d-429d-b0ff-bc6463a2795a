package com.ruoyi.instruction.mapper;

import com.ruoyi.instruction.domain.JazzInstructionReceive;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

/**
 * 指令接收Mapper接口
 * 
 * <AUTHOR>
 * @date 2022-12-19
 */
@Mapper
public interface JazzInstructionReceiveMapper
{
    /**
     * 查询指令接收
     * 
     * @param id 指令接收主键
     * @return 指令接收
     */
    public JazzInstructionReceive selectInstructionReceiveById(Long id);

    /**
     * 查询指令接收列表
     * 
     * @param instructionReceive 指令接收
     * @return 指令接收集合
     */
    public List<JazzInstructionReceive> selectInstructionReceiveList(JazzInstructionReceive instructionReceive);

    /**
     * 新增指令接收
     * 
     * @param instructionReceive 指令接收
     * @return 结果
     */
    public int insertInstructionReceive(JazzInstructionReceive instructionReceive);

    /**
     * 修改指令接收
     * 
     * @param instructionReceive 指令接收
     * @return 结果
     */
    public int updateInstructionReceive(JazzInstructionReceive instructionReceive);

    /**
     * 删除指令接收
     * 
     * @param id 指令接收主键
     * @return 结果
     */
    public int deleteInstructionReceiveById(Long id);

    /**
     * 批量删除指令接收
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteInstructionReceiveByIds(Long[] ids);

    /**
     * 根据指令id判断是否已经接收
     * @param id
     * @return
     */
    Date selectReceiveByInstructionId(Long id);

    /**
     * 查询出已经完成交办的单位
     * @param id
     * @return
     */
    List<String> findDisposeUnit(@Param("id") Long id);

//    /**
//     * 查询指令已反馈的部门
//     * @param id
//     * @return
//     */
//    List<String> findFeedBackUnit(@Param("id") Long id);

    /**
     * 查询接收最晚时间
     * @param id
     * @return
     */
    Date findReceiveTime(@Param("id") Long id,@Param("unitCount") Integer unitCount);
}
