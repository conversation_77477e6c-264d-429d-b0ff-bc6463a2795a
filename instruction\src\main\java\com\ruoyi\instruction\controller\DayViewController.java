package com.ruoyi.instruction.controller;

import java.util.List;
import java.util.stream.Collectors;
import javax.servlet.http.HttpServletResponse;

import com.ruoyi.common.core.domain.entity.SysDept;
import com.ruoyi.common.core.domain.entity.SysRole;
import com.ruoyi.common.utils.SecurityUtils;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.instruction.domain.DayView;
import com.ruoyi.instruction.service.IDayViewService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 日督查信息Controller
 * 
 * <AUTHOR>
 * @date 2023-09-02
 */
@RestController
@RequestMapping("/day/view")
public class DayViewController extends BaseController
{
    @Autowired
    private IDayViewService dayViewService;

    /**
     * 查询日督查信息列表
     */
    @PreAuthorize("@ss.hasPermi('day:view:list')")
    @GetMapping("/list")
    public TableDataInfo list(DayView dayView)
    {

        startPage();
        List<DayView> list = dayViewService.selectDayViewList(dayView);
        return getDataTable(list);
    }

    /**
     * 导出日督查信息列表
     */
    @PreAuthorize("@ss.hasPermi('day:view:export')")
    @Log(title = "日督查信息", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, DayView dayView)
    {
        List<DayView> list = dayViewService.selectDayViewList(dayView);
        ExcelUtil<DayView> util = new ExcelUtil<DayView>(DayView.class);
        util.exportExcel(response, list, "日督查信息数据");
    }

    /**
     * 获取日督查信息详细信息
     */
    @PreAuthorize("@ss.hasPermi('day:view:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(dayViewService.selectDayViewById(id));
    }

    /**
     * 新增日督查信息
     */
    @PreAuthorize("@ss.hasPermi('day:view:add')")
    @Log(title = "日督查信息", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody DayView dayView)
    {
        return toAjax(dayViewService.insertDayView(dayView));
    }

    /**
     * 修改日督查信息
     */
    @PreAuthorize("@ss.hasPermi('day:view:edit')")
    @Log(title = "日督查信息", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody DayView dayView)
    {
        return toAjax(dayViewService.updateDayView(dayView));
    }

    /**
     * 删除日督查信息
     */
    @PreAuthorize("@ss.hasPermi('day:view:remove')")
    @Log(title = "日督查信息", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(dayViewService.deleteDayViewByIds(ids));
    }
}
