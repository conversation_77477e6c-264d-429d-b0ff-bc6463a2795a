package com.ruoyi.instruction.domain.rspVo;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.HeadFontStyle;
import com.alibaba.excel.annotation.write.style.HeadRowHeight;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.common.annotation.Excel;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/2/9 14:33
 * 历年事件包含人员
 */
@Data
@ColumnWidth(16)
@HeadRowHeight(14)
@HeadFontStyle(fontHeightInPoints = 11)
public class EventPersonRspVo {
    /**
     * 人员姓名
     */
    @ExcelProperty(value = "人员姓名(必填)")
    private String personName;

    /**
     * 证件号码
     */
    @ExcelProperty(value = "证件号码(必填)")
    private String idCard;

    /**
     * 户籍所在地
     */
    @ExcelProperty(value = "户籍所在地")
    private String housePlace;

    /**
     * 当前居住地
     */
    @ExcelProperty(value = "当前居住地")
    private String currentPlace;

    /**
     * 责任所在地
     */
    @ExcelProperty(value = "管控级别")
    private String controlLevel;

    /**
     * 电话号码
     */
    @ExcelProperty(value = "电话号码")
    private String personPhone;

    /**
     * 事件标题
     */
    @ExcelProperty(value = "事件名称")
    private String eventTitle;

    /**
     * 推送时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @ExcelProperty(value = "推送时间(格式yyy-MM-dd)")
    private Date pushTime;

}
