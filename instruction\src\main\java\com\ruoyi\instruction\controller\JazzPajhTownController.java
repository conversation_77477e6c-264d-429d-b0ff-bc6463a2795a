package com.ruoyi.instruction.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.instruction.domain.JazzPajhTown;
import com.ruoyi.instruction.service.IJazzPajhTownService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 平安金华乡镇信息Controller
 * 
 * <AUTHOR>
 * @date 2023-05-15
 */
@RestController
@RequestMapping("/instruction/town")
public class JazzPajhTownController extends BaseController
{
    @Autowired
    private IJazzPajhTownService jazzPajhTownService;

    /**
     * 查询平安金华乡镇信息列表
     */
//    @PreAuthorize("@ss.hasPermi('instruction:town:list')")
    @GetMapping("/list")
    public TableDataInfo list(JazzPajhTown jazzPajhTown)
    {
        startPage();
        List<JazzPajhTown> list = jazzPajhTownService.selectJazzPajhTownList(jazzPajhTown);
        return getDataTable(list);
    }

    /**
     * 导出平安金华乡镇信息列表
     */
//    @PreAuthorize("@ss.hasPermi('instruction:town:export')")
    @Log(title = "平安金华乡镇信息", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, JazzPajhTown jazzPajhTown)
    {
        List<JazzPajhTown> list = jazzPajhTownService.selectJazzPajhTownList(jazzPajhTown);
        ExcelUtil<JazzPajhTown> util = new ExcelUtil<JazzPajhTown>(JazzPajhTown.class);
        util.exportExcel(response, list, "平安金华乡镇信息数据");
    }

    /**
     * 获取平安金华乡镇信息详细信息
     */
//    @PreAuthorize("@ss.hasPermi('instruction:town:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(jazzPajhTownService.selectJazzPajhTownById(id));
    }

    /**
     * 新增平安金华乡镇信息
     */
    @PreAuthorize("@ss.hasPermi('instruction:town:add')")
    @Log(title = "平安金华乡镇信息", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody JazzPajhTown jazzPajhTown)
    {
        return toAjax(jazzPajhTownService.insertJazzPajhTown(jazzPajhTown));
    }

    /**
     * 修改平安金华乡镇信息
     */
    @PreAuthorize("@ss.hasPermi('instruction:town:edit')")
    @Log(title = "平安金华乡镇信息", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody JazzPajhTown jazzPajhTown)
    {
        return toAjax(jazzPajhTownService.updateJazzPajhTown(jazzPajhTown));
    }

    /**
     * 删除平安金华乡镇信息
     */
    @PreAuthorize("@ss.hasPermi('instruction:town:remove')")
    @Log(title = "平安金华乡镇信息", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(jazzPajhTownService.deleteJazzPajhTownByIds(ids));
    }
}
