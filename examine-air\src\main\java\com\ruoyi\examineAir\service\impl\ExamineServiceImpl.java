package com.ruoyi.examineAir.service.impl;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;

import cn.hutool.core.util.ReflectUtil;
import com.alibaba.fastjson2.JSON;
import com.ruoyi.common.exception.GlobalException;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.examineAir.domain.ExamineConfig;
import com.ruoyi.examineAir.domain.ExamineDetails;
import com.ruoyi.examineAir.mapper.ExamineConfigMapper;
import com.ruoyi.examineAir.mapper.ExamineDetailsMapper;
import com.ruoyi.examineAir.service.IExamineDetailsService;
import com.ruoyi.examineAir.vo.BigScreenExamineVo;
import com.ruoyi.examineAir.vo.BigScreenPageRegionStatisticsVo;
import com.ruoyi.examineAir.vo.ExamineDetailsVo;
import com.ruoyi.examineAir.vo.ExamineVo;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.examineAir.mapper.ExamineMapper;
import com.ruoyi.examineAir.domain.Examine;
import com.ruoyi.examineAir.service.IExamineService;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

/**
 * 考核晾晒列Service业务层处理
 * 
 * <AUTHOR>
 * @date 2023-04-19
 */
@Service
public class ExamineServiceImpl implements IExamineService 
{
    @Autowired
    private ExamineMapper examineMapper;

    @Autowired
    private ExamineConfigMapper examineConfigMapper;

    @Autowired
    private ExamineDetailsMapper examineDetailsMapper;


    /**
     * 查询考核晾晒列
     * 
     * @param id 考核晾晒列主键
     * @return 考核晾晒列
     */
    @Override
    public ExamineVo selectExamineById(Long id)
    {
        ExamineVo examineVo=new ExamineVo();
        Examine examine = examineMapper.selectExamineById(id);
        BeanUtils.copyProperties(examine,examineVo);
        ExamineDetails examineDetails=new ExamineDetails();
        examineDetails.settExamineId(id);
        List<ExamineDetails> examineDetails1 = examineDetailsMapper.selectExamineDetailsList(examineDetails);
        examineVo.setExamineDetailsList(examineDetails1);
        return examineVo;
    }

    /**
     * 查询考核晾晒列列表
     * 
     * @param examine 考核晾晒列
     * @return 考核晾晒列
     */
    @Override
    public List<Examine> selectExamineList(Examine examine)
    {
        return examineMapper.selectExamineList(examine);
    }

    /**
     * 新增考核晾晒列
     * 
     * @param examine 考核晾晒列
     * @return 结果
     */
    @Override
    public int insertExamine(Examine examine)
    {
        return examineMapper.insertExamine(examine);
    }

    /**
     * 修改考核晾晒列
     * 
     * @param examine 考核晾晒列
     * @return 结果
     */
    @Override
    public int updateExamine(Examine examine)
    {
        return examineMapper.updateExamine(examine);
    }

    /**
     * 批量删除考核晾晒列
     * 
     * @param ids 需要删除的考核晾晒列主键
     * @return 结果
     */
    @Override
    public int deleteExamineByIds(Long[] ids)
    {
        return examineMapper.deleteExamineByIds(ids);
    }

    /**
     * 删除考核晾晒列信息
     * 
     * @param id 考核晾晒列主键
     * @return 结果
     */
    @Override
    public int deleteExamineById(Long id)
    {
        return examineMapper.deleteExamineById(id);
    }

    @Transactional
    @Override
    public int insertExamine(ExamineVo examineVo) {
        check(examineVo);
        Examine examine=new Examine();
        examine.setTitle(examineVo.getTitle());
        examine.setLike(false);
        List<Examine> examines = examineMapper.selectExamineList(examine);
        if (!CollectionUtils.isEmpty(examines)){
            throw  new GlobalException("标题不能重复");
        }
        BeanUtils.copyProperties(examineVo,examine);
        examine.setId(null);
        int i = examineMapper.insertExamine(examine);
        List<ExamineDetails> examineDetailsList = examineVo.getExamineDetailsList();
        for (ExamineDetails e:examineDetailsList){
            e.setId(null);
            e.settExamineId(examine.getId());
            examineDetailsMapper.insertExamineDetails(e);
        }
        return i;
    }

    @Override
    public int release(Examine examine) {
        Examine examine1 = examineMapper.selectExamineById(examine.getId());
        examine1.setStatus(examine.getStatus());
        if (examine.getStatus()==1){
            examine1.setReleaseTime(new Date());
        }
        int i = examineMapper.updateExamine(examine1);
        return i;
    }

    @Override
    public int updateExamine(ExamineVo examineVo) {
        check(examineVo);
        Examine examine=new Examine();
        examine.setTitle(examineVo.getTitle());
        examine.setLike(false);
        List<Examine> examines = examineMapper.selectExamineList(examine);
//        Examine examine1 = examines.get(0);
        if (CollectionUtils.isEmpty(examines)||(examines.size()==1&& examines.get(0).getId().equals(examineVo.getId()))){
            BeanUtils.copyProperties(examineVo,examine);
            int i = examineMapper.updateExamine(examine);
        }else {
            throw  new GlobalException("标题不能重复");
        }
        List<ExamineDetails> examineDetailsList = examineVo.getExamineDetailsList();
        for (ExamineDetails e:examineDetailsList){
            examineDetailsMapper.updateExamineDetails(e);
        }
        return 1;
    }

    @Override
    public BigScreenExamineVo bigScreenExamineDetails(Long id) {
        BigScreenExamineVo examineVo=new BigScreenExamineVo();
        Examine examine = examineMapper.selectExamineById(id);
        if (examine==null){
            return examineVo;
        }
        BeanUtils.copyProperties(examine,examineVo);
        ExamineDetails examineDetails=new ExamineDetails();
        examineDetails.settExamineId(id);
        List<ExamineDetails> examineDetails1 = examineDetailsMapper.selectExamineDetailsList(examineDetails);
        if (!CollectionUtils.isEmpty(examineDetails1)){
            List<ExamineDetailsVo>  examineDetailsVoList=new ArrayList<>();
            for (ExamineDetails e:examineDetails1){
                ExamineDetailsVo examineDetailsVo=new ExamineDetailsVo();
                BeanUtils.copyProperties(e,examineDetailsVo);
                examineDetailsVoList.add(examineDetailsVo);
            }
            examineVo.setExamineDetailsList(examineDetailsVoList);
        }
        return examineVo;
    }

    @Override
    public List<BigScreenPageRegionStatisticsVo> getNewAreaCount() {
        return examineMapper.getNewAreaCount();
    }

    /**
     * 按区域获取最新一条的考核评价
     * @param area
     * @return
     */
    @Override
    public BigScreenExamineVo regionExamine(String area) {
        Examine max = examineMapper.getNEW();
        BigScreenExamineVo bigScreenExamineVo=new BigScreenExamineVo();
        if (max!=null){
            BeanUtils.copyProperties(max,bigScreenExamineVo);
            ExamineDetails examineDetails=new ExamineDetails();
            examineDetails.settExamineId(max.getId());
            examineDetails.setScore2(area);
            List<ExamineDetails> examineDetails1 = examineDetailsMapper.selectExamineDetailsList(examineDetails);
            if (!CollectionUtils.isEmpty(examineDetails1)){
                List<ExamineDetailsVo> list=new ArrayList<>();
                ExamineDetails examineDetails2 = examineDetails1.get(0);
                ExamineDetailsVo examineDetailsVo=new ExamineDetailsVo();
                BeanUtils.copyProperties(examineDetails2,examineDetailsVo);
//                List<HashMap> objects = JSON.parseArray(max.getHeadContent(), HashMap.class);
//                for (HashMap h:objects){
//                    if ( h.get("name").equals("得分")){
//                        ReflectUtil.setFieldValue(examineDetailsVo,  h.get("value").toString() ,examineDetails2.getTotalScore());
//                    }
//                }
                list.add(examineDetailsVo);
                bigScreenExamineVo.setExamineDetailsList(list);
            }
        }
      return bigScreenExamineVo;
    }


    /**
     * 校验
     * @param examineVo
     */
    public  void check(ExamineVo examineVo) {
        if (examineVo.getType() == null || examineVo.getType() > 3) {
            throw new GlobalException("模板错误");
        }
        List<ExamineDetails> examineDetailsList = examineVo.getExamineDetailsList();
        if (CollectionUtils.isEmpty(examineDetailsList)||examineDetailsList.size()!=10){
            throw  new GlobalException("请参照模板填写数据");
        }
        if (StringUtils.isEmpty(examineVo.getTitle())){
            throw new GlobalException("标题不能为空");
        }
        ExamineConfig examineConfig=new ExamineConfig();
        examineConfig.setType(examineVo.getType());
        List<ExamineConfig> examineConfigs = examineConfigMapper.selectExamineConfigList(examineConfig);
        if (CollectionUtils.isEmpty(examineConfigs)){
            throw new GlobalException("模板异常");
        }
        String s=null;
        List<HashMap> objects = JSON.parseArray(examineVo.getHeadContent(), HashMap.class);
        for (HashMap h:objects){
            if (h.get("name").equals("得分")){
                s=h.get("value").toString();
            }
        }
        for (ExamineDetails e:examineDetailsList){
            BigDecimal decimal=BigDecimal.ZERO;
            for (ExamineConfig config:examineConfigs){
                if (config.getSummation()!=1){
                    continue;
                }
                BigDecimal fieldValue =(BigDecimal) ReflectUtil.getFieldValue(e, config.getFieldName());
//                int i = fieldValue.compareTo(config.getMaxValue());
//                if (i==1){
//                    throw new GlobalException(config.getFieldIntroduce()+"的值高于规定最高值："+config.getMaxValue());
//                }
                decimal=decimal.add(fieldValue);
            }
            e.setTotalScore(decimal);
            if (!StringUtils.isEmpty(s)){
                ReflectUtil.setFieldValue(e,  s ,decimal);
            }
            setParameter(examineVo,e);
        }
    }

    public  void  setParameter(ExamineVo examineVo, ExamineDetails examineDetails){
        if (examineDetails.getScore2().contains("婺城")){
            examineVo.setWucheng(examineDetails.getTotalScore());
        }else if (examineDetails.getScore2().contains("义乌")){
            examineVo.setYiwu(examineDetails.getTotalScore());
        }else if (examineDetails.getScore2().contains("兰溪")){
            examineVo.setLanxi(examineDetails.getTotalScore());
        }else if (examineDetails.getScore2().contains("开发区")){
            examineVo.setKaifa(examineDetails.getTotalScore());
        }else if (examineDetails.getScore2().contains("东阳")){
            examineVo.setDongyang(examineDetails.getTotalScore());
        }else if (examineDetails.getScore2().contains("武义")){
            examineVo.setWuyi(examineDetails.getTotalScore());
        }else if (examineDetails.getScore2().contains("永康")){
            examineVo.setYongkang(examineDetails.getTotalScore());
        }else if (examineDetails.getScore2().contains("磐安")){
            examineVo.setPanan(examineDetails.getTotalScore());
        }else if (examineDetails.getScore2().contains("金东区")){
            examineVo.setJindong(examineDetails.getTotalScore());
        } else if (examineDetails.getScore2().contains("浦江")){
            examineVo.setPujiang(examineDetails.getTotalScore());
        }
    }
}
