package com.ruoyi.instruction.domain;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 指令文件对象 t_instruction_file
 * 
 * <AUTHOR>
 * @date 2022-12-19
 */
public class InstructionFile extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 文件表主键id */
    private Long id;

    /** 文件名 */
    @Excel(name = "文件名")
    private String fileName;

    /** 文件来源 */
    @Excel(name = "文件来源")
    private String source;

    /** 文件路径 */
    @Excel(name = "文件路径")
    private String filePath;

    /** 文件url */
    @Excel(name = "文件url")
    private String fileUrl;

    /** 文件状态 1：正常 9：删除 */
    @Excel(name = "文件状态 1：正常 9：删除")
    private Integer status;

    /** 原文件名 */
    @Excel(name = "原文件名")
    private String originalFileName;

    public void setId(Long id) 
    {
        this.id = id;
    }

    public Long getId() 
    {
        return id;
    }
    public void setFileName(String fileName) 
    {
        this.fileName = fileName;
    }

    public String getFileName() 
    {
        return fileName;
    }
    public void setSource(String source) 
    {
        this.source = source;
    }

    public String getSource() 
    {
        return source;
    }
    public void setFilePath(String filePath) 
    {
        this.filePath = filePath;
    }

    public String getFilePath() 
    {
        return filePath;
    }
    public void setFileUrl(String fileUrl) 
    {
        this.fileUrl = fileUrl;
    }

    public String getFileUrl() 
    {
        return fileUrl;
    }
    public void setStatus(Integer status) 
    {
        this.status = status;
    }

    public Integer getStatus() 
    {
        return status;
    }


    public void setOriginalFileName(String originalFileName)
    {
        this.originalFileName = originalFileName;
    }

    public String getOriginalFileName()
    {
        return originalFileName;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("fileName", getFileName())
            .append("source", getSource())
            .append("filePath", getFilePath())
            .append("fileUrl", getFileUrl())
            .append("createBy", getCreateBy())
            .append("createTime", getCreateTime())
            .append("status", getStatus())
            .toString();
    }
}
