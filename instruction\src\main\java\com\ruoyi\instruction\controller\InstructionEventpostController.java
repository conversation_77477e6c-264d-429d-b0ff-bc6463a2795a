package com.ruoyi.instruction.controller;


import com.fasterxml.jackson.databind.node.ObjectNode;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.instruction.domain.AdCode;
import com.ruoyi.instruction.domain.InstructionEventpost;
import com.ruoyi.instruction.domain.InstructionFile;
import com.ruoyi.instruction.domain.rspVo.InstructionEventpostObj;
import com.ruoyi.instruction.service.IAdCodeService;
import com.ruoyi.instruction.service.IInstructionEventpostService;
import com.ruoyi.instruction.service.IInstructionFileService;
import com.ruoyi.instruction.task.AsyncTask;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.gson.Gson;
import com.google.gson.JsonObject;

/**
 * 网格直报Controller
 *
 * <AUTHOR>
 * @date 2023-07-21
 */
@RestController
@RequestMapping("/system/eventpost")
public class InstructionEventpostController extends BaseController {
    @Autowired
    private IInstructionEventpostService instructionEventpostService;

    @Autowired
    private IInstructionFileService fileService;

    @Autowired
    private IAdCodeService adCodeService;

    @Autowired
    private AsyncTask asyncTask;

    /**
     * 根据网格id获取网格详细信息
     * @param id
     * @return
     */
    @GetMapping(value = "/bigScreen/{id}")
    public AjaxResult bigScreenGetInfo(@PathVariable("id") String id) {
        InstructionEventpost instructionEventpost = instructionEventpostService.selectInstructionEventpostById(id);
        List<Map<String, Object>> mapList = instructionEventpostService.findDealInfo();
        for (Map<String, Object> map : mapList) {
            String wgzb_id = String.valueOf(map.get("wgzb_id"));
            if (instructionEventpost.getId().equals(wgzb_id)) {
                String type = String.valueOf(map.get("type"));
                String infoId = String.valueOf(map.get("infoId"));
                instructionEventpost.setDisposeType(Long.valueOf(type));
                instructionEventpost.setInfoId(Long.valueOf(infoId));
            }
        }
        return success(instructionEventpost);
    }

    /**
     * 重点工作-网格直报list
     * @return
     */
    @GetMapping("/listForBigScreen")
    public AjaxResult listForBigScreen(InstructionEventpost instructionEventpost){
        List<InstructionEventpost> list = instructionEventpostService.selectInstructionEventpostList(instructionEventpost);
        return AjaxResult.success(list);
    }

    /**
     * 查询网格直报列表
     */
    @PreAuthorize("@ss.hasPermi('system:eventpost:list')")
    @GetMapping("/list")
    public TableDataInfo list(InstructionEventpost instructionEventpost) {

        //处理类型为已处理时查询其对应事件、指令id
        List<Map<String, Object>> mapList = instructionEventpostService.findDealInfo();
        if (mapList != null && mapList.size() > 0) {
            List<String> dealIds = mapList.stream().map(m -> m.get("wgzb_id").toString()).collect(Collectors.toList());
            Map<String, Object> params = instructionEventpost.getParams();
            params.put("dealIds", dealIds);
            instructionEventpost.setParams(params);
        }
        List<InstructionEventpost> list = new ArrayList<>();
        if (mapList.size() == 0 && instructionEventpost.getDealType() == 1) {
            return getDataTable(list);
        }
        startPage();
        list = instructionEventpostService.selectInstructionEventpostList(instructionEventpost);
        list.stream().forEach(eventPost -> {
            Date reportDate = new Date();
            reportDate.setTime(eventPost.getReportDate());
            eventPost.setReportDateTime(reportDate);
            Date occurDate = new Date();
            occurDate.setTime(eventPost.getOccurDate());
            eventPost.setOccurDateTime(occurDate);
            if (instructionEventpost.getDealType() == null || instructionEventpost.getDealType() == 1) {
                String eventPostId = eventPost.getId();
                for (Map<String, Object> map : mapList) {
                    String wgzb_id = String.valueOf(map.get("wgzb_id"));
                    if (eventPostId.equals(wgzb_id)) {
                        String type = String.valueOf(map.get("type"));
                        String infoId = String.valueOf(map.get("infoId"));
                        eventPost.setDisposeType(Long.valueOf(type));
                        eventPost.setInfoId(Long.valueOf(infoId));
                    }
                }
            }
        });
        return getDataTable(list);
    }


    /**
     * ObjectNode类型封装返回值的上传方法
     */
    @PreAuthorize("@ss.hasPermi('system:eventpost:add')")
    @PostMapping
    public ObjectNode add(InstructionEventpost instructionEventpost) {
        ObjectMapper mapper = new ObjectMapper();
        ObjectNode result = mapper.createObjectNode();
        //对传输过来的数据进行过滤 事发时间 上报时间
        Date reportDate = new Date();
        reportDate.setTime(instructionEventpost.getReportDate());
        instructionEventpost.setReportDateTime(reportDate);
        Date occurDate = new Date();
        occurDate.setTime(instructionEventpost.getOccurDate());
        instructionEventpost.setOccurDateTime(occurDate);
        //文件放入文件库中
        if (instructionEventpost.getReportFiles()!=null&&!instructionEventpost.getReportFiles().equals("")){
            String[] split = instructionEventpost.getReportFiles().split(",");
            List<String> ids = new ArrayList<>();
            for (String s:split){
                InstructionFile file = new InstructionFile();
                file.setFileUrl(s);
                file.setFileName("网格直报"+ UUID.randomUUID());
                fileService.insertInstructionFile(file);
                ids.add(file.getId()+"");
            }
            String collect = ids.stream().collect(Collectors.joining(","));
            instructionEventpost.setFileIds(collect);
        }
        //网格上报县市区判断
        String reportAdCode = instructionEventpost.getReportAdCode();
        AdCode adCode = new AdCode();
        if (reportAdCode.length()>=9){
            String substring = reportAdCode.substring(0, 9);
            //查询对应乡镇街道
            adCode.setTownCode(substring);
            List<AdCode> adCodes = adCodeService.selectAdCodeList(adCode);
            AdCode adCode1 = adCodes.get(0);
            if (adCode1.getCounty().contains("开发区")){
                instructionEventpost.setCounty("开发区");
            }else {
                instructionEventpost.setCounty(adCode1.getCounty());
            }

            String town = adCode1.getTown();
            String replace = town.replace(adCode1.getCounty(), "").replace("人民政府","").replace("办事处","");

            instructionEventpost.setTown(replace);
        }else {
            //查询对应县市区
            String substring = reportAdCode.substring(0, 6);
            adCode.setCountyCode(substring);
            List<AdCode> adCodes = adCodeService.selectAdCodeList(adCode);
            AdCode adCode1 = adCodes.get(0);
            if (adCode1.getCounty().contains("开发区")){
                instructionEventpost.setCounty("开发区");
            }else {
                instructionEventpost.setCounty(adCode1.getCounty());
            }

        }
        Integer resultCode = instructionEventpostService.insertInstructionEventpost(instructionEventpost);
        Integer httpStatus = 0;
        String resultMsg = "";
        if (resultCode == 1) {
            resultCode = 0;
        } else {
            resultCode = 1;
        }
        if (resultCode == 0) {
            httpStatus = 200;
            resultMsg = "导入成功";
        }
        //事件推送过来发送工作通知
        asyncTask.sendMsgByEventPost(instructionEventpost);
        InstructionEventpostObj instructionEventpostObj = new InstructionEventpostObj();
        result.putPOJO("resultCode", resultCode);
        result.putPOJO("httpStatus", httpStatus);
        result.putPOJO("resultMsg", resultMsg);
        result.putPOJO("serverTime", DateUtils.getTime());
        instructionEventpostObj.setThirdEventId(instructionEventpost.getId());
        instructionEventpostObj.setEventId(instructionEventpost.getEventId());
        instructionEventpostObj.setThirdSerialNumber(instructionEventpost.getCode());
        result.putPOJO("obj", instructionEventpostObj);
        return result;
    }


    /**
     * 导出网格直报列表
     */
    @PreAuthorize("@ss.hasPermi('system:eventpost:export')")
    @Log(title = "网格直报", businessType = BusinessType.EXPORT)
    @GetMapping("/export")
    public AjaxResult export(HttpServletResponse response, InstructionEventpost instructionEventpost) {
        //处理类型为已处理时查询其对应事件、指令id
        List<Map<String, Object>> mapList = instructionEventpostService.findDealInfo();
        if (mapList != null && mapList.size() > 0) {
            List<String> dealIds = mapList.stream().map(m -> m.get("wgzb_id").toString()).collect(Collectors.toList());
            Map<String, Object> params = instructionEventpost.getParams();
            params.put("dealIds", dealIds);
            instructionEventpost.setParams(params);
        }
        ExcelUtil<InstructionEventpost> util = new ExcelUtil<InstructionEventpost>(InstructionEventpost.class);
        List<InstructionEventpost> list = new ArrayList<>();
        if (mapList.size() == 0 && instructionEventpost.getDealType() == 1) {
            return util.exportExcel(list, "wgzb");
        }
        list = instructionEventpostService.selectInstructionEventpostList(instructionEventpost);
        return util.exportExcel(list, "wgzb");
    }

    /**
     * 获取网格直报详细信息
     */
    @PreAuthorize("@ss.hasPermi('system:eventpost:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") String id) {
        return success(instructionEventpostService.selectInstructionEventpostById(id));
    }

    /**
     * 修改网格直报
     */
    @PreAuthorize("@ss.hasPermi('system:eventpost:edit')")
    @Log(title = "网格直报", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody InstructionEventpost instructionEventpost) {
        return toAjax(instructionEventpostService.updateInstructionEventpost(instructionEventpost));
    }

    /**
     * 删除网格直报
     */
    @PreAuthorize("@ss.hasPermi('system:eventpost:remove')")
    @Log(title = "网格直报", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable String[] ids) {
        return toAjax(instructionEventpostService.deleteInstructionEventpostByIds(ids));
    }

    @GetMapping("/updateData")
    public AjaxResult updateData(){
        InstructionEventpost instructionEventpost = new InstructionEventpost();
        List<InstructionEventpost> list = instructionEventpostService.selectInstructionEventpostList(instructionEventpost);
        list.stream().forEach(eventPost -> {
            if (eventPost.getReportFiles()!=null&&!eventPost.getReportFiles().equals("")){
                String[] split = eventPost.getReportFiles().split(",");
                List<String> ids = new ArrayList<>();
                for (String s:split){
                    InstructionFile file = new InstructionFile();
                    file.setFileUrl(s);
                    file.setFileName("网格直报"+ UUID.randomUUID());
                    fileService.insertInstructionFile(file);
                    ids.add(file.getId()+"");
                }
                String collect = ids.stream().collect(Collectors.joining(","));
                eventPost.setFileIds(collect);
            }
            Date reportDate = new Date();
            reportDate.setTime(eventPost.getReportDate());
            eventPost.setReportDateTime(reportDate);
            Date occurDate = new Date();
            occurDate.setTime(eventPost.getOccurDate());
            eventPost.setOccurDateTime(occurDate);
            //网格上报县市区判断
            String reportAdCode = eventPost.getReportAdCode();
            AdCode adCode = new AdCode();
            if (reportAdCode.length()>=9){
                String substring = reportAdCode.substring(0, 9);
                //查询对应乡镇街道
                adCode.setTownCode(substring);
                List<AdCode> adCodes = adCodeService.selectAdCodeList(adCode);
                AdCode adCode1 = adCodes.get(0);
                if (adCode1.getCounty().contains("开发区")){
                    eventPost.setCounty("开发区");
                }else {
                    eventPost.setCounty(adCode1.getCounty());
                }

                String town = adCode1.getTown();
                String replace = town.replace(adCode1.getCounty(), "").replace("人民政府","").replace("办事处","");

                eventPost.setTown(replace);
            }else {
                //查询对应县市区
                String substring = reportAdCode.substring(0, 6);
                adCode.setCountyCode(substring);
                List<AdCode> adCodes = adCodeService.selectAdCodeList(adCode);
                AdCode adCode1 = adCodes.get(0);
                if (adCode1.getCounty().contains("开发区")){
                    eventPost.setCounty("开发区");
                }else {
                    eventPost.setCounty(adCode1.getCounty());
                }

            }
            instructionEventpostService.updateInstructionEventpost(eventPost);
        });
        return AjaxResult.success();
    }

}
