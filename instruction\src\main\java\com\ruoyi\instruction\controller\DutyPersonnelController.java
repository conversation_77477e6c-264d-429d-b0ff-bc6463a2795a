package com.ruoyi.instruction.controller;

import cn.hutool.poi.excel.ExcelReader;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.config.RuoYiConfig;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.domain.entity.SysDept;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.enums.AreaDept;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.exception.GlobalException;
import com.ruoyi.common.utils.DeptUtils;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.instruction.domain.rspVo.TownRsp;
import com.ruoyi.system.mapper.SysDeptMapper;
import com.ruoyi.instruction.domain.DutyDepartment;
import com.ruoyi.instruction.domain.DutyPersonnel;
import com.ruoyi.instruction.excellistener.NewExcelUtils;
import com.ruoyi.instruction.service.IDutyPersonnelService;
import org.apache.poi.xssf.usermodel.XSSFSheet;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 值班人员Controller
 * 
 * <AUTHOR>
 * @date 2023-08-04
 */
@RestController
@RequestMapping("/xzzfj/dutyPersonnel")
public class DutyPersonnelController extends BaseController
{
    @Autowired
    private IDutyPersonnelService dutyPersonnelService;

    @Resource
    private SysDeptMapper sysDeptMapper;

    /**
     * 查询值班人员列表
     */
    @PreAuthorize("@ss.hasPermi('xzzfj:dutyPersonnel:list')")
    @GetMapping("/list")
    public TableDataInfo list(DutyPersonnel dutyPersonnel)
    {

        Long deptId = SecurityUtils.getDeptId();
        SysDept sysDept = sysDeptMapper.selectDeptById(deptId);
        String dutyPlaceByDept = DeptUtils.getDutyPlaceByDept1(sysDept);
        if (!StringUtils.isEmpty(dutyPlaceByDept)&&!dutyPlaceByDept.equals("金华市")){
            dutyPersonnel.setArea(dutyPlaceByDept);
        }
//        AreaDept byDept = AreaDept.getByDept(sysDept.getDeptName());
//        if (!StringUtils.isEmpty(byDept.getArea())&&!byDept.getArea().equals("金华市")){
//            dutyPersonnel.setArea(byDept.getArea());
//        }
        startPage();
        List<DutyPersonnel> list = dutyPersonnelService.selectDutyPersonnelList(dutyPersonnel);
        return getDataTable(list);
    }

    /**
     * 导出值班人员列表
     */
    @PreAuthorize("@ss.hasPermi('xzzfj:dutyPersonnel:export')")
    @Log(title = "值班人员", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, DutyPersonnel dutyPersonnel)
    {
        List<DutyPersonnel> list = dutyPersonnelService.selectDutyPersonnelList(dutyPersonnel);
        ExcelUtil<DutyPersonnel> util = new ExcelUtil<DutyPersonnel>(DutyPersonnel.class);
        util.exportExcel(response, list, "值班人员数据");
    }

    /**
     * 获取值班人员详细信息
     */
    @PreAuthorize("@ss.hasPermi('xzzfj:dutyPersonnel:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(dutyPersonnelService.selectDutyPersonnelById(id));
    }

    /**
     * 新增值班人员
     */
    @PreAuthorize("@ss.hasPermi('xzzfj:dutyPersonnel:add')")
    @Log(title = "值班人员", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody DutyPersonnel dutyPersonnel)
    {
        Long deptId = SecurityUtils.getDeptId();
        SysDept sysDept = sysDeptMapper.selectDeptById(deptId);
//        AreaDept byDept = AreaDept.getByDept(sysDept.getDeptName());
//        if (!StringUtils.isEmpty(byDept.getArea())&&!byDept.getArea().equals("金华市")){
//            dutyPersonnel.setArea(byDept.getArea());
//        }
        String dutyPlaceByDept = DeptUtils.getDutyPlaceByDept1(sysDept);
        if (!StringUtils.isEmpty(dutyPlaceByDept)&&!dutyPlaceByDept.equals("金华市")){
            dutyPersonnel.setArea(dutyPlaceByDept);
        }
        if (StringUtils.isEmpty(dutyPersonnel.getArea())){
            //获取部门
            DutyDepartment dutyDepartment=new DutyDepartment();
            dutyDepartment.setId(dutyPersonnel.getDeptId());
            List<DutyDepartment> deptList = dutyPersonnelService.getDeptList(dutyDepartment);
            if (!CollectionUtils.isEmpty(deptList)){
                dutyPersonnel.setArea(deptList.get(0).getArea());
            }

        }
        return toAjax(dutyPersonnelService.insertDutyPersonnel(dutyPersonnel));
    }

    /**
     * 修改值班人员
     */
    @PreAuthorize("@ss.hasPermi('xzzfj:dutyPersonnel:edit')")
    @Log(title = "值班人员", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody DutyPersonnel dutyPersonnel)
    {
        Long deptId = SecurityUtils.getDeptId();
        SysDept sysDept = sysDeptMapper.selectDeptById(deptId);
//        AreaDept byDept = AreaDept.getByDept(sysDept.getDeptName());
//        if (!StringUtils.isEmpty(byDept.getArea())&&!byDept.getArea().equals("金华市")){
//            dutyPersonnel.setArea(byDept.getArea());
//        }
        String dutyPlaceByDept = DeptUtils.getDutyPlaceByDept1(sysDept);
        if (!StringUtils.isEmpty(dutyPlaceByDept)&&!dutyPlaceByDept.equals("金华市")){
            dutyPersonnel.setArea(dutyPlaceByDept);
        }
        return toAjax(dutyPersonnelService.updateDutyPersonnel(dutyPersonnel));
    }

    /**
     * 删除值班人员
     */
    @PreAuthorize("@ss.hasPermi('xzzfj:dutyPersonnel:remove')")
    @Log(title = "值班人员", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(dutyPersonnelService.deleteDutyPersonnelByIds(ids));
    }

    /**
     * 获取部门列表
     * @return
     */
    @GetMapping("/getDeptList")
    public  AjaxResult getDeptList(DutyDepartment dutyDepartment){
        Long deptId = SecurityUtils.getDeptId();
        SysDept sysDept = sysDeptMapper.selectDeptById(deptId);
//        AreaDept byDept = AreaDept.getByDept(sysDept.getDeptName());
//        if (!StringUtils.isEmpty(byDept.getArea())&&!byDept.getArea().equals("金华市")){
//            dutyDepartment.setArea(byDept.getArea());
//        }
        String dutyPlaceByDept = DeptUtils.getDutyPlaceByDept1(sysDept);
        if (!StringUtils.isEmpty(dutyPlaceByDept)&&!dutyPlaceByDept.equals("金华市")){
            dutyDepartment.setArea(dutyPlaceByDept);
        }
        return  AjaxResult.success(dutyPersonnelService.getDeptList(dutyDepartment));
    }

    /**
     * 查询值班人员列表不分页
     */
    @PreAuthorize("@ss.hasPermi('xzzfj:dutyPersonnel:list')")
    @GetMapping("/listNotPage")
    public AjaxResult listNotPage(DutyPersonnel dutyPersonnel)
    {
        Long deptId = SecurityUtils.getDeptId();
        SysDept sysDept = sysDeptMapper.selectDeptById(deptId);
//        AreaDept byDept = AreaDept.getByDept(sysDept.getDeptName());
//        if (!StringUtils.isEmpty(byDept.getArea())&&!byDept.getArea().equals("金华市")){
//            dutyPersonnel.setArea(byDept.getArea());
//        }
        String dutyPlaceByDept = DeptUtils.getDutyPlaceByDept1(sysDept);
        if (!StringUtils.isEmpty(dutyPlaceByDept)&&!dutyPlaceByDept.equals("金华市")){
            dutyPersonnel.setArea(dutyPlaceByDept);
        }
        List<DutyPersonnel> list = dutyPersonnelService.selectDutyPersonnelList(dutyPersonnel);
        return AjaxResult.success(list);
    }

    /**
     * 驾驶舱值班列表
     * @param area  区域
     * @param type 查询类型，1市直部门，2县市区指挥中心，3乡镇指挥中心
     * @return
     */
    @GetMapping("/dutyList")
    public AjaxResult dutyList(String area,Integer type){
        List<DutyPersonnel> list =dutyPersonnelService.dutyList(area,type);
        return AjaxResult.success(list);
    }

    /**
     * 获取乡镇街道
     * @param area  区域
     * @return
     */
    @GetMapping("/getTown")
    public AjaxResult getTown(String area){
        List<TownRsp> list =dutyPersonnelService.getTown(area);
        return AjaxResult.success(list);
    }

    /**
     * 导入人员数据
     */
//    @Log(title = "导入事件数据", businessType = BusinessType.IMPORT)
    @PostMapping("/importData")
    public AjaxResult importData(MultipartFile file) throws Exception {
        ExcelUtil<DutyPersonnel> util = new ExcelUtil<DutyPersonnel>(DutyPersonnel.class);
        List<DutyPersonnel> personList = util.importExcel(file.getInputStream(),1);
        if (CollectionUtils.isEmpty(personList)){
            throw  new GlobalException("未读取到可用数据");
        }
        Map<Integer, String> pictures=new HashMap<>(personList.size());
        try {
            //hutool读取excel工具
            ExcelReader reader = cn.hutool.poi.excel.ExcelUtil.getReader(file.getInputStream());
            //将文件转成XSSFWorkbook工作簿
            XSSFWorkbook wb = new XSSFWorkbook(file.getInputStream());
            //获取工作薄中第一个excel表格
            XSSFSheet sheet = wb.getSheetAt(0);
            //核心：：：获取excel表格中所有图片,处理图片上传到oss  key:行号
            pictures = NewExcelUtils.getPicturesString(sheet,RuoYiConfig.getUploadPath());
            System.out.println("ff");

        } catch (IOException e) {
            e.printStackTrace();
        } catch (Exception e) {
            e.printStackTrace();
        }
        if (!CollectionUtils.isEmpty(pictures)){
            pictures.forEach((k, v) -> personList.get(k-2).setUserUrl(v));
        }
        //导入数据
        dutyPersonnelService.importData( personList);
        return AjaxResult.success("msg");
    }
}
