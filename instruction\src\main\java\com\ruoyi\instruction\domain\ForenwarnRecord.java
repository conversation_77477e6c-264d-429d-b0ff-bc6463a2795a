package com.ruoyi.instruction.domain;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 预警提醒记录对象 t_forenwarn_record
 *
 * <AUTHOR>
 * @date 2024-07-31
 */
public class ForenwarnRecord extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键id */
    private Long id;

    /** 人员姓名 */
    @Excel(name = "人员姓名")
    private String personName;

    /** 责任属地 */
    @Excel(name = "责任属地")
    private String dutyPlace;

    /** 乡镇街道 */
    @Excel(name = "乡镇街道")
    private String pTown;

    /** 新管控等级-1：无 0：撤档1：黄 2：橙  3：红  */
    @Excel(name = "新管控等级-1：无 0：撤档1：黄 2：橙  3：红 ")
    private Integer pLevel;

    /** 预警名称 */
    @Excel(name = "预警名称")
    private String forewarnName;

    /** 预警触发时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "预警触发时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date forewarnTime;

    /** 预警规则 */
    @Excel(name = "预警规则")
    private String forewarnRule;

    /** 处置时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "处置时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date disposeTime;

    /** 预警类型 */
    @Excel(name = "预警类型")
    private String forewarnType;

    /** 风险任务 */
    @Excel(name = "风险任务")
    private String riskTask;

    /** 风险标题 */
    @Excel(name = "风险标题")
    private String riskTitle;

    /** 化解时限 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "化解时限", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date resolutionTime;

    /** 状态 1：正常 9：删除 */
    @Excel(name = "状态 1：正常 9：删除")
    private String status;

    /** 人员id */
    @Excel(name = "人员id")
    private Long personId;

    /** 风险详情id */
    @Excel(name = "风险详情id")
    private Long riskDetailId;

    /**
     * 风险id
     */
    private Long riskId;


    public void setId(Long id)
    {
        this.id = id;
    }

    public Long getId()
    {
        return id;
    }
    public void setPersonName(String personName)
    {
        this.personName = personName;
    }

    public String getPersonName()
    {
        return personName;
    }
    public void setDutyPlace(String dutyPlace)
    {
        this.dutyPlace = dutyPlace;
    }

    public String getDutyPlace()
    {
        return dutyPlace;
    }
    public void setpTown(String pTown)
    {
        this.pTown = pTown;
    }

    public String getpTown()
    {
        return pTown;
    }
    public void setpLevel(Integer pLevel)
    {
        this.pLevel = pLevel;
    }

    public Integer getpLevel()
    {
        return pLevel;
    }
    public void setForewarnName(String forewarnName)
    {
        this.forewarnName = forewarnName;
    }

    public String getForewarnName()
    {
        return forewarnName;
    }
    public void setForewarnTime(Date forewarnTime)
    {
        this.forewarnTime = forewarnTime;
    }

    public Date getForewarnTime()
    {
        return forewarnTime;
    }
    public void setForewarnRule(String forewarnRule)
    {
        this.forewarnRule = forewarnRule;
    }

    public String getForewarnRule()
    {
        return forewarnRule;
    }
    public void setDisposeTime(Date disposeTime)
    {
        this.disposeTime = disposeTime;
    }

    public Date getDisposeTime()
    {
        return disposeTime;
    }
    public void setForewarnType(String forewarnType)
    {
        this.forewarnType = forewarnType;
    }

    public String getForewarnType()
    {
        return forewarnType;
    }
    public void setRiskTask(String riskTask)
    {
        this.riskTask = riskTask;
    }

    public String getRiskTask()
    {
        return riskTask;
    }
    public void setRiskTitle(String riskTitle)
    {
        this.riskTitle = riskTitle;
    }

    public String getRiskTitle()
    {
        return riskTitle;
    }
    public void setResolutionTime(Date resolutionTime)
    {
        this.resolutionTime = resolutionTime;
    }

    public Date getResolutionTime()
    {
        return resolutionTime;
    }
    public void setStatus(String status)
    {
        this.status = status;
    }

    public String getStatus()
    {
        return status;
    }
    public void setPersonId(Long personId)
    {
        this.personId = personId;
    }

    public Long getPersonId()
    {
        return personId;
    }
    public void setRiskDetailId(Long riskDetailId)
    {
        this.riskDetailId = riskDetailId;
    }

    public Long getRiskDetailId()
    {
        return riskDetailId;
    }

    public Long getRiskId() {
        return riskId;
    }

    public void setRiskId(final Long riskId) {
        this.riskId = riskId;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("personName", getPersonName())
            .append("dutyPlace", getDutyPlace())
            .append("pTown", getpTown())
            .append("pLevel", getpLevel())
            .append("forewarnName", getForewarnName())
            .append("forewarnTime", getForewarnTime())
            .append("forewarnRule", getForewarnRule())
            .append("disposeTime", getDisposeTime())
            .append("forewarnType", getForewarnType())
            .append("riskTask", getRiskTask())
            .append("riskTitle", getRiskTitle())
            .append("resolutionTime", getResolutionTime())
            .append("status", getStatus())
            .append("personId", getPersonId())
            .append("riskDetailId", getRiskDetailId())
            .toString();
    }
}
