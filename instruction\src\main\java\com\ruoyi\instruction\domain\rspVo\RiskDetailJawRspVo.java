package com.ruoyi.instruction.domain.rspVo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2025/7/23 17:41
 * 风险跟进提醒时效实体类（金安稳）
 */
@Data
@ExcelIgnoreUnannotated
public class RiskDetailJawRspVo {

    /**
     * id
     */

    private Long id;

    /**
     * 是否办结
     */
    private Integer dissolution;

    /**
     * 风险主表id
     */
    private String riskId;

    @ColumnWidth(30)
    @ExcelProperty(value = "标题")
    private String title;

    /**
     * 等级1：重大  2：高  3：中  4：低'
     */
    private Integer level;


    /**
     * 责任单位
     */
    @ColumnWidth(20)
    @ExcelProperty(value = "属地")
    private String dutyUnit;

    /**
     * 责任乡镇
     */
    @ColumnWidth(20)
    @ExcelProperty(value = "责任乡镇")
    private String areaName;

    @ColumnWidth(20)
    @ExcelProperty(value = "管控等级")
    private String levelName;


    /**
     * 跟进要求
     */
    @ColumnWidth(20)
    @ExcelProperty(value = "跟进要求")
    private String followRequirement;

    /**
     * 最后一次处理时间
     */
    @ColumnWidth(30)
    @ExcelProperty(value = "最后一次跟进时间")
    private Date dealTime;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 间隔日期
     */
    private Integer daysInterval;

    /**
     * 跟进状态
     */
    @ColumnWidth(30)
    @ExcelProperty(value = "未跟进超过时间")
    private String followStatus;


}
