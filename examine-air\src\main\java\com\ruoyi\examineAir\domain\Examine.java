package com.ruoyi.examineAir.domain;

import java.math.BigDecimal;
import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 考核晾晒列对象 t_examine
 * 
 * <AUTHOR>
 * @date 2023-04-20
 */
public class Examine extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /**  */
    private Long id;

    /**  */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date cTime;

    /**  */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date uTime;

    /** 标题 */
    @Excel(name = "标题")
    private String title;

    /** 类型，1.除险保安，2.亚运赛道，3.维稳工作 */
    @Excel(name = "类型，1.除险保安，2.亚运赛道，3.维稳工作")
    private Integer type;

    /** 发布时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "发布时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date releaseTime;

    /** 状态，0未发布，1已发布 */
    @Excel(name = "状态，0未发布，1已发布")
    private Long status;

    /** 磐安得分 */
    @Excel(name = "磐安得分")
    private BigDecimal panan;

    /** 武义得分 */
    @Excel(name = "武义得分")
    private BigDecimal wuyi;

    /** 东阳得分 */
    @Excel(name = "东阳得分")
    private BigDecimal dongyang;

    /** 永康得分 */
    @Excel(name = "永康得分")
    private BigDecimal yongkang;

    /** 浦江得分 */
    @Excel(name = "浦江得分")
    private BigDecimal pujiang;

    /** 义乌得分 */
    @Excel(name = "义乌得分")
    private BigDecimal yiwu;

    /** 兰溪得分 */
    @Excel(name = "兰溪得分")
    private BigDecimal lanxi;

    /** 金东得分 */
    @Excel(name = "金东得分")
    private BigDecimal jindong;

    /** 婺城得分 */
    @Excel(name = "婺城得分")
    private BigDecimal wucheng;

    /** 开发区得分 */
    @Excel(name = "开发区得分")
    private BigDecimal kaifa;

    /** 表头内容 */
//    @Excel(name = "表头内容")
    private String headContent;

//    @TableField(exist = false)
    private Date startTime;

//    @TableField(exist = false)
    private Date endTime;
    /**
     * 是否模糊查询
     */
    private Boolean isLike;

    public void setId(Long id) 
    {
        this.id = id;
    }

    public Long getId() 
    {
        return id;
    }
    public void setcTime(Date cTime) 
    {
        this.cTime = cTime;
    }

    public Date getcTime() 
    {
        return cTime;
    }
    public void setuTime(Date uTime) 
    {
        this.uTime = uTime;
    }

    public Date getuTime() 
    {
        return uTime;
    }
    public void setTitle(String title) 
    {
        this.title = title;
    }

    public String getTitle() 
    {
        return title;
    }
    public void setType(Integer type) 
    {
        this.type = type;
    }

    public Integer getType() 
    {
        return type;
    }
    public void setReleaseTime(Date releaseTime) 
    {
        this.releaseTime = releaseTime;
    }

    public Date getReleaseTime() 
    {
        return releaseTime;
    }
    public void setStatus(Long status) 
    {
        this.status = status;
    }

    public Long getStatus() 
    {
        return status;
    }
    public void setPanan(BigDecimal panan) 
    {
        this.panan = panan;
    }

    public BigDecimal getPanan() 
    {
        return panan;
    }
    public void setWuyi(BigDecimal wuyi) 
    {
        this.wuyi = wuyi;
    }

    public BigDecimal getWuyi() 
    {
        return wuyi;
    }
    public void setDongyang(BigDecimal dongyang) 
    {
        this.dongyang = dongyang;
    }

    public BigDecimal getDongyang() 
    {
        return dongyang;
    }
    public void setYongkang(BigDecimal yongkang) 
    {
        this.yongkang = yongkang;
    }

    public BigDecimal getYongkang() 
    {
        return yongkang;
    }
    public void setPujiang(BigDecimal pujiang) 
    {
        this.pujiang = pujiang;
    }

    public BigDecimal getPujiang() 
    {
        return pujiang;
    }
    public void setYiwu(BigDecimal yiwu) 
    {
        this.yiwu = yiwu;
    }

    public BigDecimal getYiwu() 
    {
        return yiwu;
    }
    public void setLanxi(BigDecimal lanxi) 
    {
        this.lanxi = lanxi;
    }

    public BigDecimal getLanxi() 
    {
        return lanxi;
    }
    public void setJindong(BigDecimal jindong) 
    {
        this.jindong = jindong;
    }

    public BigDecimal getJindong() 
    {
        return jindong;
    }
    public void setWucheng(BigDecimal wucheng) 
    {
        this.wucheng = wucheng;
    }

    public BigDecimal getWucheng() 
    {
        return wucheng;
    }
    public void setKaifa(BigDecimal kaifa) 
    {
        this.kaifa = kaifa;
    }

    public BigDecimal getKaifa() 
    {
        return kaifa;
    }
    public void setHeadContent(String headContent) 
    {
        this.headContent = headContent;
    }

    public String getHeadContent() 
    {
        return headContent;
    }

    public Date getStartTime() {
        return startTime;
    }

    public void setStartTime(Date startTime) {
        this.startTime = startTime;
    }

    public Date getEndTime() {
        return endTime;
    }

    public void setEndTime(Date endTime) {
        this.endTime = endTime;
    }

    public Boolean getLike() {
        return isLike;
    }

    public void setLike(Boolean like) {
        isLike = like;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("cTime", getcTime())
            .append("uTime", getuTime())
            .append("title", getTitle())
            .append("type", getType())
            .append("releaseTime", getReleaseTime())
            .append("status", getStatus())
            .append("panan", getPanan())
            .append("wuyi", getWuyi())
            .append("dongyang", getDongyang())
            .append("yongkang", getYongkang())
            .append("pujiang", getPujiang())
            .append("yiwu", getYiwu())
            .append("lanxi", getLanxi())
            .append("jindong", getJindong())
            .append("wucheng", getWucheng())
            .append("kaifa", getKaifa())
            .append("headContent", getHeadContent())
            .toString();
    }
}
