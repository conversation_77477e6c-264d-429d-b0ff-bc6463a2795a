package com.ruoyi.instruction.domain;

import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 应急小组名称对象 t_yjxz_info
 * 
 * <AUTHOR>
 * @date 2023-08-06
 */
@Data
public class YjxzInfo extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键 */
    private Long id;

    /** 状态 1：正常  9：删除 */
    @Excel(name = "状态 1：正常  9：删除")
    private String status;

    /** 职位工作部门 */
    @Excel(name = "职位工作部门")
    private String title;

    /** 人员名称 */
    @Excel(name = "人员名称")
    private String personName;

    /** 所属县市区 */
    @Excel(name = "所属县市区")
    private String dutyPlace;

    /** 排序 */
    @Excel(name = "排序")
    private Long orderNumber;

    /**
     * 人员姓名组织
     */
    private String[] persons;
}
