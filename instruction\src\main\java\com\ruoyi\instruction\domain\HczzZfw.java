package com.ruoyi.instruction.domain;

import java.util.List;

import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 情指推送事件对象 hczz_zfw
 *
 * <AUTHOR>
 * @date 2023-06-07
 */
@Data
public class HczzZfw extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    private Long  id;

    /** 情指数据Id */
    private String qzid;

    /** 事件名称 */
    @Excel(name = "事件名称")
    private String sjmc;

    /** 推送时间 */
    @Excel(name = "推送时间", width = 30, dateFormat = "yyyy-MM-dd")
    private String tssj;

    /** 推送类型 */
    @Excel(name = "推送类型")
    private String tslx;

    /** 责任单位 */
    @Excel(name = "责任单位")
    private String zrdw;

    /** 基本情况 */
    @Excel(name = "基本情况")
    private String jbqk;

    /** 人员信息 */
    @Excel(name = "人员信息")
    private String ryxx;

    /**
     * 处理状态 1:已处理 2:未处理
     */
    private Integer dealType;
    /**
     * 1:交办 2：放置
     */
    private Integer disposeType;
    /**
     * 已放置或已交办预警ids
     */
    private List<String> disposeIds;

    /** 部门ID */
    @Excel(name = "部门ID")
    private String bmid;

    private List<String> qzids;

    /**
     * 推送单位
     */
    private String fqdw;

    /**
     * 接收单位
     */
    private String jsdw;


}
