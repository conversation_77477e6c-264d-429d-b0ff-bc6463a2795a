package com.ruoyi.instruction.domain.reqVo;

import com.ruoyi.instruction.domain.*;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/1/6 10:25
 */
@Data
public class InstructionFlowTestVo {

    /**
     * 指令id
     */
    private Long infoId;

    /**
     * 处置部门名称
     */
    private String dealDeptName;

    /**
     * 交办栏信息
     */
    private InstructionAssign assign;

    /**
     * 接收实体类集合
     */
    private List<InstructionReceive> receiveList;


    /**
     * 处置信息实体类集合
     */
    private List<InstructionDispose> disposeList;


    /**
     * 反馈信息实体类集合
     */
    private List<InstructionFeedback> feedbackList;

    /**
     * 销号实体类集合
     */
    private List<InstructionEnd> instructionEnd;

    /**
     * 县市区反馈集合
     */
    private List<InstructionCountyFeedback> countyFeedbackList;

    /**
     * 销号状态 1：销号 2：未达到销号
     */
    private Integer endStatus;

    /**
     * 县市区是否可以反馈 1：可以反馈 2：不可反馈
     */
    private Integer countyFeedbackStatus;

    /**
     * 指令类型 1:市级指令  2:县区指令
     */
    private Integer type;

}
