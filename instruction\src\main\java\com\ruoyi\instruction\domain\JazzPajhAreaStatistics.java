package com.ruoyi.instruction.domain;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

import com.alibaba.excel.annotation.ExcelProperty;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 平安金华区域统计对象 t_jazz_pajh_area_statistics
 * 
 * <AUTHOR>
 * @date 2023-05-16
 */
public class JazzPajhAreaStatistics extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** $column.columnComment */
    private Long id;

    /** $column.columnComment */
//    @Excel(name = "${comment}", readConverterExp = "$column.readConverterExp()")
    private Date cTime;

    /** $column.columnComment */
//    @Excel(name = "${comment}", readConverterExp = "$column.readConverterExp()")
    private Date uTime;

    /** 年度 */
    @Excel(name = "年度")
    @ExcelProperty(value = "年度")
    private String year;

    /** 月份 */
    @Excel(name = "月份")
    @ExcelProperty(value = "月份")
    private String month;

    /** 地区（部门） */
    @Excel(name = "地区")
    @ExcelProperty(value = "县市区",index = 2)
    private String area;

    /** 得分 */
    @Excel(name = "得分")
    @ExcelProperty(value = "得分")
    private BigDecimal score;

    /** 排名 */
    @Excel(name = "排名")
    private Integer ranking;

    /** 地区（部门）id */
//    @Excel(name = "地区", readConverterExp = "部=门")
    private Long areaId;

    /** 类型，1地区，2部门,3地级市，4乡镇街道 */
//    @Excel(name = "类型，1地区，2部门,3地级市，4乡镇街道")
    private Long type;

    /** 乡镇街道 */
    @Excel(name = "乡镇街道")
    @ExcelProperty(value = "乡镇街道")
    private String town;

    /** 乡镇街道id */
//    @Excel(name = "乡镇街道id")
    private Long townId;

    /** 父id,用于乡镇(目前没有用) */
//    @Excel(name = "父id,用于乡镇")
    private Long parentId;

    @Excel(name = "省排名")
    @ExcelProperty(value = "省排名")
    private Integer provinceRanking;

    @ExcelProperty(value = "地级市")
    private String city;

    private List<JazzPajhAreaStatistics> list;

    public void setId(Long id) 
    {
        this.id = id;
    }

    public Long getId() 
    {
        return id;
    }
    public void setcTime(Date cTime) 
    {
        this.cTime = cTime;
    }

    public Date getcTime() 
    {
        return cTime;
    }
    public void setuTime(Date uTime) 
    {
        this.uTime = uTime;
    }

    public Date getuTime() 
    {
        return uTime;
    }
    public void setYear(String year) 
    {
        this.year = year;
    }

    public String getYear() 
    {
        return year;
    }
    public void setMonth(String month) 
    {
        this.month = month;
    }

    public String getMonth() 
    {
        return   StringUtils.isBlank(month)?null:Integer.parseInt(month)+"";
    }
    public void setArea(String area) 
    {
        this.area = area;
    }

    public String getArea() 
    {
        return area;
    }
    public void setScore(BigDecimal score) 
    {
        this.score = score;
    }

    public BigDecimal getScore() 
    {
        return score;
    }
    public void setRanking(Integer ranking)
    {
        this.ranking = ranking;
    }

    public Integer getRanking()
    {
        return ranking;
    }
    public void setAreaId(Long areaId) 
    {
        this.areaId = areaId;
    }

    public Long getAreaId() 
    {
        return areaId;
    }
    public void setType(Long type) 
    {
        this.type = type;
    }

    public Long getType() 
    {
        return type;
    }
    public void setTown(String town) 
    {
        this.town = town;
    }

    public String getTown() 
    {
        return town;
    }
    public void setTownId(Long townId) 
    {
        this.townId = townId;
    }

    public Long getTownId() 
    {
        return townId;
    }
    public void setParentId(Long parentId) 
    {
        this.parentId = parentId;
    }

    public Long getParentId() 
    {
        return parentId;
    }

    public List<JazzPajhAreaStatistics> getList() {
        return list;
    }

    public void setList(List<JazzPajhAreaStatistics> list) {
        this.list = list;
    }

    public Integer getProvinceRanking() {
        return provinceRanking;
    }

    public void setProvinceRanking(Integer provinceRanking) {
        this.provinceRanking = provinceRanking;
    }

    public String getCity() {
        return city;
    }

    public void setCity(String city) {
        this.city = city;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("cTime", getcTime())
            .append("uTime", getuTime())
            .append("year", getYear())
            .append("month", getMonth())
            .append("area", getArea())
            .append("score", getScore())
            .append("ranking", getRanking())
            .append("areaId", getAreaId())
            .append("type", getType())
            .append("town", getTown())
            .append("townId", getTownId())
            .append("parentId", getParentId())
            .toString();
    }
}
