package com.ruoyi.instruction.domain.rspVo;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class BigSceenCategoryRspVo {

    /**
     * id
     */
    private Integer id;

    /**
     * 分类名称
     */
    private String label;

    /**
     * 子级别分类
     */
    private List<BigSceenCategoryDetailRspVo> list;

    public BigSceenCategoryRspVo(Integer id, String label) {
        this.id = id;
        this.label = label;
    }
}
