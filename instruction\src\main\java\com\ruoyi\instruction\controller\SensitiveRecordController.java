package com.ruoyi.instruction.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.instruction.domain.SensitiveRecord;
import com.ruoyi.instruction.service.ISensitiveRecordService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 敏感信息Controller
 * 
 * <AUTHOR>
 * @date 2024-07-10
 */
@RestController
@RequestMapping("/sentitve/record")
public class SensitiveRecordController extends BaseController
{
    @Autowired
    private ISensitiveRecordService sensitiveRecordService;

    /**
     * 查询敏感信息列表
     */
    @PreAuthorize("@ss.hasPermi('sentitve:record:list')")
    @GetMapping("/list")
    public TableDataInfo list(SensitiveRecord sensitiveRecord)
    {
        startPage();
        List<SensitiveRecord> list = sensitiveRecordService.selectSensitiveRecordList(sensitiveRecord);
        return getDataTable(list);
    }

    /**
     * 导出敏感信息列表
     */
    @PreAuthorize("@ss.hasPermi('sentitve:record:export')")
    @Log(title = "敏感信息", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, SensitiveRecord sensitiveRecord)
    {
        List<SensitiveRecord> list = sensitiveRecordService.selectSensitiveRecordList(sensitiveRecord);
        ExcelUtil<SensitiveRecord> util = new ExcelUtil<SensitiveRecord>(SensitiveRecord.class);
        util.exportExcel(response, list, "敏感信息数据");
    }

    /**
     * 获取敏感信息详细信息
     */
    @PreAuthorize("@ss.hasPermi('sentitve:record:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(sensitiveRecordService.selectSensitiveRecordById(id));
    }

    /**
     * 新增敏感信息
     */
    @PreAuthorize("@ss.hasPermi('sentitve:record:add')")
    @Log(title = "敏感信息", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody SensitiveRecord sensitiveRecord)
    {
        return toAjax(sensitiveRecordService.insertSensitiveRecord(sensitiveRecord));
    }

    /**
     * 修改敏感信息
     */
    @PreAuthorize("@ss.hasPermi('sentitve:record:edit')")
    @Log(title = "敏感信息", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody SensitiveRecord sensitiveRecord)
    {
        return toAjax(sensitiveRecordService.updateSensitiveRecord(sensitiveRecord));
    }

    /**
     * 删除敏感信息
     */
    @PreAuthorize("@ss.hasPermi('sentitve:record:remove')")
    @Log(title = "敏感信息", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(sensitiveRecordService.deleteSensitiveRecordByIds(ids));
    }
}
