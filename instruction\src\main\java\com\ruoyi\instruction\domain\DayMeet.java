package com.ruoyi.instruction.domain;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 日会商对象 t_day_meet
 * 
 * <AUTHOR>
 * @date 2023-09-02
 */
public class DayMeet extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键 */
    private Long id;

    /** 涉稳动态 */
    @Excel(name = "涉稳动态")
    private String swdt;

    /** 预警信息 */
    @Excel(name = "预警信息")
    private String yjxx;

    /** 工作反馈 */
    @Excel(name = "工作反馈")
    private String gzfk;

    /** 内容 */
    @Excel(name = "内容")
    private String wlyq;

    /** 工作动态 */
    @Excel(name = "工作动态")
    private String gzdt;

    /** 推送时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "推送时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date pushTime;

    /** 状态 1：正常 9：异常 */
    private String status;

    /** 县市区 */
    @Excel(name = "县市区")
    private String county;

    public void setId(Long id) 
    {
        this.id = id;
    }

    public Long getId() 
    {
        return id;
    }
    public void setSwdt(String swdt) 
    {
        this.swdt = swdt;
    }

    public String getSwdt() 
    {
        return swdt;
    }
    public void setYjxx(String yjxx) 
    {
        this.yjxx = yjxx;
    }

    public String getYjxx() 
    {
        return yjxx;
    }
    public void setGzfk(String gzfk) 
    {
        this.gzfk = gzfk;
    }

    public String getGzfk() 
    {
        return gzfk;
    }
    public void setWlyq(String wlyq) 
    {
        this.wlyq = wlyq;
    }

    public String getWlyq() 
    {
        return wlyq;
    }
    public void setGzdt(String gzdt) 
    {
        this.gzdt = gzdt;
    }

    public String getGzdt() 
    {
        return gzdt;
    }
    public void setPushTime(Date pushTime) 
    {
        this.pushTime = pushTime;
    }

    public Date getPushTime() 
    {
        return pushTime;
    }
    public void setStatus(String status) 
    {
        this.status = status;
    }

    public String getStatus() 
    {
        return status;
    }
    public void setCounty(String county) 
    {
        this.county = county;
    }

    public String getCounty() 
    {
        return county;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("swdt", getSwdt())
            .append("yjxx", getYjxx())
            .append("gzfk", getGzfk())
            .append("wlyq", getWlyq())
            .append("gzdt", getGzdt())
            .append("createTime", getCreateTime())
            .append("createBy", getCreateBy())
            .append("pushTime", getPushTime())
            .append("status", getStatus())
            .append("county", getCounty())
            .toString();
    }
}
