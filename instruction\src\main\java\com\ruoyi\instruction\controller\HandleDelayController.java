package com.ruoyi.instruction.controller;

import java.util.Date;
import java.util.List;
import javax.servlet.http.HttpServletResponse;

import com.ruoyi.common.constant.InstructionRolesConstants;
import com.ruoyi.common.core.domain.entity.SysDept;
import com.ruoyi.common.core.domain.entity.SysUser;
import com.ruoyi.common.helper.DingtalkHelper;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.instruction.domain.InstructionInfo;
import com.ruoyi.instruction.mapper.InstructionInfoMapper;
import com.ruoyi.system.mapper.SysDeptMapper;
import com.ruoyi.system.mapper.SysUserMapper;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.instruction.domain.HandleDelay;
import com.ruoyi.instruction.service.IHandleDelayService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 办理期限延期表Controller
 *
 * <AUTHOR>
 * @date 2025-01-02
 */
@RestController
@RequestMapping("/handel/delay")
public class HandleDelayController extends BaseController
{
    @Autowired
    private IHandleDelayService handleDelayService;

    @Autowired
    private InstructionInfoMapper infoMapper;

    @Autowired
    private SysUserMapper userMapper;

    @Autowired
    private DingtalkHelper dingtalkHelper;

    @Autowired
    private SysDeptMapper deptMapper;

    /**
     * 查询办理期限延期表列表
     */
    @GetMapping("/list")
    public TableDataInfo list(HandleDelay handleDelay)
    {
        startPage();
        List<HandleDelay> list = handleDelayService.selectHandleDelayList(handleDelay);
        return getDataTable(list);
    }

    /**
     * 导出办理期限延期表列表
     */
    @Log(title = "办理期限延期表", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, HandleDelay handleDelay)
    {
        List<HandleDelay> list = handleDelayService.selectHandleDelayList(handleDelay);
        ExcelUtil<HandleDelay> util = new ExcelUtil<HandleDelay>(HandleDelay.class);
        util.exportExcel(response, list, "办理期限延期表数据");
    }

    /**
     * 获取办理期限延期表详细信息
     */
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(handleDelayService.selectHandleDelayById(id));
    }

    /**
     * 新增办理期限延期表
     */
    @Log(title = "办理期限延期表", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody HandleDelay handleDelay)
    {
        //判断是否已有两次延期，累计可以申请2次延期申请
        HandleDelay delay = new HandleDelay();
        delay.setInstructionId(handleDelay.getInstructionId());
        List<HandleDelay> handleDelays = handleDelayService.selectHandleDelayList(delay);
        //判断是否存在还未审核的记录
        if (handleDelays.stream().filter(delay1 -> delay1.getApplyResult() == 0).count() > 0) {
            return AjaxResult.warn("存在未审核的延期申请，无法再次申请");
        }
        if (handleDelays.stream().filter(delay1 -> delay1.getApplyResult() == 1).count() >= 2) {
            return AjaxResult.warn("已申请两次延期，无法再次申请");
        }
        SysUser user = SecurityUtils.getLoginUser().getUser();
        handleDelay.setApplyPerson(user.getUserId());
        handleDelay.setApplyTime(new Date());
        handleDelay.setApplyDuration("3个月");
        handleDelay.setApplyDept(user.getDeptId());
        InstructionInfo info = infoMapper.selectInstructionInfoById(handleDelay.getInstructionId());
        List<String> phones = userMapper.selectPhoneByDeptIdAndRoleId(info.getInstructionCreateDeptId(), InstructionRolesConstants.ASSIGN_ROLE_ID);
        //获取当前部门
        String deptName = user.getDept().getDeptName();
        SysDept dept = deptMapper.selectDeptById(user.getDept().getParentId());

        String msg = dept.getDeptName()+deptName+"就指令标题:"+info.getInstructionTitle()+"指令进行了延期申请，请及时登录系统处置";
        if (phones.size() != 0) {
            try {
                dingtalkHelper.sendWorkNotificationMsg(phones, msg);
            } catch (Exception e) {
                System.out.println(e.getMessage());
            }
        }
        return toAjax(handleDelayService.insertHandleDelay(handleDelay));
    }

    /**
     * 修改办理期限延期表
     */
    @Log(title = "办理期限延期表", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody HandleDelay handleDelay)
    {
        SysUser user = SecurityUtils.getLoginUser().getUser();
        handleDelay.setAuditPerson(user.getNickName());
        handleDelay.setAuditTime(new Date());
        int row = handleDelayService.updateHandleDelay(handleDelay);
        if (row > 0 && handleDelay.getApplyResult() == 1) {
            //如果审核通过后则将指令的办理时间延长3个月
            InstructionInfo info = infoMapper.selectInstructionInfoById(handleDelay.getInstructionId());
            Date handleTime = info.getHandleTime();
            //将handleTime延长3个月
            Date date = DateUtils.addMonths(handleTime, 3);
            info.setHandleTime(date);
            infoMapper.updateInstructionInfo(info);
        }
        return toAjax(row);
    }

    /**
     * 删除办理期限延期表
     */
    @Log(title = "办理期限延期表", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(handleDelayService.deleteHandleDelayByIds(ids));
    }
}
