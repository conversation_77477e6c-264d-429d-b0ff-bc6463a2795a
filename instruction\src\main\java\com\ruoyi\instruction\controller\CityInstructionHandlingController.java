package com.ruoyi.instruction.controller;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.constant.Constants;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.instruction.domain.InstructionInfo;
import com.ruoyi.instruction.domain.rspVo.CityHandlingOverviewRsp;
import com.ruoyi.instruction.domain.rspVo.CountyInstructionHandlingRsp;
import com.ruoyi.instruction.service.ICityInstructionHandlingService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.net.URLEncoder;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 市级指令办理情况控制器
 *
 * <AUTHOR>
 * @date 2025-01-20
 */
@RestController
@RequestMapping("/instruction/cityHandling")
public class CityInstructionHandlingController extends BaseController {

    @Autowired
    private ICityInstructionHandlingService cityInstructionHandlingService;

    /**
     * 获取市级指令办理情况统计数据
     * 默认查询金华市全部数据
     *
     * @param startTime 开始时间（可选，默认全量）
     * @param endTime 结束时间（可选，默认全量）
     * @return 所有统计数据
     */
    @GetMapping("/data")
    public AjaxResult getHandlingData(
            @RequestParam(required = false) String startTime,
            @RequestParam(required = false) String endTime) {

        // 默认查询金华市全部数据
        Long deptId = Constants.JINHUA_CITY_DEPT_ID;
        String deptName = "金华市";

        // 获取概览数据
        CityHandlingOverviewRsp overview = cityInstructionHandlingService.getHandlingOverview(
                startTime, endTime, deptId, deptName);

        // 构建返回数据，移除时间和区域信息
        Map<String, Object> result = new HashMap<>();

        //1、超时接收和超时处置
        result.put("timeoutReceiveCount", overview.getTimeoutReceiveCount());
        result.put("timeoutDisposeCount", overview.getTimeoutDisposeCount());

        //2、应处置未处置统计
        Map<String, Object> noDisposeStats = cityInstructionHandlingService.getCountyDisposeAndReceive(
                true, null, startTime, endTime);
        result.put("feedbackDeptCount", noDisposeStats.get("feedbackDeptCount"));
        result.put("feedbackDeptInstructionCount", noDisposeStats.get("feedbackDeptInstructionCount"));
        result.put("feedbackTownCount", noDisposeStats.get("feedbackTownCount"));
        result.put("feedbackTownInstructionCount", noDisposeStats.get("feedbackTownInstructionCount"));

        //3、反馈
        result.put("pendingFeedback7Days", overview.getPendingFeedback7Days());
        result.put("pendingFeedback15Days", overview.getPendingFeedback15Days());
        result.put("pendingFeedback30Days", overview.getPendingFeedback30Days());

        return AjaxResult.success(result);
    }

    /**
     * 获取市级指令办理情况详细列表
     * 默认查询金华市全部数据
     * 返回所有统计指标对应的具体数据列表
     *
     * @param startTime 开始时间（可选，默认全量）
     * @param endTime 结束时间（可选，默认全量）
     * @return 所有指标的详细数据集合
     */
    @GetMapping("/list")
    public AjaxResult getHandlingList(
            @RequestParam(required = false) String startTime,
            @RequestParam(required = false) String endTime) {

        // 默认查询金华市全部数据
        Long deptId = Constants.JINHUA_CITY_DEPT_ID;
        String deptName = "金华市";

        // 构建返回数据，包含所有指标的详细列表
        Map<String, Object> result = new HashMap<>();

        // 1. 超时接收列表
        TableDataInfo tableDataInfo = cityInstructionHandlingService.getTimeoutReceiveListV2(startTime, endTime, 999);
        List<InstructionInfo> rows = (List<InstructionInfo>) tableDataInfo.getRows();

        List<Long> timeoutReceiveIdList = rows.stream()
                .map(InstructionInfo::getId)
                .collect(Collectors.toList());
        result.put("timeoutReceiveList", timeoutReceiveIdList);

        // 2. 超时处置列表
        tableDataInfo = cityInstructionHandlingService.getTimeoutDisposeListV2(startTime, endTime, 999);
        rows = (List<InstructionInfo>) tableDataInfo.getRows();
        List<Long> timeoutDisposeIdList = rows.stream()
                .map(InstructionInfo::getId)
                .collect(Collectors.toList());
        result.put("timeoutDisposeList", timeoutDisposeIdList);

//        // 3. 应处置未处置列表
//        List<Map<String, Object>> unprocessedList = cityInstructionHandlingService.getUnprocessedList(
//                startTime, endTime, deptId, deptName);
//        result.put("unprocessedList", unprocessedList);

        // 4. 待反馈列表（按紧急程度分类）
        // 4.1 特急（7天）
        List<Map<String, Object>> pendingFeedback7DaysList = cityInstructionHandlingService.getPendingFeedbackList(
                "7", startTime, endTime, deptId, deptName);
        List<Long> pendingFeedback7DaysIdList = pendingFeedback7DaysList.stream()
                .map(item -> (Long) item.get("id"))
                .collect(Collectors.toList());
        result.put("pendingFeedback7DaysList", pendingFeedback7DaysIdList);

        // 4.2 紧急（15天）
        List<Map<String, Object>> pendingFeedback15DaysList = cityInstructionHandlingService.getPendingFeedbackList(
                "15", startTime, endTime, deptId, deptName);
        List<Long> pendingFeedback15DaysIdList = pendingFeedback15DaysList.stream()
                .map(item -> (Long) item.get("id"))
                .collect(Collectors.toList());
        result.put("pendingFeedback15DaysList", pendingFeedback15DaysIdList);

        // 4.3 一般（30天）
        List<Map<String, Object>> pendingFeedback30DaysList = cityInstructionHandlingService.getPendingFeedbackList(
                "30", startTime, endTime, deptId, deptName);
        List<Long> pendingFeedback30DaysIdList = pendingFeedback30DaysList.stream()
                .map(item -> (Long) item.get("id"))
                .collect(Collectors.toList());
        result.put("pendingFeedback30DaysList", pendingFeedback30DaysIdList);

        return AjaxResult.success(result);
    }

    /**
     * 导出市级指令办理情况
     * 默认导出金华市全部数据
     *
     * @param response HTTP响应
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @throws IOException IO异常
     */
    @Log(title = "市级指令办理情况导出", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void exportHandlingData(HttpServletResponse response,
            @RequestParam(required = false) String startTime,
            @RequestParam(required = false) String endTime) throws IOException {

        cityInstructionHandlingService.exportHandlingData(response, startTime, endTime);
    }

    // ==================== 县市层级指令办理情况 ====================

    /**
     * 获取县市层级指令办理情况统计数据
     *
     * @param deptId 县市区名称（可选，默认查询所有县市区）
     * @param startTime 开始时间（按交办时间筛选）
     * @param endTime 结束时间（按交办时间筛选）
     * @return 统计数据
     */
    @GetMapping("/county/data")
    public AjaxResult getCountyHandlingData(
            @RequestParam(required = false) Long deptId,
            @RequestParam(required = false) String startTime,
            @RequestParam(required = false) String endTime) {

        Map<String, Object> result = new HashMap<>();

        // 应接收未接收统计
        Map<String, Object> noReceiveStats = cityInstructionHandlingService.getCountyDisposeAndReceive(
                false, deptId, startTime, endTime);
        result.put("instructionDeptCount", noReceiveStats.get("instructionDeptCount"));
        result.put("deptCount", noReceiveStats.get("deptCount"));
        result.put("instructionTownCount", noReceiveStats.get("instructionTownCount"));
        result.put("townCount", noReceiveStats.get("townCount"));

        // 应处置未处置统计
//        Map<String, Object> noDisposeStats = cityInstructionHandlingService.getCountyDisposeAndReceive(
//                deptId, startTime, endTime);
        result.put("feedbackDeptCount", noReceiveStats.get("feedbackDeptCount"));
        result.put("feedbackDeptInstructionCount", noReceiveStats.get("feedbackDeptInstructionCount"));
        result.put("feedbackTownCount", noReceiveStats.get("feedbackTownCount"));
        result.put("feedbackTownInstructionCount", noReceiveStats.get("feedbackTownInstructionCount"));

        return AjaxResult.success(result);
    }

    /**
     * 导出县市层级指令办理情况
     *
     * @param response HTTP响应
     * @param deptId 县市区id
     * @param startTime 开始时间（按交办时间筛选）
     * @param endTime 结束时间（按交办时间筛选）
     * @throws IOException IO异常
     */
    @Log(title = "县市层级指令办理情况", businessType = BusinessType.EXPORT)
    @PostMapping("/county/export")
    public void exportCountyHandling(
            HttpServletResponse response,
            @RequestParam(required = false) Long deptId,
            @RequestParam(required = false) String startTime,
            @RequestParam(required = false) String endTime) throws IOException {

        cityInstructionHandlingService.exportCountyHandling(response, deptId, startTime, endTime);

    }
}
