package com.ruoyi.instruction.mapper;

import java.util.Date;
import java.util.List;
import com.ruoyi.instruction.domain.InstructionDispose;
import org.apache.ibatis.annotations.Param;

/**
 * 指令处置Mapper接口
 * 
 * <AUTHOR>
 * @date 2022-12-19
 */
public interface InstructionDisposeMapper 
{
    /**
     * 查询指令处置
     * 
     * @param id 指令处置主键
     * @return 指令处置
     */
    public InstructionDispose selectInstructionDisposeById(Long id);

    /**
     * 查询指令处置列表
     * 
     * @param instructionDispose 指令处置
     * @return 指令处置集合
     */
    public List<InstructionDispose> selectInstructionDisposeList(InstructionDispose instructionDispose);

    /**
     * 新增指令处置
     * 
     * @param instructionDispose 指令处置
     * @return 结果
     */
    public int insertInstructionDispose(InstructionDispose instructionDispose);

    /**
     * 修改指令处置
     * 
     * @param instructionDispose 指令处置
     * @return 结果
     */
    public int updateInstructionDispose(InstructionDispose instructionDispose);

    /**
     * 删除指令处置
     * 
     * @param id 指令处置主键
     * @return 结果
     */
    public int deleteInstructionDisposeById(Long id);

    /**
     * 批量删除指令处置
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteInstructionDisposeByIds(Long[] ids);

    /**
     * 通过转交id查询处置表
     * @param id
     * @return
     */
    InstructionDispose selectInstructionDisposeByTransferId(Long id);

    /**
     * 通过处置人员部门->转交表->接收表->查出指令ids
     * @param deptName
     * @return
     */
    List<Long> selectInstructionIds(@Param("deptName") String deptName);

    /**
     * 查询指令最后处置时间
     * @param id
     * @return
     */
    Date selectDisposeTimeByInstructionId(Long id);

    /**
     * 根据部门名称、指令id查询处置信息
     * @param deptName
     * @param id
     * @return
     */
    InstructionDispose findDisposeByDeptAndId(@Param("deptName") String deptName,@Param("id") Long id);

    /**
     * 根据指令id查询 指令处置时间
     * @param id
     * @return
     */
    Date findDisposeTime(@Param("id") Long id);

    /**
     * 测试查询处置时间
     * @param id
     * @return
     */
    Date testFindDisposeTime(@Param("id") Long id);

    /**
     * 根据转交记录id删除处置记录
     * @param id
     */
    void deleteByTransferId(@Param("id") Long id);

    /**
     * 根据指令id查询该条指令对应所有处置信息
     * @param infoId
     * @return
     */
    String findDisposeInfosByInstructionId(@Param("instructionId") Long infoId);
}
