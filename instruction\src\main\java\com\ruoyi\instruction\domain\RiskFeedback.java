package com.ruoyi.instruction.domain;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 风险交办-反馈对象 t_risk_feedback
 *
 * <AUTHOR>
 * @date 2025-02-24
 */
@Data
public class RiskFeedback extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键id */
    private Long id;

    /** 风险交办主id */
    @Excel(name = "风险交办主id")
    private Long riskAssignId;

    /** 状态 1：正常 9：删除 */
    @Excel(name = "状态 1：正常 9：删除")
    private String status;

    /** 反馈时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "反馈时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date feedbackTime;

    /** 反馈人 */
    @Excel(name = "反馈人")
    private String feedbackPerson;

    /** 附件ids */
    @Excel(name = "附件ids")
    private String fileIds;

    /** 是否被退回 0：未退回  1：已退回 */
    @Excel(name = "是否被退回 0：未退回  1：已退回")
    private Integer isPass;

    /** 退回原因 */
    @Excel(name = "退回原因")
    private String passReason;

    /** 退回时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "退回时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date passTime;

    /** 反馈部门id */
    @Excel(name = "反馈部门id")
    private Long feedbackDeptId;

    /** 反馈单位 */
    @Excel(name = "反馈单位")
    private String feedbackUnit;

    /**
     * 反馈部门ids
     */
    private String feedbackDeptIds;


    private String passPerson;

}
