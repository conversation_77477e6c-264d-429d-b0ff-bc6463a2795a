package com.ruoyi.instruction.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.instruction.domain.AdCode;
import com.ruoyi.instruction.service.IAdCodeService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 金华市行政区划编码Controller
 * 
 * <AUTHOR>
 * @date 2023-07-29
 */
@RestController
@RequestMapping("/instruction/code")
public class AdCodeController extends BaseController
{
    @Autowired
    private IAdCodeService adCodeService;

    /**
     * 查询金华市行政区划编码列表
     */
    @PreAuthorize("@ss.hasPermi('instruction:code:list')")
    @GetMapping("/list")
    public TableDataInfo list(AdCode adCode)
    {
        startPage();
        List<AdCode> list = adCodeService.selectAdCodeList(adCode);
        return getDataTable(list);
    }

    /**
     * 导出金华市行政区划编码列表
     */
    @PreAuthorize("@ss.hasPermi('instruction:code:export')")
    @Log(title = "金华市行政区划编码", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, AdCode adCode)
    {
        List<AdCode> list = adCodeService.selectAdCodeList(adCode);
        ExcelUtil<AdCode> util = new ExcelUtil<AdCode>(AdCode.class);
        util.exportExcel(response, list, "金华市行政区划编码数据");
    }

    /**
     * 获取金华市行政区划编码详细信息
     */
    @PreAuthorize("@ss.hasPermi('instruction:code:query')")
    @GetMapping(value = "/{city}")
    public AjaxResult getInfo(@PathVariable("city") String city)
    {
        return success(adCodeService.selectAdCodeByCity(city));
    }

    /**
     * 新增金华市行政区划编码
     */
    @PreAuthorize("@ss.hasPermi('instruction:code:add')")
    @Log(title = "金华市行政区划编码", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody AdCode adCode)
    {
        return toAjax(adCodeService.insertAdCode(adCode));
    }

    /**
     * 修改金华市行政区划编码
     */
    @PreAuthorize("@ss.hasPermi('instruction:code:edit')")
    @Log(title = "金华市行政区划编码", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody AdCode adCode)
    {
        return toAjax(adCodeService.updateAdCode(adCode));
    }

    /**
     * 删除金华市行政区划编码
     */
    @PreAuthorize("@ss.hasPermi('instruction:code:remove')")
    @Log(title = "金华市行政区划编码", businessType = BusinessType.DELETE)
	@DeleteMapping("/{citys}")
    public AjaxResult remove(@PathVariable String[] citys)
    {
        return toAjax(adCodeService.deleteAdCodeByCitys(citys));
    }
}
