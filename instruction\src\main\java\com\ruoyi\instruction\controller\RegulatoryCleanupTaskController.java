package com.ruoyi.instruction.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.instruction.domain.RegulatoryCleanupTask;
import com.ruoyi.instruction.service.IRegulatoryCleanupTaskService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 挂牌整治任务清单Controller
 * 
 * <AUTHOR>
 * @date 2023-08-10
 */
@RestController
@RequestMapping("/system/regulatoryCleanupTask")
public class RegulatoryCleanupTaskController extends BaseController
{
    @Autowired
    private IRegulatoryCleanupTaskService regulatoryCleanupTaskService;

    /**
     * 查询挂牌整治任务清单列表
     */
    @GetMapping("/list")
    public TableDataInfo list(RegulatoryCleanupTask regulatoryCleanupTask)
    {
        startPage();
        List<RegulatoryCleanupTask> list = regulatoryCleanupTaskService.selectRegulatoryCleanupTaskList(regulatoryCleanupTask);
        return getDataTable(list);
    }

    /**
     * 导出挂牌整治任务清单列表
     */
    @PreAuthorize("@ss.hasPermi('system:regulatoryCleanupTask:export')")
    @Log(title = "挂牌整治任务清单", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, RegulatoryCleanupTask regulatoryCleanupTask)
    {
        List<RegulatoryCleanupTask> list = regulatoryCleanupTaskService.selectRegulatoryCleanupTaskList(regulatoryCleanupTask);
        ExcelUtil<RegulatoryCleanupTask> util = new ExcelUtil<RegulatoryCleanupTask>(RegulatoryCleanupTask.class);
        util.exportExcel(response, list, "挂牌整治任务清单数据");
    }

    /**
     * 获取挂牌整治任务清单详细信息
     */
    @PreAuthorize("@ss.hasPermi('system:regulatoryCleanupTask:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(regulatoryCleanupTaskService.selectRegulatoryCleanupTaskById(id));
    }

    /**
     * 新增挂牌整治任务清单
     */
    @PreAuthorize("@ss.hasPermi('system:regulatoryCleanupTask:add')")
    @Log(title = "挂牌整治任务清单", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody RegulatoryCleanupTask regulatoryCleanupTask)
    {
        return toAjax(regulatoryCleanupTaskService.insertRegulatoryCleanupTask(regulatoryCleanupTask));
    }

    /**
     * 修改挂牌整治任务清单
     */
    @PreAuthorize("@ss.hasPermi('system:regulatoryCleanupTask:edit')")
    @Log(title = "挂牌整治任务清单", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody RegulatoryCleanupTask regulatoryCleanupTask)
    {
        return toAjax(regulatoryCleanupTaskService.updateRegulatoryCleanupTask(regulatoryCleanupTask));
    }

    /**
     * 删除挂牌整治任务清单
     */
    @PreAuthorize("@ss.hasPermi('system:regulatoryCleanupTask:remove')")
    @Log(title = "挂牌整治任务清单", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(regulatoryCleanupTaskService.deleteRegulatoryCleanupTaskByIds(ids));
    }
}
