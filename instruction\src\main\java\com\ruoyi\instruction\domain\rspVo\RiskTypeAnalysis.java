package com.ruoyi.instruction.domain.rspVo;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.format.NumberFormat;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.annotation.Excels;
import lombok.Data;
import lombok.Getter;

import java.lang.reflect.Field;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;

/**
 * 增加风险等级判断
 */
@Data
public class RiskTypeAnalysis {

    @ExcelProperty({"县市区域"})
    private String countyName;

    @Getter
    @ExcelIgnore
    private static HashMap<Long, List<String>> fieldMap;

    static {
        fieldMap = new HashMap<>();
        fieldMap.put(51L, Arrays.asList("type51Count", "type51SolvedCount", "type51OverTimeCount", "type51DissolutionRate"));
        fieldMap.put(52L, Arrays.asList("type52Count", "type52SolvedCount", "type52OverTimeCount", "type52DissolutionRate"));
        fieldMap.put(53L, Arrays.asList("type53Count", "type53SolvedCount", "type53OverTimeCount", "type53DissolutionRate"));
        fieldMap.put(54L, Arrays.asList("type54Count", "type54SolvedCount", "type54OverTimeCount", "type54DissolutionRate"));
        fieldMap.put(58L, Arrays.asList("type58Count", "type58SolvedCount", "type58OverTimeCount", "type58DissolutionRate"));
        fieldMap.put(59L, Arrays.asList("type59Count", "type59SolvedCount", "type59OverTimeCount", "type59DissolutionRate"));
        fieldMap.put(60L, Arrays.asList("type60Count", "type60SolvedCount", "type60OverTimeCount", "type60DissolutionRate"));
        fieldMap.put(61L, Arrays.asList("type61Count", "type61SolvedCount", "type61OverTimeCount", "type61DissolutionRate"));
        fieldMap.put(62L, Arrays.asList("type62Count", "type62SolvedCount", "type62OverTimeCount", "type62DissolutionRate"));
        fieldMap.put(63L, Arrays.asList("type63Count", "type63SolvedCount", "type63OverTimeCount", "type63DissolutionRate"));
        fieldMap.put(64L, Arrays.asList("type64Count", "type64SolvedCount", "type64OverTimeCount", "type64DissolutionRate"));
        fieldMap.put(65L, Arrays.asList("type65Count", "type65SolvedCount", "type65OverTimeCount", "type65DissolutionRate"));
        fieldMap.put(66L, Arrays.asList("type66Count", "type66SolvedCount", "type66OverTimeCount", "type66DissolutionRate"));
        fieldMap.put(67L, Arrays.asList("type67Count", "type67SolvedCount", "type67OverTimeCount", "type67DissolutionRate"));
        fieldMap.put(68L, Arrays.asList("type68Count", "type68SolvedCount", "type68OverTimeCount", "type68DissolutionRate"));
    }



    @ExcelProperty({"涉众投资", "排查数"})
    private int type51Count;
    @ExcelProperty({"涉众投资", "化解数"})
    private int type51SolvedCount;
    @ExcelProperty({"涉众投资", "化解率"})
    @NumberFormat("0.00%")
    private float type51DissolutionRate;
    @ExcelProperty({"涉众投资", "超期未化解"})
    private int type51OverTimeCount;

    @ExcelProperty({"问题楼盘", "排查数"})
    private int type52Count;
    @ExcelProperty({"问题楼盘", "化解数"})
    private int type52SolvedCount;
    @ExcelProperty({"问题楼盘", "化解率"})
    @NumberFormat("0.00%")
    private float type52DissolutionRate;
    @ExcelProperty({"问题楼盘", "超期未化解"})
    private int type52OverTimeCount;

    @ExcelProperty({"拆迁安置", "排查数"})
    private int type53Count;
    @ExcelProperty({"拆迁安置", "化解数"})
    private int type53SolvedCount;
    @ExcelProperty({"拆迁安置", "化解率"})
    @NumberFormat("0.00%")
    private float type53DissolutionRate;
    @ExcelProperty({"拆迁安置", "超期未化解"})
    private int type53OverTimeCount;

    @ExcelProperty({"政策待遇", "排查数"})
    private int type54Count;
    @ExcelProperty({"政策待遇", "化解数"})
    private int type54SolvedCount;
    @ExcelProperty({"政策待遇", "化解率"})
    @NumberFormat("0.00%")
    private float type54DissolutionRate;
    @ExcelProperty({"政策待遇", "超期未化解"})
    private int type54OverTimeCount;

    @ExcelProperty({"实体商铺", "排查数"})
    private int type58Count;
    @ExcelProperty({"实体商铺", "化解数"})
    private int type58SolvedCount;
    @ExcelProperty({"实体商铺", "化解率"})
    @NumberFormat("0.00%")
    private float type58DissolutionRate;
    @ExcelProperty({"实体商铺", "超期未化解"})
    private int type58OverTimeCount;

    @ExcelProperty({"教育领域", "排查数"})
    private int type59Count;
    @ExcelProperty({"教育领域", "化解数"})
    private int type59SolvedCount;
    @ExcelProperty({"教育领域", "化解率"})
    @NumberFormat("0.00%")
    private float type59DissolutionRate;
    @ExcelProperty({"教育领域", "超期未化解"})
    private int type59OverTimeCount;

    @ExcelProperty({"欠薪群体", "排查数"})
    private int type60Count;
    @ExcelProperty({"欠薪群体", "化解数"})
    private int type60SolvedCount;
    @ExcelProperty({"欠薪群体", "化解率"})
    @NumberFormat("0.00%")
    private float type60DissolutionRate;
    @ExcelProperty({"欠薪群体", "超期未化解"})
    private int type60OverTimeCount;

    @ExcelProperty({"生态环境", "排查数"})
    private int type61Count;
    @ExcelProperty({"生态环境", "化解数"})
    private int type61SolvedCount;
    @ExcelProperty({"生态环境", "化解率"})
    @NumberFormat("0.00%")
    private float type61DissolutionRate;
    @ExcelProperty({"生态环境", "超期未化解"})
    private int type61OverTimeCount;

    @ExcelProperty({"医疗纠纷", "排查数"})
    private int type62Count;
    @ExcelProperty({"医疗纠纷", "化解数"})
    private int type62SolvedCount;
    @ExcelProperty({"医疗纠纷", "化解率"})
    @NumberFormat("0.00%")
    private float type62DissolutionRate;
    @ExcelProperty({"医疗纠纷", "超期未化解"})
    private int type62OverTimeCount;

    @ExcelProperty({"涉法涉诉", "排查数"})
    private int type63Count;
    @ExcelProperty({"涉法涉诉", "化解数"})
    private int type63SolvedCount;
    @ExcelProperty({"涉法涉诉", "化解率"})
    @NumberFormat("0.00%")
    private float type63DissolutionRate;
    @ExcelProperty({"涉法涉诉", "超期未化解"})
    private int type63OverTimeCount;

    @ExcelProperty({"生产安全", "排查数"})
    private int type64Count;
    @ExcelProperty({"生产安全", "化解数"})
    private int type64SolvedCount;
    @ExcelProperty({"生产安全", "化解率"})
    @NumberFormat("0.00%")
    private float type64DissolutionRate;
    @ExcelProperty({"生产安全", "超期未化解"})
    private int type64OverTimeCount;

    @ExcelProperty({"涉政治类", "排查数"})
    private int type65Count;
    @ExcelProperty({"涉政治类", "化解数"})
    private int type65SolvedCount;
    @ExcelProperty({"涉政治类", "化解率"})
    @NumberFormat("0.00%")
    private float type65DissolutionRate;
    @ExcelProperty({"涉政治类", "超期未化解"})
    private int type65OverTimeCount;

    @ExcelProperty({"网络安全", "排查数"})
    private int type66Count;
    @ExcelProperty({"网络安全", "化解数"})
    private int type66SolvedCount;
    @ExcelProperty({"网络安全", "化解率"})
    @NumberFormat("0.00%")
    private float type66DissolutionRate;
    @ExcelProperty({"网络安全", "超期未化解"})
    private int type66OverTimeCount;

    @ExcelProperty({"新业态", "排查数"})
    private int type67Count;
    @ExcelProperty({"新业态", "化解数"})
    private int type67SolvedCount;
    @ExcelProperty({"新业态", "化解率"})
    @NumberFormat("0.00%")
    private float type67DissolutionRate;
    @ExcelProperty({"新业态", "超期未化解"})
    private int type67OverTimeCount;

    @ExcelProperty({"其他", "排查数"})
    private int type68Count;
    @ExcelProperty({"其他", "化解数"})
    private int type68SolvedCount;
    @ExcelProperty({"其他", "化解率"})
    @NumberFormat("0.00%")
    private float type68DissolutionRate;
    @ExcelProperty({"其他", "超期未化解"})
    private int type68OverTimeCount;


    public RiskTypeAnalysis(String countyName) {
        this.countyName = countyName;
    }

    /**
     * 根据属性名获取属性值
     *
     * @param fieldName
     * @return
     */
    public Object getFieldByName(String fieldName) {
        try {
            //根据字段名 fieldName 获取当前对象 (this) 所属类中的指定声明字段 field
            Field field = this.getClass().getDeclaredField(fieldName);
            //设置field为可访问的
            field.setAccessible(true);
            //该Java函数调用了字段field的get方法，传入this作为参数，返回该对象实例的指定字段值
            return field.get(this);
        } catch (NoSuchFieldException | IllegalAccessException e) {
            e.printStackTrace();
            return null;
        }
    }

    /**
     * 根据属性名设置属性值
     *
     * @param fieldName
     * @param newValue
     */

    public void setFieldByName(String fieldName, Object newValue) {
        try {
            Field field = this.getClass().getDeclaredField(fieldName);
            field.setAccessible(true);
            field.set(this, newValue);
        } catch (NoSuchFieldException | IllegalAccessException e) {
            e.printStackTrace();
        }
    }

    public RiskTypeAnalysis() {
    }
}




