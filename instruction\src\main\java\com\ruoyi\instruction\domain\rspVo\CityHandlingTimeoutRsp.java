package com.ruoyi.instruction.domain.rspVo;

import lombok.Data;

/**
 * 市级指令超时数据响应
 *
 * <AUTHOR>
 * @date 2025-01-20
 */
@Data
public class CityHandlingTimeoutRsp {

    /**
     * 超时接收数量
     */
    private Long timeoutReceiveCount;

    /**
     * 超时处置数量
     */
    private Long timeoutDisposeCount;

    /**
     * 当前用户所属区域
     */
    private String currentArea;

    /**
     * 统计时间范围开始
     */
    private String startTime;

    /**
     * 统计时间范围结束
     */
    private String endTime;
}
