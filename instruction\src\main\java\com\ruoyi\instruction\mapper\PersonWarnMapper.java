package com.ruoyi.instruction.mapper;

import java.util.List;
import com.ruoyi.instruction.domain.PersonWarn;

/**
 * 人员预警Mapper接口
 *
 * <AUTHOR>
 * @date 2024-01-29
 */
public interface PersonWarnMapper
{
    /**
     * 查询人员预警
     *
     * @param id 人员预警主键
     * @return 人员预警
     */
    public PersonWarn selectPersonWarnById(Long id);

    /**
     * 查询人员预警列表
     *
     * @param personWarn 人员预警
     * @return 人员预警集合
     */
    public List<PersonWarn> selectPersonWarnList(PersonWarn personWarn);

    /**
     * 新增人员预警
     *
     * @param personWarn 人员预警
     * @return 结果
     */
    public int insertPersonWarn(PersonWarn personWarn);

    /**
     * 修改人员预警
     *
     * @param personWarn 人员预警
     * @return 结果
     */
    public int updatePersonWarn(PersonWarn personWarn);

    /**
     * 删除人员预警
     *
     * @param id 人员预警主键
     * @return 结果
     */
    public int deletePersonWarnById(Long id);

    /**
     * 批量删除人员预警
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deletePersonWarnByIds(Long[] ids);

    /**
     * 批量插入人员预警数据
     * @param personWarnList
     */
    void batchSave(List<PersonWarn> personWarnList);
}
