package com.ruoyi.instruction.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.instruction.domain.QbtbZwwBkxx;
import com.ruoyi.instruction.service.IQbtbZwwBkxxService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 布控Controller
 * 
 * <AUTHOR>
 * @date 2023-06-03
 */
@RestController
@RequestMapping("/instruction/bkxx")
public class QbtbZwwBkxxController extends BaseController
{
    @Autowired
    private IQbtbZwwBkxxService qbtbZwwBkxxService;

    /**
     * 查询布控列表
     */
    @PreAuthorize("@ss.hasPermi('instruction:bkxx:list')")
    @GetMapping("/list")
    public TableDataInfo list(QbtbZwwBkxx qbtbZwwBkxx)
    {
        startPage();
        List<QbtbZwwBkxx> list = qbtbZwwBkxxService.selectQbtbZwwBkxxList(qbtbZwwBkxx);
        return getDataTable(list);
    }

    /**
     * 导出布控列表
     */
    @PreAuthorize("@ss.hasPermi('instruction:bkxx:export')")
    @Log(title = "布控", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, QbtbZwwBkxx qbtbZwwBkxx)
    {
        List<QbtbZwwBkxx> list = qbtbZwwBkxxService.selectQbtbZwwBkxxList(qbtbZwwBkxx);
        ExcelUtil<QbtbZwwBkxx> util = new ExcelUtil<QbtbZwwBkxx>(QbtbZwwBkxx.class);
        util.exportExcel(response, list, "布控数据");
    }

    /**
     * 获取布控详细信息
     */
    @PreAuthorize("@ss.hasPermi('instruction:bkxx:query')")
    @GetMapping(value = "/{nXh}")
    public AjaxResult getInfo(@PathVariable("nXh") Long nXh)
    {
        return success(qbtbZwwBkxxService.selectQbtbZwwBkxxByNXh(nXh));
    }

    /**
     * 新增布控
     */
    @PreAuthorize("@ss.hasPermi('instruction:bkxx:add')")
    @Log(title = "布控", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody QbtbZwwBkxx qbtbZwwBkxx)
    {
        return toAjax(qbtbZwwBkxxService.insertQbtbZwwBkxx(qbtbZwwBkxx));
    }

    /**
     * 修改布控
     */
    @PreAuthorize("@ss.hasPermi('instruction:bkxx:edit')")
    @Log(title = "布控", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody QbtbZwwBkxx qbtbZwwBkxx)
    {
        return toAjax(qbtbZwwBkxxService.updateQbtbZwwBkxx(qbtbZwwBkxx));
    }

    /**
     * 删除布控
     */
    @PreAuthorize("@ss.hasPermi('instruction:bkxx:remove')")
    @Log(title = "布控", businessType = BusinessType.DELETE)
	@DeleteMapping("/{nXhs}")
    public AjaxResult remove(@PathVariable Long[] nXhs)
    {
        return toAjax(qbtbZwwBkxxService.deleteQbtbZwwBkxxByNXhs(nXhs));
    }
}
