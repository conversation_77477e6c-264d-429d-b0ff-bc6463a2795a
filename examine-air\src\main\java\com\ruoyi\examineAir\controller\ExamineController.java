package com.ruoyi.examineAir.controller;

import java.io.IOException;
import java.io.OutputStream;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import javax.servlet.http.HttpServletResponse;

import cn.hutool.core.util.ReflectUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.write.builder.ExcelWriterBuilder;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.alibaba.excel.write.metadata.WriteTable;
import com.alibaba.excel.write.style.column.LongestMatchColumnWidthStyleStrategy;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONArray;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.examineAir.domain.ExamineDetails;
import com.ruoyi.examineAir.enums.ExamineHead;
import com.ruoyi.examineAir.service.IExamineDetailsService;
import com.ruoyi.examineAir.vo.ExamineVo;
import com.ruoyi.examineAir.vo.HeadContentVo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.compress.utils.Lists;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.examineAir.domain.Examine;
import com.ruoyi.examineAir.service.IExamineService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 考核晾晒列Controller
 * 
 * <AUTHOR>
 * @date 2023-04-19
 */
@RestController
@RequestMapping("/examineAir/examine")
@Slf4j
public class ExamineController extends BaseController
{
    @Autowired
    private IExamineService examineService;
    @Autowired
    private IExamineDetailsService examineDetailsService;

    /**
     * 查询考核晾晒列列表
     */
//    @PreAuthorize("@ss.hasPermi('examineAir:examine:list')")
    @GetMapping("/list")
    public TableDataInfo list(Examine examine)
    {
        startPage();
        List<Examine> list = examineService.selectExamineList(examine);
        return getDataTable(list);
    }

    /**
     * 导出考核晾晒列列表
     */
    @PreAuthorize("@ss.hasPermi('examineAir:examine:export')")
    @Log(title = "考核晾晒列", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, Examine examine)
    {
        List<Examine> list = examineService.selectExamineList(examine);
        ExcelUtil<Examine> util = new ExcelUtil<Examine>(Examine.class);
        util.exportExcel(response, list, "考核晾晒列数据");
    }

    /**
     * 获取考核晾晒列详细信息
     */
//    @PreAuthorize("@ss.hasPermi('examineAir:examine:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(examineService.selectExamineById(id));
    }

    /**
     * 新增考核晾晒列
     */
//    @PreAuthorize("@ss.hasPermi('examineAir:examine:add')")
    @Log(title = "考核晾晒列", businessType = BusinessType.INSERT)
    @PostMapping("add")
    public AjaxResult add(@RequestBody ExamineVo examine)
    {
        return toAjax(examineService.insertExamine(examine));
    }

    /**
     * 修改考核晾晒列
     */
//    @PreAuthorize("@ss.hasPermi('examineAir:examine:edit')")
    @Log(title = "考核晾晒列", businessType = BusinessType.UPDATE)
    @PostMapping("edit")
    public AjaxResult edit(@RequestBody  ExamineVo examine)
    {
        return toAjax(examineService.updateExamine(examine));
    }

    /**
     * 删除考核晾晒列
     */
//    @PreAuthorize("@ss.hasPermi('examineAir:examine:remove')")
    @Log(title = "考核晾晒列", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(examineService.deleteExamineByIds(ids));
    }


    /**
     * 发布考核晾晒
     */
    @Log(title = "考核晾晒", businessType = BusinessType.UPDATE)
    @PostMapping("/release")
    public AjaxResult release(@RequestBody Examine examine)
    {
        return toAjax(examineService.release(examine));
    }


    /**
     * 导出考核晾晒列列表
     */
//    @PreAuthorize("@ss.hasPermi('examineAir:examine:export')")
//    @Log(title = "考核晾晒列", businessType = BusinessType.EXPORT)
    @PostMapping("/exportNew")
    public void exportNew(HttpServletResponse response, Long id) throws  Exception
    {
        ExamineVo examineVo = examineService.selectExamineById(id);
//        ExcelUtil<Examine> util = new ExcelUtil<Examine>(Examine.class);
//        util.exportExcel(response, list, "考核晾晒列数据");
//        OutputStream os=response.getOutputStream();
        List<List<String>> head = createHead(examineVo);
        List<List<String>> lists = creatBodyList(examineVo,head.size()-1);
        //创建EXCEL对象
//        ExcelWriterBuilder builder = EasyExcel.write("D:\\"+examineVo.getTitle()+".xlsx");
//        //设置处理器，合并单元格，列宽处理器等
//        builder.registerWriteHandler(new LongestMatchColumnWidthStyleStrategy());
//        //获取writer对象
//        ExcelWriter writer= builder.build();
//        WriteSheet sheet = EasyExcel.writerSheet(0, "个人信息").build();
//        //创建两个表格并写入表头
//        WriteTable tableOne = EasyExcel.writerTable(0).needHead(Boolean.TRUE).build();
//        tableOne.setHead(head);
//
//
//        //写入两次数据，每次会自动生成一个表头
//        writer.write(lists,sheet,tableOne);
//        writer.finish();
        try {
            EasyExcel.write(response.getOutputStream()).head(head).sheet(examineVo.getTitle()).doWrite(lists);
        } catch (IOException e) {
            log.error(e.getMessage());
        }
    }


    /**
     * 生成excle表头
     * @param examineVo
     * @return
     */
    public   List<List<String>>  createHead( ExamineVo examineVo){
        ArrayList<List<String>> head = new ArrayList<>();
//        List<String> head1 = new ArrayList<>();
//        head1.add("序号");
//        head.add(head1);
//        List<String> head2 = new ArrayList<>();
//        head2.add("单位");
//        head.add(head2);
        String headContent = examineVo.getHeadContent();
        if (StringUtils.isEmpty(headContent)){
            headContent= ExamineHead.findEnumByType(examineVo.getType()).getTemplate();
        }
        List<HeadContentVo> objects = JSON.parseArray(headContent, HeadContentVo.class);
        for (HeadContentVo h:objects){
            List<String> head3 = new ArrayList<>();
            head3.add(h.getName());
            head.add(head3);
        }
//        List<String> head4 = new ArrayList<>();
//        head4.add("得分");
//        head.add(head4);
        return  head;
    }

    /**
     * 生成excel数据
     * @param examineVo
     * @return
     */
    public   List<List<String>>  creatBodyList( ExamineVo examineVo,int num){
        List<ExamineDetails> examineDetailsList = examineVo.getExamineDetailsList();
        ArrayList<List<String>> bodys = new ArrayList<>();
        if (!CollectionUtils.isEmpty(examineDetailsList)){
            for (int i=0;i<examineDetailsList.size();i++) {
                ExamineDetails examineDetails = examineDetailsList.get(i);
                List<String> body1 = new ArrayList<>();
                body1.add(i+1+"");
//                body1.add(examineDetails.getArea());
                for (int i1=2;i1<=num;i1++){
                    if (i1==2){
                        String fieldValue =(String) ReflectUtil.getFieldValue(examineDetails, "score" + i1);
                        body1.add(fieldValue);
                    }else {
                        BigDecimal fieldValue =(BigDecimal) ReflectUtil.getFieldValue(examineDetails, "score" + i1);
                        body1.add(fieldValue.stripTrailingZeros().toPlainString());
                    }
//                    BigDecimal fieldValue =(BigDecimal) ReflectUtil.getFieldValue(examineDetails, "score" + i1);
//                    body1.add(fieldValue.stripTrailingZeros().toPlainString());
                }
                body1.add(examineDetails.getTotalScore().stripTrailingZeros().toPlainString());
                bodys.add(body1);
            }
        }

        return  bodys;
    }
}
