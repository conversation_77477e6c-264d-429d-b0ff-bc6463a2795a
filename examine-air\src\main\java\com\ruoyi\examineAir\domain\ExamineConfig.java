package com.ruoyi.examineAir.domain;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

import java.math.BigDecimal;

/**
 * 考核晾晒动态配置信息对象 t_examine_config
 * 
 * <AUTHOR>
 * @date 2023-04-19
 */
public class ExamineConfig extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** $column.columnComment */
    private Long id;

    /** 类型，1.除险保安，2.亚运赛道，3.维稳工作 */
    @Excel(name = "类型，1.除险保安，2.亚运赛道，3.维稳工作")
    private Integer type;

    /** 字段名称 */
    @Excel(name = "字段名称")
    private String fieldName;

    /** 字段介绍 */
    @Excel(name = "字段介绍")
    private String fieldIntroduce;

    /** 最高值 */
    @Excel(name = "最高值")
    private BigDecimal maxValue;

    /** 是否需要求和，0不需要，1需要 */
    @Excel(name = "是否需要求和，0不需要，1需要")
    private Long summation;

    public void setId(Long id) 
    {
        this.id = id;
    }

    public Long getId() 
    {
        return id;
    }

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    public void setFieldName(String fieldName)
    {
        this.fieldName = fieldName;
    }

    public String getFieldName() 
    {
        return fieldName;
    }
    public void setFieldIntroduce(String fieldIntroduce) 
    {
        this.fieldIntroduce = fieldIntroduce;
    }

    public String getFieldIntroduce() 
    {
        return fieldIntroduce;
    }

    public BigDecimal getMaxValue() {
        return maxValue;
    }

    public void setMaxValue(BigDecimal maxValue) {
        this.maxValue = maxValue;
    }

    public void setSummation(Long summation)
    {
        this.summation = summation;
    }

    public Long getSummation() 
    {
        return summation;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("type", getType())
            .append("fieldName", getFieldName())
            .append("fieldIntroduce", getFieldIntroduce())
            .append("maxValue", getMaxValue())
            .append("summation", getSummation())
            .toString();
    }
}
