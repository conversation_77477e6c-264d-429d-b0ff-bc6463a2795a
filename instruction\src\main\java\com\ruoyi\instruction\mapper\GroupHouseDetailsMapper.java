package com.ruoyi.instruction.mapper;

import java.util.List;
import com.ruoyi.instruction.domain.GroupHouseDetails;

/**
 * 群体涉及户数、金额Mapper接口
 * 
 * <AUTHOR>
 * @date 2024-12-19
 */
public interface GroupHouseDetailsMapper 
{
    /**
     * 查询群体涉及户数、金额
     * 
     * @param id 群体涉及户数、金额主键
     * @return 群体涉及户数、金额
     */
    public GroupHouseDetails selectGroupHouseDetailsById(Long id);

    /**
     * 查询群体涉及户数、金额列表
     * 
     * @param groupHouseDetails 群体涉及户数、金额
     * @return 群体涉及户数、金额集合
     */
    public List<GroupHouseDetails> selectGroupHouseDetailsList(GroupHouseDetails groupHouseDetails);

    /**
     * 新增群体涉及户数、金额
     * 
     * @param groupHouseDetails 群体涉及户数、金额
     * @return 结果
     */
    public int insertGroupHouseDetails(GroupHouseDetails groupHouseDetails);

    /**
     * 修改群体涉及户数、金额
     * 
     * @param groupHouseDetails 群体涉及户数、金额
     * @return 结果
     */
    public int updateGroupHouseDetails(GroupHouseDetails groupHouseDetails);

    /**
     * 删除群体涉及户数、金额
     * 
     * @param id 群体涉及户数、金额主键
     * @return 结果
     */
    public int deleteGroupHouseDetailsById(Long id);

    /**
     * 批量删除群体涉及户数、金额
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteGroupHouseDetailsByIds(Long[] ids);
}
