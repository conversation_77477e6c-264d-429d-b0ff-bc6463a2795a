package com.ruoyi.instruction.controller.Dashboard;

import com.ruoyi.common.constant.InstructionRolesConstants;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.domain.entity.SysDept;
import com.ruoyi.common.core.domain.entity.SysRole;
import com.ruoyi.common.core.domain.entity.SysUser;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.DeptUtils;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.instruction.domain.InstrucationPerson;
import com.ruoyi.instruction.domain.InstructionGroup;
import com.ruoyi.instruction.domain.InstructionInfo;
import com.ruoyi.instruction.domain.RiskDetails;
import com.ruoyi.instruction.domain.reqVo.RiskHiddenDangerReq;
import com.ruoyi.instruction.domain.rspVo.*;
import com.ruoyi.instruction.mapper.*;
import com.ruoyi.instruction.service.IPazsService;
import com.ruoyi.instruction.service.IRiskDetailsService;
import com.ruoyi.instruction.service.IUndercoverInspectionService;
import com.ruoyi.system.mapper.SysDeptMapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 驾驶舱大屏数据统计接口
 * <AUTHOR>
 * @version 1.0
 * @date 2024/12/14 9:27
 */
@RestController
@RequestMapping("/dashboard/dataStatistics")
public class DataStatisticsController {

    @Autowired
    private IRiskDetailsService riskDetailsService;

    @Autowired
    private IPazsService pazsService;

    @Autowired
    private IUndercoverInspectionService undercoverInspectionService;

    @Autowired
    private RiskDetailsMapper riskDetailsMapper;

    @Autowired
    private InstructionInfoMapper infoMapper;

    @Autowired
    private InstructionGroupMapper groupMapper;

    @Autowired
    private InstrucationPersonMapper personMapper;

    @Autowired
    private InstructionEventMapper eventMapper;

    @Autowired
    private SysDeptMapper deptMapper;

    /**
     *风险隐患
     *
     * @return
     */
    @GetMapping("/riskHiddenDanger")
    public AjaxResult riskHiddenDanger(RiskHiddenDangerReq riskHiddenDangerReq) {
        String countyName = "";
        SysUser user = SecurityUtils.getLoginUser().getUser();
        List<String> roleList = user.getRoles().stream().map(SysRole::getRoleKey).collect(Collectors.toList());
        if (roleList.contains(InstructionRolesConstants.COUNTY_COCKPIT)) {
            countyName = DeptUtils.getDutyPlaceByDept(user.getDept());
            riskHiddenDangerReq.setDutyUnit(countyName);
        }
        RiskHiddenDangerRsp riskHiddenDangerRsp = riskDetailsService.riskHiddenDanger(riskHiddenDangerReq);
        return AjaxResult.success(riskHiddenDangerRsp);
    }
    /**
     *地图平安指数
     *
     * @return
     */
    @GetMapping("/dtPazs")
    public AjaxResult dtPazs() {
        List<DtPazsRsp> riskHiddenDangerRsp = pazsService.dtPazs();
        return AjaxResult.success(riskHiddenDangerRsp);
    }
    /**
     *首页平安考核
     *
     * @return
     */
    @GetMapping("/syPakh")
    public AjaxResult syPakh(RiskHiddenDangerReq riskHiddenDangerReq) {
        SyPakhRsp riskHiddenDangerRsp = pazsService.syPazs(riskHiddenDangerReq);
        if (riskHiddenDangerRsp==null){
            riskHiddenDangerRsp=new SyPakhRsp();
        }
        List<PmTopRsp> list= undercoverInspectionService.lywtTop10(riskHiddenDangerReq);
        List<PmTopRsp> xswtpm = undercoverInspectionService.xswtpm(riskHiddenDangerReq);
        riskHiddenDangerRsp.setLywt(list);
        riskHiddenDangerRsp.setXswtpm(xswtpm);
        return AjaxResult.success(riskHiddenDangerRsp);
    }


    /**
     *平安指数月度趋势
     *
     * @return
     */
    @GetMapping("/ydqs")
    public AjaxResult ydqs(RiskHiddenDangerReq riskHiddenDangerReq) {
        List<PazsYdqsRsp> riskHiddenDangerRsp = pazsService.ydqs(riskHiddenDangerReq);


        return AjaxResult.success(riskHiddenDangerRsp);
    }
    /**
     *暗访督察统计
     *
     * @return
     */
    @GetMapping("/afdcTj")
    public AjaxResult afdcTj(RiskHiddenDangerReq riskHiddenDangerReq) {
        AfdcTjVo riskHiddenDangerRsp = undercoverInspectionService.afdcTj(riskHiddenDangerReq);


        return AjaxResult.success(riskHiddenDangerRsp);
    }

    /**
     * 获取风险排查处置管控
     * @return
     */
    @GetMapping("/getRiskDisposeControl")
    public AjaxResult getRiskDisposeControl() {
        RiskDetails details = new RiskDetails();

        String countyName = "";
        SysUser user = SecurityUtils.getLoginUser().getUser();
        List<String> roleList = user.getRoles().stream().map(SysRole::getRoleKey).collect(Collectors.toList());
        if (roleList.contains(InstructionRolesConstants.COUNTY_COCKPIT)) {
            countyName = DeptUtils.getDutyPlaceByDept(user.getDept());
            details.setDutyUnit(countyName);
        }
        details.setDissolution("1");
        //查询应处置数
        List<Map<String, Object>> statistics = riskDetailsMapper.getStatistics(details);

        //遍历statistics
        for (int i = 0; i < statistics.size(); i++) {
            Map<String, Object> map = statistics.get(i);
            Long level = (Long) map.get("level");
            //获取待处置条数
            Long toDispose = getToDispose(level,countyName);
            map.put("toDispose",toDispose);
        }
        return AjaxResult.success(statistics);
    }

    /**
     * 获取待处置条数
     * @param level
     * @return
     */
    private Long getToDispose(final Long level, String countyName) {
        Long acount = null;
        if (level == 1) {
            //一周走访一次
            acount = riskDetailsMapper.getToDispose(level, countyName,"week",1);
        } else if (level == 2) {
            //一周走访两次
            acount = riskDetailsMapper.getToDispose(level, countyName,"week",2);
        } else if (level == 3) {
            //每月走访一次
            acount = riskDetailsMapper.getToDispose(level, countyName,"month",1);
        } else if (level == 4) {
            //每月走访一次
            acount = riskDetailsMapper.getToDispose(level, countyName,"month",1);
        }
        return acount;
    }

    /**
     * 获取指令统计
     * @return
     */
    @GetMapping("/getInstructionStatis")
    public AjaxResult getInstructionStatis(@RequestParam(name = "type", required = false) Integer type,@RequestParam(name = "dataType", required = false) Integer dataType) {
        //查询市级指令信息
        InstructionInfo info = new InstructionInfo();
        // info.setPetitionType(type);
        Date timeStart = DateUtils.getTimeStart(type == null ? 1 : type);
        info.setCreateTime(timeStart);
        info.setInstructionType(dataType);
        String countyName = "";
        Long deptId = null;
        SysUser user = SecurityUtils.getLoginUser().getUser();
        List<String> roleList = user.getRoles().stream().map(SysRole::getRoleKey).collect(Collectors.toList());
        if (roleList.contains(InstructionRolesConstants.COUNTY_COCKPIT)) {
            countyName = DeptUtils.getDutyPlaceByDept(user.getDept());
            deptId = DeptUtils.getDeptIdByDeptName(countyName);
            info.setReceiveUnit(countyName);
            info.setCreateDeptId(deptId);
        }
        List<InstructionInfo> instructionInfoList = infoMapper.getCityStatis(info);
        HashMap<String, Object> map = new HashMap<>();
        //查询市级指令数
        List<Long> collect2 = instructionInfoList.stream().map(InstructionInfo::getId).distinct().collect(Collectors.toList());
        map.put("cityCount", collect2.size());
        //查询instructionInfoList 中createTime为null的数据
        List<InstructionInfo> collect = instructionInfoList.stream().filter(item -> item.getCreateTime() == null).collect(Collectors.toList());
        map.put("cityNullReceive", collect.size());
        //查询instructionInfoList 中createTime不为null的数据 且updateTime为null status = 1的数据
        List<InstructionInfo> lqFeedbackList = instructionInfoList.stream().filter(item -> item.getCreateTime() != null && item.getUpdateTime() == null && item.getStatus().equals("1")).collect(Collectors.toList());
        map.put("cityLqFeedback", lqFeedbackList.size());
        List<InstructionInfo> cqFeedbackList = instructionInfoList.stream().filter(item -> item.getCreateTime() != null && item.getUpdateTime() == null && item.getStatus().equals("2")).collect(Collectors.toList());
        map.put("cityCqFeedback", cqFeedbackList.size());
        //待反馈
        map.put("cityWaitFeedback", lqFeedbackList.size() + cqFeedbackList.size());
        //查询出已反馈
        List<InstructionInfo> feedbackList = instructionInfoList.stream().filter(item -> item.getCreateTime() != null && item.getUpdateTime() != null).collect(Collectors.toList());
        map.put("cityFeedback", feedbackList.size());

        //查询县市区指令
        List<InstructionInfo> countyList = infoMapper.getCountyStatis(info);
        //将countyList中的id进行去重 ，统计数
        List<Long> collect1 = countyList.stream().map(InstructionInfo::getId).distinct().collect(Collectors.toList());
        map.put("countyCount", collect1.size());
        //查询instructionInfoList 中createTime为null的数据
        List<InstructionInfo> countyCollect = countyList.stream().filter(item -> item.getCreateTime() == null).collect(Collectors.toList());
        map.put("countyNullReceive", countyCollect.size());
        //查询instructionInfoList 中createTime不为null的数据 且updateTime为null status = 1的数据
        List<InstructionInfo> countyLqFeedback = countyList.stream().filter(item -> item.getCreateTime() != null && item.getUpdateTime() == null && item.getStatus().equals("1")).collect(Collectors.toList());
        map.put("countyLqFeedback", countyLqFeedback.size());
        List<InstructionInfo> countyCqFeedback = countyList.stream().filter(item -> item.getCreateTime() != null && item.getUpdateTime() == null && item.getStatus().equals("2")).collect(Collectors.toList());
        map.put("countyCqFeedback", countyCqFeedback.size());
        //待反馈
        map.put("countyWaitFeedback", countyLqFeedback.size() + countyCqFeedback.size());
        //查询出已反馈
        List<InstructionInfo> countyFeedback = countyList.stream().filter(item -> item.getCreateTime() != null && item.getUpdateTime() != null).collect(Collectors.toList());
        map.put("countyFeedback", countyFeedback.size());
        return AjaxResult.success(map);
    }

    @GetMapping("/getGroupTypePersonCount")
    public AjaxResult getGroupTypePersonCount() {
        String countyName = "";
        SysUser user = SecurityUtils.getLoginUser().getUser();
        List<String> roleList = user.getRoles().stream().map(SysRole::getRoleKey).collect(Collectors.toList());
        if (roleList.contains(InstructionRolesConstants.COUNTY_COCKPIT)) {
            countyName = DeptUtils.getDutyPlaceByDept(user.getDept());
        }
        //前五条数据
        List<Map<String, Object>> list = groupMapper.getGroupType();
        //遍历list
        String finalCountyName = countyName;
        list.forEach(map -> {
            String personIds = (String) map.get("personIds");
            if (personIds != null) {
                String[] longs = personIds.split(",");
                List<InstrucationPerson> instrucationPeople = personMapper.selectInstrucationPersonByIdsAndDept(longs, "", finalCountyName,"");
                map.put("amount", instrucationPeople.size());
            } else {
                map.put("amount", 0);
            }
        });
        List<String> typeIds = list.stream().filter(map -> map.get("type") != null).map(map -> map.get("type").toString()).collect(Collectors.toList());
        // Long count = groupMapper.getGroupTypeCount(typeIds);
        String personIds = groupMapper.getGroupTypePersonIds(typeIds);
        String[] longs = personIds.split(",");
        List<InstrucationPerson> instrucationPeople = personMapper.selectInstrucationPersonByIdsAndDept(longs, "", countyName,"");
        HashMap<String, Object> map = new HashMap<>();
        map.put("type_name", "其他");
        map.put("amount", instrucationPeople.size());
        map.put("type","99");
        list.add(map);
        return AjaxResult.success(list);
    }

    /**
     * 获取人员异动情况
     * @return
     */
    @GetMapping("/getTownPersonEvent")
    public AjaxResult getTownPersonEvent(@RequestParam(name = "startTime", required = false) String startTime, @RequestParam(name = "endTime", required = false) String endTime) {
        String countyName = "";
        Long deptId = null;
        SysUser user = SecurityUtils.getLoginUser().getUser();
        List<String> roleList = user.getRoles().stream().map(SysRole::getRoleKey).collect(Collectors.toList());
        if (roleList.contains(InstructionRolesConstants.COUNTY_COCKPIT)) {
            countyName = DeptUtils.getDutyPlaceByDept(user.getDept());
            deptId = DeptUtils.getDeptIdByDeptName(countyName);
        }

        //查询近一个月异动人员ids
        String personIds = eventMapper.findPersonIdsByTime(startTime, endTime);
        if (personIds == null || personIds.equals("")) {
            personIds = "0";
            List<InstrucationPerson> personList = personMapper.findLxPersonEvent(personIds.split(","),deptId,countyName);
            return AjaxResult.success(personList);
        }
        String[] split = personIds.split(",");
        List<InstrucationPerson> personList = personMapper.findLxPersonEvent(split, deptId, countyName);

        Map<String, Integer> map = StringUtils.countIDs(split);
        List<InstrucationPerson> instrucationPeople = personMapper.selectYdPersonByIds(split, countyName);
        //遍历

        for (InstrucationPerson instrucationPerson : instrucationPeople) {
            String pTown = instrucationPerson.getpTown();
            Integer count = map.get(instrucationPerson.getId().toString());
            //查询出personList中pTown相同的数据
            InstrucationPerson person = personList.stream().filter(item -> item.getpTown().equals(pTown)).findFirst().orElse(null);
            if (person != null) {
                person.setEventNum(person.getEventNum() + count);
            }
        }
        return AjaxResult.success(personList);
    }

    /**
     * 查询市级、县市区指令双排双办、矛盾纠纷预警指令所处环节个数
     * @return
     */
    @GetMapping("/getMzxInstructionStatus")
    public AjaxResult getMzxInstructionStatus() {

        String countyName = "";
        Long deptId = null;
        SysUser user = SecurityUtils.getLoginUser().getUser();
        List<String> roleList = user.getRoles().stream().map(SysRole::getRoleKey).collect(Collectors.toList());
        if (roleList.contains(InstructionRolesConstants.COUNTY_COCKPIT)) {
            countyName = DeptUtils.getDutyPlaceByDept(user.getDept());
            deptId = DeptUtils.getDeptIdByDeptName(countyName);
        }
        HashMap<String, Object> map = new HashMap<>();
        map.put("cityTotal", 0);
        map.put("cityToReceive", 0);
        map.put("cityToFeedBack", 0);
        map.put("cityFeedBack", 0);
        map.put("countTotal",0);
        map.put("countyToReceive", 0);
        map.put("countyToFeedBack", 0);
        map.put("countyFeedBack", 0);
        //查询市级指令
        List<InstructionInfo> cityInfoList = infoMapper.getLxCityInstructionStatus(countyName);
        if (cityInfoList != null && cityInfoList.size() != 0) {
            map.put("cityTotal", cityInfoList.size());
            //查询出待接收 handel_time 为null的数据
            List<InstructionInfo> toReceive = cityInfoList.stream().filter(item -> item.getHandleTime() == null).collect(Collectors.toList());
            //查询出待反馈的数据 receive_unit 为null的数据
            List<InstructionInfo> toFeedBack = cityInfoList.stream().filter(item -> item.getReceiveUnit() == null).collect(Collectors.toList());
            map.put("cityToReceive", toReceive.size());
            map.put("cityToFeedBack", toFeedBack.size());
            map.put("cityFeedBack", cityInfoList.size() - toFeedBack.size());
        }
        //查询县市区指令
        List<InstructionInfo> countyInfoList = infoMapper.getLxCountInstructionStatus(deptId);
        if (countyInfoList != null && countyInfoList.size() != 0) {
            //查询出待接收的数据 groupId!=creatorId
            map.put("countTotal",countyInfoList.size());
            List<InstructionInfo> toReceive = countyInfoList.stream().filter(item -> item.getGroupId() != item.getCreatorId()).collect(Collectors.toList());
            //查询出已反馈数据 groupId = createDeptId
            List<InstructionInfo> toFeedBack = countyInfoList.stream().filter(item -> item.getGroupId() == item.getCreateDeptId()).collect(Collectors.toList());
            map.put("countyToReceive", toReceive.size());
            map.put("countyToFeedBack", countyInfoList.size() - toFeedBack.size());
            map.put("countyFeedBack", toFeedBack.size());
        }
        return AjaxResult.success(map);
    }


    private static final String WCQ_ZFW = "婺城区政法委";
    private static final String WYX_ZFW = "武义县委政法委";
    private static final String WHJ = "未化解";
    private static final String YQHJ = "延期化解";
    private static final String CQHJ = "超期化解";

    /**
     * 获取矛盾纠纷、市级双排双办 化解不力数
     * @return
     */
    @GetMapping("/getFailureResolve")
    public AjaxResult getFailureResolve(@RequestParam(name = "date", required = false) String date) {
        Map<String, Object> map = new HashMap<>();
        //获取化解不力数指令
        List<InstructionInfo> list = infoMapper.getFailureResolve(date);
        //查询出延期化解
        Long yqhj = list.stream().filter(item -> item.getTown().equals("延期化解")).count();
        //查询出超期化解
        Long cqhj = list.stream().filter(item -> item.getTown().equals("超期化解")).count();
        //查询出未化解
        Long whj = list.stream().filter(item -> item.getTown().equals("未化解")).count();
        map.put("yqhj", yqhj);
        map.put("cqhj", cqhj);
        map.put("whj", whj);

        // 统计各县市区的延期化解、超期化解、未化解数
        Map<String, Long> stats = new HashMap<>();
        for (InstructionInfo item : list) {
            if (item == null || item.getCounty() == null) continue;

            String county = item.getCounty();
            String town = item.getTown();

            for (County c : County.values()) {
                if (c.getName().equals(county)) {
                    incrementStat(stats, c.name().toLowerCase() + "Whj", town.equals("未化解"));
                    incrementStat(stats, c.name().toLowerCase() + "Yqhj", town.equals("延期化解"));
                    incrementStat(stats, c.name().toLowerCase() + "Cqhj", town.equals("超期化解"));
                }
            }
        }
        Map<String, Object> countyMap = new HashMap<>();
        // 将统计结果放入map中
        for (County c : County.values()) {
            countyMap.put(c.name().toLowerCase() + "Whj", stats.getOrDefault(c.name().toLowerCase() + "Whj", 0L));
            countyMap.put(c.name().toLowerCase() + "Yqhj", stats.getOrDefault(c.name().toLowerCase() + "Yqhj", 0L));
            countyMap.put(c.name().toLowerCase() + "Cqhj", stats.getOrDefault(c.name().toLowerCase() + "Cqhj", 0L));
        }
        map.put("countyMap",countyMap);
        List<InstructionInfo> finallyList = list.stream().filter(item -> !item.getTown().equals("未知状态")).collect(Collectors.toList());

        map.put("list", finallyList);
        return AjaxResult.success(map);
    }


    private static void incrementStat(Map<String, Long> stats, String key, boolean condition) {
        if (condition) {
            stats.put(key, stats.getOrDefault(key, 0L) + 1);
        }
    }

    // 使用枚举类管理县市区名称
    private enum County {
        JINDONG("金东区"),
        LANXI("兰溪市"),
        KAIFAQU("开发区"),
        YIWU("义乌市"),
        PUJIANG("浦江县"),
        YONGKANG("永康市"),
        WUYI("武义县"),
        PANAN("磐安县"),
        DONGYANG("东阳市"),
        WUCQ("婺城区");

        private final String name;

        County(String name) {
            this.name = name;
        }

        public String getName() {
            return name;
        }
    }

    /**
     * 跟踪不力
     * @param date
     * @return
     */
    @GetMapping("/getPoorTrack")
    public AjaxResult getPoorTrack(@RequestParam(name = "date", required = false) String date) {
        Map<String, Object> map = new HashMap<>();
        List<InstructionInfo> list = infoMapper.getPoorTrack(date);
        //获取交办数
        long jbCount = list.stream().filter(item -> item.getCounty() != null).count();
        //获取未接收数
        long noReceiveCount = list.stream().filter(item -> item.getCounty() != null && item.getCreateTime() == null).count();
        //查询出超期化解
        Long cqhjTotal = list.stream().filter(item -> item.getInstrucationIsEnd() == 2 && item.getTown().equals("1")).count();

        map.put("jbCount", jbCount);
        map.put("noReceiveCount", noReceiveCount);
        map.put("cqfk", cqhjTotal);

        // 统计各县市区的延期化解、超期化解、未化解数
        Map<County, Map<String, Long>> countyStats = Arrays.stream(County.values())
                .collect(Collectors.toMap(
                        county -> county,
                        county -> {
                            long jb = list.stream()
                                    .filter(item -> county.getName().equals(item.getCounty()))
                                    .count();
                            long noReceive = list.stream()
                                    .filter(item -> county.getName().equals(item.getCounty()) && item.getCreateTime() == null)
                                    .count();
                            long cqhj = list.stream()
                                    .filter(item -> item.getInstrucationIsEnd()==2 &&county.getName().equals(item.getCounty()) && "1".equals(item.getTown()))
                                    .count();
                            Map<String, Long> stats = new HashMap<>();
                            stats.put("jb", jb);
                            stats.put("noReceive", noReceive);
                            stats.put("cqhj", cqhj);
                            return Collections.unmodifiableMap(stats);
                        }
                ));
        map.put("countyStats", countyStats);
        return AjaxResult.success(map);
    }

    /**
     * 获取化解不力统计数
     * @param county
     * @return
     */
    @GetMapping("/getHjblCount")
    public AjaxResult getHjblStatistics(@RequestParam(name = "county", required = false) String county,
                                        @RequestParam(name = "date", required = false) String date,
                                        @RequestParam(name = "startTime", required = false) String startTime,
                                        @RequestParam(name = "endTime", required = false) String endTime) {
        Long countyDeptIdByName = StringUtils.getCountyDeptIdByName(county);
        List<InstructionInfo> list = infoMapper.selectHjblCount(countyDeptIdByName,date,startTime,endTime);
        //查询出超期未化解、未化解情况
        long cqwhj = 0;
        long whj = 0;
        if (list.size()!=0){
             cqwhj = list.stream().filter(item -> item.getStatus().equals("超期未化解")).count();
             whj = list.stream().filter(item -> item.getStatus().equals("未化解")).count();
        }
        Map<String, Object> map = new HashMap<>();
        map.put("cqwhj", cqwhj);
        map.put("whj", whj);
        map.put("list", list);
        return AjaxResult.success(map);
    }

    /**
     * 获取化解不力详情
     * @param county
     * @return
     */
    @GetMapping("/getHjblDetails")
    public AjaxResult getHjblDetails(@RequestParam(name = "county", required = false) String county,
                                     @RequestParam(name = "date", required = false) String date,
                                     @RequestParam(name = "startTime", required = false) String startTime,
                                     @RequestParam(name = "endTime", required = false) String endTime) {
        ArrayList<Object> mapList = new ArrayList<>();
        Long countyDeptIdByName = StringUtils.getCountyDeptIdByName(county);
        List<InstructionInfo> list = new ArrayList<>();
        SysDept dept = new SysDept();
        if (county.equals("市级")) {
            dept.setParentId(203L);
            list = infoMapper.selectHjblDetails(countyDeptIdByName, date,startTime,endTime);
        } else {
            Long deptIdByName = StringUtils.getDeptIdByName(county);
            dept.setParentId(deptIdByName);
            list = infoMapper.selectHjblDetailsForCounty(countyDeptIdByName, date,startTime,endTime);
        }
        List<SysDept> deptList = deptMapper.selectDeptList(dept);
        for (SysDept sysDept : deptList) {
            if (sysDept.getDeptName().contains("政府机构")||sysDept.getDeptName().contains("政法")){
                continue;
            }
            Map<String, Object> map = new HashMap<>();
            map.put("county", sysDept.getDeptName());
            //查询出超期未化解、未化解情况
            long cqwhj = 0;
            long whj = 0;
            if (list.size() != 0) {
                cqwhj = list.stream().filter(item -> item.getStatus().equals("超期未化解") && sysDept.getDeptName().equals(item.getReceiveStr())).findFirst().map(item -> item.getSpareNum()).orElse(0);
                whj = list.stream().filter(item -> item.getStatus().equals("未化解") && sysDept.getDeptName().equals(item.getReceiveStr())).findFirst().map(item -> item.getSpareNum()).orElse(0);
            }
            map.put("cqwhj", cqwhj);
            map.put("whj", whj);
            mapList.add(map);
        }

        return AjaxResult.success(mapList);
    }

    /**
     * 获取县市区最新处置动态
     * @param date
     * @param level
     * @return
     */
    @GetMapping("/getRiskCountyDeal")
    public AjaxResult getRiskCountyDeal(@RequestParam(name = "date", required = false) String date, @RequestParam(name = "level", required = false) String level) {
        List<RiskDetails> list = riskDetailsMapper.getRiskCountyDeal(date, level);
        return AjaxResult.success(list);
    }

    /**
     * 获取进展不力数据
     * @param date
     * @return
     */
    @GetMapping("/getRiskSlowProgress")
    public AjaxResult getRiskSlowProgress(@RequestParam(name = "date", required = false) String date) {
        List<Map<String, Object>> mapList = new ArrayList<>();
        List<RiskDetails> list = riskDetailsMapper.getRiskSlowProgress(date);
        //查询县市区部门
        SysDept sysDept = new SysDept();
        sysDept.setParentId(203L);
        List<SysDept> deptList = deptMapper.selectDeptList(sysDept);
        for (SysDept dept : deptList) {
            Map<String, Object> map = new HashMap<>();
            map.put("county", dept.getDeptName());
            long wgj = 0;
            long ygj = 0;
            wgj = list.stream().filter(item -> dept.getDeptName().equals(item.getDutyUnit()) && item.getWorkProgress().equals("未跟进")).count();
            ygj = list.stream().filter(item -> dept.getDeptName().equals(item.getDutyUnit()) && item.getWorkProgress().equals("已跟进")).count();
            map.put("wgj", wgj);
            map.put("ygj", ygj);
            mapList.add(map);
        }
        long wgjTotal = 0;
        long ygjTotal = 0;
        wgjTotal = list.stream().filter(item -> item.getWorkProgress().equals("未跟进")).count();
        ygjTotal = list.stream().filter(item -> item.getWorkProgress().equals("已跟进")).count();
        return AjaxResult.success(mapList).put("total", list.size()).put("wgjTotal", wgjTotal).put("ygjTotal", ygjTotal);
    }

    /**
     * 获取进展不力列表
     * @param date
     * @return
     */
    @GetMapping("/getRiskSlowProgressList")
    public AjaxResult getRiskSlowProgressList(@RequestParam(name = "date", required = false) String date) {
        List<RiskDetails> list = riskDetailsMapper.getRiskSlowProgress(date);
        List<RiskDetails> wgj = list.stream().filter(item -> item.getWorkProgress().equals("未跟进")).collect(Collectors.toList());
        List<RiskDetails> ygj = list.stream().filter(item -> item.getWorkProgress().equals("已跟进")).collect(Collectors.toList());
        Map<String, Object> map = new HashMap<>();
        map.put("wgj", wgj);
        map.put("ygj", ygj);
        map.put("total", list);
        return AjaxResult.success(map);
    }

    /**
     * 获取排查不力数据
     * @param
     * @return
     */
    @GetMapping("/getRiskCheckPoor")
    public AjaxResult getRiskCheckPoor(RiskDetails riskDetails) {
        //查询近3个月高频群体
        List<InstructionGroup> groupList = groupMapper.getRiskCheckPoor();
        List<InstructionGroup> newList = new ArrayList<>();
        //查询风险排查列表
        List<RiskDetails> list = riskDetailsMapper.selectRiskDetailsList(riskDetails);
        for (InstructionGroup group:groupList) {
            group.setTypeName("未排查");
            String groupName = group.getGroupName().replace("群体", "");
            long count = list.stream().filter(item -> item.getTitle() != null && item.getTitle().contains(groupName)).count();
            if (count==0){
                newList.add(group);
            }
        }
        return AjaxResult.success(newList).put("total", groupList.size());
    }

    /**
     * 获取跟踪不力数据
     * @param date
     * @return
     */
    @GetMapping("/getPoorTrackNew")
    public AjaxResult getPoorTrack(@RequestParam(name = "county", required = false) String county,
                                   @RequestParam(name = "date", required = false) String date,
                                   @RequestParam(name = "startTime", required = false) String startTime,
                                   @RequestParam(name = "endTime", required = false) String endTime,
                                   @RequestParam(name = "instructionType", required = false) Integer instructionType) {
        ArrayList<Object> mapList = new ArrayList<>();
        Long countyDeptIdByName = StringUtils.getCountyDeptIdByName(county);
        List<InstructionInfo> list = new ArrayList<>();
        SysDept dept = new SysDept();
        if (county.equals("市级")) {
            dept.setParentId(203L);
            list = infoMapper.getPoorTrackNew(countyDeptIdByName, date,startTime,endTime,instructionType);
        } else {
            Long deptIdByName = StringUtils.getDeptIdByName(county);
            dept.setParentId(deptIdByName);
            list = infoMapper.getPoorTrackNewForCounty(countyDeptIdByName, date,startTime,endTime,instructionType);
        }
        // 筛选出指令总数，统计规则将 id 去重
        long instructionCount = list.stream()
                .map(InstructionInfo::getId)
                .collect(Collectors.toSet())
                .size();
        // 筛选出接收不力数
        long receiveCount = list.stream().filter(item -> item.getStatus().equals("接收不力")).count();
        // 筛选出反馈不力
        long feedbackCount = list.stream().filter(item -> item.getMzxType().equals("超期反馈")).count();
        List<SysDept> deptList = deptMapper.selectDeptList(dept);
        for (SysDept sysDept : deptList) {
            if (sysDept.getDeptName().contains("政府机构") || sysDept.getDeptName().contains("政法")) {
                continue;
            }
            Map<String, Object> map = new HashMap<>();
            map.put("county", sysDept.getDeptName());
            //查询出超期未化解、未化解情况
            long jsbl = 0;
            long fkbl = 0;
            long count = 0;
            if (list.size() != 0) {
                // 筛选出指令总数
                count = list.stream().filter(item -> item.getReceiveStr() != null && item.getReceiveStr().contains(sysDept.getDeptName())).count();
                // 筛选出接收不力数
                jsbl = list.stream().filter(item -> item.getStatus().equals("接收不力")&& item.getReceiveStr() != null && item.getReceiveStr().contains(sysDept.getDeptName())).count();
                // 筛选出反馈不力
                fkbl = list.stream().filter(item -> item.getMzxType().equals("超期反馈")&& item.getReceiveStr() != null && item.getReceiveStr().contains(sysDept.getDeptName())).count();
            }
            map.put("total", count);
            map.put("jsbl", jsbl);
            map.put("fkbl", fkbl);
            mapList.add(map);
        }
        Map<String, Object> map = new HashMap<>();
        map.put("total", instructionCount);
        map.put("jsbl", receiveCount);
        map.put("fkbl", feedbackCount);
        map.put("details", mapList);
        return AjaxResult.success(map);
    }


    /**
     * 获取县（市、区）化解率
     * @param county
     * @param startTime
     * @param endTime
     * @param instructionType
     * @return
     */
    @GetMapping("/getCountyHjl")
    public AjaxResult getCountyHjl(@RequestParam(name = "county", required = false) String county,
                                   @RequestParam(name = "startTime", required = false) String startTime,
                                   @RequestParam(name = "endTime", required = false) String endTime,
                                   @RequestParam(name = "instructionType", required = false) Integer instructionType) {
        ArrayList<Object> mapList = new ArrayList<>();
        Long countyDeptIdByName = StringUtils.getCountyDeptIdByName(county);
        SysDept dept = new SysDept();
        if (county.equals("市级")) {
            dept.setParentId(203L);
        } else {
            Long deptIdByName = StringUtils.getDeptIdByName(county);
            dept.setParentId(deptIdByName);
        }
        List<InstructionInfo> list = infoMapper.getCountyHjl(countyDeptIdByName,startTime,endTime,instructionType);
        List<SysDept> deptList = deptMapper.selectDeptList(dept);
        for (SysDept sysDept : deptList) {
            if (sysDept.getDeptName().contains("政府机构") || sysDept.getDeptName().contains("政法")) {
                continue;
            }
            Map<String, Object> map = new HashMap<>();
            map.put("county", sysDept.getDeptName());
            //查询出超期未化解、未化解情况
            long count = 0;
            long endCount = 0;
            if (list.size() != 0) {
                // 筛选出指令总数
                count = list.stream().filter(item -> item.getReceiveUnit() != null && item.getReceiveUnit().contains(sysDept.getDeptName())).count();
                // 筛选出接收不力数
                endCount = list.stream().filter(item -> item.getEndTime() != null && item.getReceiveUnit() != null && item.getReceiveUnit().contains(sysDept.getDeptName())).count();
            }
            if (count == 0 || endCount == 0) {
                map.put("hjl",0);
            } else {
                double result = (double) endCount / count; // 确保结果为浮点数
                double roundedResult = Math.round(result * 100.0) / 100.0; // 保留两位小数
                map.put("hjl", roundedResult); // 插入计算结果
            }
            map.put("total", count);
            map.put("endCount", endCount);
            mapList.add(map);
        }

        return AjaxResult.success(mapList);
    }



    /**
     * 获取矛盾风险预警交办反馈分析
     * @param info
     * @return
     */
    @GetMapping("/getContradictoryRisk")
    public AjaxResult getContradictoryRisk(InstructionInfo info) {
        List<Map<String, Object>> mapList = new ArrayList<>();
        String[] countyStrings = {"婺城区", "金东区", "兰溪市", "东阳市", "义乌市", "永康市", "浦江县", "武义县", "磐安县", "开发区"};
        //查询矛盾风险数据
        List<InstructionInfo> list = infoMapper.getContradictoryRisk(info);
        for (String countyStr : countyStrings) {
            Map<String, Object> map = new HashMap<>();
            map.put("county", countyStr);
            map.put("jbCount", 0);
            map.put("hjzCount", 0);
            map.put("endCount", 0);
            if (list.size() != 0) {
                //查询交办数
                long count = list.stream().filter(item -> item.getReceiveUnit().contains(countyStr)).count();
                //销号数
                long endCount = list.stream().filter(item -> item.getEndTime() != null && item.getReceiveUnit().contains(countyStr)).count();
                map.put("jbCount", count);
                map.put("hjzCount", count - endCount);
                map.put("endCount", endCount);
            }
            mapList.add(map);
        }
        return AjaxResult.success(mapList);
    }


}
