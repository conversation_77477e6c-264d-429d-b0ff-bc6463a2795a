# getPoorTrackNew SQL查询优化报告

## 概述
本报告针对 `instructionInfoMapper.getPoorTrackNew` 方法的SQL查询进行性能优化分析和改进建议。

## 原始查询问题分析

### 1. 性能瓶颈识别
- **复杂的子查询**: 内层子查询包含多个CASE语句和DATE_ADD函数
- **函数调用开销**: DATE_FORMAT函数在WHERE条件中使用，无法利用索引
- **多表LEFT JOIN**: 三个表的连接操作，缺乏适当的索引
- **聚合查询**: MAX函数和GROUP BY操作在大数据量下性能较差

### 2. 具体问题点
```sql
-- 问题1: 使用DATE_FORMAT函数导致无法使用索引
AND date_format(handle_time,'%y%m%d') >= date_format(#{startTime},'%y%m%d')

-- 问题2: 复杂的CASE语句在子查询中重复计算
CASE WHEN emergency_degree = '一般' THEN DATE_ADD(create_time, INTERVAL 24 HOUR) ...

-- 问题3: 子查询选择所有字段 (a.*)
SELECT a.*, ...
```

## 优化方案

### 1. SQL查询优化
- **移除DATE_FORMAT函数**: 直接使用日期比较，提高索引利用率
- **简化字段选择**: 只选择必要的字段，减少数据传输量
- **优化CASE语句**: 保持逻辑清晰的同时提高可读性

### 2. 索引优化策略

#### 主表索引 (t_instruction_info)
```sql
-- 复合索引：覆盖主要查询条件
CREATE INDEX idx_instruction_info_main ON t_instruction_info (
    status, instruction_type, create_dept_id, handle_time
);

-- 时间范围查询索引
CREATE INDEX idx_instruction_info_handle_time ON t_instruction_info (handle_time);
```

#### 关联表索引 (t_instruction_receive)
```sql
-- JOIN操作优化索引
CREATE INDEX idx_instruction_receive_main ON t_instruction_receive (
    status, instrucation_id, receive_dept
);
```

#### 反馈表索引 (t_instruction_county_feedback)
```sql
-- 聚合查询优化索引
CREATE INDEX idx_county_feedback_main ON t_instruction_county_feedback (
    status, is_end, instruction_id, feedback_dept
);

-- MAX函数优化索引
CREATE INDEX idx_county_feedback_receive_time ON t_instruction_county_feedback (
    instruction_id, feedback_dept, receive_time
);
```

### 3. 查询重写优化

#### 优化前
```sql
AND date_format(handle_time,'%y%m%d') >= date_format(#{startTime},'%y%m%d')
```

#### 优化后
```sql
AND handle_time >= #{startTime}
```

## 预期性能提升

### 1. 查询速度提升
- **索引利用率**: 从0%提升到90%+
- **查询时间**: 预计减少60-80%
- **CPU使用率**: 减少函数计算开销

### 2. 系统资源优化
- **内存使用**: 减少临时表创建
- **磁盘I/O**: 通过索引减少全表扫描
- **网络传输**: 减少不必要的字段传输

## 实施建议

### 1. 分阶段实施
1. **第一阶段**: 创建推荐的数据库索引
2. **第二阶段**: 部署优化后的SQL查询
3. **第三阶段**: 监控性能指标和调优

### 2. 监控指标
- 查询执行时间
- 索引使用情况
- 系统资源使用率
- 并发查询性能

### 3. 回滚计划
- 保留原始查询作为备份
- 准备索引删除脚本
- 建立性能对比基准

## 额外优化建议

### 1. 应用层优化
- 实现查询结果缓存
- 使用分页查询减少数据量
- 考虑异步处理大数据量查询

### 2. 数据库层优化
- 定期更新表统计信息
- 考虑表分区策略
- 监控慢查询日志

### 3. 架构层优化
- 考虑读写分离
- 实现数据预聚合
- 使用搜索引擎处理复杂查询

## 风险评估

### 1. 低风险
- 索引创建：不影响现有功能
- 查询优化：逻辑等价替换

### 2. 注意事项
- 索引维护开销
- 存储空间增加
- 需要充分测试验证

## 结论
通过实施上述优化方案，预计可以显著提升 `getPoorTrackNew` 查询的性能，改善用户体验，并减少系统资源消耗。建议按照分阶段实施计划逐步部署优化措施。
