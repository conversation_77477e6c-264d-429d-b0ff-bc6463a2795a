package com.ruoyi.instruction.mapper;

import java.util.List;
import java.util.Map;

import com.ruoyi.instruction.domain.BizType;
import com.ruoyi.instruction.domain.BizYjSource;
import com.ruoyi.instruction.domain.YjEvent;
import org.apache.ibatis.annotations.Param;

/**
 * 人员风险值预估Mapper接口
 *
 * <AUTHOR>
 * @date 2024-12-10
 */
public interface YjEventMapper
{
    /**
     * 查询人员风险值预估
     *
     * @param id 人员风险值预估主键
     * @return 人员风险值预估
     */
    public YjEvent selectYjEventById(Long id);

    /**
     * 查询人员风险值预估列表
     *
     * @param yjEvent 人员风险值预估
     * @return 人员风险值预估集合
     */
    public List<YjEvent> selectYjEventList(YjEvent yjEvent);

    /**
     * 新增人员风险值预估
     *
     * @param yjEvent 人员风险值预估
     * @return 结果
     */
    public int insertYjEvent(YjEvent yjEvent);

    /**
     * 修改人员风险值预估
     *
     * @param yjEvent 人员风险值预估
     * @return 结果
     */
    public int updateYjEvent(YjEvent yjEvent);

    /**
     * 删除人员风险值预估
     *
     * @param id 人员风险值预估主键
     * @return 结果
     */
    public int deleteYjEventById(String id);

    /**
     * 批量删除人员风险值预估
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteYjEventByIds(Long[] ids);

    List<Map<String, Object>> getBizType();

    void updateYjEventStatus(@Param("id") String wzEventId);

    /**
     * 获取事件类型
     * @return
     */
    List<BizType> getBizTypeList();

    /**
     * 获取预警对应集合
     * @return
     */
    List<BizYjSource> getYjSourceList();

    /**
     * 根据姓名及身份证查询事件Id
     * @param yjEvent
     * @return
     */
    List<String> findEventIds(YjEvent yjEvent);

    List<String> findPersonEventIds();

    Long findToAssign(@Param("startTime") String startTime, @Param("endTime") String endTime);

    List<Map<String,Object>> warnSituation(@Param("startTime") String startTime, @Param("endTime") String endTime);
}
