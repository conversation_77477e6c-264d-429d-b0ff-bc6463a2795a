package com.ruoyi.instruction.controller;

import java.util.*;
import java.util.stream.Collectors;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.instruction.domain.YjxzInfo;
import com.ruoyi.instruction.service.IYjxzInfoService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 应急小组名称Controller
 *
 * <AUTHOR>
 * @date 2023-08-06
 */
@RestController
@RequestMapping("/system/yjxz")
public class YjxzInfoController extends BaseController
{
    @Autowired
    private IYjxzInfoService yjxzInfoService;

    /**
     * 查询应急小组名称列表
     */
    // @PreAuthorize("@ss.hasPermi('system:yjxz:list')")
    @GetMapping("/list")
    public AjaxResult list(YjxzInfo yjxzInfo)
    {
        List<YjxzInfo> list = yjxzInfoService.selectYjxzInfoList(yjxzInfo);
        list.stream().forEach(yjxzInfo1 -> {
            if (yjxzInfo1.getPersonName()!=null&&!yjxzInfo1.getPersonName().equals("")){
                String[] split = yjxzInfo1.getPersonName().split(",");
                yjxzInfo1.setPersons(split);
            }
        });
        return AjaxResult.success(list);
    }

    /**
     * 获取应急劝返小组信息
     * @return
     */
    @GetMapping("/getYjqfInfo")
    public AjaxResult getYjqfInfo(){
        List<Map<String,Object>> mapList = yjxzInfoService.getYjqfInfo();
        Map<Object, List<Map<String, Object>>> county = mapList.stream().collect(Collectors.groupingBy(map -> map.get("county"), LinkedHashMap::new, Collectors.toList()));
        List<Map<Object, Object>> finallyMap = new ArrayList<>();
        int i = 0;
        for (Object s : county.keySet()) {
            List<Map<String, Object>> mapList1 = county.get(s);
            Map<Object, Object> map = new HashMap<>();
            map.put("name", s);
            map.put("id",i++);
            map.put("list", mapList1);
            finallyMap.add(map);
        }
        return AjaxResult.success(finallyMap);
    }

    /**
     * 导出应急小组名称列表
     */
    @PreAuthorize("@ss.hasPermi('system:yjxz:export')")
    @Log(title = "应急小组名称", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, YjxzInfo yjxzInfo)
    {
        List<YjxzInfo> list = yjxzInfoService.selectYjxzInfoList(yjxzInfo);
        ExcelUtil<YjxzInfo> util = new ExcelUtil<YjxzInfo>(YjxzInfo.class);
        util.exportExcel(response, list, "应急小组名称数据");
    }

    /**
     * 获取应急小组名称详细信息
     */
    @PreAuthorize("@ss.hasPermi('system:yjxz:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(yjxzInfoService.selectYjxzInfoById(id));
    }

    /**
     * 新增应急小组名称
     */
    @PreAuthorize("@ss.hasPermi('system:yjxz:add')")
    @Log(title = "应急小组名称", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody YjxzInfo yjxzInfo)
    {
        return toAjax(yjxzInfoService.insertYjxzInfo(yjxzInfo));
    }

    /**
     * 修改应急小组名称
     */
    @PreAuthorize("@ss.hasPermi('system:yjxz:edit')")
    @Log(title = "应急小组名称", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody YjxzInfo yjxzInfo)
    {
        return toAjax(yjxzInfoService.updateYjxzInfo(yjxzInfo));
    }

    /**
     * 删除应急小组名称
     */
    @PreAuthorize("@ss.hasPermi('system:yjxz:remove')")
    @Log(title = "应急小组名称", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(yjxzInfoService.deleteYjxzInfoByIds(ids));
    }
}
