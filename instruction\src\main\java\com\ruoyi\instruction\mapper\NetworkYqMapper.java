package com.ruoyi.instruction.mapper;

import java.util.List;
import com.ruoyi.instruction.domain.NetworkYq;

/**
 * 网络舆情Mapper接口
 * 
 * <AUTHOR>
 * @date 2023-09-14
 */
public interface NetworkYqMapper 
{
    /**
     * 查询网络舆情
     * 
     * @param id 网络舆情主键
     * @return 网络舆情
     */
    public NetworkYq selectNetworkYqById(String id);

    /**
     * 查询网络舆情列表
     * 
     * @param networkYq 网络舆情
     * @return 网络舆情集合
     */
    public List<NetworkYq> selectNetworkYqList(NetworkYq networkYq);

    /**
     * 新增网络舆情
     * 
     * @param networkYq 网络舆情
     * @return 结果
     */
    public int insertNetworkYq(NetworkYq networkYq);

    /**
     * 修改网络舆情
     * 
     * @param networkYq 网络舆情
     * @return 结果
     */
    public int updateNetworkYq(NetworkYq networkYq);

    /**
     * 删除网络舆情
     * 
     * @param id 网络舆情主键
     * @return 结果
     */
    public int deleteNetworkYqById(String id);

    /**
     * 批量删除网络舆情
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteNetworkYqByIds(String[] ids);
}
