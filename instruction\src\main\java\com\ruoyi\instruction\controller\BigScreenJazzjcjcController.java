package com.ruoyi.instruction.controller;

import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.examineAir.service.IExamineService;
import com.ruoyi.examineAir.vo.BigScreenPageRegionStatisticsVo;
import com.ruoyi.instruction.domain.InstructionEvent;
import com.ruoyi.instruction.domain.InstructionRealtimeinfo;
import com.ruoyi.instruction.domain.JazzPajhAreaStatistics;
import com.ruoyi.instruction.domain.reqVo.JazzPajhAreaStatisticsVo;
import com.ruoyi.instruction.domain.rspVo.BigScreenJazzCommonVo;
import com.ruoyi.instruction.domain.rspVo.BigScreenJazzPageJadsjCountVo;
import com.ruoyi.instruction.domain.rspVo.BigScreenJazzPageYearCountVo;
import com.ruoyi.instruction.service.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.time.LocalDate;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 金安智治驾驶舱基成基础
 * <AUTHOR> @version 1.0
 * @date 2
 *
 */
@RestController
@RequestMapping("/bigScreen/jazz/jcjc")
public class BigScreenJazzjcjcController extends BaseController {

    @Autowired
    private ITJazzJcjcSpecialWorkService tJazzJcjcSpecialWorkService;

    /**
     * 获取基层基础-专项工作详细信息
     */
    @GetMapping(value = "zxgz/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return AjaxResult.success(tJazzJcjcSpecialWorkService.selectTJazzJcjcSpecialWorkById(id));
    }

    /**
     * 获取基层基础-根据区域查询列表
     */
    @GetMapping(value = "zxgz/list")
    public AjaxResult zxgzListByArea(String area)
    {
        return AjaxResult.success(tJazzJcjcSpecialWorkService.selectByArea(area));
    }




}
