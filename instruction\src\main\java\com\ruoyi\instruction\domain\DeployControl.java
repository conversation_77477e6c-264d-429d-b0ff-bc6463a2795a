package com.ruoyi.instruction.domain;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 失联失控信息对象 t_deploy_control
 *
 * <AUTHOR>
 * @date 2024-11-11
 */
@Data
public class DeployControl extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键id */
    private Long id;

    /** 属地 */
    @Excel(name = "属地")
    private String dutyPlace;

    /** 姓名 */
    @Excel(name = "姓名")
    private String personName;

    /** 责任乡镇 */
    @Excel(name = "责任乡镇")
    private String personTown;

    /** 人员id */
    private Long personId;

    /** 人员等级 -1:无、  0：撤 、1：黄、2：橙  、3：红   */
    @Excel(name = "人员等级",readConverterExp = "-1=无,0=撤档,1=黄,2=橙,3=红")
    private Integer personLevel;

    /** 管控状态 失联、在控 */
    @Excel(name = "管控状态 失联、在控")
    private String controlStatus;

    /** 当前居住地 */
    @Excel(name = "当前居住地")
    private String currentPlace;

    /** 发现时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "发现时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date findTime;

    /** 近3年赴省次数 */
    @Excel(name = "近3年赴省次数")
    private Long threeProvinceCount;

    /** 近3年进京次数 */
    @Excel(name = "近3年进京次数")
    private Long threeCapitalCount;

    /** 处置状态 */
    @Excel(name = "处置状态")
    private String dealStatus;

    /** 布控状态 */
    @Excel(name = "布控状态")
    private String deployStatus;

    /** 指令id */
    private Long instructionId;

    /** 状态 1：正常 9：删除 */
    private String status;


}
