package com.ruoyi.instruction.domain.reqVo;

import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR> @version 1.0
 * @date 2022/12/19 15:40
 * 浙政钉删除用户
 */
@NoArgsConstructor
@Data
public class ZzdApiDeleteEmployeeVo {

    /**
     * accountId
     */
    private int accountId;
    /**
     * userInfo
     */
    private List<UserInfoBean> userInfo;
    /**
     * employeeCodes
     */
    private List<String> employeeCodes;
    /**
     * tenantId
     */
    private int tenantId;
    /**
     * tag
     */
    private String tag;
    /**
     * operator
     */
    private String operator;

    /**
     * UserInfoBean
     */
    @NoArgsConstructor
    @Data
    public static class UserInfoBean {
        /**
         * accountId
         */
        private int accountId;
        /**
         * employeeCode
         */
        private String employeeCode;
    }
}
