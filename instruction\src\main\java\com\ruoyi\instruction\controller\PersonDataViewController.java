package com.ruoyi.instruction.controller;

import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.instruction.domain.rspVo.GroupDataRspVo;
import com.ruoyi.instruction.service.IInstrucationPersonService;
import org.aspectj.weaver.loadtime.Aj;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/4/17 16:27
 */
@RestController
@RequestMapping("/person/dateView")
public class PersonDataViewController {

    @Autowired
    private IInstrucationPersonService personService;

    /**
     * 获取重点人员总数、高、中、低管控级别
     * @return
     */
    @PostMapping("/getPersonData")
    public AjaxResult getPersonData(@RequestBody(required = false) Map<String,Object> map){
        GroupDataRspVo groupDataRspVo = personService.getPersonData(map);
        return AjaxResult.success(groupDataRspVo);
    }

    /**
     * 获取每日新增人数
     * @return
     */
    @GetMapping("/getAddPerson")
    public AjaxResult getAddPerson(){
        List<Map<String,Integer>> maps = personService.getAddPerson();
        return AjaxResult.success(maps);
    }

    /**
     * 获取区域重点人员数量
     * @return
     */
    @GetMapping("/getAreaPersonCount")
    public AjaxResult getAreaPersonCount(){
        List<Map<String,Integer>> maps = personService.getAreaPersonCount();
        return AjaxResult.success(maps);
    }

    /**
     * 获取人员关联事件排名
     * dateType  时间类型。1本日，2本周，3本月，4本年
     * @return
     */
    @GetMapping("/getEventCount")
    public AjaxResult getEventCount(Integer dateType){
        List<Map<String,Object>> maps = personService.getEventCount(dateType);
        return AjaxResult.success(maps);
    }

    /**
     * 获取频繁重点人员
     * @return
     */
    @GetMapping("/getOftenPerson")
    public AjaxResult getOftenPerson(){
        List<Map<String,Integer>> maps = personService.getOftenPerson();
        return AjaxResult.success(maps);
    }
}
