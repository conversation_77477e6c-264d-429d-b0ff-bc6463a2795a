package com.ruoyi.instruction.controller;

import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.constant.Constants;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.domain.entity.SysRole;
import com.ruoyi.common.core.domain.entity.SysUser;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.instruction.domain.InstructionInfo;
import com.ruoyi.instruction.domain.ItemInfo;
import com.ruoyi.instruction.domain.JazzInstructionEvent;
import com.ruoyi.instruction.domain.rspVo.InstructionInfomiddleResVo;
import com.ruoyi.instruction.service.IIndicatorTypeService;
import com.ruoyi.instruction.service.IInstructionInfoService;
import com.ruoyi.instruction.service.IJazzInstructionEventService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.InputStream;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 事件(金安大事件)
 *
 * <AUTHOR>
 * @date 2022-12-16
 */
@RestController
@RequestMapping("/jazzInstruction/event")
public class JazzInstructionEventController extends BaseController {

    @Autowired
    private IJazzInstructionEventService jazzInstructionEventService;

    @Autowired
    private IIndicatorTypeService indicatorTypeService;

    @Autowired
    private IInstructionInfoService instructionInfoService;

    /**
     * 查询金安智治事件基本信息列表
     */
//    @PreAuthorize("@ss.hasPermi('instruction:event:list')")
    @GetMapping("/list")
    public TableDataInfo list(JazzInstructionEvent instructionEvent) {
        Long deptId = SecurityUtils.getDeptId();
        if (!deptId.equals(Constants.JINHUA_CITY_DEPT_ID)) {
            SysUser user = SecurityUtils.getLoginUser().getUser();
            String deptName = user.getDept().getDeptName();
            List<SysRole> roles = user.getRoles();
            List<String> roleList = roles.stream().map(SysRole::getRoleKey).collect(Collectors.toList());
            InstructionInfo instructionInfo = new InstructionInfo();
            List<InstructionInfomiddleResVo> instructionInfoList = instructionInfoService.getInstructionInfoList(deptId, deptName, roleList, instructionInfo);
            List<Long> collect = instructionInfoList.stream().map(InstructionInfomiddleResVo::getId).collect(Collectors.toList());
            Map<String, Object> params = instructionEvent.getParams();
            params.put("instructionIds", collect);
            instructionEvent.setParams(params);
        }
        //查询类型及其下级的
        if (instructionEvent.getType() != null) {
            List<Long> typeIds = indicatorTypeService.findChildenType(instructionEvent.getType());
            typeIds.add(Long.valueOf(instructionEvent.getType()));
            Map<String, Object> params = instructionEvent.getParams();
            params.put("typeIds", typeIds);
            instructionEvent.setParams(params);
        }
        startPage();
        List<JazzInstructionEvent> list = jazzInstructionEventService.selectInstructionEventList(instructionEvent);
        return getDataTable(list);
    }

    /**
     * 导出事件基本信息列表
     */
//    @PreAuthorize("@ss.hasPermi('instruction:event:export')")
    @Log(title = "事件基本信息", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, JazzInstructionEvent instructionEvent) {
        List<JazzInstructionEvent> list = jazzInstructionEventService.selectInstructionEventList(instructionEvent);
        ExcelUtil<JazzInstructionEvent> util = new ExcelUtil<JazzInstructionEvent>(JazzInstructionEvent.class);
        util.exportEasyExcel(response, list, "事件基本信息数据");
    }

    /**
     * 获取事件基本信息详细信息
     */
//    @PreAuthorize("@ss.hasPermi('instruction:event:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id) {
        return jazzInstructionEventService.selectInstructionEventById(id);
    }

    /**
     * 新增事件基本信息
     */
//    @PreAuthorize("@ss.hasPermi('instruction:event:add')")
    @Log(title = "事件基本信息", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody JazzInstructionEvent instructionEvent) {
        instructionEvent.setIsRelease("1");
        return jazzInstructionEventService.insertInstructionEvent(instructionEvent);
    }

    /**
     * 修改事件基本信息
     */
//    @PreAuthorize("@ss.hasPermi('instruction:event:edit')")
    @Log(title = "事件基本信息", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody JazzInstructionEvent instructionEvent) {
        return jazzInstructionEventService.updateInstructionEvent(instructionEvent);
    }

    /**
     * 删除事件基本信息
     */
//    @PreAuthorize("@ss.hasPermi('instruction:event:remove')")
    @Log(title = "事件基本信息", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long ids) {
        return toAjax(jazzInstructionEventService.deleteInstructionEventById(ids));
    }

    /**
     * 通过事件id生成指令
     *
     * @param id
     * @return
     */
    @GetMapping("/addInstruction/{id}")
    public AjaxResult addInstruction(@PathVariable("id") Long id) {
        return toAjax(jazzInstructionEventService.addInstruction(id));
    }


    /**
     * 根据群体id查询关联事件集合
     *
     * @param id
     * @return
     */
    @GetMapping("/getEventListByGroupId/{id}")
    public AjaxResult getEventListByGroupId(@PathVariable("id") Long id) {
        return AjaxResult.success(jazzInstructionEventService.getEventListByGroupId(id));
    }




//    /**
//     * 导入事件数据
//     */
//    @Log(title = "导入事件数据", businessType = BusinessType.IMPORT)
//    @PostMapping("/importData")
//    public AjaxResult importData(MultipartFile file) throws Exception {
//        ExcelUtil<JazzInstructionEvent> util = new ExcelUtil<>(JazzInstructionEvent.class);
//        List<JazzInstructionEvent> list = util.importExcel(file.getInputStream());
//        //获取操作用户
//        String operName = getUsername();
//        //存入数据
//        String msg = jazzInstructionEventService.importData(list, operName);
//        return AjaxResult.success(msg);
//    }


    /**
     * 导入多张sheet数据
     */
    @Log(title = "导入事件数据", businessType = BusinessType.IMPORT)
    @PostMapping("/importMoreSheetData")
    public AjaxResult importMoreSheetData(MultipartFile file) throws Exception {
        String msg = "";
        // 获取文件名
        if (file == null) {
            return AjaxResult.error("文件不能为空");
        }
        String fileName = file.getOriginalFilename();
        if (StringUtils.isEmpty(fileName)) {
            return AjaxResult.error("文件名不能为空");
        }
        // 获取文件后缀
        String prefix = fileName.substring(fileName.lastIndexOf("."));
        if (!prefix.toLowerCase().contains("xls") && !prefix.toLowerCase().contains("xlsx")) {
            return AjaxResult.error("文件格式异常，请上传Excel文件格式");
        }
        //由于2003和2007的版本所使用的接口不一样，所以这里统一用Workbook做兼容
        boolean isExcel2003 = prefix.toLowerCase().endsWith("xls") ? true : false;
        InputStream inputStream = null;
        try {
            inputStream = file.getInputStream();
            msg = jazzInstructionEventService.uploadEventAndPerson(inputStream, isExcel2003);
        } catch (Exception e) {
            return AjaxResult.error("上传失败,原因如下：" + e.getMessage());
        } finally {
            if (inputStream != null) {
                inputStream.close();
            }
        }

        return AjaxResult.success(msg);
    }

    /**
     * 未关联群体事件 同步更新群体id
     * @return
     */
    @GetMapping("/synchronizationGroupId")
    public AjaxResult synchronizationGroupId(){
        int row = jazzInstructionEventService.synchronizationGroupId();
        return toAjax(row);
    }

}
