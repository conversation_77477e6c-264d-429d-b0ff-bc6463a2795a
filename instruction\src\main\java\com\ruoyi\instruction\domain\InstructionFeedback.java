package com.ruoyi.instruction.domain;

import java.util.Date;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 指令反馈对象 t_instruction_feedback
 *
 * <AUTHOR>
 * @date 2022-12-19
 */
@Data
public class InstructionFeedback extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /**
     * 反馈表主键
     */
    private Long id;

    /**
     * 处置部门
     */
    @Excel(name = "处置部门")
    private String feedbackDept;

    /**
     * 反馈时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "反馈时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date feedbackTime;

    /**
     * 反馈人员
     */
    @Excel(name = "反馈人员")
    private String feedbackBy;

    /**
     * 是否办结 1：已办结 2：未办结
     */
    @Excel(name = "是否办结 1：已办结 2：未办结")
    private Integer feedbackIsEnd;

    /**
     * 情况反馈
     */
    @Excel(name = "情况反馈")
    private String feedbackSituation;

    /**
     * 处置表id
     */
    @Excel(name = "处置表id")
    private Long disposeId;

    /**
     * 文件ids
     */
    @Excel(name = "文件ids")
    private String fileIds;

    @Excel(name = "指令id")
    private Long instructionId;

    /**
     * 1:正常 9：删除
     */
    @Excel(name = "1:正常 9：删除")
    private Integer status;


    /**
     * 转交id
     */
    @Excel(name = "转交id")
    private Long transferId;

    /** 接收时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date receiveTime;

    /** 反馈部门id */
    @Excel(name = "反馈部门id")
    private Long feedbackDeptId;

    /** 接收id */
    @Excel(name = "接收id")
    private Long receiveId;

    /** 反馈要求 1：见面 2：电话 3：短信 4：其他 */
    @Excel(name = "反馈方式 1：见面 2：电话 3：短信 4：其他")
    private Long feedbackRequire;

    /** 预估完成时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "预估完成时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date forecastTime;


    /** 是否为mzx 1:是 2:不是 */
    private Long isMzx;

    /**
     * 驳回 0:不可驳回 1:可以驳回
     */
    private Integer turnDown = 0;

    /**
     * 撤回 0:不可撤回 1:可以撤回
     */
    private Integer recall = 0;

    /**
     * 稳控状态
     */
    private String wkStatus;

    /**
     * 间隔时间
     */
    private Integer intervalDays;

    /** 驳回原因 */
    private String rejectReason;

    /** 驳回人 */
    private String rejectBy;

    /** 驳回时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date rejectTime;
}
