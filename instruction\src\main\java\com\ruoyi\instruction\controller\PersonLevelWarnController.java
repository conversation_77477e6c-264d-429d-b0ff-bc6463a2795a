package com.ruoyi.instruction.controller;

import java.util.List;
import java.util.stream.Collectors;
import javax.servlet.http.HttpServletResponse;

import com.ruoyi.common.constant.InstructionRolesConstants;
import com.ruoyi.common.core.domain.entity.SysDept;
import com.ruoyi.common.core.domain.entity.SysRole;
import com.ruoyi.common.utils.DeptUtils;
import com.ruoyi.common.utils.SecurityUtils;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.instruction.domain.PersonLevelWarn;
import com.ruoyi.instruction.service.IPersonLevelWarnService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 人员调档预警Controller
 *
 * <AUTHOR>
 * @date 2024-10-22
 */
@RestController
@RequestMapping("/personLevel/warn")
public class PersonLevelWarnController extends BaseController
{
    @Autowired
    private IPersonLevelWarnService personLevelWarnService;

    /**
     * 查询人员调档预警列表
     */
    @PreAuthorize("@ss.hasPermi('personLevel:warn:list')")
    @GetMapping("/list")
    public TableDataInfo list(PersonLevelWarn personLevelWarn)
    {

        //判断用户所属那个部门
        List<SysRole> roles = SecurityUtils.getLoginUser().getUser().getRoles();
        List<String> roleList = roles.stream().map(SysRole::getRoleKey).collect(Collectors.toList());
        if (!roleList.contains(InstructionRolesConstants.CITY_INSTRUCTION)) {
            //查询用户父级部门所在县市区 县市区仅能查看自己所属区域的人员
            SysDept dept = SecurityUtils.getLoginUser().getUser().getDept();
            String dutyPlace = DeptUtils.getDutyPlaceByDept(dept);
            personLevelWarn.setDutyPlace(dutyPlace);
            if (dept.getParentId() == 213L || dept.getParentId() == 214L || dept.getParentId() == 215L || dept.getParentId() == 216L || dept.getParentId() == 217L || dept.getParentId() == 218L || dept.getParentId() == 219L || dept.getParentId() == 220L || dept.getParentId() == 221L || dept.getParentId() == 262L) {

            }else {
                personLevelWarn.setPersonTown(dept.getDeptName());
            }
        }
        startPage();
        List<PersonLevelWarn> list = personLevelWarnService.selectPersonLevelWarnList(personLevelWarn);
        return getDataTable(list);
    }

    /**
     * 导出人员调档预警列表
     */
    @PreAuthorize("@ss.hasPermi('personLevel:warn:export')")
    @Log(title = "人员调档预警", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, PersonLevelWarn personLevelWarn)
    {
        List<PersonLevelWarn> list = personLevelWarnService.selectPersonLevelWarnList(personLevelWarn);
        ExcelUtil<PersonLevelWarn> util = new ExcelUtil<PersonLevelWarn>(PersonLevelWarn.class);
        util.exportExcel(response, list, "人员调档预警数据");
    }

    /**
     * 获取人员调档预警详细信息
     */
    @PreAuthorize("@ss.hasPermi('personLevel:warn:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(personLevelWarnService.selectPersonLevelWarnById(id));
    }

    /**
     * 新增人员调档预警
     */
    @PreAuthorize("@ss.hasPermi('personLevel:warn:add')")
    @Log(title = "人员调档预警", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody PersonLevelWarn personLevelWarn)
    {
        return toAjax(personLevelWarnService.insertPersonLevelWarn(personLevelWarn));
    }

    /**
     * 修改人员调档预警
     */
    @PreAuthorize("@ss.hasPermi('personLevel:warn:edit')")
    @Log(title = "人员调档预警", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody PersonLevelWarn personLevelWarn)
    {
        return toAjax(personLevelWarnService.updatePersonLevelWarn(personLevelWarn));
    }

    /**
     * 删除人员调档预警
     */
    @PreAuthorize("@ss.hasPermi('personLevel:warn:remove')")
    @Log(title = "人员调档预警", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(personLevelWarnService.deletePersonLevelWarnByIds(ids));
    }
}
