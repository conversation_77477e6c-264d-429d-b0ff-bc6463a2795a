package com.ruoyi.instruction.controller;

import java.util.List;
import java.util.Map;
import javax.servlet.http.HttpServletResponse;

import com.ruoyi.common.constant.CommonConstant;
import com.ruoyi.common.utils.SecurityUtils;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.instruction.domain.AssignInfo;
import com.ruoyi.instruction.service.IAssignInfoService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 交办单信息Controller
 *
 * <AUTHOR>
 * @date 2023-05-18
 */
@RestController
@RequestMapping("/instruction/assignInfo")
public class AssignInfoController extends BaseController {
    @Autowired
    private IAssignInfoService assignInfoService;

    /**
     * 查询交办单信息列表
     */
    @PreAuthorize("@ss.hasPermi('instruction:assignInfo:list')")
    @GetMapping("/list")
    public TableDataInfo list(AssignInfo assignInfo) {
        Long deptId = SecurityUtils.getDeptId();
        if (!CommonConstant.ZFW_DEPT_ID.equals(deptId)) {
            assignInfo.setCreateDeptId(deptId);

        }
        startPage();
        List<AssignInfo> list = assignInfoService.selectAssignInfoList(assignInfo);
        return getDataTable(list);
    }

    /**
     * 导出交办单信息列表
     */
    @PreAuthorize("@ss.hasPermi('instruction:assignInfo:export')")
    @Log(title = "交办单信息", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, AssignInfo assignInfo) {
        Long deptId = SecurityUtils.getDeptId();
        if (!CommonConstant.ZFW_DEPT_ID.equals(deptId)) {
            assignInfo.setCreateDeptId(deptId);

        }
        List<AssignInfo> list = assignInfoService.selectAssignInfoList(assignInfo);
        ExcelUtil<AssignInfo> util = new ExcelUtil<AssignInfo>(AssignInfo.class);
        util.exportExcel(response, list, "交办单信息数据");
    }

    /**
     * 获取交办单信息详细信息
     */
    @PreAuthorize("@ss.hasPermi('instruction:assignInfo:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id) {
        return success(assignInfoService.selectAssignInfoById(id));
    }

    /**
     * 新增交办单信息
     */
    @PreAuthorize("@ss.hasPermi('instruction:assignInfo:add')")
    @Log(title = "交办单信息", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody AssignInfo assignInfo) {
        return toAjax(assignInfoService.insertAssignInfo(assignInfo));
    }

    /**
     * 修改交办单信息
     */
    @PreAuthorize("@ss.hasPermi('instruction:assignInfo:edit')")
    @Log(title = "交办单信息", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody AssignInfo assignInfo) {
        return toAjax(assignInfoService.updateAssignInfo(assignInfo));
    }

    /**
     * 删除交办单信息
     */
    @PreAuthorize("@ss.hasPermi('instruction:assignInfo:remove')")
    @Log(title = "交办单信息", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids) {
        return toAjax(assignInfoService.deleteAssignInfoByIds(ids));
    }

    /**
     * 获取三单列表
     *
     * @return
     */
    @GetMapping("/getThreeOrders")
    public AjaxResult getThreeOrders() {
        return assignInfoService.getThreeOrders();
    }

    /**
     * 更新接收状态
     *
     * @return
     */
    @PutMapping("/updateReceiveStatus")
    public AjaxResult updateReceiveStatus(Integer type, Long id) {
        int row = assignInfoService.updateReceiveStatus(type, id);
        return row > 0 ? AjaxResult.success() : AjaxResult.error();
    }

    /**
     * 根据交办单id获取督办、提醒信息
     * @param map
     * @return
     */
    @PostMapping("/getBaseInfo")
    public AjaxResult getBaseInfo(@RequestBody(required = false) Map<String,Object> map){
        Map<String,Object> result = assignInfoService.getInfo(map);
        return AjaxResult.success(result);
    }
}
