package com.ruoyi.instruction.domain.rspVo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.Date;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/12/26 14:41
 */
@Data
public class ProblemBuildExport extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    private Long id;

    /**
     * 风险排查主键id
     */
    private Long riskId;


    /**
     * 类型 1：常规  2：问题楼盘
     */
    private String riskName;

    /** 标题 */
    @Excel(name = "标题(必填)")
    @NotNull(message = "标题不能为空")
    private String title;

    @Excel(name = "排查类别(必填)")
    @NotNull(message = "排查类别不能为空")
    private String checkName;

    /** 问题类型 */
    @Excel(name = "问题类型")
    private String problemCategory;

    /** 风险等级 1：重大  2：高  3：中  4：低 */
    @Excel(name = "风险等级(必填)",readConverterExp = "1=重大,2=高,3=中,4=低",combo = {"重大","高","中","低"})
    @NotNull(message = "风险等级不能为空")
    private String level;

    /** 涉及金额 */
    @Excel(name = "涉及金额")
    private String amount;

    /** 涉及人数 */
    @Excel(name = "涉及人数")
    private Long households;

    /** 基本情况 */
    @Excel(name = "基本情况(必填)")
    @NotNull(message = "基本情况不能为空")
    private String basicSituation;

    /** 主要诉求 */
    @Excel(name = "主要诉求(必填)")
    @NotNull(message = "主要诉求不能为空")
    private String mainApply;

    /** 责任单位 */
    @Excel(name = "属地(必填)")
    @NotNull(message = "属地不能为空")
    private String dutyUnit;

    /** 所属区域名称 */
    @Excel(name = "责任乡镇(必填)")
    @NotNull(message = "责任乡镇不能为空")
    private String areaName;

    /** 责任人 */
    @Excel(name = "责任人(必填)")
    @NotNull(message = "责任人不能为空")
    private String dutyPerson;

    /** 填报单位 */
    @Excel(name = "填报部门(必填)")
    @NotNull(message = "填报部门不能为空")
    private String fillUnit;

    /** 填报人 */
    @Excel(name = "填报人(必填)")
    @NotNull(message = "填报人不能为空")
    private String fillPerson;

    /** 挂牌督办 */
    @Excel(name = "挂牌督办")
    private String handel;

    /** 化解情况 1: 未化解  2：已化解 */
    @Excel(name = "化解情况(必填)",readConverterExp = "1=未化解,2=已化解",combo = {"未化解","已化解"})
    @NotNull(message = "化解情况不能为空")
    private String dissolution;

    /** 化解时限 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "化解时限(必填)(格式:yyyy-MM-dd)", width = 30, dateFormat = "yyyy-MM-dd")
    @NotNull(message = "化解时限不能为空")
    private Date dissolutionTime;

    /**
     * 正式化解措施
     */
    // @Excel(name = "整治化解措施")
    private String measure;

    /**
     * 信访情况
     */
    // @Excel(name = "信访情况")
    private String petition;


    /**
     * 敏感程度 1：无  2：内部
     */
    private String sensitiveStr;

    /**
     * 排查类别大类
     */
    private Long checkBig;

    /**
     * 排查类别小类
     */
    private Long checkMin;



    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    /**
     * 附件ids
     */
    private String fileIds;



    /**
     * 状态 1：正常 9：删除
     */
    private String status;


    /**
     * 所属区域部门id
     */
    private Long areaDept;

    /**
     * 创建部门
     */
    private Long createDeptId;


    /**
     * 年度
     */
    private String yearStr;
}

