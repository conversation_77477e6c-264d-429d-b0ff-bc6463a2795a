package com.ruoyi.instruction.mapper;

import java.util.List;
import com.ruoyi.instruction.domain.RemindRecord;

/**
 * 工作通知记录Mapper接口
 *
 * <AUTHOR>
 * @date 2024-04-10
 */
public interface RemindRecordMapper
{
    /**
     * 查询工作通知记录
     *
     * @param id 工作通知记录主键
     * @return 工作通知记录
     */
    public RemindRecord selectRemindRecordById(Long id);

    /**
     * 查询工作通知记录列表
     *
     * @param remindRecord 工作通知记录
     * @return 工作通知记录集合
     */
    public List<RemindRecord> selectRemindRecordList(RemindRecord remindRecord);

    /**
     * 新增工作通知记录
     *
     * @param remindRecord 工作通知记录
     * @return 结果
     */
    public int insertRemindRecord(RemindRecord remindRecord);

    /**
     * 修改工作通知记录
     *
     * @param remindRecord 工作通知记录
     * @return 结果
     */
    public int updateRemindRecord(RemindRecord remindRecord);

    /**
     * 删除工作通知记录
     *
     * @param id 工作通知记录主键
     * @return 结果
     */
    public int deleteRemindRecordById(Long id);

    /**
     * 批量删除工作通知记录
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteRemindRecordByIds(Long[] ids);

    /**
     * 查询分组后的数据
     * @param remindRecord
     * @return
     */
    List<RemindRecord> findRemindRecordList(RemindRecord remindRecord);
}
