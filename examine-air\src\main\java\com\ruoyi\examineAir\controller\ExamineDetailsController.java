package com.ruoyi.examineAir.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.examineAir.domain.ExamineDetails;
import com.ruoyi.examineAir.service.IExamineDetailsService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 考核晾晒详情Controller
 * 
 * <AUTHOR>
 * @date 2023-04-19
 */
@RestController
@RequestMapping("/examineAir/examineDetails")
public class ExamineDetailsController extends BaseController
{
    @Autowired
    private IExamineDetailsService examineDetailsService;

    /**
     * 查询考核晾晒详情列表
     */
    @PreAuthorize("@ss.hasPermi('examineAir:examineDetails:list')")
    @GetMapping("/list")
    public TableDataInfo list(ExamineDetails examineDetails)
    {
        startPage();
        List<ExamineDetails> list = examineDetailsService.selectExamineDetailsList(examineDetails);
        return getDataTable(list);
    }

    /**
     * 导出考核晾晒详情列表
     */
    @PreAuthorize("@ss.hasPermi('examineAir:examineDetails:export')")
    @Log(title = "考核晾晒详情", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, ExamineDetails examineDetails)
    {
        List<ExamineDetails> list = examineDetailsService.selectExamineDetailsList(examineDetails);
        ExcelUtil<ExamineDetails> util = new ExcelUtil<ExamineDetails>(ExamineDetails.class);
        util.exportExcel(response, list, "考核晾晒详情数据");
    }

    /**
     * 获取考核晾晒详情详细信息
     */
    @PreAuthorize("@ss.hasPermi('examineAir:examineDetails:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(examineDetailsService.selectExamineDetailsById(id));
    }

    /**
     * 新增考核晾晒详情
     */
    @PreAuthorize("@ss.hasPermi('examineAir:examineDetails:add')")
    @Log(title = "考核晾晒详情", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody ExamineDetails examineDetails)
    {
        return toAjax(examineDetailsService.insertExamineDetails(examineDetails));
    }

    /**
     * 修改考核晾晒详情
     */
    @PreAuthorize("@ss.hasPermi('examineAir:examineDetails:edit')")
    @Log(title = "考核晾晒详情", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody ExamineDetails examineDetails)
    {
        return toAjax(examineDetailsService.updateExamineDetails(examineDetails));
    }

    /**
     * 删除考核晾晒详情
     */
    @PreAuthorize("@ss.hasPermi('examineAir:examineDetails:remove')")
    @Log(title = "考核晾晒详情", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(examineDetailsService.deleteExamineDetailsByIds(ids));
    }
}
