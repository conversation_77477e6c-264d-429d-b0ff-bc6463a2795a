package com.ruoyi.instruction.controller;

import java.time.LocalDate;
import java.util.List;
import java.util.Map;
import javax.servlet.http.HttpServletResponse;

import com.ruoyi.instruction.domain.DailyNews;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.instruction.domain.ProvinceYj;
import com.ruoyi.instruction.service.IProvinceYjService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 省级预警Controller
 * 
 * <AUTHOR>
 * @date 2023-09-21
 */
@RestController
@RequestMapping("/province/yj")
public class ProvinceYjController extends BaseController
{
    @Autowired
    private IProvinceYjService provinceYjService;

    /**
     * 查询省级预警列表
     */
    @PreAuthorize("@ss.hasPermi('province:yj:list')")
    @GetMapping("/list")
    public TableDataInfo list(ProvinceYj provinceYj)
    {
        String dateStr = provinceYj.getDateStr();
        if (dateStr!=null&&!dateStr.equals("")){
            String replace = dateStr.replace("年", "-").replace("月", "");
            Map<String, Object> params = provinceYj.getParams();
            int daysOfMonth = getDaysOfMonth(replace);
            params.put("beginTime",replace+"-01");
            params.put("endTime",replace+"-"+daysOfMonth);
        }
        startPage();
        List<ProvinceYj> list = provinceYjService.selectProvinceYjList(provinceYj);
        return getDataTable(list);
    }

    public static int getDaysOfMonth(String dateStr) {
        LocalDate date = LocalDate.parse(dateStr + "-01");
        return date.lengthOfMonth();
    }

    /**
     * 获取月份
     * @param provinceYj
     * @return
     */
    @GetMapping("/getMonth")
    public AjaxResult getMonth(ProvinceYj provinceYj)
    {
        List<String> list = provinceYjService.getMonth(provinceYj);
        return AjaxResult.success(list);
    }

    /**
     * 导出省级预警列表
     */
    @PreAuthorize("@ss.hasPermi('province:yj:export')")
    @Log(title = "省级预警", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, ProvinceYj provinceYj)
    {
        List<ProvinceYj> list = provinceYjService.selectProvinceYjList(provinceYj);
        ExcelUtil<ProvinceYj> util = new ExcelUtil<ProvinceYj>(ProvinceYj.class);
        util.exportExcel(response, list, "省级预警数据");
    }

    /**
     * 获取省级预警详细信息
     */
    @PreAuthorize("@ss.hasPermi('province:yj:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return AjaxResult.success(provinceYjService.selectProvinceYjById(id));
    }

    /**
     * 新增省级预警
     */
    @PreAuthorize("@ss.hasPermi('province:yj:add')")
    @Log(title = "省级预警", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody ProvinceYj provinceYj)
    {
        return toAjax(provinceYjService.insertProvinceYj(provinceYj));
    }

    /**
     * 修改省级预警
     */
    @PreAuthorize("@ss.hasPermi('province:yj:edit')")
    @Log(title = "省级预警", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody ProvinceYj provinceYj)
    {
        return toAjax(provinceYjService.updateProvinceYj(provinceYj));
    }

    /**
     * 删除省级预警
     */
    @PreAuthorize("@ss.hasPermi('province:yj:remove')")
    @Log(title = "省级预警", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(provinceYjService.deleteProvinceYjByIds(ids));
    }


    /**
     * 新增修改省级预警
     */
    @Log(title = "省级预警", businessType = BusinessType.INSERT)
    @PostMapping("/addAndUpdate")
    public AjaxResult addAndUpdate(@RequestBody ProvinceYj provinceYj)
    {
        AjaxResult ajaxResult = provinceYjService.addAndUpdate(provinceYj);
        return ajaxResult;
    }
}
