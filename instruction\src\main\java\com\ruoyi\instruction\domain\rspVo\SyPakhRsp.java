package com.ruoyi.instruction.domain.rspVo;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * 首页平安考核
 * 
 * <AUTHOR>
 * @date 2023-08-07
 */
@Data
public class SyPakhRsp implements Serializable
{
    private static final long serialVersionUID = 1L;
    /**
     * 县市区
     */
    private String county;
    /**
     * 平安指数排名
     */
    private Integer pazsRank;
    /**
     * 平安指数
     */
    private BigDecimal pazs;

    /**
     * 历史最高排名
     */
    private Integer lszgRank;
    /**
     * 领域问题top10
     */
    private List<PmTopRsp> lywt;
    /**
     * 各县市问题排名
     */
    private List<PmTopRsp> xswtpm;
}
