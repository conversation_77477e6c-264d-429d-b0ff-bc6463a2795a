package com.ruoyi.instruction.domain.reqVo;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.HashMap;
import java.util.List;

/**
 * <AUTHOR> @version 1.0
 * @date 2022/12/19 15:40
 * 浙政钉添加和修改用户
 */
@NoArgsConstructor
@Data
public class ZzdApiAddUpdateEmployeeVo {


    /**
     * accountId
     */
    private int accountId;
    /**
     * userInfo
     */
    private List<UserInfoBean> userInfo;
    /**
     * employeeChangeType
     */
    private String employeeChangeType;
    /**
     * employeeCodes
     */
    private List<String> employeeCodes;
    /**
     * employeeChangeMoMap
     */
    private HashMap<String,EmployeeChangeMoBean> employeeChangeMoMap;
    /**
     * tenantId
     */
    private int tenantId;
    /**
     * tag
     */
    private String tag;

    /**
     * EmployeeChangeMoMapBean
     */
    @NoArgsConstructor
    @Data
    public static class EmployeeChangeMoBean {

            /**
             * currEmployee
             */
            private CurrEmployeeBean currEmployee;
            /**
             * prevEmployee
             */
            private PrevEmployeeBean prevEmployee;
            /**
             * prevStatus
             */
            private String prevStatus;
            /**
             * currStatus
             */
            private String currStatus;
            /**
             * currEmployeeName
             */
            private String currEmployeeName;
            /**
             * prevEmployeeName
             */
            private String prevEmployeeName;

            /**
             * CurrEmployeeBean
             */
            @NoArgsConstructor
            @Data
            public static class CurrEmployeeBean {
                /**
                 * employeeName
                 */
                private String employeeName;
                /**
                 * creator
                 */
                private String creator;
                /**
                 * gmtModified
                 */
                private long gmtModified;
                /**
                 * modifier
                 */
                private String modifier;
                /**
                 * id
                 */
                private int id;
                /**
                 * gmtCreate
                 */
                private long gmtCreate;
                /**
                 * govaffairsBaseProperties
                 */
                private GovaffairsBasePropertiesBean govaffairsBaseProperties;
                /**
                 * employeeCode
                 */
                private String employeeCode;
                /**
                 * status
                 */
                private String status;

                /**
                 * GovaffairsBasePropertiesBean
                 */
                @NoArgsConstructor
                @Data
                public static class GovaffairsBasePropertiesBean {
                    /**
                     * govEmpCellPhoneNo
                     */
                    private String govEmpCellPhoneNo;
                    /**
                     * govEmpCellPhoneAreaCode
                     */
                    private String govEmpCellPhoneAreaCode;
                    /**
                     * govEmpJobLevel
                     */
                    private GovEmpJobLevelBean govEmpJobLevel;
                    /**
                     * extProps
                     */
                    private ExtPropsBean extProps;
                    /**
                     * govEmpBudgetedPost
                     */
                    private GovEmpBudgetedPostBean govEmpBudgetedPost;
                    /**
                     * govEmpGender
                     */
                    private GovEmpGenderBean govEmpGender;

                    /**
                     * GovEmpJobLevelBean
                     */
                    @NoArgsConstructor
                    @Data
                    public static class GovEmpJobLevelBean {
                    }

                    /**
                     * ExtPropsBean
                     */
                    @NoArgsConstructor
                    @Data
                    public static class ExtPropsBean {
                    }

                    /**
                     * GovEmpBudgetedPostBean
                     */
                    @NoArgsConstructor
                    @Data
                    public static class GovEmpBudgetedPostBean {
                        /**
                         * code
                         */
                        private String code;
                        /**
                         * name
                         */
                        private String name;
                    }

                    /**
                     * GovEmpGenderBean
                     */
                    @NoArgsConstructor
                    @Data
                    public static class GovEmpGenderBean {
                        /**
                         * code
                         */
                        private String code;
                        /**
                         * name
                         */
                        private String name;
                    }
                }
            }

            /**
             * PrevEmployeeBean
             */
            @NoArgsConstructor
            @Data
            public static class PrevEmployeeBean {
                /**
                 * employeeName
                 */
                private String employeeName;
                /**
                 * creator
                 */
                private String creator;
                /**
                 * gmtModified
                 */
                private long gmtModified;
                /**
                 * modifier
                 */
                private String modifier;
                /**
                 * id
                 */
                private int id;
                /**
                 * gmtCreate
                 */
                private long gmtCreate;
                /**
                 * govaffairsBaseProperties
                 */
                private GovaffairsBasePropertiesBean govaffairsBaseProperties;
                /**
                 * employeeCode
                 */
                private String employeeCode;
                /**
                 * status
                 */
                private String status;

                /**
                 * GovaffairsBasePropertiesBean
                 */
                @NoArgsConstructor
                @Data
                public static class GovaffairsBasePropertiesBean {
                    /**
                     * govEmpCellPhoneNo
                     */
                    private String govEmpCellPhoneNo;
                    /**
                     * govEmpCellPhoneAreaCode
                     */
                    private String govEmpCellPhoneAreaCode;
                    /**
                     * govEmpJobLevel
                     */
                    private GovEmpJobLevelBean govEmpJobLevel;
                    /**
                     * extProps
                     */
                    private ExtPropsBean extProps;
                    /**
                     * govEmpBudgetedPost
                     */
                    private GovEmpBudgetedPostBean govEmpBudgetedPost;
                    /**
                     * govEmpGender
                     */
                    private GovEmpGenderBean govEmpGender;

                    /**
                     * GovEmpJobLevelBean
                     */
                    @NoArgsConstructor
                    @Data
                    public static class GovEmpJobLevelBean {
                    }

                    /**
                     * ExtPropsBean
                     */
                    @NoArgsConstructor
                    @Data
                    public static class ExtPropsBean {
                    }

                    /**
                     * GovEmpBudgetedPostBean
                     */
                    @NoArgsConstructor
                    @Data
                    public static class GovEmpBudgetedPostBean {
                        /**
                         * code
                         */
                        private String code;
                        /**
                         * name
                         */
                        private String name;
                    }

                    /**
                     * GovEmpGenderBean
                     */
                    @NoArgsConstructor
                    @Data
                    public static class GovEmpGenderBean {
                        /**
                         * code
                         */
                        private String code;
                        /**
                         * name
                         */
                        private String name;
                    }
                }
            }
        }


    /**
     * UserInfoBean
     */
    @NoArgsConstructor
    @Data
    public static class UserInfoBean {
        /**
         * accountId
         */
        private int accountId;
        /**
         * employeeCode
         */
        private String employeeCode;
    }
}
