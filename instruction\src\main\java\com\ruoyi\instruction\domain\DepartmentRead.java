package com.ruoyi.instruction.domain;

import java.util.Date;
import java.util.List;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 部门协同已读/未读对象 t_department_read
 *
 * <AUTHOR>
 * @date 2023-10-10
 */
@Data
public class DepartmentRead extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键 */
    private Long id;

    /** 指令id */
    @Excel(name = "指令id")
    private Long instructionId;

    /** 部门id */
    @Excel(name = "部门id")
    private Long deptId;

    /** 部门名称 */
    @Excel(name = "部门名称")
    private String deptName;

    /** 已读时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "已读时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date readTime;

    /** 状态 1：正常 9：删除 */
    @Excel(name = "状态 1：正常 9：删除")
    private String status;

    /**
     * 推送时间
     */
    private Date pushTime;

    /**
     * 部门ids
     */
    private List<Long> deptIds;


    /**
     * 页码
     */
    private Integer start;

    /**
     * 每页显示条数
     */
    private Integer end;

}
