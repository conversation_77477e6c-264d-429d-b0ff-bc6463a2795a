package com.ruoyi.instruction.domain.rspVo;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.common.annotation.Excel;
import lombok.Data;

import java.util.Date;

/**
 * 应处置未处置
 * <AUTHOR>
 * @version 1.0
 * @date 2025/5/8 14:43
 */
@Data
public class ToFeedbackRsp {


    /**
     * 指令主键
     */
    private Long id;


    /**
     * 指令标题
     */
    @ExcelProperty("序号")
    private String instructionTitle;


    /**
     * 紧急程度
     */
    @ExcelProperty("紧急程度")
    @ColumnWidth(20)
    private String emergencyDegree;

    /** 指令类型 1：维稳 2：平安 3：基层 */
    @ExcelProperty("指令类型")
    @ColumnWidth(20)
    private String instructionType;


    @ExcelProperty("交办时间")
    @ColumnWidth(20)
    private Date assignTime;

    /**
     * 办理期限
     */
    @ExcelProperty("办理期限")
    @ColumnWidth(20)
    private Date handleTime;


    /** 反馈部门 */
    @ExcelProperty("应处置未处置单位")
    @ColumnWidth(20)
    private String feedbackDept;

    /**
     * 类型
     */
    @ExcelProperty("类型")
    @ColumnWidth(20)
    private String isMzx;


    /**
     * 接收不力
     */
    @ExcelProperty("上级单位(流转的单位)")
    @ColumnWidth(20)
    private String receiveDept;

    @ExcelProperty("最新一次处置时间")
    @ColumnWidth(20)
    private Date feedbackTime;


}
