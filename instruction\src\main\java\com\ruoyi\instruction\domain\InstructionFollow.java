package com.ruoyi.instruction.domain;

import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 用户关注指令信息对象 t_instruction_follow
 *
 * <AUTHOR>
 * @date 2024-12-31
 */
@Data
public class InstructionFollow extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键id */
    private Long id;

    /** 用户id */
    @Excel(name = "用户id")
    private Long userId;

    /** 关注指令ids */
    @Excel(name = "关注指令ids")
    private String instructionIds;

    /**
     * 1：关注，2：取消关注
     */
    private Integer type;
}
