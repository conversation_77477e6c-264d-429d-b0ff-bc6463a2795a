package com.ruoyi.instruction.mapper;



import com.ruoyi.instruction.domain.InstructionEventpost;
import org.apache.ibatis.annotations.Param;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * 【请填写功能名称】Mapper接口
 *
 * <AUTHOR>
 * @date 2023-07-21
 */
public interface InstructionEventpostMapper
{
    /**
     * 查询【请填写功能名称】
     *
     * @param id 【请填写功能名称】主键
     * @return 【请填写功能名称】
     */
    public InstructionEventpost selectInstructionEventpostById(String id);

    /**
     * 查询【请填写功能名称】列表
     *
     * @param instructionEventpost 【请填写功能名称】
     * @return 【请填写功能名称】集合
     */
    public List<InstructionEventpost> selectInstructionEventpostList(InstructionEventpost instructionEventpost);

    /**
     * 新增【请填写功能名称】
     *
     * @param instructionEventpost 【请填写功能名称】
     * @return 结果
     */
    public int insertInstructionEventpost(InstructionEventpost instructionEventpost);

    /**
     * 修改【请填写功能名称】
     *
     * @param instructionEventpost 【请填写功能名称】
     * @return 结果
     */
    public int updateInstructionEventpost(InstructionEventpost instructionEventpost);

    /**
     * 删除【请填写功能名称】
     *
     * @param id 【请填写功能名称】主键
     * @return 结果
     */
    public int deleteInstructionEventpostById(String id);

    /**
     * 批量删除【请填写功能名称】
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteInstructionEventpostByIds(String[] ids);


    /**
     * 查询已处置指令、事件id
     * @return
     */
    List<Map<String, Object>> findDealInfo();

    /**
     * 查询网格直报总数及已处理数
     * @return
     */
    Map<String, Object> getForeWarnCount();

    /**
     * 统计维稳事件已处理、未处理数
     * @return
     */
    Map<String, Object> getDealCountForYiDong();
}
