package com.ruoyi.instruction.mapper;

import java.util.List;
import com.ruoyi.instruction.domain.InstructionTransfer;
import org.apache.ibatis.annotations.Param;

/**
 * 指令转接Mapper接口
 * 
 * <AUTHOR>
 * @date 2022-12-19
 */
public interface InstructionTransferMapper 
{
    /**
     * 查询指令转接
     * 
     * @param id 指令转接主键
     * @return 指令转接
     */
    public InstructionTransfer selectInstructionTransferById(Long id);

    /**
     * 查询指令转接列表
     * 
     * @param instructionTransfer 指令转接
     * @return 指令转接集合
     */
    public List<InstructionTransfer> selectInstructionTransferList(InstructionTransfer instructionTransfer);

    /**
     * 新增指令转接
     * 
     * @param instructionTransfer 指令转接
     * @return 结果
     */
    public int insertInstructionTransfer(InstructionTransfer instructionTransfer);

    /**
     * 修改指令转接
     * 
     * @param instructionTransfer 指令转接
     * @return 结果
     */
    public int updateInstructionTransfer(InstructionTransfer instructionTransfer);

    /**
     * 删除指令转接
     * 
     * @param id 指令转接主键
     * @return 结果
     */
    public int deleteInstructionTransferById(Long id);

    /**
     * 批量删除指令转接
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteInstructionTransferByIds(Long[] ids);

    /**
     * 转交人 通过部门名称查询可操作哪些指令ids
     * @param deptName
     * @return
     */
    List<Long> selectInstructionIds(@Param("deptName") String deptName);

    /**
     * 转交员查询能看到的信息（根据转交员部门,指令id）
     * @param deptName
     * @param id
     * @return
     */
    InstructionTransfer findTransferByDeptAndInstructionid(@Param("deptName") String deptName,@Param("id") Long id);

    /**
     * 根据指令id查询转交记录
     * @param infoId
     * @return
     */
    List<InstructionTransfer> selectInstructionTransferByInstructionId(Long infoId);

    /**
     * 根据指令id查询已流转部门
     * @param id
     * @return
     */
    String selectTransferDeptByInstructionId(@Param("id") Long id);

    /**
     * 根据指令id删除转交记录
     * @param id
     */
    void deleteInstructionTransferByInstructionId(@Param("id") Long id);

    /**
     * 根据接收id删除转交记录
     * @param id
     */
    void deleteInstructionTransferByReceiveId(@Param("receiveId") Long id);
}
