package com.ruoyi.instruction.controller;

import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.helper.DingtalkHelper;
import com.ruoyi.common.reqvo.DingVo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 发送工作通知测试
 */
@RestController
@RequestMapping("/testSend")
public class TestSendController {

    @Autowired
    private DingtalkHelper dingtalkHelper;

    /**
     * 发送工作通知
     * @param phones
     * @param msg
     * @return
     */
    @GetMapping("/msg")
    public AjaxResult eventCount(@RequestParam("phones") List<String> phones,String msg){
        dingtalkHelper.sendWorkNotificationMsg(phones,msg);
        return AjaxResult.success();
    }

    /**
     * 发送ding消息
     * @param dingVo
     * @return
     */
    @GetMapping("/ding")
    public AjaxResult ding(@RequestBody DingVo dingVo){
        dingtalkHelper.sendWorkNotificationDing(dingVo);
        return AjaxResult.success();
    }
}
