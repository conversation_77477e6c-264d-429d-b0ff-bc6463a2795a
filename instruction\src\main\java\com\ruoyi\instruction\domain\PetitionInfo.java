package com.ruoyi.instruction.domain;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 信访信息对象 t_petition_info
 *
 * <AUTHOR>
 * @date 2024-04-28
 */
@Data
public class PetitionInfo extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键id */
    private Long id;

    /** 事项编号 */
    @Excel(name = "事项编号")
    private String evenNumber;

    /** 初重件标识 */
    @Excel(name = "初重件标志",combo = {"初件","重件"})
    private String eventType;

    /** 姓名 */
    @Excel(name = "姓名")
    private String personName;

    /** 概述 */
    @Excel(name = "概况")
    private String overview;

    /** 问题属地 */
    @Excel(name = "问题属地")
    private String problemPlace;

    /** 事项目的 */
    @Excel(name = "事项目的")
    private String eventGoal;

    /** 内容分类 */
    @Excel(name = "内容分类")
    private String contentType;

    /** 登记单位 */
    @Excel(name = "登记单位")
    private String rankUnit;

    /** 去向单位 */
    @Excel(name = "去向单位")
    private String destinationUnit;

    /** 登记时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "登记时间(格式:yyyy-MM-dd HH:mm:ss)", width = 50, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date gradeTime;

    /** 信访人数 */
    @Excel(name = "信访人数")
    private Long personNum;

    /** 状态 1：正常 9：删除 */
    private String status;


}
