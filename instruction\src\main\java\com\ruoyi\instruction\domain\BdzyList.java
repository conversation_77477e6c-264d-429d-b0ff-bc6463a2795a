package com.ruoyi.instruction.domain;

import java.util.Date;
import java.util.List;

import com.fasterxml.jackson.annotation.JsonFormat;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 八大战役列对象 t_bdzy_list
 * 
 * <AUTHOR>
 * @date 2023-08-31
 */
public class BdzyList extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** $column.columnComment */
    private Long id;

    /** 战役名称 */
    @Excel(name = "战役名称")
    private String name;

    /** 组长 */
    @Excel(name = "组长")
    private String groupLeader;

    /** 牵头单位 */
    @Excel(name = "牵头单位")
    private String unit;

    /** $column.columnComment */
    @Excel(name = "${comment}", readConverterExp = "$column.readConverterExp()")
    private Date cTime;

    /** $column.columnComment */
    @Excel(name = "${comment}", readConverterExp = "$column.readConverterExp()")
    private Date uTime;

    List<BdzyDetails> bdzyDetailsList;

    public void setId(Long id) 
    {
        this.id = id;
    }

    public Long getId() 
    {
        return id;
    }
    public void setName(String name) 
    {
        this.name = name;
    }

    public String getName() 
    {
        return name;
    }
    public void setGroupLeader(String groupLeader) 
    {
        this.groupLeader = groupLeader;
    }

    public String getGroupLeader() 
    {
        return groupLeader;
    }
    public void setUnit(String unit) 
    {
        this.unit = unit;
    }

    public String getUnit() 
    {
        return unit;
    }
    public void setcTime(Date cTime) 
    {
        this.cTime = cTime;
    }

    public Date getcTime() 
    {
        return cTime;
    }
    public void setuTime(Date uTime) 
    {
        this.uTime = uTime;
    }

    public Date getuTime() 
    {
        return uTime;
    }

    public List<BdzyDetails> getBdzyDetailsList() {
        return bdzyDetailsList;
    }

    public void setBdzyDetailsList(List<BdzyDetails> bdzyDetailsList) {
        this.bdzyDetailsList = bdzyDetailsList;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("name", getName())
            .append("groupLeader", getGroupLeader())
            .append("unit", getUnit())
            .append("cTime", getcTime())
            .append("uTime", getuTime())
            .toString();
    }
}
