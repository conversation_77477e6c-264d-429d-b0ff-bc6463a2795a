package com.ruoyi.instruction.domain;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;
import org.apache.poi.ss.usermodel.IndexedColors;

/**
 * 群体基本信息对象 t_instruction_group
 *
 * <AUTHOR>
 * @date 2022-12-16
 */
@Data
public class InstructionGroup extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /**
     * 群体id自增
     */
    private Long id;

    /**
     * 群体名称
     */
    @Excel(name = "群体名称(必填)", headerColor = IndexedColors.RED)
    private String groupName;

    /**
     * 类型名称
     */
    @Excel(name = "群体类型(必填)", headerColor = IndexedColors.RED,combo = {"社会矛盾","政治安全","社会治安","公共安全","网络安全"})
    private String typeName;

    /**
     * 群体类型（由类型表中选取填入）
     */
    private String type;

    /**
     * 管控级别（低、中、高）
     */
    @Excel(name = "管控级别(必填)", headerColor = IndexedColors.RED, readConverterExp = "1=低,2=中,3=高", combo = {"高", "中", "低"})
    private String controlLevel;

    /**
     * 管控级别中文名称
     */
    private String controlLevelName;

    /**
     * 首次填报日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "首次填报日期(yyyy-MM-dd)", width = 30, dateFormat = "yyyy-MM-dd")
    private Date firstSubmitTime;

    /**
     * 基本情况
     */
    @Excel(name = "基本情况(必填)", headerColor = IndexedColors.RED)
    private String baseInfo;

    /**
     * 群体特征
     */
    @Excel(name = "群体特征")
    private String groupCharact;

    /**
     * 防控措施
     */
    @Excel(name = "防控措施")
    private String preventionControlMeasures;

    /**
     * 人员id集合
     */
    private String personIds;

    /**
     * 1:正常 9：删除
     */
    private String status;


    private String leadPersonIds;

    /**
     * 人员信息
     */
    private List<InstrucationPerson> personList;


    /**
     * 群体关联事件数
     */
    @Excel(name = "关联事件个数", type = Excel.Type.EXPORT)
    private Integer eventCount;

    /**
     * 群体关联人数
     */
    /** 人员个数 */
    @Excel(name = "关联人员个数", type = Excel.Type.EXPORT)
    private Integer personCount;

    /**
     * 群体关联所有的人员ids
     */
    private String allPersonIds;

    /**
     * 群体关联的牵头人员
     */
    private String allLeadPersonIds;

    /**
     * 部门
     */
    private  String dutyUnit;

    /** 活跃度 */
    private Integer liveness;

    /**
     * 用户查询人员详情 判断该群体是否从事件关联
     */
    private int isEvent;

    /**
     * 情况简述
     */
    private String description;

    /** 群体区域 */
    @Excel(name = "地域")
    private String groupArea;

    /** 创建部门id */
    private Long createDeptId;

    /** 创建部门名称 */
    @Excel(name = "创建部门",type = Excel.Type.EXPORT)
    private String createDeptName;

    /**
     * 是否可以处理 0：不可处理  1： 可处理
     */
    private Integer isDeal;

    private String beginTime;

    private String endTime;

    /** 涉及人数/户数/车数 */
    private Long referPersonNum;

    /** 涉及金额 */
    private BigDecimal referAmount;

    /** 主要责任单位 */
    private String majorUnit;

    /** 主要涉及区域 */
    private String majorPlace;

    /** 主要负责（嫌疑人） */
    private String majorPerson;

    /** 属性标签 */
    private String statsLabel;

    /** 行为标签 */
    private String actLabel;

    /** 文件ids */
    private String fileIds;

    /**
     * 主要诉求
     */
    private String mainDemand;

    /**
     * 群体关联的关联户数详情
     */
    private List<GroupHouseDetails> groupHouseDetailsList;

    /**
     * 群体关联的处置详情
     */
    private List<GroupDispose> groupDisposeList;

}
