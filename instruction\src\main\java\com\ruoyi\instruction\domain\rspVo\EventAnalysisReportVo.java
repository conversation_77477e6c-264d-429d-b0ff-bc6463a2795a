package com.ruoyi.instruction.domain.rspVo;

import lombok.Data;

import java.util.HashMap;
import java.util.List;

/**
 * 事件分析报告
 * <AUTHOR>
 */
@Data
public class EventAnalysisReportVo {
    /**
     * 创建时间
     */
    private String createTime;
    /**
     * 事件名称
     */
    private  String typeName;
    /**
     * 群体关联事件数
     */
    private Integer eventCount;
    /**
     * 群体数
     */
    private Integer groupCount;
    /**
     * 群体详情
     */
    private String groupDetails;

    /**
     * 群体关联人数
     */
    private Integer personCount;
    /**
     * 群体关联人次
     */
    private Integer personNum;


    /**
     * 响应人员
     */
    private Integer respPersonCount;

    /**
     * 属地数量
     */
    private Integer  regionCount;
    /**
     * 属地数量
     */
    private String  regionDetails;
    /**
     * 事件属性详情
     */
    private String  eventPropertiesDetails;
    /**
     * 事件
     */
    List<EventAnalysisReportEventVo> eventList;
    /**
     * 各县市区异动事件和异动人员数
     */
    private HashMap<String,AreaEventAnalysisReportVo> areaEvent;
    /**
     * 各县市区各事件人员数
     */
    private HashMap<String,Integer> areaEventNum;
}
