package com.ruoyi.instruction.domain;

import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;
import org.apache.poi.ss.usermodel.IndexedColors;

/**
 * 重点人员-管控人员信息对象 t_instruction_person_control
 * 
 * <AUTHOR>
 * @date 2023-03-21
 */
@Data
public class InstructionPersonControl extends BaseEntity
{

    /** 主键id */
    private Long id;

    /** 姓名 */
    @Excel(name = "管控人员",headerColor = IndexedColors.RED)
    private String name;

    /** 单位 */
    @Excel(name = "管控人员单位")
    private String unit;

    /** 职位 */
    @Excel(name = "管控人员职位")
    private String position;

    /** 电话 */
    @Excel(name = "电话")
    private String telephone;

    @Excel(name = "人员身份证(异动/关注人员的身份证)",width = 50,headerColor = IndexedColors.RED)
    private String idCard;

    /** 情况反馈 */
    private String situationFeedback;

    /** 状态1：正常 9：删除 */
    private String status;

    /** 关联重点人员id */
    private Long personId;

    /** 是否是责任人 1：责任人 2：非责任人 */
    @Excel(name = "是否为组长",combo = {"是","否"},readConverterExp ="1=是,2=否")
    private String dutyer;




}
