package com.ruoyi.examineAir.mapper;

import java.util.List;
import com.ruoyi.examineAir.domain.Examine;
import com.ruoyi.examineAir.vo.BigScreenPageRegionStatisticsVo;

/**
 * 考核晾晒列Mapper接口
 * 
 * <AUTHOR>
 * @date 2023-04-19
 */
public interface ExamineMapper 
{
    /**
     * 查询考核晾晒列
     * 
     * @param id 考核晾晒列主键
     * @return 考核晾晒列
     */
    public Examine selectExamineById(Long id);

    /**
     * 查询考核晾晒列列表
     * 
     * @param examine 考核晾晒列
     * @return 考核晾晒列集合
     */
    public List<Examine> selectExamineList(Examine examine);

    /**
     * 新增考核晾晒列
     * 
     * @param examine 考核晾晒列
     * @return 结果
     */
    public int insertExamine(Examine examine);

    /**
     * 修改考核晾晒列
     * 
     * @param examine 考核晾晒列
     * @return 结果
     */
    public int updateExamine(Examine examine);

    /**
     * 删除考核晾晒列
     * 
     * @param id 考核晾晒列主键
     * @return 结果
     */
    public int deleteExamineById(Long id);

    /**
     * 批量删除考核晾晒列
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteExamineByIds(Long[] ids);
    /**
     * 获取最新一条发布的数据
     * @return
     */
    List<BigScreenPageRegionStatisticsVo> getNewAreaCount();

    /**
     * 获取发布考核晾晒最新的数据
     * @return
     */
    Examine getNEW();
}
