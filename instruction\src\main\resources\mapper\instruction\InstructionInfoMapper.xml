<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.instruction.mapper.InstructionInfoMapper">

    <resultMap type="InstructionInfo" id="InstructionInfoResult">
        <result property="id" column="id"/>
        <result property="instructionCode" column="instruction_code"/>
        <result property="instructionTitle" column="instruction_title"/>
        <result property="handleTime" column="handle_time"/>
        <result property="receiveUnit" column="receive_unit"/>
        <result property="emergencyDegree" column="emergency_degree"/>
        <result property="groupId" column="group_id"/>
        <result property="groupName" column="group_name"/>
        <result property="type" column="type"/>
        <result property="sourceInfo" column="source_info"/>
        <result property="instructionContent" column="instruction_content"/>
        <result property="baseInfo" column="base_info"/>
        <result property="createTime" column="create_time"/>
        <result property="creatorBy" column="creator_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="status" column="status"/>
        <result property="instructionStatus" column="instruction_status"/>
        <result property="feedback" column="feedback"/>
        <result property="personIds" column="person_ids"/>
        <result property="createDeptId" column="create_dept_id"/>
        <result property="leadPersonIds" column="lead_person_ids"/>
        <result property="petitionType" column="petition_type"/>
        <result property="pushTime" column="push_time"/>
        <result property="infoCategory" column="info_category"/>
        <result property="isRelease" column="is_release"/>
        <result property="qzid" column="qzid"/>
        <result property="gonganEventId" column="gongan_event_id"/>
        <result property="bmid" column="bmid"/>
        <result property="mcafId" column="mcaf_id"/>
        <result property="workId" column="work_id"/>
        <result property="fileIds" column="file_ids"/>
        <result property="wgzbId" column="wgzb_id"/>
        <result property="instructionType" column="instruction_type"/>
        <result property="instructionCreateDeptId" column="instruction_create_dept_id"/>
        <result property="missionName" column="mission_name"/>
        <result property="companyName" column="company_name"/>
        <result property="county" column="county"/>
        <result property="town" column="town"/>
        <result property="companyAddress" column="company_address"/>
        <result property="isAudit" column="is_audit"/>
        <result property="auditPerson" column="audit_person"/>
        <result property="auditTime" column="audit_time"/>
        <result property="receiveStr" column="receive_str"/>
        <result property="creatorId" column="creator_id"/>
        <result property="departmentStr" column="department_str"/>
        <result property="typeName" column="type_name"/>
        <result property="auditRemindCount" column="audit_remind_count"/>
        <result property="auditRemindTime" column="audit_remind_time"/>
        <result property="outsidePerson" column="outside_person"/>
        <result property="eventProperties" column="event_properties"/>
        <result property="mzxPlace" column="mzx_place"/>
        <result property="mzxNature" column="mzx_nature"/>
        <result property="mzxRelation" column="mzx_relation"/>
        <result property="mzxOccurTime" column="mzx_occur_time"/>
        <result property="mzxDutyDept" column="mzx_duty_dept"/>
        <result property="mzxType" column="mzx_type"/>
        <result property="mzxSourceInfo" column="mzx_source_info"/>
        <result property="mzxId" column="mzx_id"/>
        <result property="personCount" column="personCount"/>
        <result property="spareNum" column="spare_num"/>
        <result property="assignTime" column="assign_time"/>
        <result property="endTime" column="end_time"/>
        <result property="skslGroupIds" column="sksl_group_ids"/>
        <result property="skslType" column="sksl_type"/>
        <result property="deployControlId" column="deploy_control_id"/>
        <result property="remark" column="remark"/>
        <result property="nextSteps" column="next_steps"/>
        <result property="wzEventId" column="wz_event_id"/>
        <result property="wzEvntType" column="wz_evnt_type"/>
        <result property="caseType" column="case_type"/>
        <result property="caseReason" column="case_reason"/>
        <result property="caseTime" column="case_time"/>
        <result property="isTransferred" column="is_transferred"/>
        <result property="transferId" column="transfer_id"/>
    </resultMap>

    <sql id="selectInstructionInfoVo">
        select id,
               instruction_code,
               instruction_title,
               handle_time,
               receive_unit,
               emergency_degree,
               group_id,
               type,
               type_name,
               source_info,
               instruction_content,
               base_info,
               create_time,
               creator_by,
               update_time,
               status,
               instruction_status,
               feedback,
               person_ids,
               create_dept_id,
               lead_person_ids,
               petition_type,
               push_time,
               info_category,
               is_release,
               qzid,
               gongan_event_id,
               bmid,
               mcaf_id,
               work_id,
               file_ids,
               wgzb_id,
               instruction_type,
               instruction_create_dept_id,
               mission_name,
               company_name,
               county,
               town,
               company_address,
               is_audit,
               audit_person,
               audit_time,
               receive_str,
               creator_id,
               department_str,
               outside_person,
               event_properties,
               mzx_place,
               mzx_nature,
               mzx_relation,
               mzx_occur_time,
               mzx_duty_dept,
               mzx_type,
               mzx_source_info,
               mzx_id,
               spare_num,
               assign_time,
               sksl_group_ids,
               sksl_type,
               deploy_control_id,
               remark,
               next_steps,
               wz_event_id,
               wz_evnt_type,
               case_type,
               case_reason
        from t_instruction_info
    </sql>

    <sql id="selectInstructionInfoVo_onw">
        select id,create_dept_id,instruction_type

        from t_instruction_info
    </sql>

    <select id="selectMissionNames" resultType="String">
        SELECT
            a.mission_name
        FROM
            ( SELECT mission_name, max( create_time ) time FROM t_instruction_info WHERE status = 1 and mission_name IS NOT NULL AND mission_name != ""
        <if test='type != null  and type == "1"'> and create_dept_id = 202</if>
        <if test='type != null  and type == "2"'> and create_dept_id != 202</if>
        <if test="deptName != null  and deptName != ''"> and county = #{deptName}</if>
            GROUP BY mission_name ) a
        ORDER BY
            a.time desc
    </select>

    <select id="selectInstructionInfoList" parameterType="InstructionInfo" resultMap="InstructionInfoResult">
        <include refid="selectInstructionInfoVo_onw"/>
        <where>
            status = 1
            <if test="isRelease != null  and isRelease != ''">and is_release = #{isRelease}</if>
            <if test="instructionCode != null  and instructionCode != ''">and instruction_code = #{instructionCode}</if>
            <if test="instructionTitle != null  and instructionTitle != ''">and instruction_title like concat('%', #{instructionTitle}, '%')
            </if>
            <if test="params.beginTime != null and params.beginTime != ''"><!-- 开始时间检索 -->
                AND date_format(create_time,'%y%m%d') &gt;= date_format(#{params.beginTime},'%y%m%d')
            </if>
            <if test="params.endTime != null and params.endTime != ''"><!-- 结束时间检索 -->
                AND date_format(create_time,'%y%m%d') &lt;= date_format(#{params.endTime},'%y%m%d')
            </if>
            <if test="receiveUnit != null  and receiveUnit != ''">and receive_unit like concat('%', #{receiveUnit},
                '%')
            </if>
            <if test="emergencyDegree != null  and emergencyDegree != ''">and emergency_degree = #{emergencyDegree}</if>
            <if test="groupId != null ">and group_id = #{groupId}</if>
            <if test="type != null  and type != ''">and type = #{type}</if>
            <if test="sourceInfo != null  and sourceInfo != ''">and source_info = #{sourceInfo}</if>
            <if test="instructionContent != null  and instructionContent != ''">and instruction_content =
                #{instructionContent}
            </if>
            <if test="baseInfo != null  and baseInfo != ''">and base_info = #{baseInfo}</if>
            <if test="creatorBy != null  and creatorBy != ''">and creator_by = #{creatorBy}</if>
            <if test="status != null  and status != ''">and status = #{status}</if>
            <if test="instructionStatus != null  and instructionStatus != ''">and instruction_status =
                #{instructionStatus}
            </if>
            <if test="feedback != null  and feedback != ''">and feedback = #{feedback}</if>
            <if test="personIds != null  and personIds != ''">and person_ids = #{personIds}</if>
            <if test="createDeptId != null ">and create_dept_id = #{createDeptId}</if>
            <if test="params.ids != null and params.ids.size()>0">
                AND id in
                <foreach item="id" collection="params.ids" open="(" separator="," close=")">
                    #{id}
                </foreach>
            </if>
            <if test="petitionType != null ">
                and petition_type = #{petitionType}
            </if>
            <if test="pushTime != null ">and push_time = #{pushTime}</if>
            <if test="infoCategory != null  and infoCategory != ''">and info_category = #{infoCategory}</if>
            <if test="qzid != null  and qzid != ''">and qzid = #{qzid}</if>
            <if test="gonganEventId != null ">and gongan_event_id = #{gonganEventId}</if>
            <if test="mcafId != null ">and mcaf_id = #{mcafId}</if>
            <if test="wgzbId != null  and wgzbId != ''">and wgzb_id = #{wgzbId}</if>
            <if test="instructionType != null and instructionType != 7 ">and instruction_type = #{instructionType}</if>
            <if test="instructionType != null and instructionType == 7 ">and instruction_type in (3,6)</if>
            <if test="instructionCreateDeptId != null ">and instruction_create_dept_id = #{instructionCreateDeptId}</if>
            <if test="missionName != null  and missionName != ''">and mission_name like concat('%', #{missionName}, '%')</if>
            <if test="companyName != null  and companyName != ''">and company_name like concat('%', #{companyName}, '%')</if>
            <if test="county != null  and county != ''">and county = #{county}</if>
            <if test="town != null  and town != ''">and town = #{town}</if>
            <if test="companyAddress != null  and companyAddress != ''">and company_address = #{companyAddress}</if>
            <if test="isAudit != null  and isAudit == 0">and is_audit = 0</if>
            <if test="isAudit != null  and isAudit == 3">and is_audit in (1,2)</if>
            <if test="auditPerson != null  and auditPerson != ''">and audit_person = #{auditPerson}</if>
            <if test="auditTime != null ">and audit_time = #{auditTime}</if>
            <if test="skslGroupIds != null  and skslGroupIds != ''">and sksl_group_ids = #{skslGroupIds}</if>
            <if test="skslType != null  and skslType != ''">and sksl_type = #{skslType}</if>
            <if test="wzEvntType != null  and wzEvntType != ''">and wz_evnt_type = #{wzEvntType}</if>
        </where>
        order by create_time desc
    </select>

    <select id="selectInstructionInfoById" parameterType="Long" resultMap="InstructionInfoResult">
        <include refid="selectInstructionInfoVo"/>
        where status = 1 and id = #{id}
    </select>

    <select id="selectCount" resultType="java.lang.Long">
        select count(*) from t_instruction_info
        <where>
            status = 1
            <if test="instructionCode != null  and instructionCode != ''">and instruction_code = #{instructionCode}</if>
            <if test="instructionTitle != null  and instructionTitle != ''">and instruction_title =
                #{instructionTitle}
            </if>
            <if test="handleTime != null ">and handle_time = #{handleTime}</if>
            <if test="receiveUnit != null  and receiveUnit != ''">and receive_unit like concat('%', #{receiveUnit},
                '%')
            </if>
            <if test="emergencyDegree != null  and emergencyDegree != ''">and emergency_degree = #{emergencyDegree}</if>
            <if test="groupId != null ">and group_id = #{groupId}</if>
            <if test="type != null  and type != ''">and type = #{type}</if>
            <if test="sourceInfo != null  and sourceInfo != ''">and source_info = #{sourceInfo}</if>
            <if test="instructionContent != null  and instructionContent != ''">and instruction_content =
                #{instructionContent}
            </if>
            <if test="baseInfo != null  and baseInfo != ''">and base_info = #{baseInfo}</if>
            <if test="creatorBy != null  and creatorBy != ''">and creator_by = #{creatorBy}</if>
            <if test="status != null  and status != ''">and status = #{status}</if>
            <if test="instructionStatus != null  and instructionStatus != ''">and instruction_status =
                #{instructionStatus}
            </if>
            <if test="feedback != null  and feedback != ''">and feedback = #{feedback}</if>
            <if test="personIds != null  and personIds != ''">and person_ids = #{personIds}</if>
            <if test="createDeptId != null ">and create_dept_id = #{createDeptId}</if>
            <if test="params.ids != null and params.ids != ''"><!-- 指令ids -->
                AND id in
                <foreach item="id" collection="params.ids" open="(" separator="," close=")">
                    #{id}
                </foreach>
            </if>
            <if test="petitionType != null ">
                and petition_type = #{petitionType}
            </if>
            <if test="pushTime != null ">and push_time = #{pushTime}</if>
            <if test="infoCategory != null  and infoCategory != ''">and info_category = #{infoCategory}</if>
            <if test="qzid != null  and qzid != ''">and qzid = #{qzid}</if>
            <if test="gonganEventId != null ">and gongan_event_id = #{gonganEventId}</if>
            <if test="mcafId != null ">and mcaf_id = #{mcafId}</if>
            <if test="fileIds != null  and fileIds != ''">and file_ids = #{fileIds}</if>
            <if test="instructionType != null and instructionType != 7 ">and instruction_type = #{instructionType}</if>
            <if test="instructionType != null and instructionType == 7 ">and instruction_type in (3,6)</if>
            <if test="instructionCreateDeptId != null ">and instruction_create_dept_id = #{instructionCreateDeptId}</if>
            <if test="missionName != null  and missionName != ''">and mission_name like concat('%', #{missionName}, '%')</if>
            <if test="companyName != null  and companyName != ''">and company_name like concat('%', #{companyName}, '%')</if>
            <if test="county != null  and county != ''">and county = #{county}</if>
            <if test="town != null  and town != ''">and town = #{town}</if>
            <if test="companyAddress != null  and companyAddress != ''">and company_address = #{companyAddress}</if>
            <if test="isAudit != null  and isAudit != ''">and is_audit = #{isAudit}</if>
            <if test="auditPerson != null  and auditPerson != ''">and audit_person = #{auditPerson}</if>
            <if test="auditTime != null ">and audit_time = #{auditTime}</if>
            <if test="skslGroupIds != null  and skslGroupIds != ''">and sksl_group_ids = #{skslGroupIds}</if>
            <if test="skslType != null  and skslType != ''">and sksl_type = #{skslType}</if>
            <if test="wzEvntType != null  and wzEvntType != ''">and wz_evnt_type = #{wzEvntType}</if>
        </where>
    </select>

    <insert id="insertInstructionInfo" parameterType="InstructionInfo" useGeneratedKeys="true" keyProperty="id">
        insert into t_instruction_info
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="instructionCode != null">instruction_code,</if>
            <if test="instructionTitle != null">instruction_title,</if>
            <if test="handleTime != null">handle_time,</if>
            <if test="receiveUnit != null">receive_unit,</if>
            <if test="emergencyDegree != null">emergency_degree,</if>
            <if test="groupId != null">group_id,</if>
            <if test="type != null">type,</if>
            <if test="sourceInfo != null">source_info,</if>
            <if test="instructionContent != null">instruction_content,</if>
            <if test="baseInfo != null">base_info,</if>
            <if test="createTime != null">create_time,</if>
            <if test="creatorBy != null">creator_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="status != null">status,</if>
            <if test="instructionStatus != null">instruction_status,</if>
            <if test="feedback != null">feedback,</if>
            <if test="personIds != null">person_ids,</if>
            <if test="createDeptId != null">create_dept_id,</if>
            <if test="leadPersonIds != null">lead_person_ids,</if>
            <if test="petitionType != null">petition_type,</if>
            <if test="pushTime != null">push_time,</if>
            <if test="infoCategory != null">info_category,</if>
            <if test="isRelease != null">is_release,</if>
            <if test="qzid != null">qzid,</if>
            <if test="gonganEventId != null">gongan_event_id,</if>
            <if test="bmid != null">bmid,</if>
            <if test="mcafId != null">mcaf_id,</if>
            <if test="workId != null">work_id,</if>
            <if test="fileIds != null">file_ids,</if>
            <if test="wgzbId != null">wgzb_id,</if>
            <if test="instructionType != null">instruction_type,</if>
            <if test="instructionCreateDeptId != null">instruction_create_dept_id,</if>
            <if test="missionName != null">mission_name,</if>
            <if test="companyName != null">company_name,</if>
            <if test="county != null">county,</if>
            <if test="town != null">town,</if>
            <if test="companyAddress != null">company_address,</if>
            <if test="isAudit != null">is_audit,</if>
            <if test="auditPerson != null">audit_person,</if>
            <if test="auditTime != null">audit_time,</if>
            <if test="receiveStr != null">receive_str,</if>
            <if test="assignTime != null">assign_time,</if>
            <if test="creatorId != null">creator_id,</if>
            <if test="departmentStr != null">department_str,</if>
            <if test="typeName != null">type_name,</if>
            <if test="auditRemindCount != null">audit_remind_count,</if>
            <if test="auditRemindTime != null">audit_remind_time,</if>
            <if test="outsidePerson != null">outside_person,</if>
            <if test="eventProperties != null">event_properties,</if>
            <if test="mzxPlace != null">mzx_place,</if>
            <if test="mzxNature != null">mzx_nature,</if>
            <if test="mzxRelation != null">mzx_relation,</if>
            <if test="mzxOccurTime != null">mzx_occur_time,</if>
            <if test="mzxDutyDept != null">mzx_duty_dept,</if>
            <if test="mzxType != null">mzx_type,</if>
            <if test="mzxSourceInfo != null">mzx_source_info,</if>
            <if test="mzxId != null">mzx_id,</if>
            <if test="spareNum != null">spare_num,</if>
            <if test="skslGroupIds != null">sksl_group_ids,</if>
            <if test="skslType != null">sksl_type,</if>
            <if test="deployControlId != null">deploy_control_id,</if>
            <if test="remark != null">remark,</if>
            <if test="nextSteps != null">next_steps,</if>
            <if test="wzEventId != null">wz_event_id,</if>
            <if test="wzEvntType != null">wz_evnt_type,</if>
            <if test="transferId != null">transfer_id,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="instructionCode != null">#{instructionCode},</if>
            <if test="instructionTitle != null">#{instructionTitle},</if>
            <if test="handleTime != null">#{handleTime},</if>
            <if test="receiveUnit != null">#{receiveUnit},</if>
            <if test="emergencyDegree != null">#{emergencyDegree},</if>
            <if test="groupId != null">#{groupId},</if>
            <if test="type != null">#{type},</if>
            <if test="sourceInfo != null">#{sourceInfo},</if>
            <if test="instructionContent != null">#{instructionContent},</if>
            <if test="baseInfo != null">#{baseInfo},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="creatorBy != null">#{creatorBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="status != null">#{status},</if>
            <if test="instructionStatus != null">#{instructionStatus},</if>
            <if test="feedback != null">#{feedback},</if>
            <if test="personIds != null">#{personIds},</if>
            <if test="createDeptId != null">#{createDeptId},</if>
            <if test="leadPersonIds != null">#{leadPersonIds},</if>
            <if test="petitionType != null">#{petitionType},</if>
            <if test="pushTime != null">#{pushTime},</if>
            <if test="infoCategory != null">#{infoCategory},</if>
            <if test="isRelease != null">#{isRelease},</if>
            <if test="qzid != null">#{qzid},</if>
            <if test="gonganEventId != null">#{gonganEventId},</if>
            <if test="bmid != null">#{bmid},</if>
            <if test="mcafId != null">#{mcafId},</if>
            <if test="workId != null">#{workId},</if>
            <if test="fileIds != null">#{fileIds},</if>
            <if test="wgzbId != null">#{wgzbId},</if>
            <if test="instructionType != null">#{instructionType},</if>
            <if test="instructionCreateDeptId != null">#{instructionCreateDeptId},</if>
            <if test="missionName != null">#{missionName},</if>
            <if test="companyName != null">#{companyName},</if>
            <if test="county != null">#{county},</if>
            <if test="town != null">#{town},</if>
            <if test="companyAddress != null">#{companyAddress},</if>
            <if test="isAudit != null">#{isAudit},</if>
            <if test="auditPerson != null">#{auditPerson},</if>
            <if test="auditTime != null">#{auditTime},</if>
            <if test="receiveStr != null">#{receiveStr},</if>
            <if test="assignTime != null">#{assignTime},</if>
            <if test="creatorId != null">#{creatorId},</if>
            <if test="departmentStr != null">#{departmentStr},</if>
            <if test="typeName != null">#{typeName},</if>
            <if test="auditRemindCount != null">#{auditRemindCount},</if>
            <if test="auditRemindTime != null">#{auditRemindTime},</if>
            <if test="outsidePerson != null">#{outsidePerson},</if>
            <if test="eventProperties != null">#{eventProperties},</if>
            <if test="mzxPlace != null">#{mzxPlace},</if>
            <if test="mzxNature != null">#{mzxNature},</if>
            <if test="mzxRelation != null">#{mzxRelation},</if>
            <if test="mzxOccurTime != null">#{mzxOccurTime},</if>
            <if test="mzxDutyDept != null">#{mzxDutyDept},</if>
            <if test="mzxType != null">#{mzxType},</if>
            <if test="mzxSourceInfo != null">#{mzxSourceInfo},</if>
            <if test="mzxId != null">#{mzxId},</if>
            <if test="spareNum != null">#{spareNum},</if>
            <if test="skslGroupIds != null">#{skslGroupIds},</if>
            <if test="skslType != null">#{skslType},</if>
            <if test="deployControlId != null">#{deployControlId},</if>
            <if test="remark != null">#{remark},</if>
            <if test="nextSteps != null">#{nextSteps},</if>
            <if test="wzEventId != null">#{wzEventId},</if>
            <if test="wzEvntType != null">#{wzEvntType},</if>
            <if test="transferId != null">#{transferId},</if>
        </trim>
    </insert>

    <update id="updateInstructionInfo" parameterType="InstructionInfo">
        update t_instruction_info
        <trim prefix="SET" suffixOverrides=",">
            <if test="instructionCode != null">instruction_code = #{instructionCode},</if>
            <if test="instructionTitle != null">instruction_title = #{instructionTitle},</if>
            <if test="handleTime != null">handle_time = #{handleTime},</if>
            <if test="receiveUnit != null">receive_unit = #{receiveUnit},</if>
            <if test="emergencyDegree != null">emergency_degree = #{emergencyDegree},</if>
            <if test="groupId != null">group_id = #{groupId},</if>
            <if test="type != null">type = #{type},</if>
            <if test="sourceInfo != null">source_info = #{sourceInfo},</if>
            <if test="instructionContent != null">instruction_content = #{instructionContent},</if>
            <if test="baseInfo != null">base_info = #{baseInfo},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="creatorBy != null">creator_by = #{creatorBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="status != null">status = #{status},</if>
            <if test="instructionStatus != null">instruction_status = #{instructionStatus},</if>
            <if test="feedback != null">feedback = #{feedback},</if>
            <if test="personIds != null">person_ids = #{personIds},</if>
            <if test='personIds != null and personIds =="-1"' >person_ids = null,</if>
            <if test="createDeptId != null">create_dept_id = #{createDeptId},</if>
            <if test="leadPersonIds != null">lead_person_ids = #{leadPersonIds},</if>
            <if test='leadPersonIds != null and leadPersonIds == "-1"'>lead_person_ids = null,</if>
            <if test="petitionType != null">petition_type = #{petitionType},</if>
            <if test="pushTime != null">push_time = #{pushTime},</if>
            <if test="infoCategory != null">info_category = #{infoCategory},</if>
            <if test="isRelease != null">is_release = #{isRelease},</if>
            <if test="qzid != null">qzid = #{qzid},</if>
            <if test="gonganEventId != null">gongan_event_id = #{gonganEventId},</if>
            <if test="bmid != null">bmid = #{bmid},</if>
            <if test="mcafId != null">mcaf_id = #{mcafId},</if>
            <if test="workId != null">work_id = #{workId},</if>
            <if test="fileIds != null">file_ids = #{fileIds},</if>
            <if test="wgzbId != null">wgzb_id = #{wgzbId},</if>
            <if test="instructionType != null">instruction_type = #{instructionType},</if>
            <if test="instructionCreateDeptId != null">instruction_create_dept_id = #{instructionCreateDeptId},</if>
            <if test="missionName != null">mission_name = #{missionName},</if>
            <if test="companyName != null">company_name = #{companyName},</if>
            <if test="county != null">county = #{county},</if>
            <if test="town != null">town = #{town},</if>
            <if test="companyAddress != null">company_address = #{companyAddress},</if>
            <if test="isAudit != null">is_audit = #{isAudit},</if>
            <if test="auditPerson != null">audit_person = #{auditPerson},</if>
            <if test="auditTime != null">audit_time = #{auditTime},</if>
            <if test="receiveStr != null">receive_str = #{receiveStr},</if>
            <if test="assignTime != null">assign_time = #{assignTime},</if>
            <if test="instrucationIsEnd != null">instrucation_is_end = #{instrucationIsEnd},</if>
            <if test="feedbackDept != null">feedback_dept = #{feedbackDept},</if>
            <if test="endTime != null">end_time = #{endTime},</if>
            <if test="departmentStr != null">department_str = #{departmentStr},</if>
            <if test="typeName != null">type_name = #{typeName},</if>
            <if test="auditRemindCount != null">audit_remind_count = #{auditRemindCount},</if>
            <if test="auditRemindTime != null">audit_remind_time = #{auditRemindTime},</if>
            <if test="outsidePerson != null">outside_person = #{outsidePerson},</if>
            <if test="eventProperties != null">event_properties = #{eventProperties},</if>
            <if test="mzxPlace != null">mzx_place = #{mzxPlace},</if>
            <if test="mzxNature != null">mzx_nature = #{mzxNature},</if>
            <if test="mzxRelation != null">mzx_relation = #{mzxRelation},</if>
            <if test="mzxOccurTime != null">mzx_occur_time = #{mzxOccurTime},</if>
            <if test="mzxDutyDept != null">mzx_duty_dept = #{mzxDutyDept},</if>
            <if test="mzxType != null">mzx_type = #{mzxType},</if>
            <if test="mzxSourceInfo != null">mzx_source_info = #{mzxSourceInfo},</if>
            <if test="mzxId != null">mzx_id = #{mzxId},</if>
            <if test="spareNum != null">spare_num = #{spareNum},</if>
            <if test="skslGroupIds != null">sksl_group_ids = #{skslGroupIds},</if>
            <if test="skslType != null">sksl_type = #{skslType},</if>
            <if test="deployControlId != null">deploy_control_id = #{deployControlId},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="nextSteps != null">next_steps = #{nextSteps},</if>
            <if test="wzEventId != null">wz_event_id = #{wzEventId},</if>
            <if test="wzEvntType != null">wz_evnt_type = #{wzEvntType},</if>
            <if test="caseType != null">case_type = #{caseType},</if>
            <if test="caseReason != null">case_reason = #{caseReason},</if>
            <if test="caseTime != null">case_time  = #{caseTime},</if>
        </trim>
        where id = #{id}
    </update>
    <update id="updateCase">
        UPDATE t_instruction_info
        SET case_type = NULL,
            case_reason = NULL,
            case_time = NULL
        WHERE
            id = #{id}
    </update>


    <delete id="deleteInstructionInfoById" parameterType="Long">
        update t_instruction_info
        set status = 9
        where id = #{id}
    </delete>

    <delete id="deleteInstructionInfoByIds" parameterType="String">
        update t_instruction_info set status = 9 where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <select id="testInstructionListNew" resultType="com.ruoyi.instruction.domain.rspVo.InstructionInfomiddleResVo">
        SELECT *
        from (SELECT id,instruction_title instructionTitle,person_ids
        personIds,receive_unit
        receiveUnit,create_time createTime,create_dept_id createDeptId,type,type_name typeName,source_info sourceInfo,handle_time
        handleTime,feedback,instruction_content instructionContent,base_info baseInfo,lead_person_ids
        leadPersonIds,is_release isRelease,
        (LENGTH(receive_unit) - LENGTH(REPLACE(receive_unit, ',', ''))+1) AS receiveUnitCount,
        instruction_create_dept_id AS instructionCreateDeptId,
        mission_name AS missionName,
        CONCAT_WS('*',company_name,mission_name,county,town) AS companyName,
        county,
        town,
        company_address as companyAddress,
        is_audit as isAudit,
        audit_person as auditPerson,
        audit_time as auditTime,
        assign_time as assignTime,
        push_time as pushTime,
        instrucation_is_end as instrucationIsEnd,
        feedback_dept as feedbackDept,
        end_time as endTime,
        department_str as departmentStr,
        mzx_id as mzxId,
        outside_person as outsidePerson,
        emergency_degree as emergencyDegree,
        spare_num as spareNum,
        mzx_type as mzxType,
        mzx_place as mzxPlace,
        mzx_nature as mzxNature,
        mzx_relation as mzxRelation,
        mzx_occur_time as mzxOccurTime,
        mzx_duty_dept as mzxDutyDept,
        mzx_source_info as mzxSourceInfo,
        instruction_type as instructionType,
        case_type as caseType,
        is_transferred as isTransferred
        from
        t_instruction_info
        <where>
            <if test="instructionCode != null  and instructionCode != ''">and instruction_code = #{instructionCode}</if>
            <if test="town != null  and town != ''">and town = #{town}</if>
            <if test="instructionTitle != null  and instructionTitle != ''">and instruction_title like concat('%',
                #{instructionTitle},'%')
            </if>
            <if test="params.beginTime != null and params.beginTime != ''"><!-- 开始时间检索 -->
                AND date_format(handle_time,'%y%m%d') &gt;= date_format(#{params.beginTime},'%y%m%d')
            </if>
            <if test="params.endTime != null and params.endTime != ''"><!-- 结束时间检索 -->
                AND date_format(handle_time,'%y%m%d') &lt;= date_format(#{params.endTime},'%y%m%d')
            </if>
            <if test="params.beginAssignTime != null and params.beginAssignTime != ''"><!-- 开始时间检索 -->
                AND date_format(assign_time,'%y%m%d') &gt;= date_format(#{params.beginAssignTime},'%y%m%d')
            </if>
            <if test="params.endAssignTime != null and params.endAssignTime != ''"><!-- 结束时间检索 -->
                AND date_format(assign_time,'%y%m%d') &lt;= date_format(#{params.endAssignTime},'%y%m%d')
            </if>
            <if test="params.beginPushTime != null and params.beginPushTime != ''"><!-- 开始时间检索 -->
                AND date_format(push_time,'%y%m%d') &gt;= date_format(#{params.beginPushTime},'%y%m%d')
            </if>
            <if test="params.endPushTime != null and params.endPushTime != ''"><!-- 结束时间检索 -->
                AND date_format(push_time,'%y%m%d') &lt;= date_format(#{params.endPushTime},'%y%m%d')
            </if>
            <if test="params.queryStartTime != null "><!-- 开始时间检索 -->
                AND date_format(create_time,'%y%m%d') &gt;= date_format(#{params.queryStartTime},'%y%m%d')
            </if>
            <if test="params.queryEndTime != null "><!-- 结束时间检索 -->
                AND date_format(create_time,'%y%m%d') &lt;= date_format(#{params.queryEndTime},'%y%m%d')
            </if>
            <if test="receiveUnit != null  and receiveUnit != ''">and receive_unit like concat('%', #{receiveUnit},
                '%')
            </if>
            <if test="emergencyDegree != null  and emergencyDegree != ''">and emergency_degree = #{emergencyDegree}</if>
            <if test="groupId != null ">and group_id = #{groupId}</if>
            <if test="type != null  and type != ''">and type = #{type}</if>
            <if test="sourceInfo != null  and sourceInfo != ''">and source_info = #{sourceInfo}</if>
            <if test="instructionContent != null  and instructionContent != ''">and instruction_content =
                #{instructionContent}
            </if>
            <if test="baseInfo != null  and baseInfo != ''">and base_info = #{baseInfo}</if>
            <if test="creatorBy != null  and creatorBy != ''">and creator_by = #{creatorBy}</if>
            <if test="status != null  and status != ''">and status = #{status}  and create_dept_id=202</if>
            <if test="instructionStatus != null  and instructionStatus != ''">and instruction_status =
                #{instructionStatus}
            </if>
            <if test="feedback != null  and feedback != ''">and feedback = #{feedback}</if>
            <if test="personIds != null  and personIds != ''">and person_ids = #{personIds}</if>
            <if test="createDeptId != null and createDeptId !=''">and create_dept_id = #{createDeptId}</if>
            <if test="params.ids != null and params.ids != ''">
                AND id in
                <foreach item="id" collection="params.ids" open="(" separator="," close=")">
                    #{id}
                </foreach>
            </if>
            <if test="params.wkMzxIds != null and params.wkMzxIds != ''">
                AND id in
                <foreach item="id" collection="params.wkMzxIds" open="(" separator="," close=")">
                    #{id}
                </foreach>
            </if>
            <if test="params.myFollowIds != null and params.myFollowIds != ''">
                AND id in
                <foreach item="id" collection="params.myFollowIds" open="(" separator="," close=")">
                    #{id}
                </foreach>
            </if>
            <if test="params.receiveNotIds != null and params.receiveNotIds != ''">
                AND id in
                <foreach item="id" collection="params.receiveNotIds" open="(" separator="," close=")">
                    #{id}
                </foreach>
            </if>
            <if test="params.feedbackNotIds != null and params.feedbackNotIds != ''">
                AND id in
                <foreach item="id" collection="params.feedbackNotIds" open="(" separator="," close=")">
                    #{id}
                </foreach>
            </if>
            <if test="params.handleDelayIds != null and params.handleDelayIds != ''">
                AND id in
                <foreach item="id" collection="params.handleDelayIds" open="(" separator="," close=")">
                    #{id}
                </foreach>
            </if>
            <if test="petitionType != null ">
                and petition_type = #{petitionType}
            </if>
            <if test="pushTime != null ">and push_time = #{pushTime}</if>
            <if test="infoCategory != null  and infoCategory != ''">and info_category = #{infoCategory}</if>
            <if test="instructionType != null and instructionType != 7 ">and instruction_type = #{instructionType}</if>
            <if test="instructionType != null and instructionType == 7 ">and instruction_type in (3,6)</if>
            <if test="instructionCreateDeptId != null ">and instruction_create_dept_id = #{instructionCreateDeptId}</if>
            <if test="missionName != null  and missionName != ''">and mission_name like concat('%', #{missionName}, '%')</if>
            <if test="companyName != null  and companyName != ''">and company_name like concat('%', #{companyName}, '%')</if>
            <if test="companyAddress != null  and companyAddress != ''">and company_address like concat('%', #{companyAddress}, '%')</if>
            <if test="isAudit != null  and isAudit == 1">and is_audit = #{isAudit}</if>
            <if test="isAudit != null  and isAudit == 0">and is_audit = 0</if>
            <if test="isAudit != null  and isAudit == 3">and is_audit in (1,2)</if>
            <if test="auditPerson != null  and auditPerson != ''">and audit_person = #{auditPerson}</if>
            <if test="auditTime != null ">and audit_time = #{auditTime}</if>
            <if test="departmentStr != null  and departmentStr != ''">and department_str like concat('%', #{departmentStr}, '%')</if>
            <if test="eventProperties != null ">and event_properties = #{eventProperties}</if>
            <if test="mzxPlace != null  and mzxPlace != ''">and mzx_place = #{mzxPlace}</if>
            <if test="mzxNature != null  and mzxNature != ''">and mzx_nature = #{mzxNature}</if>
            <if test="mzxRelation != null  and mzxRelation != ''">and mzx_relation = #{mzxRelation}</if>
            <if test="mzxOccurTime != null ">and mzx_occur_time = #{mzxOccurTime}</if>
            <if test="mzxDutyDept != null  and mzxDutyDept != ''">and mzx_duty_dept = #{mzxDutyDept}</if>
            <if test="mzxType != null  and mzxType != ''">and mzx_type = #{mzxType}</if>
            <if test="mzxSourceInfo != null  and mzxSourceInfo != ''">and mzx_source_info = #{mzxSourceInfo}</if>
            <if test="params.departments != null and params.departments != ''">
                AND
                <foreach item="department" collection="params.departments" separator="or">
                    department_str like concat('%', #{department}, '%')
                </foreach>
            </if>
            <if test="params.receiveUnits != null and params.receiveUnits != ''">
                AND
                <foreach item="receiveUnit" collection="params.receiveUnits" separator="or">
                    receive_unit like concat('%', #{receiveUnit}, '%')
                </foreach>
            </if>
            <if test="personCount != null and personCount != ''">
                AND (
                IF(person_ids IS NOT NULL AND person_ids != '',
                LENGTH(person_ids) - LENGTH(REPLACE(person_ids, ',', '')) + 1,
                0)
                ) = #{personCount}
            </if>
            <if test="resolveStatusQuery != null and resolveStatusQuery != ''">
                AND id IN (
                SELECT instruction_id FROM (
                SELECT
                i.id as instruction_id,
                <![CDATA[
                CASE
                    WHEN i.instrucation_is_end = 1 AND i.end_time IS NOT NULL AND i.assign_time IS NOT NULL THEN
                        CASE
                            WHEN DATEDIFF(i.end_time, i.assign_time) &lt;= 30  THEN '1'
                            WHEN DATEDIFF(i.end_time, i.assign_time) &lt;= 60  THEN '2'
                            WHEN DATEDIFF(i.end_time, i.assign_time) &lt;= 90  THEN '3'
                            WHEN DATEDIFF(i.end_time, i.assign_time) &lt;= 180 THEN '4'
                            WHEN DATEDIFF(i.end_time, i.assign_time) &lt;= 270 THEN '5'
                            ELSE '6'
                        END
                    WHEN i.assign_time IS NOT NULL THEN
                        CASE
                            WHEN DATEDIFF(NOW(), i.assign_time) &gt;= 270 THEN '11'
                            WHEN DATEDIFF(NOW(), i.assign_time) &gt;= 180 THEN '10'
                            WHEN DATEDIFF(NOW(), i.assign_time) &gt;= 90  THEN '9'
                            WHEN DATEDIFF(NOW(), i.assign_time) &gt;= 60  THEN '8'
                            WHEN DATEDIFF(NOW(), i.assign_time) &gt;= 30  THEN '7'
                            ELSE NULL
                        END
                    ELSE NULL
                END
                ]]> as resolve_status
                FROM t_instruction_info i
                WHERE i.status = 1
                ) t
                WHERE
                <foreach item="status" collection="resolveStatusQuery.split(',')" open="(" separator="OR" close=")">
                    resolve_status = #{status}
                </foreach>
                )
            </if>
        </where>
        ) a
        LEFT JOIN (SELECT IF(COUNT(receive_time)&lt;COUNT( DISTINCT receive_dept ),NULL,MAX( receive_time
        ))reveiveTime,instrucation_id,COUNT(DISTINCT receive_dept) unitCount,MAX( receive_time) mzxReveiveTime from
        t_instruction_receive where status = 1
        <!--  <if test='instructionType != null and instructionType == "3"'>and is_mzx = 2</if>  -->
        GROUP BY instrucation_id) c on a.id=c.instrucation_id
        LEFT JOIN (SELECT count(DISTINCT transfer_dept) num,instruction_id FROM t_instruction_transfer WHERE STATUS = 1
        <!--  <if test='instructionType != null and instructionType == "3"'>and is_mzx = 2</if>  -->
        GROUP BY instruction_id) d on a.id=d.instruction_id
        LEFT JOIN (SELECT instruction_id,count(DISTINCT feedback_dept) num3,max(feedback_time) feedbackTime,max(feedback_time) mzxFeedbackTime from
        t_instruction_feedback WHERE STATUS = 1 AND feedback_is_end = 1
        <!--  <if test='instructionType != null and instructionType == "3"'>and is_mzx = 2</if>  -->
        GROUP BY instruction_id) f on
        d.instruction_id=f.instruction_id
        <if test='instructionType != null and (instructionType == "1" or instructionType == "2")'>and d.num=f.num3</if>
        LEFT JOIN ( SELECT instruction_id countyInstructionId,COUNT(DISTINCT feedback_dept)countDeptNum,MAX(create_time)countyFeedbackTime,MAX(create_time) mzxCountyFeedbackTime from t_instruction_county_feedback where status = 1 and is_end = 1 and is_mzx = 2
        <!--  <if test='instructionType != null and instructionType == "3"'>and is_mzx = 2</if>  -->
        GROUP BY instruction_id
        )h ON a.id = h.countyInstructionId

    </select>
    <select id="AssignNotHandleIds" resultType="java.lang.Long">
        SELECT i.id
        FROM t_instruction_info i
        WHERE i.STATUS = 1
          AND i.id NOT IN (
            SELECT a.instruction_id
            FROM t_instruction_assign a
            WHERE a.STATUS = 1
        )
    </select>

    <select id="ReceiveNotHandleIds" resultType="java.lang.Long">
        SELECT DISTINCT r.instrucation_id
        FROM t_instruction_receive r
                 LEFT JOIN (SELECT * from t_instruction_info where status = 1 ) i ON r.instrucation_id = i.id
        WHERE r.STATUS = 1
          AND r.receive_time is NULL
          AND i.STATUS = 1
          AND r.receive_dept_id = #{deptId}
    </select>


    <select id="FeedBackNotHandleIds" resultType="java.lang.Long">
        SELECT
            a.instruction_id AS instructionId
        FROM
            ( SELECT * FROM t_instruction_transfer WHERE STATUS = 1 AND transfer_dept_id = #{deptId} ) a
                LEFT JOIN ( SELECT DISTINCT transfer_id FROM t_instruction_feedback WHERE STATUS = 1 AND feedback_is_end = 1 AND feedback_dept_id = #{deptId} ) b ON a.id = b.transfer_id
        WHERE
            transfer_id IS NULL UNION
        SELECT
            id
        FROM
            t_instruction_info
        WHERE
            STATUS = 1
          AND create_dept_id != 202
	AND id IN (
	SELECT
		b.instrucation_id
	FROM
		( SELECT * FROM t_instruction_receive WHERE STATUS = 1 AND receive_time IS NOT NULL AND receive_dept_id = #{deptId} ) b
		LEFT JOIN ( SELECT * FROM t_instruction_feedback WHERE STATUS = 1 AND feedback_is_end = 1 AND feedback_dept_id = #{deptId} ) c ON b.id = c.receive_id
	WHERE
		c.receive_id IS NULL
	)
    </select>

    <select id="getIssueRank" resultType="java.util.Map">
        SELECT *
        from (
                 SELECT '婺城区'    AS county,
                        count(*) AS amount
                 FROM t_instruction_info
                 WHERE STATUS = 1
                   AND create_dept_id = 202
                   AND instruction_type = 1
                   AND receive_unit LIKE '%婺城区政法委%'
                 UNION
                 SELECT '金东区'    AS county,
                        count(*) AS amount
                 FROM t_instruction_info
                 WHERE STATUS = 1
                   AND create_dept_id = 202
                   AND instruction_type = 1
                   AND receive_unit LIKE '%金东区政法委%'
                 UNION
                 SELECT '兰溪市'    AS county,
                        count(*) AS amount
                 FROM t_instruction_info
                 WHERE STATUS = 1
                   AND create_dept_id = 202
                   AND instruction_type = 1
                   AND receive_unit LIKE '%兰溪市委政法委%'
                 UNION
                 SELECT '东阳市'    AS county,
                        count(*) AS amount
                 FROM t_instruction_info
                 WHERE STATUS = 1
                   AND create_dept_id = 202
                   AND instruction_type = 1
                   AND receive_unit LIKE '%东阳市委政法委%'
                 UNION
                 SELECT '义乌市'    AS county,
                        count(*) AS amount
                 FROM t_instruction_info
                 WHERE STATUS = 1
                   AND create_dept_id = 202
                   AND instruction_type = 1
                   AND receive_unit LIKE '%义乌市委政法委%'
                 UNION
                 SELECT '永康市'    AS county,
                        count(*) AS amount
                 FROM t_instruction_info
                 WHERE STATUS = 1
                   AND create_dept_id = 202
                   AND instruction_type = 1
                   AND receive_unit LIKE '%永康市委政法委%'
                 UNION
                 SELECT '浦江县'    AS county,
                        count(*) AS amount
                 FROM t_instruction_info
                 WHERE STATUS = 1
                   AND create_dept_id = 202
                   AND instruction_type = 1
                   AND receive_unit LIKE '%浦江县委政法委%'
                 UNION
                 SELECT '开发区'    AS county,
                        count(*) AS amount
                 FROM t_instruction_info
                 WHERE STATUS = 1
                   AND create_dept_id = 202
                   AND instruction_type = 1
                   AND receive_unit LIKE '%开发区政法办%'
                 UNION
                 SELECT '武义县'    AS county,
                        count(*) AS amount
                 FROM t_instruction_info
                 WHERE STATUS = 1
                   AND create_dept_id = 202
                   AND instruction_type = 1
                   AND receive_unit LIKE '%武义县委政法委%'
                 UNION
                 SELECT '磐安县'    AS county,
                        count(*) AS amount
                 FROM t_instruction_info
                 WHERE STATUS = 1
                   AND create_dept_id = 202
                   AND instruction_type = 1
                   AND receive_unit LIKE '%磐安县委政法委%'
             ) a
        ORDER BY amount DESC
    </select>


    <select id="getCountyIssueRank" resultType="java.util.Map">
        SELECT (CASE create_dept_id
                    WHEN '213' THEN '磐安县'
                    WHEN '214' THEN '兰溪市'
                    WHEN '215' THEN '东阳市'
                    WHEN '216' THEN '义乌市'
                    WHEN '217' THEN '浦江县'
                    WHEN '218' THEN '永康市'
                    WHEN '219' THEN '金东区'
                    WHEN '220' THEN '婺城区'
                    WHEN '221' THEN '开发区'
                    WHEN '262' THEN '武义县'
                    ELSE '其他' END) dept,
               COUNT(*) AS         amount
        FROM t_instruction_info
        WHERE STATUS = 1
          AND create_dept_id != 202
          AND instruction_type = 1
        GROUP BY
            create_dept_id
        ORDER BY amount DESC
    </select>

    <select id="getTypeCount" resultType="java.util.Map">
        SELECT
            tb1.petitionType,
            COALESCE ( tb2.amount, 0 ) amount
        FROM
            ( SELECT '到市' AS petitionType UNION ALL SELECT '赴省' AS petitionType UNION ALL SELECT '进京' AS petitionType ) tb1
                LEFT JOIN (
                SELECT
                    ( CASE petition_type WHEN 1 THEN '到市' WHEN 2 THEN '赴省' WHEN 3 THEN '进京' ELSE '其他' END ) petitionType,
                    count(*) amount
                FROM
                    t_instruction_info
                WHERE
                    petition_type IS NOT NULL
                  AND STATUS = 1
                  AND petition_type IN ( 1, 2, 3 )
                GROUP BY
                    petition_type
            ) tb2 ON tb1.petitionType = tb2.petitionType
    </select>

    <select id="getCountyDealHour" resultType="java.util.Map">
        SELECT tb1.county, COALESCE(tb2.dealHour, 0) AS dealHour
        FROM (
        SELECT '婺城区' AS county
        UNION ALL
        SELECT '金东区' AS county
        UNION ALL
        SELECT '兰溪市' AS county
        UNION ALL
        SELECT '东阳市' AS county
        UNION ALL
        SELECT '义乌市' AS county
        UNION ALL
        SELECT '永康市' AS county
        UNION ALL
        SELECT '浦江县' AS county
        UNION ALL
        SELECT '武义县' AS county
        UNION ALL
        SELECT '磐安县' AS county
        UNION ALL
        SELECT '开发区' AS county
        ) tb1
        LEFT JOIN (
        SELECT
            (
                CASE
                    a.feedback_dept
                    WHEN '婺城区政法委' THEN
                        '婺城区'
                    WHEN '金东区政法委' THEN
                        '金东区'
                    WHEN '兰溪市委政法委' THEN
                        '兰溪市'
                    WHEN '东阳市委政法委' THEN
                        '东阳市'
                    WHEN '义乌市委政法委' THEN
                        '义乌市'
                    WHEN '永康市委政法委' THEN
                        '永康市'
                    WHEN '浦江县委政法委' THEN
                        '浦江县'
                    WHEN '武义县委政法委' THEN
                        '武义县'
                    WHEN '磐安县委政法委' THEN
                        '磐安县'
                    WHEN '开发区政法办' THEN
                        '开发区' ELSE '其他'
                    END
                ) county,
            (
                sum( a.hours ) DIV count(*)) dealHour
        FROM
            (
                SELECT
                    a.id,
                    a.receive_unit,
                    a.assign_time,
                    d.receive_time,
                    d.feedback_dept,
                    d.instruction_id,
                    d.feedback_dept_id,
                    TIMESTAMPDIFF( HOUR, a.assign_time, d.receive_time ) hours
                FROM
                    ( SELECT * FROM t_instruction_info WHERE STATUS = 1 AND instruction_type = 1 AND create_dept_id = 202
                    <if test='type == "1"'> AND create_time &gt;= CURDATE()</if>
                    <if test='type == "2"'> AND create_time &gt;= DATE_ADD(CURDATE(), INTERVAL 2-DAYOFWEEK(CURDATE()) DAY)</if>
                    <if test='type == "3"'> AND create_time &gt;= DATE_FORMAT(CURDATE(), '%Y-%m-01')</if>
                        ) a
                        LEFT JOIN ( SELECT * FROM t_instruction_county_feedback WHERE STATUS = 1 AND is_end = 1 ) d ON a.id = d.instruction_id
            ) a
        GROUP BY
            a.feedback_dept
        )tb2 on tb1.county = tb2.county
    </select>

    <select id="getLastFeedBackTimeById" resultType="java.util.Date">
        SELECT
            feedback_time feedBackTime
        FROM
            t_instruction_feedback
        WHERE
            STATUS = 1
          AND feedback_time IS NOT NULL
          AND instruction_id = #{instructionId}
        ORDER BY
            feedback_time DESC
            LIMIT 1
    </select>

    <!--获取指令信息用于重点风险治理-->
    <select id="selectInfo" resultType="com.ruoyi.instruction.domain.rspVo.JazzInstructionInfoStatisticsRspVo">
        SELECT a.id,instruction_title instructionTitle,b.feedback_time feedbackTime,c.instrucation_is_end instrucationIsEnd,a.create_time createTime,a.handle_time handleTime ,c.end_time endTime from (SELECT * from  t_instruction_info where `status`=1   ) a LEFT JOIN t_instruction_feedback b on  a.id =b.instruction_id and b.status=1  LEFT JOIN t_instruction_end c on a.id=c.instruction_id and c.status=1 and instrucation_is_end=1
    </select>

    <select id="selectInfoDetailsById" resultType="com.ruoyi.instruction.domain.rspVo.JazzInstructionInfoStatisticsRspVo">
        SELECT a.id,instruction_title instructionTitle,c.instrucation_is_end instrucationIsEnd,a.create_time createTime,a.handle_time handleTime ,c.end_time endTime, REPLACE(REPLACE(receive_unit,'[',''),']','') as receiveUnit,a.base_info baseInfo from (SELECT * from  t_instruction_info where `status`=1   ) a  LEFT JOIN t_instruction_end c on a.id=c.instruction_id and c.status=1 and instrucation_is_end=1 where a.id=#{id}
    </select>

    <select id="isToEnd" resultType="java.lang.Integer">
        SELECT CASE
                   WHEN (
                            SELECT count(DISTINCT receive_dept)
                            FROM t_instruction_receive
                            WHERE STATUS = 1
                              AND instrucation_id = #{id}
--                               AND is_mzx in (2,3)
                        ) = (
                            SELECT count(DISTINCT feedback_dept)
                            FROM t_instruction_feedback
                            WHERE status = 1
                              AND instruction_id = #{id}
                              AND feedback_is_end = 1
--                               AND is_mzx in (2,3)
                        ) THEN
                       CASE
                           WHEN (
                                    SELECT count(DISTINCT receive_dept)
                                    FROM t_instruction_receive
                                    WHERE STATUS = 1
                                      AND instrucation_id = #{id}
--                                       AND is_mzx in (2,3)
                                ) = 0 THEN 2
                           ELSE 1
                           END
                   ELSE 2
                   END AS result;
    </select>
    <select id="findCountyFeedBackStatus" resultType="java.lang.Integer">
        SELECT
            CASE
                WHEN (

                         SELECT
                             count(DISTINCT transfer_dept)
                         FROM
                             t_instruction_transfer
                         WHERE
                             STATUS = 1
                           AND receive_id IN (
                             SELECT
                                 id
                             FROM
                                 t_instruction_receive
                             WHERE
                                 STATUS = 1
                               AND instrucation_id = #{id} and receive_dept = #{deptName}
                         )
                     ) = (
                         SELECT count(DISTINCT feedback_dept) from t_instruction_feedback where status = 1 and feedback_is_end = 1 and transfer_id in (
                             SELECT
                                 id
                             FROM
                                 t_instruction_transfer
                             WHERE
                                 STATUS = 1
                               AND receive_id IN (
                                 SELECT
                                     id
                                 FROM
                                     t_instruction_receive
                                 WHERE
                                     STATUS = 1
                                   AND instrucation_id = #{id} and receive_dept = #{deptName}
                             )
                         )

                     ) THEN
                    CASE
                        WHEN (
                                 SELECT
                                     count(DISTINCT transfer_dept)
                                 FROM
                                     t_instruction_transfer
                                 WHERE
                                     STATUS = 1
                                   AND receive_id IN (
                                     SELECT
                                         id
                                     FROM
                                         t_instruction_receive
                                     WHERE
                                         STATUS = 1
                                       AND instrucation_id = #{id} and receive_dept = #{deptName}
                                 )
                             ) = 0 THEN 2
                        ELSE 1
                        END
                ELSE 2
                END AS result;

    </select>

    <select id="findQzids" resultType="java.lang.String">
        SELECT qzid FROM t_instruction_info where status = 1 and qzid is not NULL
        UNION
        SELECT qzid from t_instruction_event where status = 1 and qzid is not NULL
    </select>
    <select id="findQzids1" resultType="java.util.Map">
        SELECT qzid,CAST(1 AS SIGNED) type FROM t_instruction_info WHERE STATUS = 1 AND qzid IS NOT NULL
        UNION
        SELECT qzid,CAST(2 AS SIGNED) type FROM t_instruction_event WHERE STATUS = 1 AND qzid IS NOT NULL
    </select>

    <select id="instructionListNewForCounty" resultType="com.ruoyi.instruction.domain.rspVo.InstructionInfomiddleResVo">
        SELECT
        i.id, i.instruction_title AS instructionTitle, i.person_ids AS personIds, i.receive_unit AS receiveUnit,
        i.create_time AS createTime, i.create_dept_id AS createDeptId, i.type, i.source_info AS sourceInfo,
        i.handle_time AS handleTime, i.feedback, i.instruction_content AS instructionContent, i.base_info AS baseInfo,
        i.lead_person_ids AS leadPersonIds, i.is_release AS isRelease,
        (LENGTH(i.receive_unit) - LENGTH(REPLACE(i.receive_unit, ',', '')) + 1) AS receiveUnitCount,
        i.instruction_create_dept_id AS instructionCreateDeptId, i.mission_name AS missionName,
        CONCAT_WS('*', i.company_name, i.mission_name, i.county, i.town) AS companyName,
        i.county, i.town, i.company_address AS companyAddress, i.is_audit AS isAudit,
        i.audit_person AS auditPerson, i.audit_time AS auditTime, i.assign_time AS assignTime,
        i.instrucation_is_end AS instrucationIsEnd, i.feedback_dept AS feedbackDept, i.end_time AS endTime,
        i.department_str AS departmentStr, i.outside_person AS outsidePerson, i.emergency_degree AS emergencyDegree,
        i.spare_num AS spareNum, i.mzx_type AS mzxType, i.mzx_place AS mzxPlace,
        i.mzx_nature AS mzxNature, i.mzx_relation AS mzxRelation, i.mzx_occur_time AS mzxOccurTime,
        i.mzx_duty_dept AS mzxDutyDept, i.mzx_source_info AS mzxSourceInfo, i.case_type AS caseType,
        i.instruction_type AS instructionType, i.transfer_id AS transferId,

        c.reveiveTime, c.mzxReveiveTime,
        d.num, d.transferDept,
        f.num3, f.feedbackTime, f.mzxFeedbackTime,
        h.countyInstructionId, h.countDeptNum, h.countyFeedbackTime, h.mzxCountyFeedbackTime

        FROM
        t_instruction_info i

        LEFT JOIN (
        SELECT MAX(receive_time) AS reveiveTime, MAX(receive_time) AS mzxReveiveTime, instrucation_id, id
        FROM t_instruction_receive
        WHERE STATUS = 1
        <if test="receiveUnit != null and receiveUnit != ''"> AND receive_dept = #{receiveUnit} </if>
        <if test="params.receiveDeptId != null"> AND receive_dept_id = #{params.receiveDeptId} </if>
        GROUP BY instrucation_id, id
        ) c ON i.id = c.instrucation_id

        LEFT JOIN (
        SELECT count(DISTINCT transfer_dept) AS num, instruction_id, receive_id, GROUP_CONCAT(transfer_dept) AS transferDept
        FROM t_instruction_transfer
        WHERE STATUS = 1
        <if test="params.deptNameList != null and params.deptNameList.size > 0">
            AND transfer_dept_id IN <foreach item="id" collection="params.deptNameList" open="(" separator="," close=")">#{id}</foreach>
        </if>
        <if test="params.feedBackName != null and params.feedBackName != ''"> AND transfer_dept = #{params.feedBackName} </if>
        <if test="params.receiveDeptId != null"> AND transfer_dept_id = #{params.receiveDeptId} </if>
        GROUP BY instruction_id, receive_id
        ) d ON c.id = d.receive_id

        LEFT JOIN (
        SELECT instruction_id, count(DISTINCT feedback_dept) AS num3, MAX(feedback_time) AS feedbackTime, MAX(feedback_time) AS mzxFeedbackTime
        FROM t_instruction_feedback
        WHERE STATUS = 1 AND feedback_is_end = 1
        <if test="params.feedBackName != null and params.feedBackName != ''"> AND feedback_dept = #{params.feedBackName} </if>
        <if test="params.receiveDeptId != null"> AND feedback_dept_id = #{params.receiveDeptId} </if>
        <if test="params.deptNameList != null and params.deptNameList.size > 0">
            AND feedback_dept_id IN <foreach item="id" collection="params.deptNameList" open="(" separator="," close=")">#{id}</foreach>
        </if>
        GROUP BY instruction_id
        ) f ON d.instruction_id = f.instruction_id AND d.num = f.num3

        LEFT JOIN (
        SELECT instruction_id AS countyInstructionId, COUNT(DISTINCT feedback_dept) AS countDeptNum, MAX(create_time) AS countyFeedbackTime, MAX(create_time) AS mzxCountyFeedbackTime
        FROM t_instruction_county_feedback
        WHERE STATUS = 1 AND is_end = 1 AND is_mzx = 2
        <if test="receiveUnit != null and receiveUnit != ''"> AND feedback_dept = #{receiveUnit} </if>
        <if test="params.receiveDeptId != null"> AND feedback_dept_id = #{params.receiveDeptId} </if>
        GROUP BY instruction_id
        ) h ON i.id = h.countyInstructionId

        <where>
            <if test="instructionCode != null and instructionCode != ''">AND i.instruction_code = #{instructionCode}</if>
            <if test="town != null and town != ''">AND i.town = #{town}</if>
            <if test="instructionTitle != null and instructionTitle != ''">AND i.instruction_title LIKE CONCAT('%', #{instructionTitle}, '%')</if>

            <if test="params.beginTime != null and params.beginTime != ''"><![CDATA[ AND i.handle_time >= #{params.beginTime} ]]></if>
            <if test="params.endTime != null and params.endTime != ''"><![CDATA[ AND i.handle_time <= #{params.endTime} ]]></if>
            <if test="params.beginAssignTime != null and params.beginAssignTime != ''"><![CDATA[ AND i.assign_time >= #{params.beginAssignTime} ]]></if>
            <if test="params.endAssignTime != null and params.endAssignTime != ''"><![CDATA[ AND i.assign_time <= #{params.endAssignTime} ]]></if>
            <if test="params.beginPushTime != null and params.beginPushTime != ''"><![CDATA[ AND i.push_time >= #{params.beginPushTime} ]]></if>
            <if test="params.endPushTime != null and params.endPushTime != ''"><![CDATA[ AND i.push_time <= #{params.endPushTime} ]]></if>
            <if test="params.queryStartTime != null"><![CDATA[ AND i.create_time >= #{params.queryStartTime} ]]></if>
            <if test="params.queryEndTime != null"><![CDATA[ AND i.create_time <= #{params.queryEndTime} ]]></if>

            <if test="receiveUnit != null and receiveUnit != ''">AND (i.receive_unit LIKE CONCAT('%', #{receiveUnit}, '%') OR i.department_str LIKE CONCAT('%', #{receiveUnit}, '%'))</if>
            <if test="emergencyDegree != null and emergencyDegree != ''">AND i.emergency_degree = #{emergencyDegree}</if>
            <if test="groupId != null">AND i.group_id = #{groupId}</if>
            <if test="type != null and type != ''">AND i.type = #{type}</if>
            <if test="sourceInfo != null and sourceInfo != ''">AND i.source_info = #{sourceInfo}</if>
            <if test="instructionContent != null and instructionContent != ''">AND i.instruction_content = #{instructionContent}</if>
            <if test="baseInfo != null and baseInfo != ''">AND i.base_info = #{baseInfo}</if>
            <if test="creatorBy != null and creatorBy != ''">AND i.creator_by = #{creatorBy}</if>
            <if test="status != null and status != ''">AND i.status = #{status}</if>
            <if test="instructionStatus != null and instructionStatus != ''">AND i.instruction_status = #{instructionStatus}</if>
            <if test="feedback != null and feedback != ''">AND i.feedback = #{feedback}</if>
            <if test="personIds != null and personIds != ''">AND i.person_ids = #{personIds}</if>
            <if test="createDeptId != null and createDeptId != ''">AND i.create_dept_id = #{createDeptId}</if>

            <if test="params.ids != null and params.ids.size > 0">
                AND i.id IN <foreach item="id" collection="params.ids" open="(" separator="," close=")">#{id}</foreach>
            </if>
            <if test="params.handleDelayIds != null and params.handleDelayIds.size > 0">
                AND i.id IN <foreach item="id" collection="params.handleDelayIds" open="(" separator="," close=")">#{id}</foreach>
            </if>
            <if test="params.receiveNotIds != null and params.receiveNotIds.size > 0">
                AND i.id IN <foreach item="id" collection="params.receiveNotIds" open="(" separator="," close=")">#{id}</foreach>
            </if>

            <if test="petitionType != null">AND i.petition_type = #{petitionType}</if>
            <if test="pushTime != null">AND i.push_time = #{pushTime}</if>
            <if test="infoCategory != null and infoCategory != ''">AND i.info_category = #{infoCategory}</if>

            <if test="instructionType != null">
                <if test="instructionType == 7">AND i.instruction_type IN (3,6)</if>
                <if test="instructionType != 7">AND i.instruction_type = #{instructionType}</if>
            </if>

            <if test="instructionCreateDeptId != null">AND i.instruction_create_dept_id = #{instructionCreateDeptId}</if>
            <if test="missionName != null and missionName != ''">AND i.mission_name LIKE CONCAT('%', #{missionName}, '%')</if>
            <if test="companyName != null and companyName != ''">AND i.company_name LIKE CONCAT('%', #{companyName}, '%')</if>
            <if test="companyAddress != null and companyAddress != ''">AND i.company_address LIKE CONCAT('%', #{companyAddress}, '%')</if>
            <if test="isAudit != null and isAudit != ''">AND i.is_audit = #{isAudit}</if>
            <if test="auditPerson != null and auditPerson != ''">AND i.audit_person = #{auditPerson}</if>
            <if test="auditTime != null">AND i.audit_time = #{auditTime}</if>
            <if test="eventProperties != null">AND i.event_properties = #{eventProperties}</if>
            <if test="departmentStr != null and departmentStr != ''">AND (i.department_str LIKE CONCAT('%', #{departmentStr}, '%') OR i.receive_unit LIKE CONCAT('%', #{params.receiveDeptName}, '%'))</if>

            <if test="mzxPlace != null and mzxPlace != ''">AND i.mzx_place = #{mzxPlace}</if>
            <if test="mzxNature != null and mzxNature != ''">AND i.mzx_nature = #{mzxNature}</if>
            <if test="mzxRelation != null and mzxRelation != ''">AND i.mzx_relation = #{mzxRelation}</if>
            <if test="mzxOccurTime != null">AND i.mzx_occur_time = #{mzxOccurTime}</if>
            <if test="mzxDutyDept != null and mzxDutyDept != ''">AND i.mzx_duty_dept = #{mzxDutyDept}</if>
            <if test="mzxType != null and mzxType != ''">AND i.mzx_type = #{mzxType}</if>
            <if test="mzxSourceInfo != null and mzxSourceInfo != ''">AND i.mzx_source_info = #{mzxSourceInfo}</if>

            <if test="params.departments != null and params.departments.size > 0">
                AND (<foreach item="department" collection="params.departments" separator=" OR ">i.department_str LIKE CONCAT('%', #{department}, '%')</foreach>)
            </if>
            <if test="params.receiveUnits != null and params.receiveUnits.size > 0">
                AND (<foreach item="receiveUnit" collection="params.receiveUnits" separator=" OR ">i.receive_unit LIKE CONCAT('%', #{receiveUnit}, '%')</foreach>)
            </if>

            <if test="resolveStatusQuery != null and resolveStatusQuery != ''">
                AND (
                CASE
                <![CDATA[
                    WHEN i.instrucation_is_end = 1 AND i.end_time IS NOT NULL AND i.assign_time IS NOT NULL THEN
                        (CASE
                            WHEN DATEDIFF(i.end_time, i.assign_time) <= 30 THEN '1'
                            WHEN DATEDIFF(i.end_time, i.assign_time) <= 60 THEN '2'
                            WHEN DATEDIFF(i.end_time, i.assign_time) <= 90 THEN '3'
                            WHEN DATEDIFF(i.end_time, i.assign_time) <= 180 THEN '4'
                            WHEN DATEDIFF(i.end_time, i.assign_time) <= 270 THEN '5'
                            ELSE '6'
                        END)
                    WHEN i.assign_time IS NOT NULL THEN
                        (CASE
                            WHEN DATEDIFF(NOW(), i.assign_time) >= 180 THEN '10'
                            WHEN DATEDIFF(NOW(), i.assign_time) >= 90 THEN '9'
                            WHEN DATEDIFF(NOW(), i.assign_time) >= 60 THEN '8'
                            WHEN DATEDIFF(NOW(), i.assign_time) >= 30 THEN '7'
                            ELSE NULL
                        END)
                    ELSE NULL
                    ]]>
                END
                ) = #{resolveStatusQuery}
            </if>

            <if test="params.transferDepts != null and params.transferDepts.size > 0">
                AND (<foreach item="transferDept" collection="params.transferDepts" separator=" OR ">d.transferDept LIKE CONCAT('%', #{transferDept}, '%')</foreach>)
            </if>
        </where>
    </select>
    <select id="findToRemind" resultType="java.lang.Long">
        SELECT
            a.id
        FROM
            (
                SELECT
                    id,
                    handle_time,
                    create_time,
                    TIMESTAMPDIFF(MINUTE, handle_time, NOW()) AS minutes_diff
                FROM
                    t_instruction_info
                WHERE
                    STATUS = 1
                  AND create_time &gt; '2023-06-15'
            ) a
                LEFT JOIN ( SELECT * FROM t_instruction_end WHERE STATUS = 1 AND instrucation_is_end = 1 ) b ON a.id = b.instruction_id
        where end_time is null and minutes_diff &gt;-15
    </select>

    <select id="ToRemindInfo" resultType="com.ruoyi.instruction.domain.rspVo.InstructionRemindVo">
        SELECT
        a.*,
        c.isReceiveDept,
        c.isReceiveCount,
        d.transferDept,
        (LENGTH(d.transferDept) - LENGTH(REPLACE(d.transferDept, ',', ''))+1) AS transferDeptCount,
        e.feedbackDept,
        (LENGTH(e.feedbackDept) - LENGTH(REPLACE(e.feedbackDept, ',', ''))+1) AS feedbackDeptCount,
        f.countyFeedbackDept,
        (LENGTH(f.countyFeedbackDept) - LENGTH(REPLACE(f.countyFeedbackDept, ',', ''))+1) AS countyFeedbackDeptCount,
        TIMESTAMPDIFF(DAY, handleTime, NOW()) AS minutesDiff
        FROM
        ( SELECT id, instruction_title instructionTitle, handle_time handleTime, REPLACE(REPLACE(receive_unit,'[',''),']','') receiveUnit,(LENGTH(receive_unit) - LENGTH(REPLACE(receive_unit, ',', ''))+1) AS receiveUnitCount, create_dept_id createDeptId FROM t_instruction_info WHERE STATUS = 1
            <if test="ids != null and ids.size() >0">
                AND id in
                <foreach item="id" collection="ids" open="(" separator="," close=")">
                    #{id}
                </foreach>
            </if>
            ) a
        LEFT JOIN ( SELECT * FROM t_instruction_end WHERE STATUS = 1 AND instrucation_is_end = 1 ) b ON a.id = b.instruction_id
        LEFT JOIN ( SELECT instrucation_id, GROUP_CONCAT( receive_dept ) isReceiveDept, count(*) isReceiveCount FROM t_instruction_receive WHERE STATUS = 1 AND receive_time IS NOT NULL GROUP BY instrucation_id ) c ON a.id = c.instrucation_id
        LEFT JOIN (
        SELECT instruction_id,GROUP_CONCAT(transfer_dept) transferDept from t_instruction_transfer where status = 1 GROUP BY instruction_id
        )d on a.id = d.instruction_id
        LEFT JOIN (
        SELECT instruction_id,GROUP_CONCAT(feedback_dept) feedbackDept from t_instruction_feedback where status = 1 and feedback_is_end = 1 GROUP BY instruction_id
        )e
        on a.id = e.instruction_id
        LEFT JOIN (
        SELECT instruction_id,GROUP_CONCAT(DISTINCT feedback_dept)countyFeedbackDept from t_instruction_county_feedback where status = 1 and is_end = 1 GROUP BY instruction_id
        )f
        on a.id = f.instruction_id
        WHERE
        b.end_time IS NULL
    </select>

    <select id="getCountyInstructionList"
            resultType="com.ruoyi.instruction.domain.rspVo.InstructionInfomiddleResVo">
        SELECT
        *
        FROM
        (
        SELECT
        id,
        instruction_title instructionTitle,
        person_ids personIds,
        receive_unit receiveUnit,
        create_time createTime,
        create_dept_id createDeptId,
        type,
        source_info sourceInfo,
        handle_time handleTime,
        feedback,
        instruction_content instructionContent,
        base_info baseInfo,
        lead_person_ids leadPersonIds,
        is_release isRelease,
        ( LENGTH( receive_unit ) - LENGTH( REPLACE ( receive_unit, ',', '' ))+ 1 ) AS receiveUnitCount,
        instruction_create_dept_id AS instructionCreateDeptId,
        mission_name AS missionName,
        CONCAT_WS('*',company_name,mission_name,county,town) AS companyName,
        county,
        town,
        company_address as companyAddress,
        is_audit as isAudit,
        audit_person as auditPerson,
        audit_time as auditTime,
        assign_time as assignTime,
        instrucation_is_end as instrucationIsEnd,
        feedback_dept as feedbackDept,
        end_time as endTime,
        mzx_id as mzxId,
        outside_person as outsidePerson,
        emergency_degree as emergencyDegree,
        spare_num as spareNum,
        department_str as departmentStr,
        mzx_type as mzxType,
        mzx_place as mzxPlace,
        mzx_nature as mzxNature,
        mzx_relation as mzxRelation,
        mzx_occur_time as mzxOccurTime,
        mzx_duty_dept as mzxDutyDept,
        mzx_source_info as mzxSourceInfo,
        case_type as caseType,
        instruction_type as instructionType,
        transfer_id as transferId
        FROM
        t_instruction_info
        <where>
            create_dept_id != 202
            <if test="instructionCode != null  and instructionCode != ''">and instruction_code = #{instructionCode}</if>
            <if test="instructionTitle != null  and instructionTitle != ''">and instruction_title like concat('%',
                #{instructionTitle},'%')
            </if>
            <if test="params.beginTime != null and params.beginTime != ''"><!-- 开始时间检索 -->
                AND date_format(handle_time,'%y%m%d') &gt;= date_format(#{params.beginTime},'%y%m%d')
            </if>
            <if test="params.endTime != null and params.endTime != ''"><!-- 结束时间检索 -->
                AND date_format(handle_time,'%y%m%d') &lt;= date_format(#{params.endTime},'%y%m%d')
            </if>
            <if test="params.beginPushTime != null and params.beginPushTime != ''"><!-- 开始时间检索 -->
                AND date_format(push_time,'%y%m%d') &gt;= date_format(#{params.beginPushTime},'%y%m%d')
            </if>
            <if test="params.endPushTime != null and params.endPushTime != ''"><!-- 结束时间检索 -->
                AND date_format(push_time,'%y%m%d') &lt;= date_format(#{params.endPushTime},'%y%m%d')
            </if>
            <if test="params.beginAssignTime != null and params.beginAssignTime != ''"><!-- 开始时间检索 -->
                AND date_format(assign_time,'%y%m%d') &gt;= date_format(#{params.beginAssignTime},'%y%m%d')
            </if>
            <if test="params.endAssignTime != null and params.endAssignTime != ''"><!-- 结束时间检索 -->
                AND date_format(assign_time,'%y%m%d') &lt;= date_format(#{params.endAssignTime},'%y%m%d')
            </if>
            <if test="params.queryStartTime != null "><!-- 开始时间检索 -->
                AND date_format(create_time,'%y%m%d') &gt;= date_format(#{params.queryStartTime},'%y%m%d')
            </if>
            <if test="params.queryEndTime != null "><!-- 结束时间检索 -->
                AND date_format(create_time,'%y%m%d') &lt;= date_format(#{params.queryEndTime},'%y%m%d')
            </if>
            <if test="receiveUnit != null  and receiveUnit != ''">and (receive_unit like concat('%', #{receiveUnit},'%') or department_str like concat('%', #{receiveUnit}, '%'))
            </if>
            <if test="emergencyDegree != null  and emergencyDegree != ''">and emergency_degree = #{emergencyDegree}</if>
            <if test="groupId != null ">and group_id = #{groupId}</if>
            <if test="type != null  and type != ''">and type = #{type}</if>
            <if test="sourceInfo != null  and sourceInfo != ''">and source_info = #{sourceInfo}</if>
            <if test="instructionContent != null  and instructionContent != ''">and instruction_content =
                #{instructionContent}
            </if>
            <if test="baseInfo != null  and baseInfo != ''">and base_info = #{baseInfo}</if>
            <if test="creatorBy != null  and creatorBy != ''">and creator_by = #{creatorBy}</if>
            <if test="status != null  and status != ''">and status = #{status}</if>
            <if test="instructionStatus != null  and instructionStatus != ''">and instruction_status =
                #{instructionStatus}
            </if>
            <if test="feedback != null  and feedback != ''">and feedback = #{feedback}</if>
            <if test="personIds != null  and personIds != ''">and person_ids = #{personIds}</if>
            <if test="createDeptId != null and createDeptId !=''">and create_dept_id = #{createDeptId}</if>
            <if test="params.ids != null and params.ids != ''">
                AND id in
                <foreach item="id" collection="params.ids" open="(" separator="," close=")">
                    #{id}
                </foreach>
            </if>
            <if test="petitionType != null ">
                and petition_type = #{petitionType}
            </if>
            <if test="pushTime != null ">and push_time = #{pushTime}</if>
            <if test="infoCategory != null  and infoCategory != ''">and info_category = #{infoCategory}</if>
            <if test="instructionType != null and instructionType != 7 ">and instruction_type = #{instructionType}</if>
            <if test="instructionType != null and instructionType == 7 ">and instruction_type in (3,6)</if>
            <if test="instructionCreateDeptId != null ">and instruction_create_dept_id = #{instructionCreateDeptId}</if>
            <if test="missionName != null  and missionName != ''">and mission_name like concat('%', #{missionName},
                '%')
            </if>
            <if test="companyName != null  and companyName != ''">and company_name like concat('%', #{companyName},
                '%')
            </if>
            <if test="companyAddress != null  and companyAddress != ''">and company_address like concat('%',
                #{companyAddress}, '%')
            </if>
            <if test="isAudit != null  and isAudit != ''">and is_audit = #{isAudit}</if>
            <if test="auditPerson != null  and auditPerson != ''">and audit_person = #{auditPerson}</if>
            <if test="eventProperties != null ">and event_properties = #{eventProperties}</if>
            <if test="auditTime != null ">and audit_time = #{auditTime}</if>
            <if test="departmentStr != null  and departmentStr != ''">and (department_str like concat('%', #{departmentStr}, '%') or receive_unit like concat('%', #{params.feedBackName},'%'))</if>
            <if test="eventProperties != null ">and event_properties = #{eventProperties}</if>
            <if test="mzxPlace != null  and mzxPlace != ''">and mzx_place = #{mzxPlace}</if>
            <if test="mzxNature != null  and mzxNature != ''">and mzx_nature = #{mzxNature}</if>
            <if test="mzxRelation != null  and mzxRelation != ''">and mzx_relation = #{mzxRelation}</if>
            <if test="mzxOccurTime != null ">and mzx_occur_time = #{mzxOccurTime}</if>
            <if test="mzxDutyDept != null  and mzxDutyDept != ''">and mzx_duty_dept = #{mzxDutyDept}</if>
            <if test="mzxType != null  and mzxType != ''">and mzx_type = #{mzxType}</if>
            <if test="mzxSourceInfo != null  and mzxSourceInfo != ''">and mzx_source_info = #{mzxSourceInfo}</if>
            <if test="params.departments != null and params.departments != ''">
                AND
                <foreach item="department" collection="params.departments" separator="or">
                    department_str like concat('%', #{department}, '%')
                </foreach>
            </if>
            <if test="params.receiveUnits != null and params.receiveUnits != ''">
                AND
                <foreach item="receiveUnit" collection="params.receiveUnits" separator="or">
                    receive_unit like concat('%', #{receiveUnit}, '%')
                </foreach>
            </if>
            <if test="params.receiveNotIds != null and params.receiveNotIds != ''">
                AND id in
                <foreach item="id" collection="params.receiveNotIds" open="(" separator="," close=")">
                    #{id}
                </foreach>
            </if>
        </where>
        ) a
        LEFT JOIN ( SELECT MAX(receive_time) reveiveTime,MAX(receive_time)  mzxReveiveTime,instrucation_id,id,
        ( LENGTH( GROUP_CONCAT(receive_dept) ) - LENGTH( REPLACE ( GROUP_CONCAT(receive_dept), ',', '' ))+ 1 ) AS
        unitCount
        FROM t_instruction_receive WHERE STATUS = 1 and receive_time is not null
        <if test="receiveUnit != null  and receiveUnit != ''">and receive_dept = #{receiveUnit}</if>
        <if test="params.receiveDeptId != null">AND receive_dept_id = #{params.receiveDeptId}</if>
        <!-- <if test='instructionType != null and instructionType == "3"'>and is_mzx = 2</if>-->
        GROUP BY instrucation_id ) c ON a.id = c.instrucation_id
        <if test='params.roles != null and params.roles=="assgin" and  params.mzx != null and params.mzx == 2'>
            AND c.unitCount = a.receiveUnitCount
        </if>
<!--        <if test='params.roles != null and params.roles=="assgin" and  params.mzx != null and params.mzx == 1'>-->
<!--            AND c.unitCount = a.receiveUnitCount-->
<!--        </if>-->
        LEFT JOIN (
        SELECT
        instruction_id,
        count( DISTINCT feedback_dept ) num3,
        max( feedback_time ) feedbackTime,
        max( feedback_time ) mzxFeedbackTime
        FROM
        t_instruction_feedback
        WHERE
        STATUS = 1
        AND feedback_is_end = 1
        <if test="params.feedBackName != null">
            AND feedback_dept = #{params.feedBackName}
        </if>
        <!-- <if test='instructionType != null and instructionType == "3"'>and is_mzx = 2</if>-->
        GROUP BY
        instruction_id
        ) f ON a.id = f.instruction_id
        <if test='params.roles != null and params.roles=="assgin" and  params.mzx != null and params.mzx == 2'>
            AND a.receiveUnitCount &lt;= f.num3
        </if>
<!--        <if test='params.roles != null and params.roles=="assgin" and  params.mzx != null and params.mzx == 1'>-->
<!--            AND a.receiveUnitCount = f.num3-->
<!--        </if>-->
    </select>

    <select id="countyFeedBackNotHandleIds" resultType="java.lang.Long">
        SELECT
            a.id
        FROM
                ( SELECT * FROM t_instruction_info WHERE STATUS = 1 AND create_dept_id != 202 ) a
                    LEFT JOIN ( SELECT * FROM t_instruction_receive WHERE STATUS = 1 AND receive_time IS NOT NULL and receive_dept = #{deptName} ) b ON a.id = b.instrucation_id
                    LEFT JOIN (SELECT * from t_instruction_feedback where status = 1 )c on b.id = c.receive_id
        where b.id is not NULL and c.feedback_time is NULL
        UNION
        SELECT
            b.instruction_id
        FROM
            (
                SELECT
                    a.*,
                    e.instrucation_is_end,
                    e.end_dept
                FROM
                    (
                        SELECT
                            MAX(end_time) AS max_end_time,
                            COUNT(*) AS endSum,
                            instruction_id
                        FROM
                            t_instruction_end
                        WHERE
                            STATUS = 1
                        GROUP BY
                            instruction_id
                        HAVING
                            endSum > 1
                    ) a
                        JOIN t_instruction_end e ON e.instruction_id = a.instruction_id
                        AND e.end_time = a.max_end_time
                WHERE
                    e.instrucation_is_end = 2
            ) b
                LEFT JOIN (
                SELECT
                    instruction_id,
                    feedback_dept,
                    MAX(feedback_time) AS feedback_time
                FROM
                    t_instruction_feedback
                WHERE
                    STATUS = 1 and feedback_dept = #{deptName}
                GROUP BY
                    instruction_id,
                    feedback_dept
            ) f ON f.instruction_id = b.instruction_id
                AND f.feedback_dept = b.end_dept
        where max_end_time &gt; f.feedback_time
    </select>

    <select id="countyEndNotHandleIds" resultType="java.lang.Long">
        SELECT
            instruction_id
        FROM
            (
                SELECT
                    b.*,
                    c.*,
                    ( LENGTH( b.feedBackStr ) - LENGTH( REPLACE ( b.feedBackStr, ',', '' ))+ 1 ) AS feedbackUnitCount,
                    ( LENGTH( c.receiveDeptStr ) - LENGTH( REPLACE ( c.receiveDeptStr, ',', '' ))+ 1 ) AS receiveUnitCount
                FROM
                        ( SELECT id FROM t_instruction_info WHERE STATUS = 1 AND create_dept_id = #{deptId} ) a
                            LEFT JOIN ( SELECT instruction_id, GROUP_CONCAT( DISTINCT feedback_dept ) feedBackStr FROM t_instruction_feedback WHERE STATUS = 1 AND feedback_is_end = 1 GROUP BY instruction_id ) b ON a.id = b.instruction_id
                            LEFT JOIN ( SELECT instrucation_id, GROUP_CONCAT( receive_dept ) receiveDeptStr FROM t_instruction_receive WHERE STATUS = 1 GROUP BY instrucation_id ) c ON a.id = c.instrucation_id
                            LEFT JOIN (
                        SELECT
                            a.*,
                            e.instrucation_is_end,
                            e.end_dept,
                            e.id
                        FROM
                            ( SELECT MAX( end_time ) AS max_end_time, COUNT(*) AS endSum, instruction_id FROM t_instruction_end WHERE STATUS = 1 GROUP BY instruction_id ) a
                                JOIN t_instruction_end e ON e.instruction_id = a.instruction_id
                                AND e.end_time = a.max_end_time
                    ) e ON a.id = e.instruction_id
                WHERE
                    e.id IS NULL
                   OR instrucation_is_end = 2
            ) d
        WHERE
            feedbackUnitCount = receiveUnitCount
    </select>


    <select id="countyReceiveNotHandleIds" resultType="java.lang.Long">
        SELECT DISTINCT r.instrucation_id
        FROM t_instruction_receive r
                 LEFT JOIN (SELECT * from t_instruction_info where status = 1 and create_dept_id !=202) i ON r.instrucation_id = i.id
        WHERE r.STATUS = 1
          AND r.receive_time is NULL
          AND i.STATUS = 1
          AND r.receive_dept = #{deptName}
    </select>
    <select id="CountyNotHandleIds" resultType="java.lang.Long">
        SELECT DISTINCT a.id
        FROM t_instruction_info a
                 INNER JOIN t_instruction_receive b ON a.id = b.instrucation_id
            AND b.status = 1
            AND b.receive_dept_id = #{deptId}
                 INNER JOIN (
            SELECT
                receive_id,
                COUNT(transfer_dept) AS transferDeptCount
            FROM t_instruction_transfer
            WHERE status = 1
            GROUP BY receive_id
        ) c ON b.id = c.receive_id
                 INNER JOIN (
            SELECT
                receive_id,
                COUNT(DISTINCT feedback_dept) AS feedbackCount
            FROM t_instruction_feedback
            WHERE status = 1
              AND feedback_is_end = 1
            GROUP BY receive_id
        ) e ON c.receive_id = e.receive_id AND c.transferDeptCount = e.feedbackCount
        WHERE a.status = 1
          AND a.create_dept_id = 202
          AND NOT EXISTS (
                SELECT 1
                FROM t_instruction_end f
                WHERE f.status = 1
                  AND f.instrucation_is_end = 1
                  AND f.instruction_id = a.id
            )
          AND NOT EXISTS (
                SELECT 1
                FROM t_instruction_county_feedback g
                WHERE g.status = 1
                  AND g.feedback_dept_id = #{deptId}
                  AND g.is_end = 1
                  AND g.instruction_id = a.id
            )
    </select>

    <select id="EndNoHandleIds" resultType="java.lang.Long">
        SELECT
        distinct a.id
        FROM
        (
        SELECT
        id,
        receive_unit,
        CASE
        WHEN receive_unit IS NULL OR LENGTH(receive_unit) &lt;= 2 THEN 0
        ELSE LENGTH(SUBSTRING(receive_unit, 2, LENGTH(receive_unit) - 2)) - LENGTH(REPLACE(SUBSTRING(receive_unit, 2, LENGTH(receive_unit) - 2), ',', '')) + 1
        END AS unit_count
        FROM
        t_instruction_info
        WHERE
        STATUS = 1
        AND create_dept_id = 202
        AND instruction_type != 2
        AND ( instrucation_is_end IS NULL OR instrucation_is_end = 2 )
        AND end_time IS NULL
        ) a
        INNER JOIN (
        SELECT
        instruction_id,
        COUNT(DISTINCT feedback_dept) AS acount
        FROM
        t_instruction_county_feedback
        WHERE
        STATUS = 1
        AND is_end = 1
        AND is_mzx = 2
        GROUP BY
        instruction_id
        ) b ON a.id = b.instruction_id
        WHERE
        <![CDATA[
        a.unit_count > 0 AND a.unit_count <= b.acount
    ]]>
    </select>
    <select id="getCountyInstructionCount" resultType="java.util.Map">
        SELECT
            a.deptName,IFNULL(b.acount,0)acount
        FROM
            (
                SELECT
                    dept_name AS deptName
                FROM
                    sys_dept
                WHERE
                    STATUS = 0
                  AND del_flag = 0
                  AND dept_id IN ( 204, 205, 206, 207, 208, 209, 210, 211, 212, 261 )
            ) a
                LEFT JOIN (
                SELECT
                    CASE
                        create_dept_id
                        WHEN 213 THEN
                            '磐安县'
                        WHEN 214 THEN
                            '兰溪市'
                        WHEN 215 THEN
                            '东阳市'
                        WHEN 216 THEN
                            '义乌市'
                        WHEN 217 THEN
                            '浦江县'
                        WHEN 218 THEN
                            '永康市'
                        WHEN 219 THEN
                            '金东区'
                        WHEN 220 THEN
                            '婺城区'
                        WHEN 221 THEN
                            '开发区'
                        END AS deptName,
                    count(*) acount
                FROM
                    t_instruction_info
                WHERE
                    STATUS = 1
                  AND create_dept_id != 202
		AND instruction_type = 1
                GROUP BY
                    create_dept_id
            ) b ON a.deptName = b.deptName
    </select>

    <select id="getCountyStatistics" resultType="java.util.Map">
        SELECT
            sum(
                    TIMESTAMPDIFF( HOUR, a.create_time, b.end_time )) hourSum,
            count(*) recordTotal,
            (SELECT count(*) from t_instruction_info where status = 1 and instruction_type = 1 and create_dept_id = #{deptId} ORDER BY create_time DESC)instructionTotal
        FROM
            ( SELECT * FROM t_instruction_info WHERE STATUS = 1 AND instruction_type = 1 AND create_dept_id = #{deptId} ORDER BY create_time DESC ) a
                LEFT JOIN ( SELECT * FROM t_instruction_end WHERE STATUS = 1 ) b ON a.id = b.instruction_id
        WHERE
            b.instrucation_is_end = 1
    </select>

    <select id="getCountyDealTime" resultType="java.util.Map">
        SELECT IFNULL(c.hourSum DIV c.acount,0) as averageHour,c.* from (

                                                                            SELECT
                                                                                IFNULL( sum( TIMESTAMPDIFF( HOUR, a.create_time, b.maxTime )), 0 ) hourSum,
                                                                                '磐安县' AS county,
                                                                                count(*) acount
                                                                            FROM
                                                                                ( SELECT * FROM t_instruction_info WHERE STATUS = 1 AND instruction_type = 1 AND create_dept_id = 202 ) a
                                                                                    LEFT JOIN (
                                                                                    SELECT
                                                                                        id,
                                                                                        feedback_dept,
                                                                                        MAX( create_time ) maxTime,
                                                                                        instruction_id,
                                                                                        count(*)
                                                                                    FROM
                                                                                        t_instruction_county_feedback
                                                                                    WHERE
                                                                                        STATUS = 1
                                                                                      AND is_end = 1
                                                                                      AND feedback_dept = '磐安县委政法委'
                                                                                    GROUP BY
                                                                                        instruction_id
                                                                                ) b ON a.id = b.instruction_id
                                                                            WHERE
                                                                                maxTime IS NOT NULL UNION
                                                                            SELECT
                                                                                IFNULL( sum( TIMESTAMPDIFF( HOUR, a.create_time, b.maxTime )), 0 ) hourSum,
                                                                                '兰溪市' AS county,
                                                                                count(*) acount
                                                                            FROM
                                                                                ( SELECT * FROM t_instruction_info WHERE STATUS = 1 AND instruction_type = 1 AND create_dept_id = 202 ) a
                                                                                    LEFT JOIN (
                                                                                    SELECT
                                                                                        id,
                                                                                        feedback_dept,
                                                                                        MAX( create_time ) maxTime,
                                                                                        instruction_id,
                                                                                        count(*)
                                                                                    FROM
                                                                                        t_instruction_county_feedback
                                                                                    WHERE
                                                                                        STATUS = 1
                                                                                      AND is_end = 1
                                                                                      AND feedback_dept = '兰溪市委政法委'
                                                                                    GROUP BY
                                                                                        instruction_id
                                                                                ) b ON a.id = b.instruction_id
                                                                            WHERE
                                                                                maxTime IS NOT NULL UNION
                                                                            SELECT
                                                                                IFNULL( sum( TIMESTAMPDIFF( HOUR, a.create_time, b.maxTime )), 0 ) hourSum,
                                                                                '东阳市' AS county,
                                                                                count(*) acount
                                                                            FROM
                                                                                ( SELECT * FROM t_instruction_info WHERE STATUS = 1 AND instruction_type = 1 AND create_dept_id = 202 ) a
                                                                                    LEFT JOIN (
                                                                                    SELECT
                                                                                        id,
                                                                                        feedback_dept,
                                                                                        MAX( create_time ) maxTime,
                                                                                        instruction_id,
                                                                                        count(*)
                                                                                    FROM
                                                                                        t_instruction_county_feedback
                                                                                    WHERE
                                                                                        STATUS = 1
                                                                                      AND is_end = 1
                                                                                      AND feedback_dept = '东阳市委政法委'
                                                                                    GROUP BY
                                                                                        instruction_id
                                                                                ) b ON a.id = b.instruction_id
                                                                            WHERE
                                                                                maxTime IS NOT NULL UNION
                                                                            SELECT
                                                                                IFNULL( sum( TIMESTAMPDIFF( HOUR, a.create_time, b.maxTime )), 0 ) hourSum,
                                                                                '义乌市' AS county,
                                                                                count(*) acount
                                                                            FROM
                                                                                ( SELECT * FROM t_instruction_info WHERE STATUS = 1 AND instruction_type = 1 AND create_dept_id = 202 ) a
                                                                                    LEFT JOIN (
                                                                                    SELECT
                                                                                        id,
                                                                                        feedback_dept,
                                                                                        MAX( create_time ) maxTime,
                                                                                        instruction_id,
                                                                                        count(*)
                                                                                    FROM
                                                                                        t_instruction_county_feedback
                                                                                    WHERE
                                                                                        STATUS = 1
                                                                                      AND is_end = 1
                                                                                      AND feedback_dept = '义乌市委政法委'
                                                                                    GROUP BY
                                                                                        instruction_id
                                                                                ) b ON a.id = b.instruction_id
                                                                            WHERE
                                                                                maxTime IS NOT NULL UNION
                                                                            SELECT
                                                                                IFNULL( sum( TIMESTAMPDIFF( HOUR, a.create_time, b.maxTime )), 0 ) hourSum,
                                                                                '浦江县' AS county,
                                                                                count(*) acount
                                                                            FROM
                                                                                ( SELECT * FROM t_instruction_info WHERE STATUS = 1 AND instruction_type = 1 AND create_dept_id = 202 ) a
                                                                                    LEFT JOIN (
                                                                                    SELECT
                                                                                        id,
                                                                                        feedback_dept,
                                                                                        MAX( create_time ) maxTime,
                                                                                        instruction_id,
                                                                                        count(*)
                                                                                    FROM
                                                                                        t_instruction_county_feedback
                                                                                    WHERE
                                                                                        STATUS = 1
                                                                                      AND is_end = 1
                                                                                      AND feedback_dept = '浦江县委政法委'
                                                                                    GROUP BY
                                                                                        instruction_id
                                                                                ) b ON a.id = b.instruction_id
                                                                            WHERE
                                                                                maxTime IS NOT NULL UNION
                                                                            SELECT
                                                                                IFNULL( sum( TIMESTAMPDIFF( HOUR, a.create_time, b.maxTime )), 0 ) hourSum,
                                                                                '永康市' AS county,
                                                                                count(*) acount
                                                                            FROM
                                                                                ( SELECT * FROM t_instruction_info WHERE STATUS = 1 AND instruction_type = 1 AND create_dept_id = 202 ) a
                                                                                    LEFT JOIN (
                                                                                    SELECT
                                                                                        id,
                                                                                        feedback_dept,
                                                                                        MAX( create_time ) maxTime,
                                                                                        instruction_id,
                                                                                        count(*)
                                                                                    FROM
                                                                                        t_instruction_county_feedback
                                                                                    WHERE
                                                                                        STATUS = 1
                                                                                      AND is_end = 1
                                                                                      AND feedback_dept = '永康市委政法委'
                                                                                    GROUP BY
                                                                                        instruction_id
                                                                                ) b ON a.id = b.instruction_id
                                                                            WHERE
                                                                                maxTime IS NOT NULL UNION
                                                                            SELECT
                                                                                IFNULL( sum( TIMESTAMPDIFF( HOUR, a.create_time, b.maxTime )), 0 ) hourSum,
                                                                                '金东区' AS county,
                                                                                count(*) acount
                                                                            FROM
                                                                                ( SELECT * FROM t_instruction_info WHERE STATUS = 1 AND instruction_type = 1 AND create_dept_id = 202 ) a
                                                                                    LEFT JOIN (
                                                                                    SELECT
                                                                                        id,
                                                                                        feedback_dept,
                                                                                        MAX( create_time ) maxTime,
                                                                                        instruction_id,
                                                                                        count(*)
                                                                                    FROM
                                                                                        t_instruction_county_feedback
                                                                                    WHERE
                                                                                        STATUS = 1
                                                                                      AND is_end = 1
                                                                                      AND feedback_dept = '金东区政法委'
                                                                                    GROUP BY
                                                                                        instruction_id
                                                                                ) b ON a.id = b.instruction_id
                                                                            WHERE
                                                                                maxTime IS NOT NULL UNION
                                                                            SELECT
                                                                                IFNULL( sum( TIMESTAMPDIFF( HOUR, a.create_time, b.maxTime )), 0 ) hourSum,
                                                                                '婺城区' AS county,
                                                                                count(*) acount
                                                                            FROM
                                                                                ( SELECT * FROM t_instruction_info WHERE STATUS = 1 AND instruction_type = 1 AND create_dept_id = 202 ) a
                                                                                    LEFT JOIN (
                                                                                    SELECT
                                                                                        id,
                                                                                        feedback_dept,
                                                                                        MAX( create_time ) maxTime,
                                                                                        instruction_id,
                                                                                        count(*)
                                                                                    FROM
                                                                                        t_instruction_county_feedback
                                                                                    WHERE
                                                                                        STATUS = 1
                                                                                      AND is_end = 1
                                                                                      AND feedback_dept = '婺城区政法委'
                                                                                    GROUP BY
                                                                                        instruction_id
                                                                                ) b ON a.id = b.instruction_id
                                                                            WHERE
                                                                                maxTime IS NOT NULL UNION
                                                                            SELECT
                                                                                IFNULL( sum( TIMESTAMPDIFF( HOUR, a.create_time, b.maxTime )), 0 ) hourSum,
                                                                                '开发区' AS county,
                                                                                count(*) acount
                                                                            FROM
                                                                                ( SELECT * FROM t_instruction_info WHERE STATUS = 1 AND instruction_type = 1 AND create_dept_id = 202 ) a
                                                                                    LEFT JOIN (
                                                                                    SELECT
                                                                                        id,
                                                                                        feedback_dept,
                                                                                        MAX( create_time ) maxTime,
                                                                                        instruction_id,
                                                                                        count(*)
                                                                                    FROM
                                                                                        t_instruction_county_feedback
                                                                                    WHERE
                                                                                        STATUS = 1
                                                                                      AND is_end = 1
                                                                                      AND feedback_dept = '开发区政法办'
                                                                                    GROUP BY
                                                                                        instruction_id
                                                                                ) b ON a.id = b.instruction_id
                                                                            WHERE
                                                                                maxTime IS NOT NULL UNION
                                                                            SELECT
                                                                                IFNULL( sum( TIMESTAMPDIFF( HOUR, a.create_time, b.maxTime )), 0 ) hourSum,
                                                                                '武义县' AS county,
                                                                                count(*) acount
                                                                            FROM
                                                                                ( SELECT * FROM t_instruction_info WHERE STATUS = 1 AND instruction_type = 1 AND create_dept_id = 202 ) a
                                                                                    LEFT JOIN (
                                                                                    SELECT
                                                                                        id,
                                                                                        feedback_dept,
                                                                                        MAX( create_time ) maxTime,
                                                                                        instruction_id,
                                                                                        count(*)
                                                                                    FROM
                                                                                        t_instruction_county_feedback
                                                                                    WHERE
                                                                                        STATUS = 1
                                                                                      AND is_end = 1
                                                                                      AND feedback_dept = '武义县委政法委'
                                                                                    GROUP BY
                                                                                        instruction_id
                                                                                ) b ON a.id = b.instruction_id
                                                                            WHERE
                                                                                maxTime IS NOT NULL
                                                                        )c
    </select>
    <select id="getBatchReceiveMoveInfo" resultType="java.util.Map">
        SELECT
            a.*,
            b.id receiveId
        FROM
            (
                SELECT
                    a.id,
                    a.instruction_title,
                    a.mcaf_id,
                    c.county,
                    c.town
                FROM
                    ( SELECT id, mcaf_id,instruction_title FROM t_instruction_info WHERE STATUS = 1 AND instruction_type = 2
                        and id in
                        <foreach item="id" collection="array" open="(" separator="," close=")">
                            #{id}
                        </foreach>
                        ) a
                        LEFT JOIN ( SELECT unique_no, inspection_id FROM t_open_undercover_inspection_details ) b ON a.mcaf_id = b.unique_no
                        LEFT JOIN ( SELECT county, town, inspection_id FROM t_open_undercover_inspection ) c ON b.inspection_id = c.inspection_id
                GROUP BY
                    a.id,
                    c.county,
                    c.town
            ) a
                LEFT JOIN ( SELECT * FROM t_instruction_receive WHERE STATUS = 1 AND receive_time IS NULL ) b ON a.id = b.instrucation_id
        WHERE
            b.id IS NOT NULL and a.county is not null
    </select>

    <select id="listByMcafIds" resultType="java.lang.Long">
        select id
        from t_instruction_info
        where status = 1
        and mcaf_id in
        <foreach item="id" collection="inspectionUniqueNoList" open="(" separator="," close=")">
            #{id}
        </foreach>
    </select>
    <select id="findBatchEndId" resultType="java.util.Map">
        SELECT
            a.id,a.instruction_title
        FROM
            (
                SELECT
                    id,
                    receive_unit,
                    instruction_title,
                    LENGTH( receive_unit )- LENGTH(
                            REPLACE ( receive_unit, ',', '' ))+ 1 AS receiveCount
                FROM
                    t_instruction_info
                WHERE
                    STATUS = 1
                  AND id in
                <foreach item="id" collection="array" open="(" separator="," close=")">
                    #{id}
                </foreach>
            ) a
                LEFT JOIN ( SELECT instruction_id, count(*) feedCount, GROUP_CONCAT( feedback_dept ) feedbackDepts FROM t_instruction_county_feedback WHERE STATUS = 1 AND is_end = 1 GROUP BY instruction_id ) b ON a.id = b.instruction_id
                LEFT JOIN ( SELECT id, instruction_id, instrucation_is_end FROM t_instruction_end WHERE STATUS = 1 AND instrucation_is_end = 1 ) c ON a.id = c.instruction_id
        WHERE
            a.receiveCount = b.feedCount and c.id is NULL
    </select>
    <select id="selcetDealList" parameterType="InstructionInfo" resultMap="InstructionInfoResult">
        SELECT
            a.*
        FROM
            ( SELECT *
                     FROM t_instruction_info
        <where>
            STATUS = 1 AND instruction_type = 1 AND is_audit = 1
            <if test="instructionTitle != null  and instructionTitle != ''">and instruction_title like concat('%', #{instructionTitle}, '%')
            </if>
            <if test="createDeptId != null ">and create_dept_id = #{createDeptId}</if>
            <if test="instructionType != null and instructionType != 7 ">and instruction_type = #{instructionType}</if>
            <if test="instructionType != null and instructionType == 7 ">and instruction_type in (3,6)</if>
            <if test="instructionCreateDeptId != null ">and instruction_create_dept_id = #{instructionCreateDeptId}</if>
            <if test="params.beginTime != null and params.beginTime != ''"><!-- 开始时间检索 -->
                AND date_format(create_time,'%y%m%d') &gt;= date_format(#{params.beginTime},'%y%m%d')
            </if>
            <if test="params.endTime != null and params.endTime != ''"><!-- 结束时间检索 -->
                AND date_format(create_time,'%y%m%d') &lt;= date_format(#{params.endTime},'%y%m%d')
            </if>
        </where>
                ) a
                LEFT JOIN ( SELECT instruction_id FROM t_instruction_feedback WHERE STATUS = 1 AND feedback_is_end = 1 GROUP BY instruction_id ) b ON a.id = b.instruction_id
        WHERE
            b.instruction_id IS NOT NULL  ORDER BY a.create_time DESC

    </select>
    <select id="selcetFeedBackList" parameterType="InstructionInfo" resultMap="InstructionInfoResult">
        SELECT
            *
        FROM
            ( SELECT *
                     FROM t_instruction_info
        <where>
            STATUS = 1 AND instruction_type = 1 AND is_audit = 1
            <if test="instructionTitle != null  and instructionTitle != ''">and instruction_title like concat('%', #{instructionTitle}, '%')
            </if>
            <if test="createDeptId != null ">and create_dept_id = #{createDeptId}</if>
            <if test="instructionType != null and instructionType != 7 ">and instruction_type = #{instructionType}</if>
            <if test="instructionType != null and instructionType == 7 ">and instruction_type in (3,6)</if>
            <if test="instructionCreateDeptId != null ">and instruction_create_dept_id = #{instructionCreateDeptId}</if>
            <if test="params.beginTime != null and params.beginTime != ''"><!-- 开始时间检索 -->
                AND date_format(create_time,'%y%m%d') &gt;= date_format(#{params.beginTime},'%y%m%d')
            </if>
            <if test="params.endTime != null and params.endTime != ''"><!-- 结束时间检索 -->
                AND date_format(create_time,'%y%m%d') &lt;= date_format(#{params.endTime},'%y%m%d')
            </if>
        </where>
                ) a
                LEFT JOIN ( SELECT instruction_id FROM t_instruction_county_feedback WHERE STATUS = 1 AND is_end = 1 GROUP BY instruction_id ) b ON a.id = b.instruction_id
        WHERE
            b.instruction_id IS NOT NULL  ORDER BY a.create_time DESC
    </select>
    <select id="selcetEndList" parameterType="InstructionInfo" resultMap="InstructionInfoResult">
        SELECT
            *
        FROM
            ( SELECT *
            FROM t_instruction_info
        <where>
            STATUS = 1 AND instruction_type = 1 AND is_audit = 1
            <if test="instructionTitle != null  and instructionTitle != ''">and instruction_title like concat('%', #{instructionTitle}, '%')
            </if>
            <if test="createDeptId != null ">and create_dept_id = #{createDeptId}</if>
            <if test="instructionType != null and instructionType != 7 ">and instruction_type = #{instructionType}</if>
            <if test="instructionType != null and instructionType == 7 ">and instruction_type in (3,6)</if>
            <if test="instructionCreateDeptId != null ">and instruction_create_dept_id = #{instructionCreateDeptId}</if>
            <if test="params.beginTime != null and params.beginTime != ''"><!-- 开始时间检索 -->
                AND date_format(create_time,'%y%m%d') &gt;= date_format(#{params.beginTime},'%y%m%d')
            </if>
            <if test="params.endTime != null and params.endTime != ''"><!-- 结束时间检索 -->
                AND date_format(create_time,'%y%m%d') &lt;= date_format(#{params.endTime},'%y%m%d')
            </if>
        </where>
                ) a
                LEFT JOIN ( SELECT instruction_id FROM t_instruction_end WHERE STATUS = 1 AND instrucation_is_end = 1 GROUP BY instruction_id ) b ON a.id = b.instruction_id
        WHERE
            b.instruction_id IS NOT NULL  ORDER BY a.create_time DESC
    </select>

    <select id="getYiDongStatistics" resultType="java.util.Map">
        SELECT
            ( SELECT count(*) FROM t_instruction_info
        <where>
            STATUS = 1 AND instruction_type = 1 AND is_audit = 0
            <if test="instructionTitle != null  and instructionTitle != ''">and instruction_title like concat('%', #{instructionTitle}, '%')
            </if>
            <if test="createDeptId != null ">and create_dept_id = #{createDeptId}</if>
            <if test="instructionType != null ">and instruction_type = #{instructionType}</if>
            <if test="instructionCreateDeptId != null ">and instruction_create_dept_id = #{instructionCreateDeptId}</if>
            <if test="params.beginTime != null and params.beginTime != ''"><!-- 开始时间检索 -->
                AND date_format(create_time,'%y%m%d') &gt;= date_format(#{params.beginTime},'%y%m%d')
            </if>
            <if test="params.endTime != null and params.endTime != ''"><!-- 结束时间检索 -->
                AND date_format(create_time,'%y%m%d') &lt;= date_format(#{params.endTime},'%y%m%d')
            </if>
        </where>
             ) notAuditCount,
            ( SELECT count(*) FROM t_instruction_info
        <where>
            STATUS = 1 AND instruction_type = 1 AND  is_audit IN ( 1, 2 )
            <if test="instructionTitle != null  and instructionTitle != ''">and instruction_title like concat('%', #{instructionTitle}, '%')
            </if>
            <if test="createDeptId != null ">and create_dept_id = #{createDeptId}</if>
            <if test="instructionType != null ">and instruction_type = #{instructionType}</if>
            <if test="instructionCreateDeptId != null ">and instruction_create_dept_id = #{instructionCreateDeptId}</if>
            <if test="params.beginTime != null and params.beginTime != ''"><!-- 开始时间检索 -->
                AND date_format(create_time,'%y%m%d') &gt;= date_format(#{params.beginTime},'%y%m%d')
            </if>
            <if test="params.endTime != null and params.endTime != ''"><!-- 结束时间检索 -->
                AND date_format(create_time,'%y%m%d') &lt;= date_format(#{params.endTime},'%y%m%d')
            </if>
        </where>
              ) AuditCount,
            (
                SELECT
                    count(*)
                FROM
                    ( SELECT * FROM t_instruction_info
        <where>
            STATUS = 1 AND instruction_type = 1 AND is_audit = 1
            <if test="instructionTitle != null  and instructionTitle != ''">and instruction_title like concat('%', #{instructionTitle}, '%')
            </if>
            <if test="createDeptId != null ">and create_dept_id = #{createDeptId}</if>
            <if test="instructionType != null ">and instruction_type = #{instructionType}</if>
            <if test="instructionCreateDeptId != null ">and instruction_create_dept_id = #{instructionCreateDeptId}</if>
            <if test="params.beginTime != null and params.beginTime != ''"><!-- 开始时间检索 -->
                AND date_format(create_time,'%y%m%d') &gt;= date_format(#{params.beginTime},'%y%m%d')
            </if>
            <if test="params.endTime != null and params.endTime != ''"><!-- 结束时间检索 -->
                AND date_format(create_time,'%y%m%d') &lt;= date_format(#{params.endTime},'%y%m%d')
            </if>
        </where>
                    ) a
                        LEFT JOIN ( SELECT instruction_id FROM t_instruction_feedback WHERE STATUS = 1 AND feedback_is_end = 1 GROUP BY instruction_id ) b ON a.id = b.instruction_id
                WHERE
                    b.instruction_id IS NOT NULL
                ORDER BY
                    a.create_time DESC
            ) dealCount,
            (
                SELECT
                    count(*)
                FROM
                    ( SELECT id FROM t_instruction_info
        <where>
            STATUS = 1 AND instruction_type = 1 AND is_audit = 1
            <if test="instructionTitle != null  and instructionTitle != ''">and instruction_title like concat('%', #{instructionTitle}, '%')
            </if>
            <if test="createDeptId != null ">and create_dept_id = #{createDeptId}</if>
            <if test="instructionType != null ">and instruction_type = #{instructionType}</if>
            <if test="instructionCreateDeptId != null ">and instruction_create_dept_id = #{instructionCreateDeptId}</if>
            <if test="params.beginTime != null and params.beginTime != ''"><!-- 开始时间检索 -->
                AND date_format(create_time,'%y%m%d') &gt;= date_format(#{params.beginTime},'%y%m%d')
            </if>
            <if test="params.endTime != null and params.endTime != ''"><!-- 结束时间检索 -->
                AND date_format(create_time,'%y%m%d') &lt;= date_format(#{params.endTime},'%y%m%d')
            </if>
        </where>
                ) a
                        LEFT JOIN ( SELECT instruction_id FROM t_instruction_county_feedback WHERE STATUS = 1 AND is_end = 1 GROUP BY instruction_id ) b ON a.id = b.instruction_id
                WHERE
                    b.instruction_id IS NOT NULL
            ) feedbackCount,
            (
                SELECT
                    count(*)
                FROM
                    ( SELECT id FROM t_instruction_info
        <where>
            STATUS = 1 AND instruction_type = 1 AND is_audit = 1
            <if test="instructionTitle != null  and instructionTitle != ''">and instruction_title like concat('%', #{instructionTitle}, '%')
            </if>
            <if test="createDeptId != null ">and create_dept_id = #{createDeptId}</if>
            <if test="instructionType != null ">and instruction_type = #{instructionType}</if>
            <if test="instructionCreateDeptId != null ">and instruction_create_dept_id = #{instructionCreateDeptId}</if>
            <if test="params.beginTime != null and params.beginTime != ''"><!-- 开始时间检索 -->
                AND date_format(create_time,'%y%m%d') &gt;= date_format(#{params.beginTime},'%y%m%d')
            </if>
            <if test="params.endTime != null and params.endTime != ''"><!-- 结束时间检索 -->
                AND date_format(create_time,'%y%m%d') &lt;= date_format(#{params.endTime},'%y%m%d')
            </if>
        </where>
             ) a
                        LEFT JOIN ( SELECT instruction_id FROM t_instruction_end WHERE STATUS = 1 AND instrucation_is_end = 1 GROUP BY instruction_id ) b ON a.id = b.instruction_id
                WHERE
                    b.instruction_id IS NOT NULL
            ) endCount
    </select>

    <select id="getInstructionTime" resultType="java.util.Map">
        SELECT
            a.id,
            a.receiveDeptCount,
            a.assign_time as assignTime,
            c.receiveTime,
            c.receiveCount,
            d.transferDeptCount,
            e.dealTime as feedbackTime,
            e.dealDeptCount,
            f.countyFeedbackTime,
            f.countyFeedback,
            g.endTime
        FROM
            ( SELECT id,assign_time, LENGTH(receive_unit)-LENGTH(REPLACE(receive_unit,',',''))+1 as receiveDeptCount FROM t_instruction_info WHERE STATUS = 1  and id = #{id}) a
                LEFT JOIN (
                SELECT
                    instrucation_id,
                    MAX( receive_time ) receiveTime,
                    count(*) receiveCount
                FROM
                    t_instruction_receive
                WHERE
                    STATUS = 1
                  AND receive_time IS NOT NULL
                GROUP BY
                    instrucation_id
            ) c ON a.id = c.instrucation_id

                LEFT JOIN (
                SELECT instruction_id,count(*)transferDeptCount from t_instruction_transfer where status = 1 GROUP BY instruction_id

            ) d on a.id = d.instruction_id
                LEFT JOIN (
                SELECT
                    instruction_id,
                    max(feedback_time) dealTime,
                    LENGTH(GROUP_CONCAT( DISTINCT feedback_dept ))-LENGTH(REPLACE(GROUP_CONCAT( DISTINCT feedback_dept ),',',''))+1 as dealDeptCount
                FROM
                    t_instruction_feedback
                WHERE
                    STATUS = 1
                  AND feedback_is_end = 1
                GROUP BY
                    instruction_id
            )e on a.id = e.instruction_id
                LEFT JOIN (
                SELECT
                    instruction_id,
                    max(receive_time)countyFeedbackTime,
                    LENGTH(GROUP_CONCAT(DISTINCT feedback_dept ))-LENGTH(REPLACE(GROUP_CONCAT(DISTINCT feedback_dept ),',',''))+1 countyFeedback
                FROM
                    t_instruction_county_feedback
                WHERE
                    STATUS = 1
                  AND is_end = 1
                GROUP BY
                    instruction_id
            )f on a.id = f.instruction_id
                LEFT JOIN (
                SELECT instruction_id,max(end_time)endTime from t_instruction_end where status = 1 and instrucation_is_end = 1 GROUP BY instruction_id
            )g on a.id = g.instruction_id
    </select>

    <select id="getYiDongIssueStatistics" resultType="java.util.Map">
        SELECT
            a.time,
            IFNULL( b.provinceCount, 0 ) provinceCount,
            IFNULL( c.cityCount, 0 ) cityCount,
            IFNULL( d.countyInstruction, 0 ) countyInstruction
        FROM
            (
                SELECT
                    SUBSTR( DATE_SUB( NOW(), INTERVAL 11 MONTH ), 1, 7 ) time UNION
                SELECT
                    SUBSTR( DATE_SUB( NOW(), INTERVAL 10 MONTH ), 1, 7 ) time UNION
                SELECT
                    SUBSTR( DATE_SUB( NOW(), INTERVAL 9 MONTH ), 1, 7 ) time UNION
                SELECT
                    SUBSTR( DATE_SUB( NOW(), INTERVAL 8 MONTH ), 1, 7 ) time UNION
                SELECT
                    SUBSTR( DATE_SUB( NOW(), INTERVAL 7 MONTH ), 1, 7 ) time UNION
                SELECT
                    SUBSTR( DATE_SUB( NOW(), INTERVAL 6 MONTH ), 1, 7 ) time UNION
                SELECT
                    SUBSTR( DATE_SUB( NOW(), INTERVAL 5 MONTH ), 1, 7 ) time UNION
                SELECT
                    SUBSTR( DATE_SUB( NOW(), INTERVAL 4 MONTH ), 1, 7 ) time UNION
                SELECT
                    SUBSTR( DATE_SUB( NOW(), INTERVAL 3 MONTH ), 1, 7 ) time UNION
                SELECT
                    SUBSTR( DATE_SUB( NOW(), INTERVAL 2 MONTH ), 1, 7 ) time UNION
                SELECT
                    SUBSTR( DATE_SUB( NOW(), INTERVAL 1 MONTH ), 1, 7 ) time UNION
                SELECT
                    SUBSTR( NOW(), 1, 7 ) time
            ) a
                LEFT JOIN (
                SELECT
                    count(*) provinceCount,
                    substr( handle_time, 1, 7 ) time
                FROM
                    t_instruction_info
                WHERE
                    STATUS = 1
                  AND instruction_type = 1
                  AND create_dept_id = 202
                  AND source_info = '上级通报'
                GROUP BY
                    substr( handle_time, 1, 7 )
            ) b ON a.time = b.time
                LEFT JOIN (
                SELECT
                    count(*) cityCount,
                    substr( handle_time, 1, 7 ) time
                FROM
                    t_instruction_info
                WHERE
                    STATUS = 1
                  AND instruction_type = 1
                  AND create_dept_id = 202
                  AND source_info != '上级通报'
                GROUP BY
                    substr( handle_time, 1, 7 )
            ) c ON a.time = c.time
                LEFT JOIN (
                SELECT
                    count(*) countyInstruction,
                    substr( handle_time, 1, 7 ) time
                FROM
                    t_instruction_info
                WHERE
                    STATUS = 1
                  AND instruction_type = 1
                  AND create_dept_id != 202
                GROUP BY
                    substr( handle_time, 1, 7 )
            ) d ON a.time = d.time ORDER BY a.time
    </select>
    <select id="getAuditRemind" resultMap="InstructionInfoResult">
        SELECT
        *
        FROM
        (
        SELECT
        id,
        instruction_title,
        is_audit,
        create_dept_id,
        audit_remind_count,
        audit_remind_time,
        TIMESTAMPDIFF(
        HOUR,
        audit_remind_time,
        NOW()) hours
        FROM
        t_instruction_info
        WHERE
        STATUS = 1
        AND is_audit = 0
        AND audit_remind_count &lt; 3 ) a WHERE a.hours &gt;=1
    </select>
    <select id="exportCityInstructionInfo"
            resultType="com.ruoyi.instruction.domain.rspVo.CityInstructionExportRspVo">
        SELECT
            id,
            instruction_title instructionTitle,
            assign_time assignTime,
            audit_time auditTime,
            feedback,
            handle_time handleTime
        FROM
            t_instruction_info
        WHERE
            STATUS = 1
          AND instruction_type = 1
          AND create_dept_id = 202
        <if test="params.beginTime != null and params.beginTime != ''"><!-- 开始时间检索 -->
            AND date_format(assign_time,'%y%m%d') &gt;= date_format(#{params.beginTime},'%y%m%d')
        </if>
        <if test="params.endTime != null and params.endTime != ''"><!-- 结束时间检索 -->
            AND date_format(assign_time,'%y%m%d') &lt;= date_format(#{params.endTime},'%y%m%d')
        </if>
        <if test="params.county != null and params.county != '全部'">
            AND receive_unit like concat('%', #{params.county}, '%')
        </if>

    </select>
    <select id="getContactInstructionByPersonId" resultMap="InstructionInfoResult">
        SELECT
            *,
            LENGTH( person_ids ) - LENGTH(
                    REPLACE ( person_ids, ',', '' )) + 1 AS personCount
        FROM
            t_instruction_info
        WHERE
            STATUS = 1
          AND find_in_set(
                #{id},
                person_ids)
    </select>
    <select id="EndNoHandleForCountyIds" resultType="java.lang.Long">
        SELECT a.id FROM ( SELECT id, receive_unit,( LENGTH( receive_unit ) - LENGTH( REPLACE ( receive_unit, ',', '' ))+ 1 ) AS receiveUnitCount
                           FROM
                               t_instruction_info
                           WHERE
                               STATUS = 1
                             AND create_dept_id = #{deptId}
                             AND (instrucation_is_end is null or instrucation_is_end = 2)
		AND instruction_type != 2
		AND spare_num IS NULL
                         ) a
                             LEFT JOIN (
            SELECT
                count( DISTINCT feedback_dept ) feedbackCount,
                instruction_id,
                GROUP_CONCAT( DISTINCT feedback_dept )
            FROM
                t_instruction_feedback
            WHERE
                STATUS = 1
              AND feedback_is_end = 1
            GROUP BY
                instruction_id
        ) b ON a.id = b.instruction_id
        WHERE
            b.feedbackCount = a.receiveUnitCount UNION
        SELECT
            a.id
        FROM
            (
                SELECT
                    id,
                    spare_num
                FROM
                    t_instruction_info
                WHERE
                    STATUS = 1
                  AND create_dept_id = #{deptId}
                  AND (instrucation_is_end is null or instrucation_is_end = 2)
		AND instruction_type != 2
		AND spare_num IS NOT NULL
            ) a
                LEFT JOIN (
                SELECT
                    count( DISTINCT feedback_dept ) feedbackCount,
                    instruction_id,
                    GROUP_CONCAT( DISTINCT feedback_dept )
                FROM
                    t_instruction_feedback
                WHERE
                    STATUS = 1
                  AND feedback_is_end = 1
                GROUP BY
                    instruction_id
            ) b ON a.id = b.instruction_id
        WHERE
            b.feedbackCount = a.spare_num
    </select>
    <select id="mzxCountyIsToEnd" resultType="java.lang.Integer">
        SELECT CASE
                   WHEN (
                            SELECT count(DISTINCT receive_dept)
                            FROM t_instruction_receive
                            WHERE STATUS = 1
                              AND instrucation_id = #{id}
                              AND is_mzx in (2,3)
                        ) &lt;= (
                            SELECT count(DISTINCT feedback_dept)
                            FROM t_instruction_feedback
                            WHERE status = 1
                              AND instruction_id = #{id}
                              AND feedback_is_end = 1
                              AND is_mzx in (2,3)
                        ) THEN
                       CASE
                           WHEN (
                                    SELECT count(DISTINCT receive_dept)
                                    FROM t_instruction_receive
                                    WHERE STATUS = 1
                                      AND instrucation_id = #{id}
                                      AND is_mzx in (2,3)
                                ) = 0 THEN 2
                           ELSE 1
                           END
                   ELSE 2
                   END AS result;

    </select>
    <select id="getMzxAssign" resultType="java.util.Map">
        SELECT
            (SELECT COUNT(*) AS daily_count
             FROM t_instruction_info
             WHERE STATUS = 1
               AND instruction_type = 3
               AND DATE(create_time) = CURDATE()) AS daily_count,

            (SELECT COUNT(*) AS weekly_count
        FROM t_instruction_info
        WHERE STATUS = 1
          AND instruction_type = 3
          AND YEARWEEK(create_time, 1) = YEARWEEK(CURDATE(), 1)) AS weekly_count,

            (SELECT COUNT(*) AS monthly_count
        FROM t_instruction_info
        WHERE STATUS = 1
          AND instruction_type = 3
          AND DATE_FORMAT(create_time, '%Y-%m') = DATE_FORMAT(CURDATE(), '%Y-%m')) AS monthly_count;
    </select>
    <select id="getMzxEnd" resultType="java.util.Map">
        SELECT
            SUM(CASE WHEN DATE(b.end_time) = CURDATE() THEN 1 ELSE 0 END) AS daily_count,
            SUM(CASE WHEN YEARWEEK(b.end_time, 1) = YEARWEEK(CURDATE(), 1) THEN 1 ELSE 0 END) AS weekly_count,
            SUM(CASE WHEN DATE_FORMAT(b.end_time, '%Y-%m') = DATE_FORMAT(CURDATE(), '%Y-%m') THEN 1 ELSE 0 END) AS monthly_count
        FROM
                ( SELECT * FROM t_instruction_info WHERE STATUS = 1 AND instruction_type = 3 ) a
                    LEFT JOIN
                ( SELECT * FROM t_instruction_end WHERE instrucation_is_end = 1 ) b
                ON
                    a.id = b.instruction_id
        WHERE
            b.id IS NOT NULL;
    </select>
    <select id="getMzxInstructionLast20" resultType="java.util.Map">
        SELECT
            i.id,
            SUBSTRING_INDEX(i.instruction_title, '存在', 1) AS instruction_title,
            i.mzx_nature,
            i.receive_unit,
            CASE
                WHEN e.endCount > 0 THEN '销号'
                WHEN f.feedbackCount > 0 THEN '处置'
                WHEN r.receiveCount > 0 THEN '接收'
                ELSE '交办'
                END AS status
        FROM
            t_instruction_info i
                LEFT JOIN (
                SELECT
                    instrucation_id,
                    COUNT(*) AS receiveCount
                FROM
                    t_instruction_receive
                WHERE
                    STATUS = 1
                  AND receive_time IS NOT NULL
                GROUP BY
                    instrucation_id
            ) r ON i.id = r.instrucation_id
                LEFT JOIN (
                SELECT
                    instruction_id,
                    COUNT(*) AS feedbackCount
                FROM
                    t_instruction_feedback
                WHERE
                    status = 1
                  AND feedback_is_end = 1
                GROUP BY
                    instruction_id
            ) f ON i.id = f.instruction_id
                LEFT JOIN (
                SELECT
                    instruction_id,
                    COUNT(*) AS endCount
                FROM
                    t_instruction_end
                WHERE
                    status = 1
                  AND instrucation_is_end = 1
                GROUP BY
                    instruction_id
            ) e ON i.id = e.instruction_id
        WHERE
            i.STATUS = 1
          AND i.instruction_type = 3
        ORDER BY
            i.create_time DESC
            LIMIT 20;


    </select>
    <select id="getCityCount" resultType="java.lang.Long">
        SELECT
            count(*)
        FROM
            t_instruction_info
        WHERE
            STATUS = 1
          AND create_dept_id = 202
    </select>
    <select id="selectWkMzxIds" resultType="java.lang.Long">
        SELECT
            DISTINCT instruction_id
        FROM
            t_instruction_feedback
        WHERE
            wk_status = '易激化' and
                ( transfer_id, id ) IN ( SELECT transfer_id, MAX( id ) FROM t_instruction_feedback WHERE STATUS = 1 AND transfer_id IS NOT NULL GROUP BY transfer_id );
    </select>
    <select id="getCityStatis" resultMap="InstructionInfoResult">
        SELECT
        a.id,
        c.receive_time AS create_time,
        b.feedbackTime AS update_time,
        CASE
        WHEN a.handle_time > NOW()
        AND a.handle_time &lt;= DATE_ADD( NOW(), INTERVAL 1 HOUR ) THEN
        1
        WHEN a.handle_time &lt; NOW() THEN
        2 ELSE 2
        END AS STATUS
        FROM
        ( SELECT id, instruction_title, handle_time FROM t_instruction_info WHERE STATUS = 1 AND receive_unit like concat('%', #{receiveUnit}, '%') AND create_dept_id = 202
        <choose>
            <when test="petitionType == 1">
                AND create_time &gt;= CURDATE()
            </when>
            <when test="petitionType == 2">
                AND YEARWEEK(create_time, 1) &gt;= YEARWEEK(CURDATE(), 1)
            </when>
            <when test="petitionType == 3">
                AND DATE_FORMAT(create_time, '%Y-%m') &gt;= DATE_FORMAT(CURDATE(), '%Y-%m')
            </when>
            <when test="petitionType == 4">
                AND YEAR(create_time) &gt;= YEAR(CURDATE())
            </when>
        </choose>
        <if test="createTime != null "><!-- 开始时间检索 -->
            AND date_format(create_time,'%y%m%d') &gt;= date_format(#{createTime},'%y%m%d')
        </if>
        <if test="instructionType !=null ">and instruction_type = #{instructionType}</if>
            ) a
        LEFT JOIN (
        SELECT
        instruction_id,
        feedback_dept,
        receive_time AS feedbackTime
        FROM
        t_instruction_county_feedback
        WHERE
        STATUS = 1
        AND feedback_dept like concat('%', #{receiveUnit}, '%')
        AND is_end = 1
        GROUP BY
        instruction_id,
        feedback_dept
        ) b ON a.id = b.instruction_id
        LEFT JOIN ( SELECT receive_dept, receive_time, instrucation_id FROM t_instruction_receive WHERE STATUS = 1 AND receive_dept like concat('%', #{receiveUnit}, '%') ) c ON a.id = c.instrucation_id
    </select>


    <select id="getCountyStatis" resultMap="InstructionInfoResult">
        SELECT a.id, c.receive_time AS create_time, b.feedback_time AS update_time, CASE WHEN a.handle_time > NOW()
        AND a.handle_time &lt;= DATE_ADD( NOW(), INTERVAL 1 HOUR ) THEN
        1
        WHEN a.handle_time &lt; NOW() THEN
        2 ELSE 2
        END AS STATUS
        FROM
        (
        SELECT
        id,
        instruction_title,
        handle_time,
        receive_unit,
        create_dept_id
        FROM
        t_instruction_info
        WHERE
        STATUS = 1
        AND create_dept_id = #{createDeptId}
        <choose>
            <when test="petitionType == 1">
                AND create_time &gt;= CURDATE()
            </when>
            <when test="petitionType == 2">
                AND YEARWEEK(create_time, 1) &gt;= YEARWEEK(CURDATE(), 1)
            </when>
            <when test="petitionType == 3">
                AND DATE_FORMAT(create_time, '%Y-%m') &gt;= DATE_FORMAT(CURDATE(), '%Y-%m')
            </when>
            <when test="petitionType == 4">
                AND YEAR(create_time) &gt;= YEAR(CURDATE())
            </when>
        </choose>
        <if test="createTime != null "><!-- 开始时间检索 -->
            AND date_format(create_time,'%y%m%d') &gt;= date_format(#{createTime},'%y%m%d')
        </if>
        <if test="instructionType !=null ">and instruction_type = #{instructionType}</if>
        ) a
        LEFT JOIN (
        SELECT
        instruction_id,
        feedback_dept,
        feedback_time
        FROM
        t_instruction_feedback
        WHERE
        STATUS = 1
        AND feedback_is_end = 1
        GROUP BY
        instruction_id,
        feedback_dept
        ) b ON a.id = b.instruction_id
        LEFT JOIN ( SELECT receive_dept, receive_time, instrucation_id FROM t_instruction_receive WHERE STATUS = 1  ) c ON a.id = c.instrucation_id

    </select>
    <select id="getLxCityInstructionStatus" resultMap="InstructionInfoResult">
        SELECT
            a.id,b.receive_time handel_time,c.feedback_dept receive_unit
        FROM
            ( SELECT id FROM t_instruction_info WHERE STATUS = 1 AND create_dept_id = 202 AND receive_unit like concat('%', #{dutyPlace}, '%') AND instruction_type IN ( 3, 6 ) and create_time &gt;= '2025-01-01'   ) a
                LEFT JOIN ( SELECT instrucation_id, receive_time FROM t_instruction_receive WHERE STATUS = 1 AND receive_dept like concat('%', #{dutyPlace}, '%') ) b ON a.id = b.instrucation_id
                LEFT JOIN (
                SELECT
                    feedback_dept,
                    instruction_id,
                    COUNT(*) acount
                FROM
                    t_instruction_county_feedback
                WHERE
                    STATUS = 1
                  AND is_end = 1
                  AND feedback_dept like concat('%', #{dutyPlace}, '%')
                GROUP BY
                    feedback_dept,
                    instruction_id
            ) c ON a.id = c.instruction_id

    </select>
    <select id="getLxCountInstructionStatus" resultMap="InstructionInfoResult">
        SELECT
            a.id,a.group_id,IFNULL(b.bCount,0) as creator_id,IFNULL(c.cCount,0) as create_dept_id
        FROM
            (
                SELECT
                    id,
                    CASE

                        WHEN LENGTH( receive_unit ) - LENGTH(
                                REPLACE ( receive_unit, ',', '' )) = 0 THEN
                            1 ELSE LENGTH( receive_unit ) - LENGTH(
                            REPLACE ( receive_unit, ',', '' )) + 1
                        END AS group_id
                FROM
                    t_instruction_info
                WHERE
                    STATUS = 1
                  AND create_dept_id = #{deptId}
                  AND instruction_type IN ( 3, 6 )
                  AND assign_time &gt;= '2025-01-01'
            ) a
                LEFT JOIN ( SELECT instrucation_id, count(*) bCount FROM t_instruction_receive WHERE STATUS = 1 AND receive_time IS NOT NULL AND receive_time &gt;= '2025-01-01' GROUP BY instrucation_id ) b ON a.id = b.instrucation_id
                LEFT JOIN ( SELECT instruction_id,count(*) cCount from t_instruction_feedback where status = 1 and feedback_is_end = 1 and feedback_time &gt;= '2025-01-01' GROUP BY instruction_id
            )c on a.id = c.instruction_id

    </select>
    <select id="getFailureResolve" resultMap="InstructionInfoResult">
        SELECT
        a.id,
        a.instruction_title,
        a.handle_time,
        a.receive_unit,
        a.mzx_type,
        REPLACE(REPLACE(REPLACE(c.receive_dept,'委政法委',''),'政法办',''),'政法委','') county,
        c.receive_time as create_time,
        b.receive_time as audit_remind_time,
        d.id as create_dept_id,
        CASE
        WHEN b.receive_time IS NULL THEN
        '未化解'
        WHEN b.receive_time IS NOT NULL
        AND a.handle_time &lt; b.receive_time THEN '超期化解' WHEN d.id IS NOT NULL AND a.handle_time > b.receive_time THEN
        '延期化解' ELSE '未知状态'
        END AS town
        FROM
        (
        SELECT
        id,
        instruction_title,
        mzx_type,
        handle_time,
        receive_unit
        FROM
        t_instruction_info
        WHERE
        STATUS = 1
        AND create_dept_id = 202
        AND instruction_type IN ( 3, 6 )
        ) a
        LEFT JOIN ( SELECT receive_dept,receive_time, instrucation_id FROM t_instruction_receive WHERE STATUS = 1 AND is_mzx = 2 ) c ON a.id = c.instrucation_id
        LEFT JOIN ( SELECT receive_time, feedback_dept, instruction_id FROM t_instruction_county_feedback WHERE STATUS = 1 AND is_end = 1 AND is_mzx = 2 ) b ON c.instrucation_id = b.instruction_id
        AND c.receive_dept = b.feedback_dept
        LEFT JOIN ( SELECT id, instruction_id FROM t_handle_delay WHERE STATUS = 1 AND apply_result = 1 ) d ON a.id = d.instruction_id
        <where>
            <if test="date != null  and date != ''">AND DATE_FORMAT( handle_time, '%Y-%m' ) = #{date}</if>
        </where>
    </select>

    <select id="getPoorTrack" resultMap="InstructionInfoResult">
        SELECT
        a.id,
        a.handle_time,
        a.receive_unit,
        a.mzx_type,
        a.emergency_degree,
        IFNULL(a.instrucation_is_end,2) instrucationIsEnd,
        REPLACE ( REPLACE ( REPLACE ( c.receive_dept, '委政法委', '' ), '政法办', '' ), '政法委', '' ) county,
        c.receive_time AS create_time,
        CASE
        WHEN a.emergency_degree = '一般' AND IFNULL(DATEDIFF(CURDATE(), b.feedback_time),99) > 30 THEN 1
        WHEN a.emergency_degree = '一般' AND IFNULL(DATEDIFF(CURDATE(), b.feedback_time),99) &lt;= 30 THEN 0
        WHEN a.emergency_degree = '紧急' AND IFNULL(DATEDIFF(CURDATE(), b.feedback_time),99) > 14 THEN 1
        WHEN a.emergency_degree = '紧急' AND IFNULL(DATEDIFF(CURDATE(), b.feedback_time),99) &lt;= 14 THEN 0
        WHEN a.emergency_degree = '特急' AND IFNULL(DATEDIFF(CURDATE(), b.feedback_time),99) > 7 THEN 1
        WHEN a.emergency_degree = '特急' AND IFNULL(DATEDIFF(CURDATE(), b.feedback_time),99) &lt;= 7 THEN 0
        ELSE 0
        END AS town
        FROM
        (
        SELECT
        id,
        instruction_title,
        mzx_type,
        handle_time,
        receive_unit,
        emergency_degree,
        instrucation_is_end
        FROM
        t_instruction_info
        WHERE
        STATUS = 1
        AND create_dept_id = 202
        AND instruction_type IN ( 3, 6 )
        ) a
        LEFT JOIN ( SELECT id,receive_dept, receive_time, instrucation_id FROM t_instruction_receive WHERE STATUS = 1 AND is_mzx = 2 ) c ON a.id = c.instrucation_id
        LEFT JOIN (SELECT id,feedback_dept,instruction_id,receive_id,feedback_time from t_instruction_feedback where status = 1 and feedback_is_end = 1 and is_mzx = 2
        ) b on c.id = b.receive_id
        <where>
            <if test="date != null  and date != ''">AND DATE_FORMAT( handle_time, '%Y-%m' ) = #{date}</if>
        </where>
    </select>
    <select id="selectHjblCount" resultMap="InstructionInfoResult">
        SELECT
        id,
        instruction_title,
        mzx_type,
        receive_unit,
        instruction_type,
        CASE

        WHEN DATE_ADD( assign_time, INTERVAL 270 DAY ) &lt; NOW() AND end_time IS NULL THEN '超期未化解' WHEN DATE_ADD( assign_time, INTERVAL 270 DAY ) > NOW()
        AND end_time IS NULL THEN
        '未化解' ELSE '其他'
        END AS STATUS
        FROM
        t_instruction_info
        WHERE
        STATUS = 1
        <if test="deptId != null  and deptId != ''">AND create_dept_id = #{deptId}</if>
        <if test="date != null  and date != ''">AND DATE_FORMAT( handle_time, '%Y-%m' ) = #{date}</if>
        <if test="startTime != null and startTime != ''"><!-- 开始时间检索 -->
            AND date_format(handle_time,'%y%m%d') &gt;= date_format(#{startTime},'%y%m%d')
        </if>
        <if test="endTime != null and endTime != ''"><!-- 结束时间检索 -->
            AND date_format(handle_time,'%y%m%d') &lt;= date_format(#{endTime},'%y%m%d')
        </if>
        AND end_time IS NULL
        AND instruction_type IN ( 3, 6 )
    </select>

    <select id="selectHjblDetails" resultMap="InstructionInfoResult">
        SELECT REPLACE(REPLACE(REPLACE(receive_dept,'委政法委',''),'政法委',''),'政法办','')receive_str,status ,count(*) spare_num from (
        SELECT
        a.id,
        a.instruction_title,
        a.assign_time,
        DATE_ADD( a.assign_time, INTERVAL 270 DAY ),
        b.receive_dept,
        receive_time,
        a.instruction_type,
        CASE
        WHEN DATE_ADD( a.assign_time, INTERVAL 270 DAY ) &lt; NOW() THEN '超期未化解' WHEN DATE_ADD( a.assign_time, INTERVAL 270 DAY ) > NOW() THEN
        '未化解' ELSE '其他'
        END AS STATUS
        FROM
        (
        SELECT
        id,
        instruction_title,
        mzx_type,
        receive_unit,
        DATE_ADD( assign_time, INTERVAL 270 DAY ) assign_time,
        instruction_type
        FROM
        t_instruction_info
        WHERE
        STATUS = 1
        AND end_time IS NULL
        <if test="deptId != null  and deptId != ''">AND create_dept_id = #{deptId}</if>
        <if test="date != null  and date != ''">AND DATE_FORMAT( handle_time, '%Y-%m' ) = #{date}</if>
        <if test="startTime != null and startTime != ''"><!-- 开始时间检索 -->
            AND date_format(handle_time,'%y%m%d') &gt;= date_format(#{startTime},'%y%m%d')
        </if>
        <if test="endTime != null and endTime != ''"><!-- 结束时间检索 -->
            AND date_format(handle_time,'%y%m%d') &lt;= date_format(#{endTime},'%y%m%d')
        </if>
        AND instruction_type IN ( 3, 6 )
        ) a
        LEFT JOIN ( SELECT receive_dept, receive_time, instrucation_id FROM t_instruction_receive WHERE STATUS = 1  ) b ON a.id = b.instrucation_id
        WHERE
        b.receive_dept is not null  ) f GROUP BY receive_dept,`status`

    </select>
    <select id="selectHjblDetailsForCounty" resultMap="InstructionInfoResult">
        SELECT receive_dept receive_str,status ,count(*) spare_num from (
        SELECT
        a.id,
        a.instruction_title,
        a.assign_time,
        DATE_ADD( a.assign_time, INTERVAL 270 DAY ),
        b.receive_dept,
        receive_time,
        a.instruction_type,
        CASE
        WHEN DATE_ADD( a.assign_time, INTERVAL 270 DAY ) &lt; NOW() THEN '超期未化解' WHEN DATE_ADD( a.assign_time, INTERVAL 270 DAY ) > NOW() THEN
        '未化解' ELSE '其他'
        END AS STATUS
        FROM
        (
        SELECT
        id,
        instruction_title,
        mzx_type,
        receive_unit,
        DATE_ADD( assign_time, INTERVAL 270 DAY ) assign_time,
        instruction_type
        FROM
        t_instruction_info
        WHERE
        STATUS = 1
        AND end_time IS NULL
        <if test="deptId != null  and deptId != ''">AND create_dept_id = #{deptId}</if>
        <if test="date != null  and date != ''">AND DATE_FORMAT( handle_time, '%Y-%m' ) = #{date}</if>
        <if test="startTime != null and startTime != ''"><!-- 开始时间检索 -->
            AND date_format(handle_time,'%y%m%d') &gt;= date_format(#{startTime},'%y%m%d')
        </if>
        <if test="endTime != null and endTime != ''"><!-- 结束时间检索 -->
            AND date_format(handle_time,'%y%m%d') &lt;= date_format(#{endTime},'%y%m%d')
        </if>
        AND instruction_type IN ( 3, 6 )
        ) a
        LEFT JOIN ( SELECT receive_dept, receive_time, instrucation_id FROM t_instruction_receive WHERE STATUS = 1  ) b ON a.id = b.instrucation_id
        LEFT JOIN ( SELECT
        instruction_id,
        feedback_dept,
        max( feedback_time )
        FROM
        t_instruction_feedback
        WHERE
        STATUS = 1
        AND feedback_is_end = 1
        GROUP BY
        instruction_id,
        feedback_dept ) c ON a.id = c.instruction_id
        WHERE
        c.instruction_id IS NULL and b.receive_dept is not null  ) f GROUP BY receive_dept,`status`
    </select>

    <select id="getPoorTrackNew" resultMap="InstructionInfoResult">
        SELECT
        a.*,
        b.receive_dept as receive_str,
        b.receive_time,
        c.feedback_dept,
        c.feedback_time,
        CASE

        WHEN b.receive_time IS NULL
        AND a.create_time > NOW() THEN
        '正常接收'
        WHEN b.receive_time IS NULL THEN
        '接收不力'
        WHEN b.receive_time > a.create_time THEN
        '接收不力' ELSE '正常接收'
        END AS STATUS ,
        CASE
        WHEN c.feedback_time IS NULL AND a.update_time &lt; NOW() THEN '超期反馈'
        WHEN c.feedback_time IS NULL AND a.update_time > NOW() THEN '正常反馈'
        WHEN c.feedback_time &lt; a.update_time THEN '正常反馈'
        ELSE '超期反馈'
        END AS mzx_type
        FROM
        (
        SELECT
        id,
        emergency_degree,
        assign_time,
        CASE

        WHEN emergency_degree = '一般' THEN
        DATE_ADD( create_time, INTERVAL 24 HOUR )
        WHEN emergency_degree = '紧急' THEN
        DATE_ADD( create_time, INTERVAL 12 HOUR )
        WHEN emergency_degree = '特急' THEN
        DATE_ADD( create_time, INTERVAL 2 HOUR ) ELSE create_time
        END AS create_time,
        CASE

        WHEN emergency_degree = '一般' THEN
        DATE_ADD( assign_time, INTERVAL 1 MONTH )
        WHEN emergency_degree = '紧急' THEN
        DATE_ADD( assign_time, INTERVAL 2 WEEK )
        WHEN emergency_degree = '特急' THEN
        DATE_ADD( assign_time, INTERVAL 1 WEEK ) ELSE assign_time
        END AS update_time
        FROM
        t_instruction_info
        WHERE
        STATUS = 1
        AND instruction_type IN ( 3, 6 )
        <if test="deptId != null  and deptId != ''">AND create_dept_id = #{deptId}</if>
        <if test="date != null  and date != ''">AND DATE_FORMAT( handle_time, '%Y-%m' ) = #{date}</if>
        <if test="startTime != null and startTime != ''"><!-- 开始时间检索 -->
            AND date_format(handle_time,'%y%m%d') &gt;= date_format(#{startTime},'%y%m%d')
        </if>
        <if test="endTime != null and endTime != ''"><!-- 结束时间检索 -->
            AND date_format(handle_time,'%y%m%d') &lt;= date_format(#{endTime},'%y%m%d')
        </if>
        <if test="instructionType != null ">and instruction_type = #{instructionType}</if>
        ) a
        LEFT JOIN ( SELECT id, receive_dept, receive_time, instrucation_id FROM t_instruction_receive WHERE STATUS = 1 ) b ON a.id = b.instrucation_id
        LEFT JOIN ( SELECT instruction_id, feedback_dept, MAX( receive_time ) feedback_time FROM t_instruction_county_feedback WHERE STATUS = 1 AND is_end = 1 GROUP BY instruction_id, feedback_dept ) c ON b.instrucation_id = c.instruction_id
        AND b.receive_dept = c.feedback_dept
    </select>

    <select id="getPoorTrackNewForCounty" resultMap="InstructionInfoResult">
        SELECT
        a.*,
        b.receive_dept as receive_str,
        b.receive_time,
        c.feedback_dept,
        c.feedback_time,
        CASE

        WHEN b.receive_time IS NULL
        AND a.create_time > NOW() THEN
        '正常接收'
        WHEN b.receive_time IS NULL THEN
        '接收不力'
        WHEN b.receive_time > a.create_time THEN
        '接收不力' ELSE '正常接收'
        END AS STATUS ,
        CASE
        WHEN c.feedback_time IS NULL AND a.update_time &lt; NOW() THEN '超期反馈'
        WHEN c.feedback_time IS NULL AND a.update_time > NOW() THEN '正常反馈'
        WHEN c.feedback_time &lt; a.update_time THEN '正常反馈'
        ELSE '超期反馈'
        END AS mzx_type
        FROM
        (
        SELECT
        id,
        emergency_degree,
        assign_time,
        CASE

        WHEN emergency_degree = '一般' THEN
        DATE_ADD( create_time, INTERVAL 24 HOUR )
        WHEN emergency_degree = '紧急' THEN
        DATE_ADD( create_time, INTERVAL 12 HOUR )
        WHEN emergency_degree = '特急' THEN
        DATE_ADD( create_time, INTERVAL 2 HOUR ) ELSE create_time
        END AS create_time,
        CASE

        WHEN emergency_degree = '一般' THEN
        DATE_ADD( assign_time, INTERVAL 1 MONTH )
        WHEN emergency_degree = '紧急' THEN
        DATE_ADD( assign_time, INTERVAL 2 WEEK )
        WHEN emergency_degree = '特急' THEN
        DATE_ADD( assign_time, INTERVAL 1 WEEK ) ELSE assign_time
        END AS update_time
        FROM
        t_instruction_info
        WHERE
        STATUS = 1
        AND instruction_type IN ( 3, 6 )
        <if test="deptId != null  and deptId == '202'">AND create_dept_id != #{deptId}</if>
        <if test="deptId != null  and deptId != '' and deptId == '202' ">AND create_dept_id = #{deptId}</if>
        <if test="date != null  and date != ''">AND DATE_FORMAT( handle_time, '%Y-%m' ) = #{date}</if>
        <if test="startTime != null and startTime != ''"><!-- 开始时间检索 -->
            AND date_format(handle_time,'%y%m%d') &gt;= date_format(#{startTime},'%y%m%d')
        </if>
        <if test="endTime != null and endTime != ''"><!-- 结束时间检索 -->
            AND date_format(handle_time,'%y%m%d') &lt;= date_format(#{endTime},'%y%m%d')
        </if>
        <if test="instructionType != null ">and instruction_type = #{instructionType}</if>
        ) a
        LEFT JOIN ( SELECT id, receive_dept, receive_time, instrucation_id FROM t_instruction_receive WHERE STATUS = 1 ) b ON a.id = b.instrucation_id
        LEFT JOIN ( SELECT
        feedback_dept,instruction_id,MAX(feedback_time) feedback_time
        FROM
        t_instruction_feedback
        WHERE
        STATUS = 1
        AND feedback_is_end = 1 GROUP BY feedback_dept,instruction_id ) c ON b.instrucation_id = c.instruction_id
        AND b.receive_dept = c.feedback_dept
    </select>


    <select id="getReportOne" resultMap="InstructionInfoResult">
        SELECT
        id,
        assign_time,
        end_time,
        instruction_type,
        create_dept_id,
        CASE
        WHEN DATE_ADD( push_time, INTERVAL 270 DAY ) &lt; end_time THEN
        '超期化解' ELSE ''
        END AS remark
        FROM
        t_instruction_info
        WHERE
        instruction_type IN ( 3, 6 )
        AND STATUS = 1
        <if test="params.beginTime != null and params.beginTime != ''"><!-- 开始时间检索 -->
            AND date_format(push_time,'%y%m%d') &gt;= date_format(#{params.beginTime},'%y%m%d')
        </if>
        <if test="params.endTime != null and params.endTime != ''"><!-- 结束时间检索 -->
            AND date_format(push_time,'%y%m%d') &lt;= date_format(#{params.endTime},'%y%m%d')
        </if>
    </select>

    <select id="getCountyHjl" resultMap="InstructionInfoResult">
        SELECT
            a.id,b.receive_dept receive_unit,end_time
        FROM
            ( SELECT id, receive_unit, assign_time, end_time FROM t_instruction_info WHERE STATUS = 1
        <if test="startTime != null and startTime != ''"><!-- 开始时间检索 -->
            AND date_format(assign_time,'%y%m%d') &gt;= date_format(#{startTime},'%y%m%d')
        </if>
        <if test="endTime != null and endTime != ''"><!-- 结束时间检索 -->
            AND date_format(assign_time,'%y%m%d') &lt;= date_format(#{endTime},'%y%m%d')
        </if>
        <if test="instructionType != null ">and instruction_type = #{instructionType}</if>
        <if test="deptId != null  and deptId != ''">AND create_dept_id = #{deptId}</if>
            ) a
                LEFT JOIN ( SELECT id, receive_dept, instrucation_id FROM t_instruction_receive WHERE STATUS = 1 ) b ON a.id = b.instrucation_id
    </select>

    <select id="getReceivePoor" resultMap="InstructionInfoResult">
        SELECT
        a.*,
        b.receive_dept AS receive_str,
        b.receive_time AS update_time,
        b.is_mzx AS spare_num
        FROM
        (
        SELECT
        id,
        instruction_title,
        instruction_type,
        emergency_degree,
        assign_time,
        handle_time,
        CASE
        WHEN emergency_degree = '一般' THEN
        DATE_ADD( assign_time, INTERVAL 24 HOUR )
        WHEN emergency_degree = '紧急' THEN
        DATE_ADD( assign_time, INTERVAL 12 HOUR )
        WHEN emergency_degree = '特急' THEN
        DATE_ADD( assign_time, INTERVAL 2 HOUR ) ELSE assign_time
        END AS create_time
        FROM
        t_instruction_info
        WHERE
        STATUS = 1
        AND instruction_type IN ( 3, 6 )
        <if test="params != null and params.beginTime != null and params.beginTime != ''"><!-- 开始时间检索 -->
            AND date_format(assign_time,'%y%m%d') &gt;= date_format(#{params.beginTime},'%y%m%d')
        </if>
        <if test="params != null and params.endTime != null and params.endTime != ''"><!-- 结束时间检索 -->
            AND date_format(assign_time,'%y%m%d') &lt;= date_format(#{params.endTime},'%y%m%d')
        </if>
        <if test="params != null and params.isInclude != null and params.isInclude == 0">
            AND instruction_type = 3
        </if>
        <if test='deptId != null  and deptId != "" and deptId != "-1"'>AND create_dept_id = #{deptId}</if>
        <if test='deptId != null  and deptId != "" and deptId == "-1"'>AND create_dept_id != 202</if>
        ) a
        LEFT JOIN ( SELECT id, receive_dept, IFNULL(receive_time,NOW()) now_time,receive_time, instrucation_id,is_mzx
        FROM t_instruction_receive WHERE STATUS = 1 ) b ON a.id = b.instrucation_id
        where a.create_time &lt; b.now_time
        <if test="deptName != null  and deptName != ''">and b.receive_dept = #{deptName}</if>
    </select>
    <select id="getFeedbackPoor" resultType="com.ruoyi.instruction.domain.rspVo.FeedbackPoorRsp">
        SELECT
            b.id AS id,
            b.instruction_title AS instructionTitle,
            b.emergency_degree AS emergencyDegree,
            b.assign_time AS assignTime,
            b.handle_time AS handleTime,
            c.receive_dept AS receiveDept,
            c.receive_time AS receiveTime,
            a.feedback_dept AS feedbackDept,
            a.feedback_time AS feedbackTime,
            a.interval_days AS intervalDays,
            CASE
                WHEN c.is_mzx = 1 THEN '协办单位'
                WHEN c.is_mzx = 2 THEN '主办单位'
                ELSE NULL
                END AS isMzx,
            CASE
                WHEN b.instruction_type = 3 THEN '双排双办'
                WHEN b.instruction_type = 6 THEN '预警研判'
                ELSE NULL
                END AS instructionType
        FROM
            (
                SELECT
                    instruction_id,
                    receive_id,
                    feedback_dept_id,
                    MAX( feedback_time ) AS feedback_time,
                    feedback_dept,
                    MAX( interval_days ) AS interval_days
                FROM
                    t_instruction_feedback
                WHERE
                    STATUS = 1
                  AND interval_days IS NOT NULL
                GROUP BY
                    receive_id,
                    feedback_dept_id
            ) a
                LEFT JOIN (
                SELECT
                    id,
                    instruction_title,
                    emergency_degree,
                    instruction_type,
                    assign_time,
                    handle_time
                FROM
                    t_instruction_info
                WHERE
                    STATUS = 1
                  AND instruction_type IN ( 3, 6 )
                  AND create_dept_id = 202
            <if test="params.beginTime != null and params.beginTime != ''"><!-- 开始时间检索 -->
                AND date_format(assign_time,'%y%m%d') &gt;= date_format(#{params.beginTime},'%y%m%d')
            </if>
            <if test="params.endTime != null and params.endTime != ''"><!-- 结束时间检索 -->
                AND date_format(assign_time,'%y%m%d') &lt;= date_format(#{params.endTime},'%y%m%d')
            </if>
            <if test="params.isInclude != null and params.isInclude == 0">
                AND instruction_type = 3
            </if>
            ) b ON a.instruction_id = b.id
                LEFT JOIN ( SELECT id, receive_dept, is_mzx, receive_time FROM t_instruction_receive WHERE STATUS = 1 ) c ON a.receive_id = c.id
        <if test='isFilt != null and isFilt == "1"'>
            WHERE
            ( b.emergency_degree = '一般' AND a.interval_days > 30 )
            OR ( b.emergency_degree = '紧急' AND a.interval_days > 15 )
            OR ( b.emergency_degree = '特急' AND a.interval_days > 7 );
        </if>


    </select>
    <select id="getReceiveData" resultType="com.ruoyi.instruction.domain.rspVo.PoorReceiveOneRsp">
        SELECT
            b.receive_dept AS receiveDept,
            b.is_mzx AS isMzx,
            COUNT(*) acount
        FROM
            ( SELECT id FROM t_instruction_info WHERE STATUS = 1 AND instruction_type IN ( 3, 6 ) AND create_dept_id = 202
                <if test="params.beginTime != null and params.beginTime != ''"><!-- 开始时间检索 -->
                    AND date_format(assign_time,'%y%m%d') &gt;= date_format(#{params.beginTime},'%y%m%d')
                </if>
                <if test="params.endTime != null and params.endTime != ''"><!-- 结束时间检索 -->
                    AND date_format(assign_time,'%y%m%d') &lt;= date_format(#{params.endTime},'%y%m%d')
                </if>
                <if test="params.isInclude != null and params.isInclude == 0">
                    AND instruction_type = 3
                </if>
                ) a
                LEFT JOIN ( SELECT instrucation_id, receive_dept, is_mzx FROM t_instruction_receive WHERE STATUS = 1 ) b ON a.id = b.instrucation_id
        WHERE
            b.receive_dept IS NOT NULL
        GROUP BY
            b.receive_dept,
            b.is_mzx
    </select>
    <select id="findMyTodo" resultType="java.util.Map">
        SELECT
        (SELECT
        count(*)
        FROM
        t_instruction_info
        WHERE
        STATUS = 1
        AND create_dept_id = 202
        AND instruction_type IN ( 3, 6 )
        <if test="startTime != null and startTime != ''"><!-- 开始时间检索 -->
            AND date_format(assign_time,'%y%m%d') &gt;= date_format(#{startTime},'%y%m%d')
        </if>
        <if test="endTime != null and endTime != ''"><!-- 结束时间检索 -->
            AND date_format(assign_time,'%y%m%d') &lt;= date_format(#{endTime},'%y%m%d')
        </if>
        )yjb,
        (SELECT
        count(*)
        FROM
        t_instruction_info
        WHERE
        STATUS = 1
        AND create_dept_id = 202
        AND instruction_type IN ( 3, 6 )
        <if test="startTime != null and startTime != ''"><!-- 开始时间检索 -->
            AND date_format(assign_time,'%y%m%d') &gt;= date_format(#{startTime},'%y%m%d')
        </if>
        <if test="endTime != null and endTime != ''"><!-- 结束时间检索 -->
            AND date_format(assign_time,'%y%m%d') &lt;= date_format(#{endTime},'%y%m%d')
        </if>
        and end_time is not null )yxh,
        (SELECT
        count(*)
        FROM
        ( SELECT * FROM t_handle_delay WHERE STATUS = 1 AND apply_result = 0 ) a
        LEFT JOIN (
        SELECT
        id
        FROM
        t_instruction_info
        WHERE
        STATUS = 1
        AND create_dept_id = 202
        AND instruction_type IN ( 3, 6 )
        <if test="startTime != null and startTime != ''"><!-- 开始时间检索 -->
            AND date_format(assign_time,'%y%m%d') &gt;= date_format(#{startTime},'%y%m%d')
        </if>
        <if test="endTime != null and endTime != ''"><!-- 结束时间检索 -->
            AND date_format(assign_time,'%y%m%d') &lt;= date_format(#{endTime},'%y%m%d')
        </if>
        ) b ON a.instruction_id = b.id
        where b.id is not null )yqdsh,
        (SELECT
        count(DISTINCT id) acount
        FROM
        (
        SELECT
        id,
        receive_unit,(
        LENGTH( receive_unit ) - LENGTH(
        REPLACE ( receive_unit, ',', '' )) + 1
        ) AS unit_count
        FROM
        t_instruction_info
        WHERE
        STATUS = 1
        AND create_dept_id = 202
        AND instruction_type IN ( 3, 6 )
        AND (instrucation_is_end is null or instrucation_is_end = 2)
        AND end_time IS NULL
        <if test="startTime != null and startTime != ''"><!-- 开始时间检索 -->
            AND date_format(assign_time,'%y%m%d') &gt;= date_format(#{startTime},'%y%m%d')
        </if>
        <if test="endTime != null and endTime != ''"><!-- 结束时间检索 -->
            AND date_format(assign_time,'%y%m%d') &lt;= date_format(#{endTime},'%y%m%d')
        </if>
        ) a
        LEFT JOIN ( SELECT
        instruction_id,
        COUNT( DISTINCT feedback_dept ) AS acount
        FROM
        t_instruction_county_feedback
        WHERE
        STATUS = 1
        AND is_end = 1
        GROUP BY
        instruction_id,
        feedback_dept_id ) b ON a.id = b.instruction_id
        where a.unit_count &lt;= b.acount)dxh
        from  DUAL
    </select>
    <select id="findMyFollow" resultMap="InstructionInfoResult">
        SELECT
            id,
            instruction_type,
            instruction_title,
            assign_time,
            end_time
        FROM
            t_instruction_info
        WHERE
            STATUS = 1
          AND create_dept_id = 202
          AND instruction_type IN (3,6 )
        <if test="myFollowIds != null and myFollowIds != ''">
            AND id in
            <foreach item="id" collection="myFollowIds" open="(" separator="," close=")">
                #{id}
            </foreach>
        </if>
        <if test="wkMzxIds != null and wkMzxIds != ''">
            AND id in
            <foreach item="id" collection="wkMzxIds" open="(" separator="," close=")">
                #{id}
            </foreach>
        </if>
    </select>
    <select id="getCaseList" resultMap="InstructionInfoResult">
        SELECT
        id,
        instruction_title,
        REPLACE ( REPLACE ( receive_unit, '[', '' ), ']', '' ) receive_unit,
        case_type,
        case_reason,
        case_time,
        instruction_type
        FROM
        t_instruction_info
        WHERE
        STATUS = 1
        AND case_type IS NOT NULL
        <if test="instructionType != null">AND instruction_type = #{instructionType}</if>
        <if test="instructionTitle != null  and instructionTitle != ''">and instruction_title like concat('%', #{instructionTitle}, '%')</if>
        <if test="params.beginTime != null and params.beginTime != ''"><!-- 开始时间检索 -->
            AND date_format(case_time,'%y%m%d') &gt;= date_format(#{params.beginTime},'%y%m%d')
        </if>
        <if test="params.endTime != null and params.endTime != ''"><!-- 结束时间检索 -->
            AND date_format(case_time,'%y%m%d') &lt;= date_format(#{params.endTime},'%y%m%d')
        </if>
        order by case_time desc
    </select>
    <select id="getNoReceive" resultType="com.ruoyi.instruction.domain.rspVo.ToReceiveRsp">
        SELECT
            a.id,
            a.instruction_title instructionTitle,
            a.emergency_degree emergencyDegree,
            a.assign_time assignTime,
            CASE a.instruction_type
                WHEN 3 THEN '双排双办'
                WHEN 6 THEN '预警研判'
                END as instructionType,
            a.handle_time handleTime,
            CASE b.is_mzx
                WHEN 1 THEN '协办单位'
                WHEN 2 THEN '主办单位'
                END as isMzx,
            b.receive_dept receiveUnit
        FROM
            (SELECT
                 id,
                 instruction_title,
                 emergency_degree,
                 assign_time,
                 instruction_type,
                 handle_time
             FROM
                 t_instruction_info
             WHERE
                 STATUS = 1
                <choose>
                    <when test="cityFlag">
                        AND create_dept_id = 202
                    </when>
                    <otherwise>
                        AND create_dept_id != 202
                    </otherwise>
                </choose>
               AND instruction_type IN (3, 6)
                <if test="deptId != null">
                    and create_dept_id = #{deptId}
                </if>
                <if test="startTime != null and startTime != ''">
                    and date_format(assign_time,'%y%m%d') &gt;= date_format(#{startTime},'%y%m%d')
                </if>
                <if test="endTime != null and endTime != ''">
                    and date_format(assign_time,'%y%m%d') &lt;= date_format(#{endTime},'%y%m%d')
                </if>
            ) a
                LEFT JOIN (
                SELECT
                    id,
                    receive_dept,
                    instrucation_id,
                    is_mzx
                FROM
                    t_instruction_receive
                WHERE
                    STATUS = 1
                  AND receive_time IS NULL
            ) b ON a.id = b.instrucation_id
        WHERE
            b.id IS NOT NULL
            <if test="countyType != null and countyType != ''">
                <choose>
                    <when test="countyType == '部门'">
                        and (b.receive_dept like '%政法委%' or b.receive_dept like '%局%')
                    </when>
                    <when test="countyType == '镇街'">
                        and (b.receive_dept like '%街道%' or b.receive_dept like '%镇%' or b.receive_dept like '%乡%')
                    </when>
                </choose>
            </if>

        AND (
                (a.emergency_degree = '一般' AND TIMESTAMPDIFF(HOUR, a.assign_time, NOW()) > 24)
                OR
                (a.emergency_degree = '紧急' AND TIMESTAMPDIFF(HOUR, a.assign_time, NOW()) > 12)
                OR
                (a.emergency_degree = '特急' AND TIMESTAMPDIFF(HOUR, a.assign_time, NOW()) > 2)
            );

    </select>
    <select id="getSpsbAndyjIds" resultType="java.lang.Long">
        SELECT
            id
        FROM
            t_instruction_info
        WHERE
            STATUS = 1
        <choose>
            <when test="cityFlag">
                AND create_dept_id = 202
            </when>
            <otherwise>
                AND create_dept_id != 202
            </otherwise>
        </choose>
          AND end_time IS NULL
          AND instruction_type IN ( 3, 6 )
        <if test="deptId != null">
            and create_dept_id = #{deptId}
        </if>
        <if test="startTime != null and startTime != ''">
            and date_format(assign_time,'%y%m%d') &gt;= date_format(#{startTime},'%y%m%d')
        </if>
        <if test="endTime != null and endTime != ''">
            and date_format(assign_time,'%y%m%d') &lt;= date_format(#{endTime},'%y%m%d')
        </if>

<!--        SELECT-->
<!--        t1.id-->
<!--        FROM-->
<!--        t_instruction_info t1-->
<!--        left join-->
<!--        t_instruction_transfer t2 on t1.id = t2.instruction_id-->
<!--        WHERE-->
<!--        t1.STATUS = 1-->
<!--        AND t1.create_dept_id = 202-->
<!--        AND t1.end_time IS NULL-->
<!--        AND t1.instruction_type IN ( 3, 6 )-->
<!--        <if test="deptId != null">-->
<!--            and t1.create_dept_id = #{deptId}-->
<!--        </if>-->
<!--        <if test="startTime != null and startTime != ''">-->
<!--            and date_format(t1.assign_time,'%y%m%d') &gt;= date_format(#{startTime},'%y%m%d')-->
<!--        </if>-->
<!--        <if test="endTime != null and endTime != ''">-->
<!--            and date_format(t1.assign_time,'%y%m%d') &lt;= date_format(#{endTime},'%y%m%d')-->
<!--        </if>-->
<!--        <if test="countyFlag != null and countyFlag != ''">-->
<!--            <choose>-->
<!--                <when test="countyFlag == '部门'">-->
<!--                    and (t2.transfer_dept like '%政法委%' or t2.transfer_dept like '%局%')-->
<!--                </when>-->
<!--                <when test="countyFlag == '镇街'">-->
<!--                    and (t2.transfer_dept like '%街道%' or t2.transfer_dept like '%镇%' or t2.transfer_dept like '%乡%')-->
<!--                </when>-->
<!--            </choose>-->
<!--        </if>-->
    </select>
    <select id="getToFeedbackList" resultType="com.ruoyi.instruction.domain.rspVo.ToFeedbackRsp">
        SELECT
        a.id,
        a.emergency_degree AS emergencyDegree,
        a.instruction_title AS instructionTitle,
        CASE a.instruction_type
        WHEN 3 THEN '双排双办'
        WHEN 6 THEN '预警研判'
        END AS instructionType,
        a.assign_time AS assignTime,
        a.handle_time AS handleTime,
        c.receive_dept AS receiveDept,
        GROUP_CONCAT(b.feedback_dept SEPARATOR ', ') AS feedbackDept,
        MAX(b.feedback_time) AS feedbackTime,
        GROUP_CONCAT(
        CASE b.is_mzx
        WHEN 1 THEN '协办单位'
        WHEN 2 THEN '主办单位'
        END SEPARATOR ', '
        ) AS isMzx
        FROM
        (
        SELECT
        id, emergency_degree, instruction_title, instruction_type, assign_time, handle_time
        FROM
        t_instruction_info
        WHERE
        STATUS = 1
        <choose>
            <when test="cityFlag">
                AND create_dept_id = 202
            </when>
            <otherwise>
                AND create_dept_id != 202
            </otherwise>
        </choose>
        AND end_time IS NULL AND instruction_type IN (3, 6)
        ) a
        INNER JOIN
        t_instruction_receive c ON a.id = c.instrucation_id AND c.STATUS = 1
        INNER JOIN
        (
        SELECT
        f.instruction_id,
        f.receive_id,
        f.feedback_dept,
        f.feedback_time,
        f.is_mzx
        FROM
        t_instruction_feedback f
        INNER JOIN (
        SELECT
        instruction_id,
        receive_id,
        feedback_dept_id,
        MAX(feedback_time) AS max_feedback_time
        FROM
        t_instruction_feedback
        WHERE
        STATUS = 1
        AND feedback_dept_id IS NOT NULL
        AND receive_id IS NOT NULL
        <if test="ids != null and ids.size() > 0">
            AND instruction_id IN
            <foreach item="id" collection="ids" open="(" separator="," close=")">
                #{id}
            </foreach>
        </if>
        GROUP BY
        instruction_id, receive_id, feedback_dept_id
        ) latest_feedback ON f.instruction_id = latest_feedback.instruction_id
        AND f.receive_id = latest_feedback.receive_id
        AND f.feedback_dept_id = latest_feedback.feedback_dept_id
        AND f.feedback_time = latest_feedback.max_feedback_time
        WHERE
        f.STATUS = 1
        AND f.feedback_is_end = 2
        ) b ON c.id = b.receive_id AND a.id = b.instruction_id
        GROUP BY
        a.id, c.id
    </select>
    <select id="findFeedbackTodo" resultMap="InstructionInfoResult">
        SELECT
            b.*
        FROM (
                 SELECT
                     t1.id,
                     t1.instruction_id,
                     t1.feedback_dept_id,
                     t1.feedback_is_end,
                     t1.interval_days,
                     t1.feedback_time
                 FROM t_instruction_feedback t1
                          INNER JOIN (
                     SELECT
                         instruction_id,
                         feedback_dept_id,
                         MAX(id) as max_id
                     FROM t_instruction_feedback
                     WHERE status = 1
                       AND feedback_dept_id IS NOT NULL
                     GROUP BY instruction_id, feedback_dept_id
                 ) t2 ON t1.id = t2.max_id
                     AND t1.instruction_id = t2.instruction_id
                     AND t1.feedback_dept_id = t2.feedback_dept_id
                 WHERE t1.status = 1
                   AND t1.feedback_dept_id IS NOT NULL
                   AND t1.feedback_is_end = 2
             ) a
                 LEFT JOIN (
            SELECT
                id,
                instruction_type,
                receive_unit,
                emergency_degree
            FROM t_instruction_info
            WHERE STATUS = 1
              AND create_dept_id = 202
              AND instruction_type in (3,6)
              AND end_time is NULL
        ) b ON a.instruction_id = b.id
        WHERE b.id is not null
          AND (
                (b.emergency_degree = '一般' AND DATEDIFF(NOW(), a.feedback_time) &gt;= 29)  -- 一般指令 &gt;= 29天
                OR (b.emergency_degree = '紧急' AND DATEDIFF(NOW(), a.feedback_time) &gt;= 14)  -- 紧急指令 &gt;= 14天
                OR (b.emergency_degree = '特急' AND DATEDIFF(NOW(), a.feedback_time) &gt;= 6)   -- 特急指令 &gt;= 6天
            )
          AND a.feedback_dept_id = #{deptId}
    </select>

    <!-- 获取金安稳待销号数据 -->
    <select id="getPendingCloseCount" resultType="java.lang.Integer">
        SELECT
            COUNT(DISTINCT id) AS acount
        FROM
            (
                SELECT
                    id,
                    receive_unit,
                    (LENGTH(receive_unit) - LENGTH(REPLACE(receive_unit, ',', '')) + 1) AS unit_count
                FROM
                    t_instruction_info
                WHERE
                    STATUS = 1
                    AND create_dept_id = 202
                    AND instruction_type = 1
                    AND (instrucation_is_end IS NULL OR instrucation_is_end = 2)
                    AND end_time IS NULL
            ) a
            LEFT JOIN (
                SELECT
                    instruction_id,
                    COUNT(DISTINCT feedback_dept) AS acount
                FROM
                    t_instruction_county_feedback
                WHERE
                    STATUS = 1
                    AND is_end = 1
                GROUP BY
                    instruction_id,
                    feedback_dept_id
            ) b ON a.id = b.instruction_id
        WHERE
            a.unit_count &lt;= b.acount
    </select>

    <!-- 指令概况统计 -->
    <select id="getInstructionOverview" resultType="java.util.Map">
        SELECT
            assigned_count AS assignedCount,
            closed_count AS closedCount,
            (assigned_count - closed_count) AS processingCount
        FROM (
            SELECT
                (SELECT COUNT(id)
                 FROM t_instruction_info
                 WHERE STATUS = 1
                   AND create_dept_id = 202
                   AND instruction_type = 1
                   <if test="startTime != null and startTime != ''">
                       AND assign_time &gt;= #{startTime}
                   </if>
                   <if test="endTime != null and endTime != ''">
                       AND assign_time &lt;= #{endTime}
                   </if>
                ) AS assigned_count,
                (SELECT COUNT(id)
                 FROM t_instruction_info
                 WHERE STATUS = 1
                   AND create_dept_id = 202
                   AND instruction_type = 1
                   AND end_time IS NOT NULL
                   <if test="startTime != null and startTime != ''">
                       AND assign_time &gt;= #{startTime}
                   </if>
                   <if test="endTime != null and endTime != ''">
                       AND assign_time &lt;= #{endTime}
                   </if>
                ) AS closed_count
                 ) overview
    </select>

    <!-- 查询提醒时效（未按时接收的指令） -->
    <select id="getRemindTimeoutList" resultType="java.util.Map">
        SELECT
            a.id,
            a.instruction_title AS instructionTitle,
            a.emergency_degree AS emergencyDegree,
            a.handle_time AS handleTime,
            a.assign_time AS assignTime,
            b.receive_dept AS receiveDept,
            b.receive_dept_id AS receiveDeptId,
            b.receive_time AS receiveTime,
            TIMESTAMPDIFF(MINUTE, a.assign_time, IFNULL(b.receive_time, NOW())) AS delayMinutes,
            CASE
                WHEN b.receive_time IS NULL THEN '未接收'
                ELSE '已接收但超时'
            END AS statusDesc,
            CASE
                WHEN a.emergency_degree = '一般' THEN 30
                WHEN a.emergency_degree = '紧急' THEN 20
                WHEN a.emergency_degree = '特急' THEN 10
                ELSE 30
            END AS requiredMinutes
        FROM
            (SELECT id, instruction_title, emergency_degree, assign_time, handle_time
             FROM t_instruction_info
             WHERE STATUS = 1
               AND create_dept_id = 202
               AND instruction_type = 1
               <if test="startTime != null and startTime != ''">
                   AND assign_time &gt;= #{startTime}
               </if>
               <if test="endTime != null and endTime != ''">
                   AND assign_time &lt;= #{endTime}
               </if>
            ) a
        LEFT JOIN
            (SELECT receive_dept, receive_dept_id, instrucation_id, receive_time
             FROM t_instruction_receive
             WHERE STATUS = 1) b ON a.id = b.instrucation_id
        WHERE
            a.id > 705
            AND (
                -- 一般指令：超过30分钟（未接收用当前时间计算）
                (a.emergency_degree = '一般' AND
                 TIMESTAMPDIFF(MINUTE, a.assign_time, IFNULL(b.receive_time, NOW())) > 30)
                OR
                -- 紧急指令：超过20分钟（未接收用当前时间计算）
                (a.emergency_degree = '紧急' AND
                 TIMESTAMPDIFF(MINUTE, a.assign_time, IFNULL(b.receive_time, NOW())) > 20)
                OR
                -- 特急指令：超过10分钟（未接收用当前时间计算）
                (a.emergency_degree = '特急' AND
                 TIMESTAMPDIFF(MINUTE, a.assign_time, IFNULL(b.receive_time, NOW())) > 10)
            )
                 ORDER BY a.assign_time DESC
    </select>

        <!-- 查询提醒时效统计（未按时接收和未按时反馈统计数据） -->
    <select id="getRemindTimeoutStatistics" resultType="java.util.Map">
        SELECT
            -- 未按时接收统计
            receive_timeout.deptCount AS receiveTimeoutDeptCount,
            receive_timeout.instructionCount AS receiveTimeoutInstructionCount,
            -- 未按时反馈统计
            feedback_timeout.deptCount AS feedbackTimeoutDeptCount,
            feedback_timeout.instructionCount AS feedbackTimeoutInstructionCount
        FROM
            (
                -- 未按时接收统计
                SELECT
                    COUNT(DISTINCT b.receive_dept) AS deptCount,
                    COUNT(DISTINCT a.id) AS instructionCount
                FROM
                    (SELECT id, instruction_title, emergency_degree, assign_time, handle_time
                     FROM t_instruction_info
                     WHERE STATUS = 1
                       AND create_dept_id = 202
                       AND instruction_type = 1
                       <if test="startTime != null and startTime != ''">
                           AND assign_time &gt;= #{startTime}
                       </if>
                       <if test="endTime != null and endTime != ''">
                           AND assign_time &lt;= #{endTime}
                       </if>
                    ) a
                LEFT JOIN
                    (SELECT receive_dept, receive_dept_id, instrucation_id, receive_time
                     FROM t_instruction_receive
                     WHERE STATUS = 1) b ON a.id = b.instrucation_id
                WHERE
                    a.id > 705
                    AND (
                        -- 一般指令：超过30分钟（未接收用当前时间计算）
                        (a.emergency_degree = '一般' AND
                         TIMESTAMPDIFF(MINUTE, a.assign_time, IFNULL(b.receive_time, NOW())) > 30)
                        OR
                        -- 紧急指令：超过20分钟（未接收用当前时间计算）
                        (a.emergency_degree = '紧急' AND
                         TIMESTAMPDIFF(MINUTE, a.assign_time, IFNULL(b.receive_time, NOW())) > 20)
                        OR
                        -- 特急指令：超过10分钟（未接收用当前时间计算）
                        (a.emergency_degree = '特急' AND
                         TIMESTAMPDIFF(MINUTE, a.assign_time, IFNULL(b.receive_time, NOW())) > 10)
                    )
            ) receive_timeout
        CROSS JOIN
            (
                -- 未按时反馈统计
                SELECT
                    COUNT(DISTINCT c.feedback_dept_id) AS deptCount,
                    COUNT(DISTINCT a.id) AS instructionCount
                FROM
                    (SELECT id, instruction_title, emergency_degree, assign_time, handle_time
                     FROM t_instruction_info
                     WHERE STATUS = 1
                       AND create_dept_id = 202
                       AND instruction_type = 1
                       <if test="startTime != null and startTime != ''">
                           AND assign_time &gt;= #{startTime}
                       </if>
                       <if test="endTime != null and endTime != ''">
                           AND assign_time &lt;= #{endTime}
                       </if>
                    ) a
                LEFT JOIN
                    (SELECT id, receive_dept, receive_dept_id, instrucation_id, receive_time
                     FROM t_instruction_receive
                     WHERE STATUS = 1) b ON a.id = b.instrucation_id
                LEFT JOIN
                    (SELECT transfer_dept, receive_id
                     FROM t_instruction_transfer
                     WHERE status = 1) d ON b.id = d.receive_id
                LEFT JOIN
                    (SELECT receive_time, feedback_dept_id, instruction_id
                     FROM t_instruction_county_feedback
                     WHERE STATUS = 1 AND is_end = 1) c ON b.instrucation_id = c.instruction_id
                     AND b.receive_dept_id = c.feedback_dept_id
                WHERE
                    a.handle_time IS NOT NULL
                    AND IFNULL(c.receive_time, NOW()) > a.handle_time
            ) feedback_timeout
    </select>

    <!-- 查询未按时反馈的指令列表 -->
    <select id="getOvertimeFeedbackList" resultType="java.util.Map">
        SELECT
            a.id,
            a.instruction_title AS instructionTitle,
            a.emergency_degree AS emergencyDegree,
            a.handle_time AS handleTime,
            a.assign_time AS assignTime,
            b.receive_dept AS receiveDept,
            b.receive_dept_id AS receiveDeptId,
            b.receive_time AS receiveTime,
            c.feedback_dept_id AS feedbackDeptId,
            c.receive_time AS feedbackTime,
            d.transfer_dept AS transferDept,
            CASE
                WHEN c.receive_time IS NULL THEN '未反馈'
                ELSE '已反馈但超期'
            END AS statusDesc,
            TIMESTAMPDIFF(DAY, a.handle_time, IFNULL(c.receive_time, NOW())) AS overdueDays
        FROM
            (SELECT id, instruction_title, emergency_degree, assign_time, handle_time
             FROM t_instruction_info
             WHERE STATUS = 1
               AND create_dept_id = 202
               AND instruction_type = 1
               <if test="startTime != null and startTime != ''">
                   AND assign_time &gt;= #{startTime}
               </if>
               <if test="endTime != null and endTime != ''">
                   AND assign_time &lt;= #{endTime}
               </if>
            ) a
        LEFT JOIN
            (SELECT id, receive_dept, receive_dept_id, instrucation_id, receive_time
             FROM t_instruction_receive
             WHERE STATUS = 1) b ON a.id = b.instrucation_id
        LEFT JOIN
            (SELECT transfer_dept, receive_id
             FROM t_instruction_transfer
             WHERE status = 1) d ON b.id = d.receive_id
        LEFT JOIN
            (SELECT receive_time, feedback_dept_id, instruction_id
             FROM t_instruction_county_feedback
             WHERE STATUS = 1 AND is_end = 1) c ON b.instrucation_id = c.instruction_id
             AND b.receive_dept_id = c.feedback_dept_id
        WHERE
            a.handle_time IS NOT NULL
            AND IFNULL(c.receive_time, NOW()) > a.handle_time
        ORDER BY a.assign_time DESC
    </select>

    <!-- 查询未按时接收按部门分组统计 -->
    <select id="getReceiveTimeoutByDept" resultType="java.util.Map">
        SELECT
            b.receive_dept AS deptName,
            COUNT(DISTINCT a.id) AS totalCount,
            COUNT(DISTINCT CASE
                WHEN (
                    (a.emergency_degree = '一般' AND TIMESTAMPDIFF(MINUTE, a.assign_time, IFNULL(b.receive_time, NOW())) > 30)
                    OR (a.emergency_degree = '紧急' AND TIMESTAMPDIFF(MINUTE, a.assign_time, IFNULL(b.receive_time, NOW())) > 20)
                    OR (a.emergency_degree = '特急' AND TIMESTAMPDIFF(MINUTE, a.assign_time, IFNULL(b.receive_time, NOW())) > 10)
                ) THEN a.id END
            ) AS timeoutCount
        FROM
            (SELECT id, instruction_title, emergency_degree, assign_time, handle_time
             FROM t_instruction_info
             WHERE STATUS = 1
               AND create_dept_id = 202
               AND instruction_type = 1
               <if test="startTime != null and startTime != ''">
                   AND assign_time &gt;= #{startTime}
               </if>
               <if test="endTime != null and endTime != ''">
                   AND assign_time &lt;= #{endTime}
               </if>
            ) a
        LEFT JOIN
            (SELECT id, receive_dept, receive_dept_id, instrucation_id, receive_time
             FROM t_instruction_receive
             WHERE STATUS = 1) b ON a.id = b.instrucation_id
        WHERE
            a.id > 705
            AND b.receive_dept IS NOT NULL
        GROUP BY b.receive_dept
        ORDER BY timeoutCount DESC, totalCount DESC
    </select>

    <!-- 查询未按时反馈按部门分组统计 -->
    <select id="getFeedbackTimeoutByDept" resultType="java.util.Map">
        SELECT
            b.receive_dept AS deptName,
            COUNT(DISTINCT a.id) AS totalCount,
            COUNT(DISTINCT CASE
                WHEN a.handle_time IS NOT NULL AND IFNULL(c.receive_time, NOW()) > a.handle_time
                THEN a.id END
            ) AS timeoutCount
        FROM
            (SELECT id, instruction_title, emergency_degree, assign_time, handle_time
             FROM t_instruction_info
             WHERE STATUS = 1
               AND create_dept_id = 202
               AND instruction_type = 1
               <if test="startTime != null and startTime != ''">
                   AND assign_time &gt;= #{startTime}
               </if>
               <if test="endTime != null and endTime != ''">
                   AND assign_time &lt;= #{endTime}
               </if>
            ) a
        LEFT JOIN
            (SELECT id, receive_dept, receive_dept_id, instrucation_id, receive_time
             FROM t_instruction_receive
             WHERE STATUS = 1) b ON a.id = b.instrucation_id
        LEFT JOIN
            (SELECT receive_time, feedback_dept_id, instruction_id
             FROM t_instruction_county_feedback
             WHERE STATUS = 1 AND is_end = 1) c ON b.instrucation_id = c.instruction_id
             AND b.receive_dept_id = c.feedback_dept_id
        WHERE
            b.receive_dept IS NOT NULL
        GROUP BY b.receive_dept
        ORDER BY timeoutCount DESC, totalCount DESC
    </select>

    <!-- 指令移交：更新指令的移交状态 -->
    <update id="updateTransferStatus">
        UPDATE t_instruction_info
        SET is_transferred = 1
        WHERE id = #{instructionId} AND is_transferred = 0
    </update>

    <!-- 查询重点人员类型统计 -->
    <select id="getPersonTypeStatistics" resultType="java.util.Map">
        SELECT
            IFNULL(p_type,'其他类') AS pType,
            count(*) AS acount
        FROM
            t_instruction_person
        WHERE
            p_level IS NOT NULL
            AND STATUS = 1
            AND p_level != 0
            <if test="pLevel != null">
                AND p_level = #{pLevel}
            </if>
        GROUP BY
            CASE
                WHEN p_type IS NULL OR p_type = '' THEN '其他类'
                ELSE p_type
            END
        ORDER BY acount DESC
    </select>

    <!-- 查询人员走访记录统计 -->
    <select id="getPersonVisitStatistics" resultType="java.util.Map">
        SELECT
            p.id,
            p.person_name AS personName,
            p.duty_place AS dutyPlace,
            p.p_town AS pTown,
            p.p_level AS pLevel,
            CASE
                WHEN p.p_level = 1 THEN '黄色等级'
                WHEN p.p_level = 2 THEN '橙色等级'
                WHEN p.p_level = 3 THEN '红色等级'
                ELSE '未知等级'
            END AS levelName,
            CASE
                WHEN p.p_level = 3 THEN '每天一次'
                WHEN p.p_level = 2 THEN '每周一次'
                WHEN p.p_level = 1 THEN '每月一次'
                ELSE '未知要求'
            END AS visitRequirement,
            IFNULL(v.lastVisitTime, '从未走访') AS lastVisitTime,
            CASE
                WHEN v.lastVisitTime IS NULL THEN 9999
                WHEN p.p_level = 3 THEN DATEDIFF(CURDATE(), DATE_ADD(v.lastVisitTime, INTERVAL 1 DAY))    -- 红色：加1天
                WHEN p.p_level = 2 THEN DATEDIFF(CURDATE(), DATE_ADD(v.lastVisitTime, INTERVAL 1 WEEK))   -- 橙色：加1周
                WHEN p.p_level = 1 THEN DATEDIFF(CURDATE(), DATE_ADD(v.lastVisitTime, INTERVAL 1 MONTH))  -- 黄色：加1月
                ELSE 0
            END AS daysSinceLastVisit,
            CASE
                WHEN v.lastVisitTime IS NULL THEN '从未走访'
                WHEN (p.p_level = 3 AND DATEDIFF(CURDATE(), DATE_ADD(v.lastVisitTime, INTERVAL 1 DAY)) > 30) OR
                     (p.p_level = 2 AND DATEDIFF(CURDATE(), DATE_ADD(v.lastVisitTime, INTERVAL 1 WEEK)) > 30) OR
                     (p.p_level = 1 AND DATEDIFF(CURDATE(), DATE_ADD(v.lastVisitTime, INTERVAL 1 MONTH)) > 30) THEN '超过30天'
                WHEN (p.p_level = 3 AND DATEDIFF(CURDATE(), DATE_ADD(v.lastVisitTime, INTERVAL 1 DAY)) &gt;= 15) OR
                     (p.p_level = 2 AND DATEDIFF(CURDATE(), DATE_ADD(v.lastVisitTime, INTERVAL 1 WEEK)) &gt;= 15) OR
                     (p.p_level = 1 AND DATEDIFF(CURDATE(), DATE_ADD(v.lastVisitTime, INTERVAL 1 MONTH)) &gt;= 15) THEN '超过15-30天'
                WHEN (p.p_level = 3 AND DATEDIFF(CURDATE(), DATE_ADD(v.lastVisitTime, INTERVAL 1 DAY)) &gt;= 7) OR
                     (p.p_level = 2 AND DATEDIFF(CURDATE(), DATE_ADD(v.lastVisitTime, INTERVAL 1 WEEK)) &gt;= 7) OR
                     (p.p_level = 1 AND DATEDIFF(CURDATE(), DATE_ADD(v.lastVisitTime, INTERVAL 1 MONTH)) &gt;= 7) THEN '超过7-15天'
                WHEN (p.p_level = 3 AND DATEDIFF(CURDATE(), DATE_ADD(v.lastVisitTime, INTERVAL 1 DAY)) &gt;= 3) OR
                     (p.p_level = 2 AND DATEDIFF(CURDATE(), DATE_ADD(v.lastVisitTime, INTERVAL 1 WEEK)) &gt;= 3) OR
                     (p.p_level = 1 AND DATEDIFF(CURDATE(), DATE_ADD(v.lastVisitTime, INTERVAL 1 MONTH)) &gt;= 3) THEN '超过3-7天'
                WHEN (p.p_level = 3 AND DATEDIFF(CURDATE(), DATE_ADD(v.lastVisitTime, INTERVAL 1 DAY)) &gt;= 1) OR
                     (p.p_level = 2 AND DATEDIFF(CURDATE(), DATE_ADD(v.lastVisitTime, INTERVAL 1 WEEK)) &gt;= 1) OR
                     (p.p_level = 1 AND DATEDIFF(CURDATE(), DATE_ADD(v.lastVisitTime, INTERVAL 1 MONTH)) &gt;= 1) THEN '超过1-3天'
                ELSE '今日已走访'
            END AS overduePeriod,
            CASE
                WHEN v.lastVisitTime IS NULL THEN 1
                WHEN p.p_level = 3 AND DATEDIFF(CURDATE(), DATE_ADD(v.lastVisitTime, INTERVAL 1 DAY)) > 0 THEN 1
                WHEN p.p_level = 2 AND DATEDIFF(CURDATE(), DATE_ADD(v.lastVisitTime, INTERVAL 1 WEEK)) > 0 THEN 1
                WHEN p.p_level = 1 AND DATEDIFF(CURDATE(), DATE_ADD(v.lastVisitTime, INTERVAL 1 MONTH)) > 0 THEN 1
                ELSE 0
            END AS isOverdue
        FROM
            t_instruction_person p
        LEFT JOIN (
            SELECT
                person_id,
                MAX(interview_time) AS lastVisitTime
            FROM
                t_person_interview
            WHERE
                status = '1'
                AND person_id IS NOT NULL
            GROUP BY person_id
        ) v ON p.id = v.person_id
        WHERE
            p.status = 1
            AND p.p_level IS NOT NULL
            AND p.p_level != 0
            -- 过滤掉加上间隔时间后大于当前时间的记录
            AND (
                v.lastVisitTime IS NULL OR
                (p.p_level = 3 AND DATE_ADD(v.lastVisitTime, INTERVAL 1 DAY) &lt;= CURDATE()) OR
                (p.p_level = 2 AND DATE_ADD(v.lastVisitTime, INTERVAL 1 WEEK) &lt;= CURDATE()) OR
                (p.p_level = 1 AND DATE_ADD(v.lastVisitTime, INTERVAL 1 MONTH) &lt;= CURDATE())
            )
            <if test="timeRange != null and timeRange != ''">
                <choose>
                    <when test="timeRange == 'month30'">
                        AND (
                            v.lastVisitTime IS NULL OR
                            (p.p_level = 3 AND DATEDIFF(CURDATE(), DATE_ADD(v.lastVisitTime, INTERVAL 1 DAY)) > 30) OR
                            (p.p_level = 2 AND DATEDIFF(CURDATE(), DATE_ADD(v.lastVisitTime, INTERVAL 1 WEEK)) > 30) OR
                            (p.p_level = 1 AND DATEDIFF(CURDATE(), DATE_ADD(v.lastVisitTime, INTERVAL 1 MONTH)) > 30)
                        )
                    </when>
                    <when test="timeRange == 'day15to30'">
                        AND (
                            (p.p_level = 3 AND DATEDIFF(CURDATE(), DATE_ADD(v.lastVisitTime, INTERVAL 1 DAY)) BETWEEN 15 AND 30) OR
                            (p.p_level = 2 AND DATEDIFF(CURDATE(), DATE_ADD(v.lastVisitTime, INTERVAL 1 WEEK)) BETWEEN 15 AND 30) OR
                            (p.p_level = 1 AND DATEDIFF(CURDATE(), DATE_ADD(v.lastVisitTime, INTERVAL 1 MONTH)) BETWEEN 15 AND 30)
                        )
                    </when>
                    <when test="timeRange == 'day7to15'">
                        AND (
                            (p.p_level = 3 AND DATEDIFF(CURDATE(), DATE_ADD(v.lastVisitTime, INTERVAL 1 DAY)) BETWEEN 7 AND 14) OR
                            (p.p_level = 2 AND DATEDIFF(CURDATE(), DATE_ADD(v.lastVisitTime, INTERVAL 1 WEEK)) BETWEEN 7 AND 14) OR
                            (p.p_level = 1 AND DATEDIFF(CURDATE(), DATE_ADD(v.lastVisitTime, INTERVAL 1 MONTH)) BETWEEN 7 AND 14)
                        )
                    </when>
                    <when test="timeRange == 'day3to7'">
                        AND (
                            (p.p_level = 3 AND DATEDIFF(CURDATE(), DATE_ADD(v.lastVisitTime, INTERVAL 1 DAY)) BETWEEN 3 AND 6) OR
                            (p.p_level = 2 AND DATEDIFF(CURDATE(), DATE_ADD(v.lastVisitTime, INTERVAL 1 WEEK)) BETWEEN 3 AND 6) OR
                            (p.p_level = 1 AND DATEDIFF(CURDATE(), DATE_ADD(v.lastVisitTime, INTERVAL 1 MONTH)) BETWEEN 3 AND 6)
                        )
                    </when>
                    <when test="timeRange == 'day1to3'">
                        AND (
                            (p.p_level = 3 AND DATEDIFF(CURDATE(), DATE_ADD(v.lastVisitTime, INTERVAL 1 DAY)) BETWEEN 1 AND 2) OR
                            (p.p_level = 2 AND DATEDIFF(CURDATE(), DATE_ADD(v.lastVisitTime, INTERVAL 1 WEEK)) BETWEEN 1 AND 2) OR
                            (p.p_level = 1 AND DATEDIFF(CURDATE(), DATE_ADD(v.lastVisitTime, INTERVAL 1 MONTH)) BETWEEN 1 AND 2)
                        )
                    </when>
                </choose>
            </if>
        ORDER BY
            p.p_level DESC,
            daysSinceLastVisit DESC,
            p.person_name ASC
    </select>

    <!-- 查询人员走访记录统计汇总（按属地分组） -->
    <select id="getPersonVisitStatisticsSummary" resultType="java.util.Map">
        SELECT
            p.duty_place AS dutyPlace,
            -- 超过1个月人数
            SUM(CASE
                WHEN v.lastVisitTime IS NULL OR
                     (p.p_level = 3 AND DATEDIFF(CURDATE(), DATE_ADD(v.lastVisitTime, INTERVAL 1 DAY)) > 30) OR
                     (p.p_level = 2 AND DATEDIFF(CURDATE(), DATE_ADD(v.lastVisitTime, INTERVAL 1 WEEK)) > 30) OR
                     (p.p_level = 1 AND DATEDIFF(CURDATE(), DATE_ADD(v.lastVisitTime, INTERVAL 1 MONTH)) > 30)
                THEN 1 ELSE 0
            END) AS month30Count,
            -- 超15-30天人数
            SUM(CASE
                WHEN (p.p_level = 3 AND DATEDIFF(CURDATE(), DATE_ADD(v.lastVisitTime, INTERVAL 1 DAY)) BETWEEN 15 AND 30) OR
                     (p.p_level = 2 AND DATEDIFF(CURDATE(), DATE_ADD(v.lastVisitTime, INTERVAL 1 WEEK)) BETWEEN 15 AND 30) OR
                     (p.p_level = 1 AND DATEDIFF(CURDATE(), DATE_ADD(v.lastVisitTime, INTERVAL 1 MONTH)) BETWEEN 15 AND 30)
                THEN 1 ELSE 0
            END) AS day15to30Count,
            -- 超7-15天人数
            SUM(CASE
                WHEN (p.p_level = 3 AND DATEDIFF(CURDATE(), DATE_ADD(v.lastVisitTime, INTERVAL 1 DAY)) BETWEEN 7 AND 14) OR
                     (p.p_level = 2 AND DATEDIFF(CURDATE(), DATE_ADD(v.lastVisitTime, INTERVAL 1 WEEK)) BETWEEN 7 AND 14) OR
                     (p.p_level = 1 AND DATEDIFF(CURDATE(), DATE_ADD(v.lastVisitTime, INTERVAL 1 MONTH)) BETWEEN 7 AND 14)
                THEN 1 ELSE 0
            END) AS day7to15Count,
            -- 超3-7天人数
            SUM(CASE
                WHEN (p.p_level = 3 AND DATEDIFF(CURDATE(), DATE_ADD(v.lastVisitTime, INTERVAL 1 DAY)) BETWEEN 3 AND 6) OR
                     (p.p_level = 2 AND DATEDIFF(CURDATE(), DATE_ADD(v.lastVisitTime, INTERVAL 1 WEEK)) BETWEEN 3 AND 6) OR
                     (p.p_level = 1 AND DATEDIFF(CURDATE(), DATE_ADD(v.lastVisitTime, INTERVAL 1 MONTH)) BETWEEN 3 AND 6)
                THEN 1 ELSE 0
            END) AS day3to7Count,
            -- 超1-3天人数
            SUM(CASE
                WHEN (p.p_level = 3 AND DATEDIFF(CURDATE(), DATE_ADD(v.lastVisitTime, INTERVAL 1 DAY)) BETWEEN 1 AND 2) OR
                     (p.p_level = 2 AND DATEDIFF(CURDATE(), DATE_ADD(v.lastVisitTime, INTERVAL 1 WEEK)) BETWEEN 1 AND 2) OR
                     (p.p_level = 1 AND DATEDIFF(CURDATE(), DATE_ADD(v.lastVisitTime, INTERVAL 1 MONTH)) BETWEEN 1 AND 2)
                THEN 1 ELSE 0
            END) AS day1to3Count
        FROM
            t_instruction_person p
        LEFT JOIN (
            SELECT
                person_id,
                MAX(interview_time) AS lastVisitTime
            FROM
                t_person_interview
            WHERE
                status = '1'
                AND person_id IS NOT NULL
            GROUP BY person_id
        ) v ON p.id = v.person_id
        WHERE
            p.status = 1
            AND p.p_level IS NOT NULL
            AND p.p_level != 0
            -- 过滤掉加上间隔时间后大于当前时间的记录
            AND (
                v.lastVisitTime IS NULL OR
                (p.p_level = 3 AND DATE_ADD(v.lastVisitTime, INTERVAL 1 DAY) &lt;= CURDATE()) OR
                (p.p_level = 2 AND DATE_ADD(v.lastVisitTime, INTERVAL 1 WEEK) &lt;= CURDATE()) OR
                (p.p_level = 1 AND DATE_ADD(v.lastVisitTime, INTERVAL 1 MONTH) &lt;= CURDATE())
            )
        GROUP BY p.duty_place
        ORDER BY p.duty_place ASC
    </select>

    <!-- 查询人员走访记录按等级分组统计 -->
    <select id="getPersonVisitStatisticsByLevel" resultType="java.util.Map">
        SELECT
            p.p_level AS pLevel,
            CASE
                WHEN p.p_level = 1 THEN '黄色等级'
                WHEN p.p_level = 2 THEN '橙色等级'
                WHEN p.p_level = 3 THEN '红色等级'
                ELSE '未知等级'
            END AS levelName,
            COUNT(*) AS totalCount
        FROM
            t_instruction_person p
        LEFT JOIN (
            SELECT
                person_id,
                MAX(interview_time) AS lastVisitTime
            FROM
                t_person_interview
            WHERE
                status = '1'
                AND person_id IS NOT NULL
            GROUP BY person_id
        ) v ON p.id = v.person_id
        WHERE
            p.status = 1
            AND p.p_level IS NOT NULL
            AND p.p_level != 0
            -- 过滤掉加上间隔时间后大于当前时间的记录
            AND (
                v.lastVisitTime IS NULL OR
                (p.p_level = 3 AND DATE_ADD(v.lastVisitTime, INTERVAL 1 DAY) &lt;= CURDATE()) OR
                (p.p_level = 2 AND DATE_ADD(v.lastVisitTime, INTERVAL 1 WEEK) &lt;= CURDATE()) OR
                (p.p_level = 1 AND DATE_ADD(v.lastVisitTime, INTERVAL 1 MONTH) &lt;= CURDATE())
            )
            <if test="timeRange != null and timeRange != ''">
                <choose>
                    <when test="timeRange == 'month30'">
                        AND (
                            v.lastVisitTime IS NULL OR
                            (p.p_level = 3 AND DATEDIFF(CURDATE(), DATE_ADD(v.lastVisitTime, INTERVAL 1 DAY)) > 30) OR
                            (p.p_level = 2 AND DATEDIFF(CURDATE(), DATE_ADD(v.lastVisitTime, INTERVAL 1 WEEK)) > 30) OR
                            (p.p_level = 1 AND DATEDIFF(CURDATE(), DATE_ADD(v.lastVisitTime, INTERVAL 1 MONTH)) > 30)
                        )
                    </when>
                    <when test="timeRange == 'day15to30'">
                        AND (
                            (p.p_level = 3 AND DATEDIFF(CURDATE(), DATE_ADD(v.lastVisitTime, INTERVAL 1 DAY)) BETWEEN 15 AND 30) OR
                            (p.p_level = 2 AND DATEDIFF(CURDATE(), DATE_ADD(v.lastVisitTime, INTERVAL 1 WEEK)) BETWEEN 15 AND 30) OR
                            (p.p_level = 1 AND DATEDIFF(CURDATE(), DATE_ADD(v.lastVisitTime, INTERVAL 1 MONTH)) BETWEEN 15 AND 30)
                        )
                    </when>
                    <when test="timeRange == 'day7to15'">
                        AND (
                            (p.p_level = 3 AND DATEDIFF(CURDATE(), DATE_ADD(v.lastVisitTime, INTERVAL 1 DAY)) BETWEEN 7 AND 14) OR
                            (p.p_level = 2 AND DATEDIFF(CURDATE(), DATE_ADD(v.lastVisitTime, INTERVAL 1 WEEK)) BETWEEN 7 AND 14) OR
                            (p.p_level = 1 AND DATEDIFF(CURDATE(), DATE_ADD(v.lastVisitTime, INTERVAL 1 MONTH)) BETWEEN 7 AND 14)
                        )
                    </when>
                    <when test="timeRange == 'day3to7'">
                        AND (
                            (p.p_level = 3 AND DATEDIFF(CURDATE(), DATE_ADD(v.lastVisitTime, INTERVAL 1 DAY)) BETWEEN 3 AND 6) OR
                            (p.p_level = 2 AND DATEDIFF(CURDATE(), DATE_ADD(v.lastVisitTime, INTERVAL 1 WEEK)) BETWEEN 3 AND 6) OR
                            (p.p_level = 1 AND DATEDIFF(CURDATE(), DATE_ADD(v.lastVisitTime, INTERVAL 1 MONTH)) BETWEEN 3 AND 6)
                        )
                    </when>
                    <when test="timeRange == 'day1to3'">
                        AND (
                            (p.p_level = 3 AND DATEDIFF(CURDATE(), DATE_ADD(v.lastVisitTime, INTERVAL 1 DAY)) BETWEEN 1 AND 2) OR
                            (p.p_level = 2 AND DATEDIFF(CURDATE(), DATE_ADD(v.lastVisitTime, INTERVAL 1 WEEK)) BETWEEN 1 AND 2) OR
                            (p.p_level = 1 AND DATEDIFF(CURDATE(), DATE_ADD(v.lastVisitTime, INTERVAL 1 MONTH)) BETWEEN 1 AND 2)
                        )
                    </when>
                </choose>
            </if>
        GROUP BY p.p_level
        ORDER BY p.p_level DESC
    </select>

    <!-- 查询近N天所有person_ids -->
    <select id="getPersonIdsConcat" resultType="java.lang.String">
        SELECT GROUP_CONCAT(person_ids)
        FROM t_instruction_event
        WHERE STATUS = 1
        AND create_time &gt; #{startTime}
        AND person_ids IS NOT NULL
        AND person_ids != ''
    </select>

    <!-- 根据ID列表查询人员信息 -->
    <select id="getPersonInfoByIds" resultType="java.util.Map">
        SELECT
            id,
            person_name,
            duty_place,
            p_town,
            IFNULL(group_name, '') as group_name
        FROM t_instruction_person
        WHERE status = 1
        AND id IN
        <foreach collection="personIds" item="personId" open="(" separator="," close=")">
            #{personId}
        </foreach>
    </select>

    <select id="getContradictoryRisk" resultMap="InstructionInfoResult">
        SELECT
        a.*,
        b.receive_dept AS receive_unit
        FROM
        (
        SELECT *
        FROM t_instruction_info
        <where>
            STATUS = 1 AND instruction_type = 6
            <if test="params.beginTime != null and params.beginTime != ''">
                <![CDATA[
                    AND date_format(push_time, '%Y%m%d') >= date_format(#{params.beginTime}, '%Y%m%d')
                ]]>
            </if>
            <if test="params.endTime != null and params.endTime != ''">
                <![CDATA[
                    AND date_format(push_time, '%Y%m%d') <= date_format(#{params.endTime}, '%Y%m%d')
                ]]>
            </if>
        </where>
        ) a
        INNER JOIN (
        SELECT receive_dept, instrucation_id
        FROM t_instruction_receive
        WHERE STATUS = 1
        ) b ON a.id = b.instrucation_id
    </select>
    <select id="getCityIssuedStatistics" resultType="java.util.Map">
        SELECT
            COUNT(DISTINCT CASE WHEN b.receive_dept_id IS NOT NULL THEN a.id END) AS assigned_count,
            COUNT(DISTINCT CASE WHEN b.receive_time IS NOT NULL AND b.instrucation_id IS NOT NULL THEN a.id END) AS received_count,
            COUNT(DISTINCT CASE WHEN b.receive_time IS NULL AND b.instrucation_id IS NOT NULL THEN a.id END) AS not_received_count,
            SUM(CASE WHEN b.receive_dept_id IS NOT NULL AND a.isEnd = 1 THEN 1 ELSE 0 END) AS completed_count,
            SUM(CASE WHEN b.receive_dept_id IS NOT NULL AND a.isEnd = 2 THEN 1 ELSE 0 END) AS not_completed_count
        FROM
            (SELECT id, IFNULL(instrucation_is_end, 2) AS isEnd
             FROM t_instruction_info
             WHERE STATUS = 1 AND instruction_type IN (3, 6) AND create_dept_id = 202
                <if test="startTime != null and startTime != ''">
                    AND assign_time &gt;= #{startTime}
                </if>
                <if test="endTime != null and endTime != ''">
                    AND assign_time &lt;= #{endTime}
                </if>
                ) a
                LEFT JOIN
            (SELECT instrucation_id, receive_time, receive_dept_id
             FROM t_instruction_receive
             WHERE STATUS = 1 AND receive_dept_id = #{deptId}) b
            ON a.id = b.instrucation_id
    </select>
    <select id="getCountyIssuedStatistics" resultType="java.util.Map">
        SELECT
            '县级自主上报指令数' AS metric,
            COUNT(*) AS value
        FROM (
            SELECT id, IFNULL(instrucation_is_end, 2) AS is_end
            FROM t_instruction_info
            WHERE STATUS = 1
            AND instruction_type IN (3, 6)
            <if test="startTime != null and startTime != ''">
                AND assign_time &gt;= #{startTime}
            </if>
            <if test="endTime != null and endTime != ''">
                AND assign_time &lt;= #{endTime}
            </if>
            AND create_dept_id = #{deptId}
            ) AS instruction_base

        UNION ALL

        SELECT
            '部门总数' AS metric,
            COUNT(DISTINCT receive_dept_id) AS value
        FROM t_instruction_receive
        WHERE STATUS = 1
        <if test="deptIds != null and deptIds.size() >0">
            AND receive_dept_id  IN
            <foreach item="id" collection="deptIds" open="(" separator="," close=")">
                #{id}
            </foreach>
        </if>
          AND instrucation_id IN (
            SELECT id
            FROM t_instruction_info
            WHERE STATUS = 1
          AND instruction_type IN (3, 6)
        <if test="startTime != null and startTime != ''">
            AND assign_time &gt;= #{startTime}
        </if>
        <if test="endTime != null and endTime != ''">
            AND assign_time &lt;= #{endTime}
        </if>
          AND create_dept_id = #{deptId}
            )

        UNION ALL

        SELECT
            CONCAT('部门', receive_status) AS metric,
            instruction_count AS value
        FROM (
            SELECT
            CASE WHEN b.receive_time IS NULL THEN '未接收' ELSE '已接收' END AS receive_status,
            COUNT(DISTINCT a.id) AS instruction_count
            FROM (
            SELECT id
            FROM t_instruction_info
            WHERE STATUS = 1
            AND instruction_type IN (3, 6)
        <if test="startTime != null and startTime != ''">
            AND assign_time &gt;= #{startTime}
        </if>
        <if test="endTime != null and endTime != ''">
            AND assign_time &lt;= #{endTime}
        </if>
            AND create_dept_id = #{deptId}
            ) a
            JOIN t_instruction_receive b ON a.id = b.instrucation_id
            WHERE b.STATUS = 1
            <if test="deptIds != null and deptIds.size() >0">
                AND b.receive_dept_id  IN
                <foreach item="id" collection="deptIds" open="(" separator="," close=")">
                    #{id}
                </foreach>
            </if>

            GROUP BY CASE WHEN b.receive_time IS NULL THEN '未接收' ELSE '已接收' END
            ) t

        UNION ALL

        SELECT
            CONCAT('街道', receive_status) AS metric,
            instruction_count AS value
        FROM (
            SELECT
            CASE WHEN b.receive_time IS NULL THEN '未接收' ELSE '已接收' END AS receive_status,
            COUNT(DISTINCT a.id) AS instruction_count
            FROM (
            SELECT id
            FROM t_instruction_info
            WHERE STATUS = 1
            AND instruction_type IN (3, 6)
        <if test="startTime != null and startTime != ''">
            AND assign_time &gt;= #{startTime}
        </if>
        <if test="endTime != null and endTime != ''">
            AND assign_time &lt;= #{endTime}
        </if>
            AND create_dept_id = #{deptId}
            ) a
            JOIN t_instruction_receive b ON a.id = b.instrucation_id
            WHERE b.STATUS = 1
            <if test="deptIds != null and deptIds.size() >0">
                AND b.receive_dept_id NOT IN
                <foreach item="id" collection="deptIds" open="(" separator="," close=")">
                    #{id}
                </foreach>
            </if>
            GROUP BY CASE WHEN b.receive_time IS NULL THEN '未接收' ELSE '已接收' END
            ) t

        UNION ALL

        SELECT
            CASE WHEN IFNULL(instrucation_is_end, 2) = 1 THEN '已销号' ELSE '未销号' END AS metric,
            COUNT(*) AS value
        FROM t_instruction_info
        WHERE STATUS = 1
          AND instruction_type IN (3, 6)
        <if test="startTime != null and startTime != ''">
            AND assign_time &gt;= #{startTime}
        </if>
        <if test="endTime != null and endTime != ''">
            AND assign_time &lt;= #{endTime}
        </if>
          AND create_dept_id = #{deptId}
        GROUP BY CASE WHEN IFNULL(instrucation_is_end, 2) = 1 THEN '已销号' ELSE '未销号' END;
    </select>
    <select id="getEmergencyDegreeStatistics" resultType="java.util.Map">
        SELECT
            a.emergency_degree,
            IFNULL(b.acount,0) acount
        FROM
            ( SELECT DISTINCT emergency_degree FROM t_instruction_info WHERE STATUS AND emergency_degree IS NOT NULL ) a
                LEFT JOIN (
                SELECT
                    emergency_degree,
                    count(*) acount
                FROM
                    t_instruction_info
                WHERE
                    STATUS = 1
                  AND instruction_type IN ( 3, 6 )
                <if test="startTime != null and startTime != ''">
                    AND assign_time &gt;= #{startTime}
                </if>
                <if test="endTime != null and endTime != ''">
                    AND assign_time &lt;= #{endTime}
                </if>
                  AND create_dept_id = 202
                  AND receive_unit like concat('%', #{deptName}, '%')
                GROUP BY
                    emergency_degree
            ) b ON a.emergency_degree = b.emergency_degree

    </select>

</mapper>
