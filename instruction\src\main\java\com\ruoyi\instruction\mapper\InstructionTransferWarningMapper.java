package com.ruoyi.instruction.mapper;

import java.util.List;
import com.ruoyi.instruction.domain.InstructionTransferWarning;
import io.lettuce.core.dynamic.annotation.Param;

/**
 * 指令移交预警 Mapper接口
 *
 * <AUTHOR>
 * @date 2025/07/22
 */
public interface InstructionTransferWarningMapper {
    /**
     * 根据ID查询详情
     */
    public InstructionTransferWarning selectWarningById(Long id);

    /**
     * 查询列表
     */
    public List<InstructionTransferWarning> selectWarningList(InstructionTransferWarning warning);

    /**
     * 新增
     */
    public int insertWarning(InstructionTransferWarning warning);

    /**
     * 修改
     */
    public int updateWarning(InstructionTransferWarning warning);

    /**
     * 批量删除
     */
    public int deleteWarningByIds(Long[] ids);

    /**
     * 将 t_instruction_transfer_warning 表中指定 instructionId 的记录，其 instructionId 字段设置为null。
     *
     * @param instructionId 要被置空的指令ID
     * @return 影响的行数
     */
    int setInstructionIdToNull(@Param("instructionId") Long instructionId);
}