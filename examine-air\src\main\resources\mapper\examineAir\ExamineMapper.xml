<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.examineAir.mapper.ExamineMapper">

    <resultMap type="Examine" id="ExamineResult">
            <result property="id" column="id"/>
            <result property="cTime" column="c_time"/>
            <result property="uTime" column="u_time"/>
            <result property="title" column="title"/>
            <result property="type" column="type"/>
            <result property="releaseTime" column="release_time"/>
            <result property="status" column="status"/>
            <result property="panan" column="panan"/>
            <result property="wuyi" column="wuyi"/>
            <result property="dongyang" column="dongyang"/>
            <result property="yongkang" column="yongkang"/>
            <result property="pujiang" column="pujiang"/>
            <result property="yiwu" column="yiwu"/>
            <result property="lanxi" column="lanxi"/>
            <result property="jindong" column="jindong"/>
            <result property="wucheng" column="wucheng"/>
            <result property="kaifa" column="kaifa"/>
            <result property="headContent" column="head_content"/>
    </resultMap>

    <sql id="selectExamineVo">
        select id, c_time, u_time, title, type, release_time, status, panan, wuyi, dongyang, yongkang, pujiang, yiwu, lanxi, jindong, wucheng, kaifa,head_content
        from t_examine
    </sql>

    <select id="selectExamineList" parameterType="Examine" resultMap="ExamineResult">
        <include refid="selectExamineVo"/>
        <where>
            <if test="cTime != null ">and c_time = #{cTime}</if>
            <if test="uTime != null ">and u_time = #{uTime}</if>
            <if test="title != null  and title != '' and isLike==null">and title  like concat('%', #{title}, '%')</if>
            <if test="title != null  and title != '' and isLike!=null">and title = #{title}</if>
            <if test="type != null ">and type = #{type}</if>
            <if test="releaseTime != null ">and release_time = #{releaseTime}</if>
            <if test="status != null ">and status = #{status}</if>
            <if test="panan != null ">and panan = #{panan}</if>
            <if test="wuyi != null ">and wuyi = #{wuyi}</if>
            <if test="dongyang != null ">and dongyang = #{dongyang}</if>
            <if test="yongkang != null ">and yongkang = #{yongkang}</if>
            <if test="pujiang != null ">and pujiang = #{pujiang}</if>
            <if test="yiwu != null ">and yiwu = #{yiwu}</if>
            <if test="lanxi != null ">and lanxi = #{lanxi}</if>
            <if test="jindong != null ">and jindong = #{jindong}</if>
            <if test="wucheng != null ">and wucheng = #{wucheng}</if>
            <if test="kaifa != null ">and kaifa = #{kaifa}</if>
            <if test="headContent != null  and headContent != ''">and head_content = #{headContent}</if>
            <if test="startTime != null  and endTime != null">and c_time  between #{startTime} and #{endTime}</if>
        </where>
    </select>

    <select id="selectExamineById" parameterType="Long"
            resultMap="ExamineResult">
            <include refid="selectExamineVo"/>
            where   id = #{id}
    </select>

    <insert id="insertExamine" parameterType="Examine" useGeneratedKeys="true" keyProperty="id">
        insert into t_examine
        <trim prefix="(" suffix=")" suffixOverrides=",">
                    <if test="id != null">id,</if>
                    <if test="cTime != null">c_time,</if>
                    <if test="uTime != null">u_time,</if>
                    <if test="title != null">title,</if>
                    <if test="type != null">type,</if>
                    <if test="releaseTime != null">release_time,</if>
                    <if test="status != null">status,</if>
                    <if test="panan != null">panan,</if>
                    <if test="wuyi != null">wuyi,</if>
                    <if test="dongyang != null">dongyang,</if>
                    <if test="yongkang != null">yongkang,</if>
                    <if test="pujiang != null">pujiang,</if>
                    <if test="yiwu != null">yiwu,</if>
                    <if test="lanxi != null">lanxi,</if>
                    <if test="jindong != null">jindong,</if>
                    <if test="wucheng != null">wucheng,</if>
                    <if test="kaifa != null">kaifa,</if>
                    <if test="headContent != null">head_content,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
                    <if test="id != null">#{id},</if>
                    <if test="cTime != null">#{cTime},</if>
                    <if test="uTime != null">#{uTime},</if>
                    <if test="title != null">#{title},</if>
                    <if test="type != null">#{type},</if>
                    <if test="releaseTime != null">#{releaseTime},</if>
                    <if test="status != null">#{status},</if>
                    <if test="panan != null">#{panan},</if>
                    <if test="wuyi != null">#{wuyi},</if>
                    <if test="dongyang != null">#{dongyang},</if>
                    <if test="yongkang != null">#{yongkang},</if>
                    <if test="pujiang != null">#{pujiang},</if>
                    <if test="yiwu != null">#{yiwu},</if>
                    <if test="lanxi != null">#{lanxi},</if>
                    <if test="jindong != null">#{jindong},</if>
                    <if test="wucheng != null">#{wucheng},</if>
                    <if test="kaifa != null">#{kaifa},</if>
                    <if test="headContent != null">#{headContent},</if>
        </trim>
    </insert>

    <update id="updateExamine" parameterType="Examine">
        update t_examine
        <trim prefix="SET" suffixOverrides=",">
                    <if test="cTime != null">c_time = #{cTime},</if>
                    <if test="uTime != null">u_time = #{uTime},</if>
                    <if test="title != null">title = #{title},</if>
                    <if test="type != null">type = #{type},</if>
                    <if test="releaseTime != null">release_time = #{releaseTime},</if>
                    <if test="status != null">status = #{status},</if>
                    <if test="panan != null">panan = #{panan},</if>
                    <if test="wuyi != null">wuyi = #{wuyi},</if>
                    <if test="dongyang != null">dongyang = #{dongyang},</if>
                    <if test="yongkang != null">yongkang = #{yongkang},</if>
                    <if test="pujiang != null">pujiang = #{pujiang},</if>
                    <if test="yiwu != null">yiwu = #{yiwu},</if>
                    <if test="lanxi != null">lanxi = #{lanxi},</if>
                    <if test="jindong != null">jindong = #{jindong},</if>
                    <if test="wucheng != null">wucheng = #{wucheng},</if>
                    <if test="kaifa != null">kaifa = #{kaifa},</if>
                    <if test="headContent != null">head_content = #{headContent},</if>
        </trim>
        where id = #{id}
    </update>

    <update id="deleteExamineById" parameterType="Long">
        delete from t_examine
        where id = #{id}
    </update>

    <update id="deleteExamineByIds" parameterType="String">
        delete from t_examine  where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>

    <select id="getNewAreaCount" resultType="com.ruoyi.examineAir.vo.BigScreenPageRegionStatisticsVo">
        SELECT score2 dutyPlace, 0 + CAST(total_score AS CHAR)  count from t_examine_details where t_examine_id =(SELECT id from t_examine where status=1 order by release_time desc LIMIT 1)
    </select>

    <select id="getNEW" resultMap="ExamineResult">
        SELECT * from t_examine where `status`=1 order by release_time desc LIMIT 1
    </select>
</mapper>