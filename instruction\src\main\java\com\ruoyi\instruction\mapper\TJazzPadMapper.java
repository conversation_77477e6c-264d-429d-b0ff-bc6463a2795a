package com.ruoyi.instruction.mapper;

import java.util.List;
import java.util.Map;

import com.ruoyi.instruction.domain.unqualifiedPadResVo;
import org.apache.ibatis.annotations.MapKey;
import org.apache.ibatis.annotations.Mapper;
import com.ruoyi.instruction.domain.TJazzPad;
import org.apache.ibatis.annotations.Param;

/**
 * 金安智治-平安金华-平安鼎Mapper接口
 *
 * <AUTHOR>
 * @date 2023-07-11
 */
@Mapper
public interface TJazzPadMapper
{
    /**
     * 查询金安智治-平安金华-平安鼎
     *
     * @param id 金安智治-平安金华-平安鼎ID
     * @return 金安智治-平安金华-平安鼎
     */
    public TJazzPad selectTJazzPadById(Long id);

    /**
     * 查询金安智治-平安金华-平安鼎列表
     *
     * @param tJazzPad 金安智治-平安金华-平安鼎
     * @return 金安智治-平安金华-平安鼎集合
     */
    public List<TJazzPad> selectTJazzPadList(TJazzPad tJazzPad);

    public List<unqualifiedPadResVo> selectTJazzunPadList(TJazzPad tJazzPad);

    /**
     * 新增金安智治-平安金华-平安鼎
     *
     * @param tJazzPad 金安智治-平安金华-平安鼎
     * @return 结果
     */
    public int insertTJazzPad(TJazzPad tJazzPad);

    /**
     * 修改金安智治-平安金华-平安鼎
     *
     * @param tJazzPad 金安智治-平安金华-平安鼎
     * @return 结果
     */
    public int updateTJazzPad(TJazzPad tJazzPad);

    /**
     * 删除金安智治-平安金华-平安鼎
     *
     * @param id 金安智治-平安金华-平安鼎ID
     * @return 结果
     */
    public int deleteTJazzPadById(Long id);

    /**
     * 批量删除金安智治-平安金华-平安鼎
     *
     * @param ids 需要删除的数据ID
     * @return 结果
     */
    public int deleteTJazzPadByIds(Long[] ids);

    /**
     * 获取各县市最新的一条平安鼎的数据
     * @return
     */
    List<TJazzPad> getPadList();

    @MapKey("id")
    List<TJazzPad> selectTJazzPadListByYear(TJazzPad tJazzPad);

    @MapKey("id")
    List<TJazzPad> selectTJazzPadListByQx(TJazzPad tJazzPad);

    String selectTJazzPadByYear(@Param("year") String year, @Param("area") String area);

    List<Map> selectCurrentPadListByYear(String condition);

    String selectCurrentPadListByQx(@Param("condition") String condition,@Param("year") String year);

    List<TJazzPad> selectExitTJazz(@Param("year")String year,@Param("area")String area);

    List<TJazzPad> selectUnqualifiedTJazzPadList(TJazzPad tJazzPad);
}
