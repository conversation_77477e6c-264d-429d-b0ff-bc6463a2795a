package com.ruoyi.instruction.controller;

import java.util.Arrays;
import java.util.List;
import java.util.Map;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.instruction.domain.RemindRecord;
import com.ruoyi.instruction.service.IRemindRecordService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 工作通知记录Controller
 *
 * <AUTHOR>
 * @date 2024-04-10
 */
@RestController
@RequestMapping("/remind/record")
public class RemindRecordController extends BaseController
{
    @Autowired
    private IRemindRecordService remindRecordService;

    /**
     * 查询工作通知记录列表
     */
    @PreAuthorize("@ss.hasPermi('remind:record:list')")
    @GetMapping("/list")
    public TableDataInfo list(RemindRecord remindRecord)
    {
        if (remindRecord.getRemindUnit() != null && !remindRecord.getRemindUnit().isEmpty()) {
            List<String> split = Arrays.asList(remindRecord.getRemindUnit().split(","));
            Map<String, Object> params = remindRecord.getParams();
            params.put("remindUnits",split);
            remindRecord.setParams(params);
        }
        startPage();
        List<RemindRecord> list = remindRecordService.findRemindRecordList(remindRecord);
        return getDataTable(list);
    }

    /**
     * 导出工作通知记录列表
     */
    @PreAuthorize("@ss.hasPermi('remind:record:export')")
    @Log(title = "工作通知记录", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, RemindRecord remindRecord)
    {
        List<RemindRecord> list = remindRecordService.selectRemindRecordList(remindRecord);
        ExcelUtil<RemindRecord> util = new ExcelUtil<RemindRecord>(RemindRecord.class);
        util.exportExcel(response, list, "工作通知记录数据");
    }

    /**
     * 获取工作通知记录详细信息
     */
    @PreAuthorize("@ss.hasPermi('remind:record:query')")
    @GetMapping(value = "/getInfo")
    public AjaxResult getInfo(RemindRecord remindRecord)
    {
        return success(remindRecordService.selectRemindRecordList(remindRecord));
    }

    /**
     * 新增工作通知记录
     */
    @PreAuthorize("@ss.hasPermi('remind:record:add')")
    @Log(title = "工作通知记录", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody RemindRecord remindRecord)
    {
        return toAjax(remindRecordService.insertRemindRecord(remindRecord));
    }

    /**
     * 修改工作通知记录
     */
    @PreAuthorize("@ss.hasPermi('remind:record:edit')")
    @Log(title = "工作通知记录", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody RemindRecord remindRecord)
    {
        return toAjax(remindRecordService.updateRemindRecord(remindRecord));
    }

    /**
     * 删除工作通知记录
     */
    @PreAuthorize("@ss.hasPermi('remind:record:remove')")
    @Log(title = "工作通知记录", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(remindRecordService.deleteRemindRecordByIds(ids));
    }
}
