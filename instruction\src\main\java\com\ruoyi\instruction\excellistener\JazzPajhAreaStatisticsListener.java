package com.ruoyi.instruction.excellistener;

import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.enums.CellExtraTypeEnum;
import com.alibaba.excel.event.AnalysisEventListener;
import com.alibaba.excel.metadata.CellExtra;
import com.ruoyi.instruction.domain.JazzPajhAreaStatistics;
import com.ruoyi.instruction.domain.rspVo.EventPersonRspVo;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/2/9 14:28
 */
public class JazzPajhAreaStatisticsListener extends AnalysisEventListener<JazzPajhAreaStatistics> {

    /**
     * 正文起始行
     */
    private Integer headRowNumber;

    public List<CellExtra> extraMergeInfoList = new ArrayList<>();

    public JazzPajhAreaStatisticsListener(int headRowNum) {
        this.headRowNumber = headRowNum;
    }

    List<JazzPajhAreaStatistics> personList = new ArrayList<>();

    @Override
    public void invoke(final JazzPajhAreaStatistics person, final AnalysisContext analysisContext) {
        personList.add(person);
    }

    @Override
    public void doAfterAllAnalysed(final AnalysisContext analysisContext) {

    }

    public List<JazzPajhAreaStatistics> getDataList() {
        return personList;
    }

    @Override
    public void extra(CellExtra extra, AnalysisContext context) {
        CellExtraTypeEnum type = extra.getType();
        switch (type) {
            case MERGE: {
                if (extra.getRowIndex() >= headRowNumber) {
                    extraMergeInfoList.add(extra);
                }
                break;
            }
            default: {
            }
        }
    }
}
