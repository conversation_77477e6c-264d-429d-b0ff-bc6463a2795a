package com.ruoyi.instruction.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.instruction.domain.Risk;
import com.ruoyi.instruction.service.IRiskService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 风险排查主Controller
 * 
 * <AUTHOR>
 * @date 2023-12-25
 */
@RestController
@RequestMapping("/system/risk")
public class RiskController extends BaseController
{
    @Autowired
    private IRiskService riskService;

    /**
     * 查询风险排查主列表
     */
    @PreAuthorize("@ss.hasPermi('system:risk:list')")
    @GetMapping("/list")
    public TableDataInfo list(Risk risk)
    {
        startPage();
        List<Risk> list = riskService.selectRiskList(risk);
        return getDataTable(list);
    }

    /**
     * 导出风险排查主列表
     */
    @PreAuthorize("@ss.hasPermi('system:risk:export')")
    @Log(title = "风险排查主", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, Risk risk)
    {
        List<Risk> list = riskService.selectRiskList(risk);
        ExcelUtil<Risk> util = new ExcelUtil<Risk>(Risk.class);
        util.exportExcel(response, list, "风险排查主数据");
    }

    /**
     * 获取风险排查主详细信息
     */
    @PreAuthorize("@ss.hasPermi('system:risk:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(riskService.selectRiskById(id));
    }

    /**
     * 新增风险排查主
     */
    @PreAuthorize("@ss.hasPermi('system:risk:add')")
    @Log(title = "风险排查主", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody Risk risk)
    {
        return toAjax(riskService.insertRisk(risk));
    }

    /**
     * 修改风险排查主
     */
    @PreAuthorize("@ss.hasPermi('system:risk:edit')")
    @Log(title = "风险排查主", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody Risk risk)
    {
        return toAjax(riskService.updateRisk(risk));
    }

    /**
     * 删除风险排查主
     */
    @PreAuthorize("@ss.hasPermi('system:risk:remove')")
    @Log(title = "风险排查主", businessType = BusinessType.DELETE)
	@DeleteMapping("/{id}")
    public AjaxResult remove(@PathVariable Long id)
    {
        return toAjax(riskService.deleteRiskById(id));
    }
}
