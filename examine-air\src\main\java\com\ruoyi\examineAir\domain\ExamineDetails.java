package com.ruoyi.examineAir.domain;

import java.math.BigDecimal;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 考核晾晒详情对象 t_examine_details
 * 
 * <AUTHOR>
 * @date 2023-04-19
 */
public class ExamineDetails extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** $column.columnComment */
    private Long id;

    /** 区县 */
    @Excel(name = "区县")
    private String area;

    /** 总分 */
    @Excel(name = "总分")
    private BigDecimal totalScore;

    /** 考核列表id */
    @Excel(name = "考核列表id")
    private Long tExamineId;

    /** $column.columnComment */
    @Excel(name = "${comment}", readConverterExp = "$column.readConverterExp()")
    private Integer score1;

    /** $column.columnComment */
    @Excel(name = "${comment}", readConverterExp = "$column.readConverterExp()")
    private String score2;

    /** $column.columnComment */
    @Excel(name = "${comment}", readConverterExp = "$column.readConverterExp()")
    private BigDecimal score3;

    /** $column.columnComment */
    @Excel(name = "${comment}", readConverterExp = "$column.readConverterExp()")
    private BigDecimal score4;

    /** $column.columnComment */
    @Excel(name = "${comment}", readConverterExp = "$column.readConverterExp()")
    private BigDecimal score5;

    /** $column.columnComment */
    @Excel(name = "${comment}", readConverterExp = "$column.readConverterExp()")
    private BigDecimal score6;

    /** $column.columnComment */
    @Excel(name = "${comment}", readConverterExp = "$column.readConverterExp()")
    private BigDecimal score7;

    /** $column.columnComment */
    @Excel(name = "${comment}", readConverterExp = "$column.readConverterExp()")
    private BigDecimal score8;

    /** $column.columnComment */
    @Excel(name = "${comment}", readConverterExp = "$column.readConverterExp()")
    private BigDecimal score9;

    /** $column.columnComment */
    @Excel(name = "${comment}", readConverterExp = "$column.readConverterExp()")
    private BigDecimal score10;

    /** $column.columnComment */
    @Excel(name = "${comment}", readConverterExp = "$column.readConverterExp()")
    private BigDecimal score11;

    /** $column.columnComment */
    @Excel(name = "${comment}", readConverterExp = "$column.readConverterExp()")
    private BigDecimal score12;

    /** $column.columnComment */
    @Excel(name = "${comment}", readConverterExp = "$column.readConverterExp()")
    private BigDecimal score13;

    /** $column.columnComment */
    @Excel(name = "${comment}", readConverterExp = "$column.readConverterExp()")
    private BigDecimal score14;

    /** $column.columnComment */
    @Excel(name = "${comment}", readConverterExp = "$column.readConverterExp()")
    private BigDecimal score15;

    public void setId(Long id) 
    {
        this.id = id;
    }

    public Long getId() 
    {
        return id;
    }
    public void setArea(String area) 
    {
        this.area = area;
    }

    public String getArea() 
    {
        return area;
    }
    public void setTotalScore(BigDecimal totalScore) 
    {
        this.totalScore = totalScore;
    }

    public BigDecimal getTotalScore() 
    {
        return totalScore;
    }
    public void settExamineId(Long tExamineId) 
    {
        this.tExamineId = tExamineId;
    }

    public Long gettExamineId() 
    {
        return tExamineId;
    }

    public Integer getScore1() {
        return score1;
    }

    public void setScore1(Integer score1) {
        this.score1 = score1;
    }

    public String getScore2() {
        return score2;
    }

    public void setScore2(String score2) {
        this.score2 = score2;
    }

    public void setScore3(BigDecimal score3)
    {
        this.score3 = score3;
    }

    public BigDecimal getScore3() 
    {
        return score3;
    }
    public void setScore4(BigDecimal score4) 
    {
        this.score4 = score4;
    }

    public BigDecimal getScore4() 
    {
        return score4;
    }
    public void setScore5(BigDecimal score5) 
    {
        this.score5 = score5;
    }

    public BigDecimal getScore5() 
    {
        return score5;
    }
    public void setScore6(BigDecimal score6) 
    {
        this.score6 = score6;
    }

    public BigDecimal getScore6() 
    {
        return score6;
    }
    public void setScore7(BigDecimal score7) 
    {
        this.score7 = score7;
    }

    public BigDecimal getScore7() 
    {
        return score7;
    }
    public void setScore8(BigDecimal score8) 
    {
        this.score8 = score8;
    }

    public BigDecimal getScore8() 
    {
        return score8;
    }
    public void setScore9(BigDecimal score9) 
    {
        this.score9 = score9;
    }

    public BigDecimal getScore9() 
    {
        return score9;
    }
    public void setScore10(BigDecimal score10) 
    {
        this.score10 = score10;
    }

    public BigDecimal getScore10() 
    {
        return score10;
    }
    public void setScore11(BigDecimal score11) 
    {
        this.score11 = score11;
    }

    public BigDecimal getScore11() 
    {
        return score11;
    }
    public void setScore12(BigDecimal score12) 
    {
        this.score12 = score12;
    }

    public BigDecimal getScore12() 
    {
        return score12;
    }
    public void setScore13(BigDecimal score13) 
    {
        this.score13 = score13;
    }

    public BigDecimal getScore13() 
    {
        return score13;
    }
    public void setScore14(BigDecimal score14) 
    {
        this.score14 = score14;
    }

    public BigDecimal getScore14() 
    {
        return score14;
    }
    public void setScore15(BigDecimal score15) 
    {
        this.score15 = score15;
    }

    public BigDecimal getScore15() 
    {
        return score15;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("area", getArea())
            .append("totalScore", getTotalScore())
            .append("tExamineId", gettExamineId())
            .append("score1", getScore1())
            .append("score2", getScore2())
            .append("score3", getScore3())
            .append("score4", getScore4())
            .append("score5", getScore5())
            .append("score6", getScore6())
            .append("score7", getScore7())
            .append("score8", getScore8())
            .append("score9", getScore9())
            .append("score10", getScore10())
            .append("score11", getScore11())
            .append("score12", getScore12())
            .append("score13", getScore13())
            .append("score14", getScore14())
            .append("score15", getScore15())
            .toString();
    }
}
