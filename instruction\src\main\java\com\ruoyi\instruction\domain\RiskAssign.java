package com.ruoyi.instruction.domain;

import java.util.Date;
import java.util.List;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 风险交办单页面对象 t_risk_assign
 *
 * <AUTHOR>
 * @date 2025-02-26
 */
@Data
public class RiskAssign extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键id */
    private Long id;

    /** 标题 */
    @Excel(name = "标题")
    private String title;

    /** 发文编号 */
    @Excel(name = "发文编号")
    private String number;

    /** 风险交办类型 */
    @Excel(name = "风险交办类型")
    private String assignType;

    /** 正文内容 */
    @Excel(name = "正文内容")
    private String mainBody;

    /** 接收单位 */

    private String receiveUnit;

    /**
     * 接收单位中文
     */
    @Excel(name = "接收单位")
    private String receiveUnitStr;

    /** 附件ids */
    private String fileIds;

    /** 是否需要反馈 1：需要反馈  2：无需反馈 */
    @Excel(name = "是否需要反馈",readConverterExp = "1=需要反馈,2=无需反馈",combo = {"需要反馈","无需反馈"})
    private Integer isFeedback;

    /** 反馈时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "反馈时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date feedbackTime;

    /** 联系人 */
    @Excel(name = "联系人")
    private String contactPerson;

    /** 联系电话 */
    @Excel(name = "联系电话")
    private String contactPhone;

    /** 状态1：正常  9：删除 */
    private String status;

    /** 当前状态 */
    @Excel(name = "当前状态")
    private String currentStatus;

    /** 发送时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "发送时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date sendTime;

    /** 反馈期限 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "反馈期限", width = 30, dateFormat = "yyyy-MM-dd")
    private Date deadline;

    /**
     * 查询类型
     */
    private Integer type;

    /**
     * 反馈列表
     */
    private List<RiskFeedback> riskFeedbackList;

    /**
     * 创建部门id
     */
    private Long createDeptId;

    /**
     * 操作栏类型
     * 交办角色
     * 状态    按钮              类型
     * 已完结  处理、修改、删除     1
     * 待反馈  处理、修改、删除     1
     * 待发送  发送、修改、删除     2
     *
     *
     * 接收角色
     * 待反馈    处理             3
     * 已反馈    处理             3
     * 无需反馈   				4
     */
    private Integer operateType;


}
