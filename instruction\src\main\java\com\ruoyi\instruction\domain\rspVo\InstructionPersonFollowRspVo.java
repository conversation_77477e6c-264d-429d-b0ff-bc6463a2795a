package com.ruoyi.instruction.domain.rspVo;

import com.ruoyi.common.annotation.Excel;
import lombok.Data;

/**
 * 关注人员导出模板
 * <AUTHOR>
 * @version 1.0
 * @date 2024/1/23 10:39
 */
@Data
public class InstructionPersonFollowRspVo {

    /** 人员姓名 */
    @Excel(name = "人员姓名(必填)")
    private String personName;

    /** 证件号码 */
    @Excel(name = "证件号码(必填)",type = Excel.Type.IMPORT)
    private String idCard;

    /** 户籍所在地 */
    @Excel(name = "户籍地址")
    private String housePlace;

    /** 当前居住地 */
    @Excel(name = "常住地址")
    private String currentPlace;

    /** 责任所在地 */
    // @Excel(name = "管控级别", combo = {"红,橙,黄"},readConverterExp = "1=黄,2=橙,3=黄")
    private String controlLevel;

    /** 电话号码 */
    @Excel(name = "电话号码")
    private String personPhone;

    @Excel(name = "属地(必填)",combo = {"婺城区","金东区","兰溪市","东阳市","义乌市","永康市","浦江县","武义县","磐安县","开发区"})
    private String dutyPlace;



    /** 人员所属乡镇 */
    @Excel(name = "责任乡镇")
    private String pTown;

    /** 一级标题 */
    private String firstTitle;

    /** 二级标题 */
    private String secondTitle;


    /** 用户性别0：男 1：女 2：未知 */
    @Excel(name = "性别",combo = "男,女")
    private String sex;


    /**
     * 乡镇街道
     */
    private String town;


    /**
     * 关联事件数
     */
    @Excel(name = "活跃次数", type = Excel.Type.EXPORT)
    private Integer eventNum;


    /** 管控策略1：五包一 */
    @Excel(name = "管控策略",combo = "五包一", readConverterExp = "1=五包一")
    private String controlStrategy;


    /** 人员类型  */
    @Excel(name = "人员类型",combo ="涉稳警情类,诉求群体类,个人极端类,煽动串联类,易肇事肇祸精神病")
    private String pType;


    /** 申请单位意见 */
    // @Excel(name = "单位意见(必填)")
    private String sqdwReason;

    /**
     * 人员管理状态，1在控，2失联
     */
    private  Integer manageStatus;

    /**
     * 人员最后一次类型名称
     */
    @Excel(name = "事件类型", type = Excel.Type.EXPORT)
    private String typeName;

    /**
     *挑头次数
     */
    @Excel(name = "挑头次数", type = Excel.Type.EXPORT)
    private Integer leaderNum;

    /**
     * 响应次数
     */
    @Excel(name = "响应次数", type = Excel.Type.EXPORT)
    private Integer respondNum;


    /** 人员来源 1:系统变更  2：新增 */
    private Long personSource;

    public String getpTown() {
        return pTown;
    }

    public void setpTown(final String pTown) {
        this.pTown = pTown;
    }

    public String getpType() {
        return pType;
    }

    public void setpType(final String pType) {
        this.pType = pType;
    }
}
