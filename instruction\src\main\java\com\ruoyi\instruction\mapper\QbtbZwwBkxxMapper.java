package com.ruoyi.instruction.mapper;

import java.util.List;

import com.ruoyi.common.annotation.DataSource;
import com.ruoyi.common.enums.DataSourceType;
import com.ruoyi.instruction.domain.QbtbZwwBkxx;

/**
 * 布控Mapper接口
 * 
 * <AUTHOR>
 * @date 2023-06-03
 */
public interface QbtbZwwBkxxMapper
{
    /**
     * 查询布控
     * 
     * @param nXh 布控主键
     * @return 布控
     */
    public QbtbZwwBkxx selectQbtbZwwBkxxByNXh(Long nXh);

    /**
     * 查询布控列表
     * 
     * @param qbtbZwwBkxx 布控
     * @return 布控集合
     */
    public List<QbtbZwwBkxx> selectQbtbZwwBkxxList(QbtbZwwBkxx qbtbZwwBkxx);

    /**
     * 新增布控
     * 
     * @param qbtbZwwBkxx 布控
     * @return 结果
     */
    public int insertQbtbZwwBkxx(QbtbZwwBkxx qbtbZwwBkxx);

    /**
     * 修改布控
     * 
     * @param qbtbZwwBkxx 布控
     * @return 结果
     */
    public int updateQbtbZwwBkxx(QbtbZwwBkxx qbtbZwwBkxx);

    /**
     * 删除布控
     * 
     * @param nXh 布控主键
     * @return 结果
     */
    public int deleteQbtbZwwBkxxByNXh(Long nXh);

    /**
     * 批量删除布控
     * 
     * @param nXhs 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteQbtbZwwBkxxByNXhs(Long[] nXhs);
}
