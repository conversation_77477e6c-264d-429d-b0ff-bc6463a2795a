package com.ruoyi.instruction.controller;

import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.examineAir.service.IExamineService;
import com.ruoyi.instruction.domain.InstrucationPerson;
import com.ruoyi.instruction.domain.InstructionEcologicalEnv;
import com.ruoyi.instruction.domain.InstructionEvent;
import com.ruoyi.instruction.domain.InstructionTrend;
import com.ruoyi.instruction.domain.rspVo.BigScreenJazzCommonVo;
import com.ruoyi.instruction.mapper.InstructionJazzWwEventMapper;
import com.ruoyi.instruction.service.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.time.LocalDate;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;

/**
 * 金安智治驾驶舱金安大数据公共安全
 * <AUTHOR> @version 1.0
 * @date 2
 *
 */
@RestController
@RequestMapping("/bigScreen/jazz/commonSafe")
public class BigScreenJazzCommonSafeController extends BaseController {
    @Autowired
    private IInstructionEventService instructionEventService;
    /**
     *  去调用公共安全板块：生态环境安全模块 的查询方法连续五次，拿到这五年的数据
     */
    @GetMapping("/MultipleYearsEnvInfo")
    public AjaxResult getMultipleYearsEnvInfo() {
        LocalDate now=LocalDate.now();
        int realTimeYear=now.getYear();
        List<InstructionEcologicalEnv> result = new ArrayList<>();
        for (int year = realTimeYear-5; year <= realTimeYear; year++) {
            result.add(getEnvInfo(year));
        }
        return AjaxResult.success(result);
    }
    /**
     * 公共安全板块：生态环境安全模块 的查询一次方法
     */
    public InstructionEcologicalEnv getEnvInfo(int year){
        InstructionEcologicalEnv instructionEcologicalEnv=instructionEventService.getEnvInfo(year);
        return instructionEcologicalEnv;
    }

    @Autowired
    private InstructionJazzWwEventMapper instructionJazzWwEventMapper;
    /**
     * 突发公共事件
     * @return
     */
    @GetMapping("/publicEmergency")
    public AjaxResult publicEmergency() {
        InstructionEvent instructionEvent=new InstructionEvent();
        instructionEvent.setType("22");
        List<HashMap> hashMaps = instructionJazzWwEventMapper.countTypeNum(instructionEvent);
        return  AjaxResult.success(hashMaps);

    }
    /**
     * 趋势分析:通过调用五次 下面的那个查询一次年度事件统计的方法
     * <AUTHOR>
     */
    @GetMapping("/Trend")
    public AjaxResult getMultipleYearsTrend(){
        LocalDate now=LocalDate.now();
        int realTimeYear=now.getYear();
        List<InstructionTrend> list= new ArrayList<>();
        for (int year = realTimeYear-5; year <= realTimeYear; year++) {
            list.add(getOneYear(year));
        }
        return AjaxResult.success(list);
    }

    /**
     * 查询一次年度事件的方法
     */
    public InstructionTrend getOneYear(int year){
        InstructionTrend instructionTrend=instructionEventService.getEventTrend(year);
        return instructionTrend;
    }

}
