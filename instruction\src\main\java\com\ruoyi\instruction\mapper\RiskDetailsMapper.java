package com.ruoyi.instruction.mapper;

import java.util.Date;
import java.util.List;
import java.util.Map;

import com.ruoyi.instruction.domain.RiskDetailWork;
import com.ruoyi.instruction.domain.RiskDetails;
import com.ruoyi.instruction.domain.reqVo.RiskHiddenDangerReq;
import com.ruoyi.instruction.domain.rspVo.FxpcjdpcqkRspVp;
import com.ruoyi.instruction.domain.rspVo.RiskDetailJawRspVo;
import com.ruoyi.instruction.domain.rspVo.RiskHiddenDangerRsp;
import com.ruoyi.instruction.domain.rspVo.RiskSummaryRspVo;
import com.ruoyi.instruction.domain.rspVo.RiskTypeTopRsp;
import org.apache.ibatis.annotations.Param;

/**
 * 风险排查详情Mapper接口
 *
 * <AUTHOR>
 * @date 2023-12-25
 */
public interface RiskDetailsMapper
{
    /**
     * 查询风险排查详情
     *
     * @param id 风险排查详情主键
     * @return 风险排查详情
     */
    public RiskDetails selectRiskDetailsById(Long id);

    /**
     * 查询风险排查详情列表
     *
     * @param riskDetails 风险排查详情
     * @return 风险排查详情集合
     */
    public List<RiskDetails> selectRiskDetailsList(RiskDetails riskDetails);
    /**
     * 查询风险排查详情列表
     *
     * @param riskDetails 风险排查详情
     * @return 风险排查详情集合
     */
    public List<RiskDetails> selectRiskDetailsListNew(RiskDetails riskDetails);

    /**
     * 新增风险排查详情
     *
     * @param riskDetails 风险排查详情
     * @return 结果
     */
    public int insertRiskDetails(RiskDetails riskDetails);

    /**
     * 修改风险排查详情
     *
     * @param riskDetails 风险排查详情
     * @return 结果
     */
    public int updateRiskDetails(RiskDetails riskDetails);

    /**
     * 删除风险排查详情
     *
     * @param id 风险排查详情主键
     * @return 结果
     */
    public int deleteRiskDetailsById(Long id);

    /**
     * 批量删除风险排查详情
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteRiskDetailsByIds(Long[] ids);

    /**
     * 根据父级id查询风险类别
     * @param parentId
     * @return
     */
    List<Map<String,Object>> getRiskCategory(@Param("parentId") Long parentId);

    /**
     * 获取风险统计
     * @param riskDetails
     * @return
     */
    List<Map<String,Object>> getStatistics(RiskDetails riskDetails);

    /**
     * 获取类别统计
     * @param riskDetails
     * @return
     */
    List<Map<String, Object>> getCategoryStatistics(RiskDetails riskDetails);

    /**
     * 获取风险
     * @return
     */
    List<String> getRiskYear();

    List<Map<String, Object>> getRiskNameAndId();

    /**
     * 根据riskId删除风险排查详情id
     * @param id
     */
    void deleteRiskDetailsByRiskId(@Param("id") Long id);

    /**
     * 县市排查数
     * @param riskDetails
     * @return
     */
    List<Map<String, Object>> getCountyNumberStatistics(RiskDetails riskDetails);

    /**
     * 查询风险排查类别
     * @return
     */
    List<Map<String, Object>> findRiskType();

    /**
     * 根据年份/任务/县市区查询风险信息
     * @return
     */
    List<RiskDetails> selectRiskDetailsListByConditions(
            @Param("years") List<Integer> years,
            @Param("riskIds") List<Integer> riskIds,
            @Param("countyNames") List<String> countyNames,
            @Param("riskType") Integer riskType
    );

    /**
     * 根据条件查询风险排查信息
     * @param level
     * @param dateBefore
     * @param count
     * @return
     */
    List<RiskDetails> findForeWarnRisk(@Param("level") Integer level, @Param("dateBefore") Date dateBefore, @Param("count") Integer count);

    /**
     * 更新风险排查预警记录表信息
     * @param riskDetails
     */
    void updateRiskRemind(RiskDetails riskDetails);

    /**
     * 风险隐患
     * @param riskHiddenDangerReq
     * @return
     */
    RiskHiddenDangerRsp riskHiddenDanger(RiskHiddenDangerReq riskHiddenDangerReq);

    /**
     * 风险类型top5
     * @param riskHiddenDangerReq
     * @return
     */
    List<RiskTypeTopRsp> riskTypeTop5(RiskHiddenDangerReq riskHiddenDangerReq);

    /**
     * 季度排查情况
     * @param riskHiddenDangerReq
     * @return
     */
    List<FxpcjdpcqkRspVp> ydpcqk(RiskHiddenDangerReq riskHiddenDangerReq);

    /**
     * 获取风险统计接口新
     * @param riskDetails
     * @return
     */
    List<Map<String, Object>> getStatisticsNew(RiskDetails riskDetails);

    /**
     * 根据参数查询风险排查待处理数
     * @param level
     * @param countyName
     * @param week
     * @param i
     * @return
     */
    Long getToDispose(@Param("level") Long level, @Param("dutyPlace") String countyName, @Param("timeType") String week, @Param("count") Integer count);

    /**
     * 更新风险排查详情处理时间
     * @param riskDetailWork
     */
    void updateDealTime(RiskDetailWork riskDetailWork);

    /**
     * 获取县市区最新处置动态
     * @param date
     * @param level
     * @return
     */
    List<RiskDetails> getRiskCountyDeal(@Param("date") String date, @Param("level") String level);

    /**
     * 获取进展不力数据
     * @param date
     * @return
     */
    List<RiskDetails> getRiskSlowProgress(@Param("date") String date);

    /**
     * 获取风险排查待处理数
     * @param type
     * @return
     */
    List<RiskDetailJawRspVo> getRiskRemindStatis(@Param("type") String type);

    /**
     * 根据动态条件获取风险概要统计数据
     *
     * @param riskDetails 包含过滤条件的实体
     * @return 统计结果的 DTO 对象
     */
    RiskSummaryRspVo getRiskSummaryStatistics(RiskDetails riskDetails);
}
