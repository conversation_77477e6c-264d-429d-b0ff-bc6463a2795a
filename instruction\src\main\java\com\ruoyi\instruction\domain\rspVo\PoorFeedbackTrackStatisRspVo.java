package com.ruoyi.instruction.domain.rspVo;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import lombok.Data;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2025/4/23 9:58
 */
@Data
public class PoorFeedbackTrackStatisRspVo {

    @ColumnWidth(20)
    @ExcelProperty({"处置不力统计","单位名称"})
    private String unitName;

    @ColumnWidth(20)
    @ExcelProperty({"处置不力统计","作为主责单位不力数据", "应处置指令数"})
    private Long receiveNum;

    @ColumnWidth(25)
    @ExcelProperty({"处置不力统计","作为主责单位不力数据", "未及时处置指令数"})
    private Long receiveTimeOutNum;

    @ColumnWidth(20)
    @ExcelProperty({"处置不力统计","作为协同单位不力数据", "应处置指令数"})
    private Long collaborationNum;

    @ColumnWidth(25)
    @ExcelProperty({"处置不力统计","作为协同单位不力数据", "未及时处置指令数"})
    private Long collaborationTimeOutNum;
}
