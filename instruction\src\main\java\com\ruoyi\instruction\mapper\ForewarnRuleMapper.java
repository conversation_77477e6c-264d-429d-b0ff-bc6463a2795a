package com.ruoyi.instruction.mapper;

import java.util.List;
import com.ruoyi.instruction.domain.ForewarnRule;

/**
 * 预警提醒规则Mapper接口
 *
 * <AUTHOR>
 * @date 2024-07-30
 */
public interface ForewarnRuleMapper
{
    /**
     * 查询预警提醒规则
     *
     * @param id 预警提醒规则主键
     * @return 预警提醒规则
     */
    public ForewarnRule selectForewarnRuleById(Long id);

    /**
     * 查询预警提醒规则列表
     *
     * @param forewarnRule 预警提醒规则
     * @return 预警提醒规则集合
     */
    public List<ForewarnRule> selectForewarnRuleList(ForewarnRule forewarnRule);

    /**
     * 新增预警提醒规则
     *
     * @param forewarnRule 预警提醒规则
     * @return 结果
     */
    public int insertForewarnRule(ForewarnRule forewarnRule);

    /**
     * 修改预警提醒规则
     *
     * @param forewarnRule 预警提醒规则
     * @return 结果
     */
    public int updateForewarnRule(ForewarnRule forewarnRule);

    /**
     * 删除预警提醒规则
     *
     * @param id 预警提醒规则主键
     * @return 结果
     */
    public int deleteForewarnRuleById(Long id);

    /**
     * 批量删除预警提醒规则
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteForewarnRuleByIds(Long[] ids);

    /**
     * 查询预警提醒规则数量
     * @param forewarnRule
     * @return
     */
    Long findCount(ForewarnRule forewarnRule);
}
