package com.ruoyi.examineAir.service;

import java.util.List;
import com.ruoyi.examineAir.domain.ExamineConfig;

/**
 * 考核晾晒动态配置信息Service接口
 * 
 * <AUTHOR>
 * @date 2023-04-19
 */
public interface IExamineConfigService 
{
    /**
     * 查询考核晾晒动态配置信息
     * 
     * @param id 考核晾晒动态配置信息主键
     * @return 考核晾晒动态配置信息
     */
    public ExamineConfig selectExamineConfigById(Long id);

    /**
     * 查询考核晾晒动态配置信息列表
     * 
     * @param examineConfig 考核晾晒动态配置信息
     * @return 考核晾晒动态配置信息集合
     */
    public List<ExamineConfig> selectExamineConfigList(ExamineConfig examineConfig);

    /**
     * 新增考核晾晒动态配置信息
     * 
     * @param examineConfig 考核晾晒动态配置信息
     * @return 结果
     */
    public int insertExamineConfig(ExamineConfig examineConfig);

    /**
     * 修改考核晾晒动态配置信息
     * 
     * @param examineConfig 考核晾晒动态配置信息
     * @return 结果
     */
    public int updateExamineConfig(ExamineConfig examineConfig);

    /**
     * 批量删除考核晾晒动态配置信息
     * 
     * @param ids 需要删除的考核晾晒动态配置信息主键集合
     * @return 结果
     */
    public int deleteExamineConfigByIds(Long[] ids);

    /**
     * 删除考核晾晒动态配置信息信息
     * 
     * @param id 考核晾晒动态配置信息主键
     * @return 结果
     */
    public int deleteExamineConfigById(Long id);
}
