package com.ruoyi.instruction.domain;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;
import lombok.Data;
import org.apache.poi.ss.usermodel.IndexedColors;

import java.util.Date;

@Data
public class PersonnelStrike extends BaseEntity {

    /**
     * 主键
     */
    private Integer id;

    /**
     * 打击单位
     */
    @Excel(name = "打击单位",headerColor= IndexedColors.RED,combo = {"婺城分局","江南分局","金东分局","兰溪分局","义乌分局","东阳分局","永康分局","武义分局","浦江分局","磐安分局"})
    private String strikeUnit;

    /**
     * 打击时间
     */
    @Excel(name = "打击时间(yyyy-MM-dd)",headerColor= IndexedColors.RED, width = 30, dateFormat = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date strikeDate;


    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date strikeDateStart;

    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date strikeDateEnd;

    /**
     * 被打击人姓名
     */
    @Excel(name = "被打击人姓名",headerColor= IndexedColors.RED)
    private String attackName;

    /**
     * 身份证号
     */
    @Excel(name = "身份证号",headerColor= IndexedColors.RED)
    private String sfzh;

    /**
     * 打击罪名
     */
    @Excel(name = "打击罪名")
    private String strikeCharge;

    /**
     * 行政处罚
     */
    @Excel(name = "行政处罚")
    private String administrativePenalty;

    /**
     * 刑事处罚
     */
    @Excel(name = "刑事处罚")
    private String criminalPenalty;

}
