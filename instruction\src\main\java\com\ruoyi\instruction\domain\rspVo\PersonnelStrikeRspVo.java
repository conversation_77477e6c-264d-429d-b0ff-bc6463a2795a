package com.ruoyi.instruction.domain.rspVo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;
import lombok.Data;

import java.util.Date;

@Data
public class PersonnelStrikeRspVo extends BaseEntity {

    /**
     * 主键
     */
    private Integer id;

    /**
     * 打击单位
     */
    @Excel(name = "打击单位")
    private String strikeUnit;

    /**
     * 打击时间
     */
    @Excel(name = "打击时间", width = 30, dateFormat = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date strikeDate;

    /**
     * 被打击人姓名
     */
    @Excel(name = "被打击人姓名")
    private String attackName;

    private String sfzh;

    /**
     * 打击罪名
     */
    @Excel(name = "打击罪名")
    private String strikeCharge;

    /**
     * 行政处罚
     */
    @Excel(name = "行政处罚")
    private String administrativePenalty;

    /**
     * 刑事处罚
     */
    @Excel(name = "刑事处罚")
    private String criminalPenalty;
}
