package com.ruoyi.instruction.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;

import com.ruoyi.instruction.mapper.RiskDetailsMapper;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.instruction.domain.RiskDetailWork;
import com.ruoyi.instruction.service.IRiskDetailWorkService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 风险排查详情-工作进展Controller
 *
 * <AUTHOR>
 * @date 2024-01-16
 */
@RestController
@RequestMapping("/riskDetail/work")
public class RiskDetailWorkController extends BaseController
{
    @Autowired
    private IRiskDetailWorkService riskDetailWorkService;

    @Autowired
    private RiskDetailsMapper detailsMapper;

    /**
     * 查询风险排查详情-工作进展列表
     */
    // @PreAuthorize("@ss.hasPermi('riskDetail:work:list')")
    @GetMapping("/list")
    public AjaxResult list(RiskDetailWork riskDetailWork)
    {
        List<RiskDetailWork> list = riskDetailWorkService.selectRiskDetailWorkList(riskDetailWork);
        return AjaxResult.success(list);
    }

    /**
     * 导出风险排查详情-工作进展列表
     */
    // @PreAuthorize("@ss.hasPermi('riskDetail:work:export')")
    @Log(title = "风险排查详情-工作进展", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, RiskDetailWork riskDetailWork)
    {
        List<RiskDetailWork> list = riskDetailWorkService.selectRiskDetailWorkList(riskDetailWork);
        ExcelUtil<RiskDetailWork> util = new ExcelUtil<RiskDetailWork>(RiskDetailWork.class);
        util.exportExcel(response, list, "风险排查详情-工作进展数据");
    }

    /**
     * 获取风险排查详情-工作进展详细信息
     */
    // @PreAuthorize("@ss.hasPermi('riskDetail:work:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(riskDetailWorkService.selectRiskDetailWorkById(id));
    }

    /**
     * 新增风险排查详情-工作进展
     */
    // @PreAuthorize("@ss.hasPermi('riskDetail:work:add')")
    @Log(title = "风险排查详情-工作进展", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody RiskDetailWork riskDetailWork)
    {
        int row = riskDetailWorkService.insertRiskDetailWork(riskDetailWork);
        detailsMapper.updateDealTime(riskDetailWork);
        return toAjax(row);
    }

    /**
     * 修改风险排查详情-工作进展
     */
    // @PreAuthorize("@ss.hasPermi('riskDetail:work:edit')")
    @Log(title = "风险排查详情-工作进展", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody RiskDetailWork riskDetailWork)
    {
        return toAjax(riskDetailWorkService.updateRiskDetailWork(riskDetailWork));
    }

    /**
     * 删除风险排查详情-工作进展
     */
    // @PreAuthorize("@ss.hasPermi('riskDetail:work:remove')")
    @Log(title = "风险排查详情-工作进展", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        RiskDetailWork riskDetailWork = riskDetailWorkService.selectRiskDetailWorkById(ids[0]);
        int row = riskDetailWorkService.deleteRiskDetailWorkByIds(ids);
        detailsMapper.updateDealTime(riskDetailWork);
        return toAjax(row);
    }
}
