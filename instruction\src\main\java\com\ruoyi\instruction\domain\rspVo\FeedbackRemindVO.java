package com.ruoyi.instruction.domain.rspVo;

import lombok.Data;

import java.util.Date;

/**
 * 频次提醒 返回结果
 * <AUTHOR>
 * @version 1.0
 * @date 2025/6/3 10:29
 */
@Data
public class FeedbackRemindVO {

    private Long instructionId;           // 指令ID
    private Long feedbackDeptId;          // 反馈部门ID
    private String feedbackDept;          // 反馈部门名称
    private Integer instructionType;      // 指令类型(3或6)
    private String instructionTitle;      // 指令标题
    private String emergencyDays;         // 紧急程度对应的天数("30","15","7")
    private Date assignTime;              // 分配时间
    private Date handleTime;              // 处理时间
    private Integer daysFeedback;         // 反馈天数(DATEDIFF结果)
    private Long receiveDeptId;           // 接收部门ID
    private String receiveDept;           // 处理后的接收部门名称(已去除"政法委"等字样)
}
