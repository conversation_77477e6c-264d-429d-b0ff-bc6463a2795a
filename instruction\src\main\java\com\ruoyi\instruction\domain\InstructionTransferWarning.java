package com.ruoyi.instruction.domain;

import java.util.Date;
import java.util.List;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 指令移交预警信息对象 t_instruction_transfer_warning
 *
 * <AUTHOR>
 * @date 2025/07/22
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class InstructionTransferWarning extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /** 主键ID */
    private Long id;

    /** 预警标题 (来自指令标题) */
    @Excel(name = "预警标题")
    private String warningTitle;

    /** 移交原因类型 (字典: 1=超期未化解, 2=双排双办移交) */
    @Excel(name = "移交原因", readConverterExp = "1=超期未化解,2=双排双办移交")
    private Integer transferReasonType;

    /** 事件类型 (来自指令) */
    @Excel(name = "事件类型")
    private String eventType;

    /** 归属区域 (来自指令) */
    @Excel(name = "归属区域")
    private String areaName;

    /** 事件描述 (来自指令基本情况) */
    @Excel(name = "事件描述")
    private String contentDesc;

    /** 风险等级 (转换自指令紧急程度) 3：一般，4：紧急，5：特急*/
    @Excel(name = "风险等级")
    private Long riskLevel;

    /** 表现行为 (来自指令纠纷性质) */
    @Excel(name = "表现行为")
    private String behavior;

    /** 上报时间 (冗余自指令的创建时间 create_time) */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "上报时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date reportTime;

    /** 移交时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "移交时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date transferTime;

    /** 交办单位 (来自指令) */
    @Excel(name = "交办单位")
    private String assignedUnit;

    /** 关联人员ID列表 (用于人员详情) */
    private String relatedPersonIds;

    /** 来源指令ID (用于原指令详情) */
    private Long sourceInstructionId;

    /**
     * 关联人员列表
     */
    private List<InstrucationPerson> personList;

    /**
     * 指令id
     */
    private Long instructionId;

}
