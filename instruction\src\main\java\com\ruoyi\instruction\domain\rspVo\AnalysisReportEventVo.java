package com.ruoyi.instruction.domain.rspVo;

import lombok.Data;

/**
 * 群体分析报告(事件)
 * <AUTHOR>
 */
@Data
public class AnalysisReportEventVo {
    /**
     * 创建时间
     */
    private String createTime;
    /**
     * 事件标题
     */
    private  String eventTitle;
    /**
     * 挑头人员
     */
    private String leadPerson;

    /**
     * 响应人员
     */
    private String respPerson;
    /**
     * 所属地
     */
    private String region;
    /**
     * 是否实地上访
     */
    private String isSiteVisits;

    /**
     * 实地上访人数
     */
    private Integer siteVisitsCount;
    /**
     * 实地上访类型
     */
    private String   siteVisitsType;
    /**
     * 序号
     */
    private  Integer num;

    /**
     * 事件属性
     */
    private String   eventProperties;
    /**
     * 无身份人员
     */
    private Integer outsidePerson;


}
