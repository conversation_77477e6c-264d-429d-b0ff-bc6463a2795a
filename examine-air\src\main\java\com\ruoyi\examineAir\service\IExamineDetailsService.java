package com.ruoyi.examineAir.service;

import java.util.List;
import com.ruoyi.examineAir.domain.ExamineDetails;

/**
 * 考核晾晒详情Service接口
 * 
 * <AUTHOR>
 * @date 2023-04-19
 */
public interface IExamineDetailsService 
{
    /**
     * 查询考核晾晒详情
     * 
     * @param id 考核晾晒详情主键
     * @return 考核晾晒详情
     */
    public ExamineDetails selectExamineDetailsById(Long id);

    /**
     * 查询考核晾晒详情列表
     * 
     * @param examineDetails 考核晾晒详情
     * @return 考核晾晒详情集合
     */
    public List<ExamineDetails> selectExamineDetailsList(ExamineDetails examineDetails);

    /**
     * 新增考核晾晒详情
     * 
     * @param examineDetails 考核晾晒详情
     * @return 结果
     */
    public int insertExamineDetails(ExamineDetails examineDetails);

    /**
     * 修改考核晾晒详情
     * 
     * @param examineDetails 考核晾晒详情
     * @return 结果
     */
    public int updateExamineDetails(ExamineDetails examineDetails);

    /**
     * 批量删除考核晾晒详情
     * 
     * @param ids 需要删除的考核晾晒详情主键集合
     * @return 结果
     */
    public int deleteExamineDetailsByIds(Long[] ids);

    /**
     * 删除考核晾晒详情信息
     * 
     * @param id 考核晾晒详情主键
     * @return 结果
     */
    public int deleteExamineDetailsById(Long id);
}
