package com.ruoyi.instruction.controller;

import java.util.List;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.instruction.domain.TJazzJcjcSpecialWork;
import com.ruoyi.instruction.service.ITJazzJcjcSpecialWorkService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import lombok.extern.slf4j.Slf4j;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 基层基础-专项工作Controller
 * 
 * <AUTHOR>
 * @date 2023-07-17
 */
@RestController
@RequestMapping("/instruction/specialWork")
@Slf4j
public class TJazzJcjcSpecialWorkController extends BaseController
{
    @Autowired
    private ITJazzJcjcSpecialWorkService tJazzJcjcSpecialWorkService;

    /**
     * 查询基层基础-专项工作列表
     */
    @PreAuthorize("@ss.hasPermi('instruction:specialWork:list')")
    @GetMapping("/list")
    public TableDataInfo list(TJazzJcjcSpecialWork tJazzJcjcSpecialWork)
    {
        startPage();
        List<TJazzJcjcSpecialWork> list = tJazzJcjcSpecialWorkService.selectTJazzJcjcSpecialWorkList(tJazzJcjcSpecialWork);
        return getDataTable(list);
    }

    /**
     * 导出基层基础-专项工作列表
     */
    @PreAuthorize("@ss.hasPermi('instruction:specialWork:export')")
    @Log(title = "基层基础-专项工作", businessType = BusinessType.EXPORT)
    @GetMapping("/export")
    public AjaxResult export(TJazzJcjcSpecialWork tJazzJcjcSpecialWork)
    {
        List<TJazzJcjcSpecialWork> list = tJazzJcjcSpecialWorkService.selectTJazzJcjcSpecialWorkList(tJazzJcjcSpecialWork);
        ExcelUtil<TJazzJcjcSpecialWork> util = new ExcelUtil<TJazzJcjcSpecialWork>(TJazzJcjcSpecialWork.class);
        return util.exportExcel(list, "specialWork");
    }

    /**
     * 获取基层基础-专项工作详细信息
     */
    @PreAuthorize("@ss.hasPermi('instruction:specialWork:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return AjaxResult.success(tJazzJcjcSpecialWorkService.selectTJazzJcjcSpecialWorkById(id));
    }

    /**
     * 新增基层基础-专项工作
     */
    @PreAuthorize("@ss.hasPermi('instruction:specialWork:add')")
    @Log(title = "基层基础-专项工作", businessType = BusinessType.INSERT)
    @PostMapping("/add")
    public AjaxResult add(@RequestBody TJazzJcjcSpecialWork tJazzJcjcSpecialWork)
    {
        return toAjax(tJazzJcjcSpecialWorkService.insertTJazzJcjcSpecialWork(tJazzJcjcSpecialWork));
    }

    /**
     * 修改基层基础-专项工作
     */
    @PreAuthorize("@ss.hasPermi('instruction:specialWork:edit')")
    @Log(title = "基层基础-专项工作", businessType = BusinessType.UPDATE)
    @PostMapping("/edit")
    public AjaxResult edit(@RequestBody TJazzJcjcSpecialWork tJazzJcjcSpecialWork)
    {
        return toAjax(tJazzJcjcSpecialWorkService.updateTJazzJcjcSpecialWork(tJazzJcjcSpecialWork));
    }

    /**
     * 删除基层基础-专项工作
     */
    @PreAuthorize("@ss.hasPermi('instruction:specialWork:remove')")
    @Log(title = "基层基础-专项工作", businessType = BusinessType.DELETE)
	@PostMapping("/remove/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(tJazzJcjcSpecialWorkService.deleteTJazzJcjcSpecialWorkByIds(ids));
    }
}
