package com.ruoyi.instruction.domain;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 基层治理-人员关系对象 yj_event_person
 *
 * <AUTHOR>
 * @date 2024-12-10
 */
public class YjEventPerson extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** $column.columnComment */
    private Long id;

    /** 预警事件id */
    @Excel(name = "预警事件id")
    private Long yjEvtId;

    /** 姓名 */
    @Excel(name = "姓名")
    private String username;

    /** 身份证 */
    @Excel(name = "身份证")
    private String idCard;

    /**
     * 行政编码
     */
    private String areaCode;

    /**
     * 住址
     */
    private String addr;

    /**
     * 责任所属地
     */
    private String dutyPlace;

    public void setId(Long id)
    {
        this.id = id;
    }

    public Long getId()
    {
        return id;
    }
    public void setYjEvtId(Long yjEvtId)
    {
        this.yjEvtId = yjEvtId;
    }

    public Long getYjEvtId()
    {
        return yjEvtId;
    }
    public void setUsername(String username)
    {
        this.username = username;
    }

    public String getUsername()
    {
        return username;
    }
    public void setIdCard(String idCard)
    {
        this.idCard = idCard;
    }

    public String getIdCard()
    {
        return idCard;
    }

    public String getAreaCode() {
        return areaCode;
    }

    public void setAreaCode(final String areaCode) {
        this.areaCode = areaCode;
    }

    public String getAddr() {
        return addr;
    }

    public void setAddr(final String addr) {
        this.addr = addr;
    }

    public String getDutyPlace() {
        return dutyPlace;
    }

    public void setDutyPlace(final String dutyPlace) {
        this.dutyPlace = dutyPlace;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("yjEvtId", getYjEvtId())
            .append("username", getUsername())
            .append("idCard", getIdCard())
            .toString();
    }
}
