package com.ruoyi.instruction.mapper;

import java.util.List;
import java.util.Map;

import com.ruoyi.instruction.domain.HczzZfw;
import org.apache.ibatis.annotations.Param;

/**
 * 情指推送事件Mapper接口
 *
 * <AUTHOR>
 * @date 2023-06-07
 */
public interface HczzZfwMapper
{
    /**
     * 查询情指推送事件
     *
     * @param qzid 情指推送事件主键
     * @return 情指推送事件
     */
    public HczzZfw selectHczzZfwByQzid(String qzid);

    /**
     * 查询情指推送事件列表
     *
     * @param hczzZfw 情指推送事件
     * @return 情指推送事件集合
     */
    public List<HczzZfw> selectHczzZfwList(HczzZfw hczzZfw);

    /**
     * 新增情指推送事件
     *
     * @param hczzZfw 情指推送事件
     * @return 结果
     */
    public int insertHczzZfw(HczzZfw hczzZfw);

    /**
     * 修改情指推送事件
     *
     * @param hczzZfw 情指推送事件
     * @return 结果
     */
    public int updateHczzZfw(HczzZfw hczzZfw);

    /**
     * 删除情指推送事件
     *
     * @param qzid 情指推送事件主键
     * @return 结果
     */
    public int deleteHczzZfwByQzid(String qzid);

    /**
     * 批量删除情指推送事件
     *
     * @param qzids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteHczzZfwByQzids(String[] qzids);

    /**
     * 查询事件表当前最大ID
     * @return
     */
    public long selectMaxID();

    /**
     * 查询id比startID大的事件名称
     * @param startID
     * @return
     */
    public List<String> selectSJMCList (@Param("startID") Long startID);

    /**
     * 查询各县市区接收单位预警最大ID
     * @return
     */
    List<Map<String, Object>> selectAllMaxID();

    /**
     * 根据接收单位、id 进行查询事件名称
     * @param jsdw
     * @param currentID
     * @return
     */
    List<String> selectSjmcByParams(@Param("jsdw") String jsdw, @Param("currentID") Long currentID);

}
