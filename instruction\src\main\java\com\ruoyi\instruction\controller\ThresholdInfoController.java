package com.ruoyi.instruction.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.instruction.domain.ThresholdInfo;
import com.ruoyi.instruction.service.IThresholdInfoService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 研判阈值信息表Controller
 * 
 * <AUTHOR>
 * @date 2023-05-05
 */
@RestController
@RequestMapping("/threshold/info")
public class ThresholdInfoController extends BaseController
{
    @Autowired
    private IThresholdInfoService thresholdInfoService;

    /**
     * 查询研判阈值信息表列表
     */
    @PreAuthorize("@ss.hasPermi('threshold:info:list')")
    @GetMapping("/list")
    public TableDataInfo list(ThresholdInfo thresholdInfo)
    {
        startPage();
        List<ThresholdInfo> list = thresholdInfoService.selectThresholdInfoList(thresholdInfo);
        return getDataTable(list);
    }

    /**
     * 导出研判阈值信息表列表
     */
    @PreAuthorize("@ss.hasPermi('threshold:info:export')")
    @Log(title = "研判阈值信息表", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, ThresholdInfo thresholdInfo)
    {
        List<ThresholdInfo> list = thresholdInfoService.selectThresholdInfoList(thresholdInfo);
        ExcelUtil<ThresholdInfo> util = new ExcelUtil<ThresholdInfo>(ThresholdInfo.class);
        util.exportExcel(response, list, "研判阈值信息表数据");
    }

    /**
     * 获取研判阈值信息表详细信息
     */
    @PreAuthorize("@ss.hasPermi('threshold:info:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(thresholdInfoService.selectThresholdInfoById(id));
    }

    /**
     * 新增研判阈值信息表
     */
    @PreAuthorize("@ss.hasPermi('threshold:info:add')")
    @Log(title = "研判阈值信息表", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody ThresholdInfo thresholdInfo)
    {
        return toAjax(thresholdInfoService.insertThresholdInfo(thresholdInfo));
    }

    /**
     * 修改研判阈值信息表
     */
    @PreAuthorize("@ss.hasPermi('threshold:info:edit')")
    @Log(title = "研判阈值信息表", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody ThresholdInfo thresholdInfo)
    {
        return toAjax(thresholdInfoService.updateThresholdInfo(thresholdInfo));
    }

    /**
     * 删除研判阈值信息表
     */
    @PreAuthorize("@ss.hasPermi('threshold:info:remove')")
    @Log(title = "研判阈值信息表", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(thresholdInfoService.deleteThresholdInfoByIds(ids));
    }
}
