package com.ruoyi.instruction.domain;

import java.util.Date;
import java.util.List;

import com.fasterxml.jackson.annotation.JsonFormat;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 暗访督察对象 t_undercover_inspection
 * 
 * <AUTHOR>
 * @date 2023-05-23
 */
public class UndercoverInspection extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** id */
    private Long id;

    /** 暗访名称 */
    @Excel(name = "暗访名称")
    private String name;

    /** 暗访地点 */
    @Excel(name = "暗访地点")
    private String place;

    /** 创建时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "创建时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date cTime;

    /** 修改时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "修改时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date uTime;

    /** 暗访时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "暗访时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date secretVisitTime;

    /** 暗访类型 */
    @Excel(name = "暗访类型")
    private Long type;
    /**
     * 暗访类型名称
     */
    private String typeName;

    /** 责任人 */
    @Excel(name = "责任人")
    private String personResponsible;

    /** 联系电话 */
    @Excel(name = "联系电话")
    private String phone;

    /** 反馈时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "反馈时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date feedbackTime;

    /** 基本情况 */
    @Excel(name = "基本情况")
    private String baseInfo;

    /** 人员id集合 */
    @Excel(name = "人员id集合")
    private String personIds;

    /** 状态，1正常，9删除 */
    @Excel(name = "状态，1正常，9删除")
    private Long status;

    private List<UndercoverPerson> undercoverPersonList;
    public void setId(Long id) 
    {
        this.id = id;
    }

    public Long getId() 
    {
        return id;
    }
    public void setName(String name) 
    {
        this.name = name;
    }

    public String getName() 
    {
        return name;
    }
    public void setPlace(String place) 
    {
        this.place = place;
    }

    public String getPlace() 
    {
        return place;
    }
    public void setcTime(Date cTime) 
    {
        this.cTime = cTime;
    }

    public Date getcTime() 
    {
        return cTime;
    }
    public void setuTime(Date uTime) 
    {
        this.uTime = uTime;
    }

    public Date getuTime() 
    {
        return uTime;
    }
    public void setSecretVisitTime(Date secretVisitTime) 
    {
        this.secretVisitTime = secretVisitTime;
    }

    public Date getSecretVisitTime() 
    {
        return secretVisitTime;
    }
    public void setType(Long type) 
    {
        this.type = type;
    }

    public Long getType() 
    {
        return type;
    }
    public void setPersonResponsible(String personResponsible) 
    {
        this.personResponsible = personResponsible;
    }

    public String getPersonResponsible() 
    {
        return personResponsible;
    }
    public void setPhone(String phone) 
    {
        this.phone = phone;
    }

    public String getPhone() 
    {
        return phone;
    }
    public void setFeedbackTime(Date feedbackTime) 
    {
        this.feedbackTime = feedbackTime;
    }

    public Date getFeedbackTime() 
    {
        return feedbackTime;
    }
    public void setBaseInfo(String baseInfo) 
    {
        this.baseInfo = baseInfo;
    }

    public String getBaseInfo() 
    {
        return baseInfo;
    }
    public void setPersonIds(String personIds) 
    {
        this.personIds = personIds;
    }

    public String getPersonIds() 
    {
        return personIds;
    }
    public void setStatus(Long status) 
    {
        this.status = status;
    }

    public Long getStatus() 
    {
        return status;
    }

    public List<UndercoverPerson> getUndercoverPersonList() {
        return undercoverPersonList;
    }

    public void setUndercoverPersonList(List<UndercoverPerson> undercoverPersonList) {
        this.undercoverPersonList = undercoverPersonList;
    }

    public String getTypeName() {
        return typeName;
    }

    public void setTypeName(String typeName) {
        this.typeName = typeName;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("name", getName())
            .append("place", getPlace())
            .append("cTime", getcTime())
            .append("uTime", getuTime())
            .append("secretVisitTime", getSecretVisitTime())
            .append("type", getType())
            .append("personResponsible", getPersonResponsible())
            .append("phone", getPhone())
            .append("feedbackTime", getFeedbackTime())
            .append("baseInfo", getBaseInfo())
            .append("personIds", getPersonIds())
            .append("status", getStatus())
            .toString();
    }
}
