package com.ruoyi.instruction.controller;

import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.instruction.domain.InstructionExperience;
import com.ruoyi.instruction.service.IInstructionExperienceService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 【经验创新】Controller
 *
 * <AUTHOR>
 * @date 2023-05-17
 */
@RestController
@RequestMapping("/instruction/experience")
public class InstructionExperienceController extends BaseController
{
    @Autowired
    private IInstructionExperienceService instructionExperienceService;

    /**
     * 查询【经验创新】列表
     */
    @PreAuthorize("@ss.hasPermi('instruction:experience:list')")
    @GetMapping("/explist")
    public TableDataInfo list(InstructionExperience instructionExperience)
    {
        startPage();
        List<InstructionExperience> list = instructionExperienceService.selectInstructionExperienceList(instructionExperience);
        return getDataTable(list);
    }

    /**
     * 导出【经验创新】列表
     */
    @PreAuthorize("@ss.hasPermi('instruction:experience:export')")
    @Log(title = "【导出】", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, InstructionExperience instructionExperience)
    {
        List<InstructionExperience> list = instructionExperienceService.selectInstructionExperienceList(instructionExperience);
        ExcelUtil<InstructionExperience> util = new ExcelUtil<InstructionExperience>(InstructionExperience.class);
        util.exportExcel(response, list, "【经验创新】数据");
    }

    /**
     * 获取【经验创新】详细信息
     */
    @PreAuthorize("@ss.hasPermi('instruction:experience:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(instructionExperienceService.selectInstructionExperienceById(id));
    }

    /**
     * 新增【请填经验创新写功能名称】
     */
    @PreAuthorize("@ss.hasPermi('instruction:experience:add')")
    @Log(title = "【新增】", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody InstructionExperience instructionExperience)
    {
        return toAjax(instructionExperienceService.insertInstructionExperience(instructionExperience));
    }

    /**
     * 修改【请填写功能名称】
     */
    @PreAuthorize("@ss.hasPermi('instruction:experience:edit')")
    @Log(title = "【编辑】", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody InstructionExperience instructionExperience)
    {
        return toAjax(instructionExperienceService.updateInstructionExperience(instructionExperience));
    }

    /**
     * 删除【经验创新】
     */
    @PreAuthorize("@ss.hasPermi('instruction:experience:remove')")
    @Log(title = "【删除】", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(instructionExperienceService.deleteInstructionExperienceByIds(ids));
    }
}
