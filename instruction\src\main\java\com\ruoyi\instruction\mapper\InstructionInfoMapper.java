package com.ruoyi.instruction.mapper;

import java.util.*;

import com.ruoyi.instruction.domain.InstructionInfo;
import com.ruoyi.instruction.domain.rspVo.*;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

/**
 * 指令基本信息Mapper接口
 *
 * <AUTHOR>
 * @date 2022-12-15
 */
@Repository
public interface InstructionInfoMapper {
    /**
     * 查询指令基本信息
     *
     * @param id 指令基本信息主键
     * @return 指令基本信息
     */
    public InstructionInfo selectInstructionInfoById(Long id);

    /**
     * 查询全部任务名称
     * @return 全部任务名称
     * @param type
     * @param deptName
     */
    public List<String> selectMissionNames(@Param("type") final Integer type,@Param("deptName") final String deptName);

    /**
     * 查询指令基本信息列表
     *
     * @param instructionInfo 指令基本信息
     * @return 指令基本信息集合
     */
    public List<InstructionInfo> selectInstructionInfoList(InstructionInfo instructionInfo);

    /**
     * 新增指令基本信息
     *
     * @param instructionInfo 指令基本信息
     * @return 结果
     */
    public int insertInstructionInfo(InstructionInfo instructionInfo);

    /**
     * 修改指令基本信息
     *
     * @param instructionInfo 指令基本信息
     * @return 结果
     */
    public int updateInstructionInfo(InstructionInfo instructionInfo);

    /**
     * 删除指令基本信息
     *
     * @param id 指令基本信息主键
     * @return 结果
     */
    public int deleteInstructionInfoById(Long id);

    /**
     * 批量删除指令基本信息
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteInstructionInfoByIds(Long[] ids);


    /**
     * 查询总数
     *
     * @param instructionInfo
     * @return
     */
    Long selectCount(InstructionInfo instructionInfo);

    /**
     * 获取指令列表
     * @param instructionInfo
     * @return
     */
    List<InstructionInfomiddleResVo> testInstructionListNew(InstructionInfo instructionInfo);

    /**
     * 查询出未交办的指令ids
     *
     * @return
     */
    Set<Long> AssignNotHandleIds();

    /**
     * 查询出未接收的指令ids
     *
     * @return
     */
    Set<Long> ReceiveNotHandleIds(@Param("deptId") Long depeName);


    /**
     * 查询出需要反馈的指令ids
     *
     * @param deptName
     * @return
     */
    Set<Long> FeedBackNotHandleIds(@Param("deptId") Long deptName);

    /**
     * 市本级下发接收单位排名
     *
     * @return
     */
    List<Map<String, Integer>> getIssueRank();

    /**
     * 获取县市区下发指令
     * @return
     */
    List<Map<String, Integer>> getCountyIssueRank();

    /**
     * 获取指令到访类型
     * @return
     */
    List<Map<String, Integer>> getTypeCount();

    /**
     * 获取县市区平均处置时长排名
     * @param type
     * @return
     */
    List<Map<String, Integer>> getCountyDealHour(@Param("type") String type);

    /**
     * 查询指令最后一条反馈时间
     * @param instructionId
     * @return
     */
    Date getLastFeedBackTimeById(@Param("instructionId") Long instructionId);

    /**
     * 获取指令信息用于重点风险治理
     * @return
     */
    List<JazzInstructionInfoStatisticsRspVo> selectInfo();
    /**
     * 获取指令信息用于重点风险治理(详情)
     * @return
     */
    JazzInstructionInfoStatisticsRspVo selectInfoDetailsById(Long id);

    /**
     * 判断指令是否可以销号
     * @param id
     * @return
     */
    Integer isToEnd(@Param("id") Long id);

    /**
     * 通过指令id、部门名称查询县市区是否可以进行反馈
     * @param id
     * @param deptName
     */
    Integer findCountyFeedBackStatus(@Param("id") Long id, @Param("deptName") String deptName);

    /**
     * 查询已放置/交办情指id
     * @return
     */
    List<String> findQzids();
    List<Map<String, Object>> findQzids1();
    /**
     * 查询指令列表For县市区
     * @param instructionInfo
     * @return
     */
    List<InstructionInfomiddleResVo> instructionListNewForCounty(InstructionInfo instructionInfo);

    /**
     * 查询待提醒指令id
     * @return
     */
    List<Long> findToRemind();

    /**
     * 查询待提醒指令
     * @param ids
     * @return
     */
    List<InstructionRemindVo> ToRemindInfo(@Param("ids")List<Long> ids);

    /**
     * 获取县市区创建指令
     * @param instructionInfo
     * @return
     */
    List<InstructionInfomiddleResVo> getCountyInstructionList(InstructionInfo instructionInfo);

    /**
     * 县市区待反馈指令id
     * @param deptName
     * @return
     */
    Set<Long> countyFeedBackNotHandleIds(@Param("deptName") String deptName);

    /**
     * 县市区销号员查看待销号指令
     * @param deptId
     * @return
     */
    Set<Long> countyEndNotHandleIds(@Param("deptId") Long deptId);

    /**
     *  查询县市区创建指令待接收指令
     * @param deptName
     * @return
     */
    Set<Long> countyReceiveNotHandleIds(@Param("deptName") String deptName);

    /**
     * 市本级创建指令,县市区待反馈记录
     * @param deptName
     * @param deptId
     * @return
     */
    Set<Long> CountyNotHandleIds(@Param("deptName") String deptName, @Param("deptId") Long deptId);

    /**
     * 市本级待销号指令ids
     * @return
     */
    Set<Long> EndNoHandleIds();


    /**
     * 获取县市区指令下达数
     * @return
     */
    List<Map<String, Object>> getCountyInstructionCount();

    /**
     * 获取县市区指令下发统计数
     * @param depId
     * @return
     */
    Map<String, Object> getCountyStatistics(@Param("deptId") Long depId);

    /**
     * 获取县市区平均处置时长
     * @return
     */
    List<Map<String, Object>> getCountyDealTime();

    /**
     * 根据指令id查询批量接收流程指令信息
     * @param ids
     * @return
     */
    List<Map<String, Object>> getBatchReceiveMoveInfo(Long[] ids);

    /**
     * 根据uniqueNo获取info
     *
     * @param inspectionUniqueNoList
     * @return
     */
    List<Long> listByMcafIds(@Param("inspectionUniqueNoList") List<String> inspectionUniqueNoList);

    /**
     * 查询符合销号条件的指令
     * @param ids
     * @return
     */
    List<Map<String, Object>> findBatchEndId(Long[] ids);

    /**
     * 查询已处置数据
     * @param info
     * @return
     */
    List<InstructionInfo> selcetDealList(InstructionInfo info);

    /**
     * 查询市本级反馈数据
     * @param info
     * @return
     */
    List<InstructionInfo> selcetFeedBackList(InstructionInfo info);


    /**
     * 查询销号记录
     * @param info
     * @return
     */
    List<InstructionInfo> selcetEndList(InstructionInfo info);

    /**
     * 获取移动端统计数
     * @param info
     * @return
     */
    Map<String, Object> getYiDongStatistics(InstructionInfo info);

    /**
     * 根据指令id获取指令流程时间
     * @param id
     * @return
     */
    Map<String, Object> getInstructionTime(@Param("id") Long id);

    /**
     * 获取移动端指令下发统计
     * @return
     */
    List<Map<String, Object>> getYiDongIssueStatistics();

    /**
     * 获取需要提醒审核指令集合
     * @return
     */
    List<InstructionInfo> getAuditRemind();

    /**
     * 查询市级维稳指令数据
     * @param params
     * @return
     */
    List<CityInstructionExportRspVo> exportCityInstructionInfo(@Param("params") Map<String, Object> params);


    /**
     * 根据人员id获取人员关联指令
     * @param id
     * @return
     */
    List<InstructionInfo> getContactInstructionByPersonId(@Param("id") Long id);

    /**
     * 查询县市区待销号指令ids
     * @param deptId
     * @return
     */
    Set<Long> EndNoHandleForCountyIds(@Param("deptId") Long deptId);

    /**
     * 查询mzx县市区是否满足销号条件
     * @param id
     * @return
     */
    Integer mzxCountyIsToEnd(@Param("id") Long id);

    /**
     * 获取mzx交办信息
     * @return
     */
    Map getMzxAssign();

    /**
     * 获取mzx销号信息
     * @return
     */
    Map getMzxEnd();

    /**
     * 获取mzx指令最新20条数据
     * @return
     */
    List<Map<String, Object>> getMzxInstructionLast20();

    /**
     * 获取市级指令数
     * @return
     */
    Long getCityCount();

    /**
     * 查询出易激化指令
     * @return
     */
    List<Long> selectWkMzxIds();

    /**
     * 获取兰溪驾驶舱统计数据
     * @param info
     * @return
     */
    List<InstructionInfo> getCityStatis(InstructionInfo info);

    /**
     * 获取县市区指令统计数据
     * @param info
     * @return
     */
    List<InstructionInfo> getCountyStatis(InstructionInfo info);

    /**
     * 查询市级指令状态
     * @param countyName
     * @return
     */
    List<InstructionInfo> getLxCityInstructionStatus(@Param("dutyPlace") String countyName);

    /**
     * 查询县市区指令状态
     * @param deptId
     * @return
     */
    List<InstructionInfo> getLxCountInstructionStatus(@Param("deptId") Long deptId);

    /**
     * 获取化解不力集合
     * @param date
     * @return
     */
    List<InstructionInfo> getFailureResolve(@Param("date") String date);

    /**
     * 获取跟踪不力集合
     * @param date
     * @return
     */
    List<InstructionInfo> getPoorTrack(@Param("date") String date);

    /**
     * 查询化解不力数统计
     * @param countyDeptIdByName
     * @param date
     * @return
     */
    List<InstructionInfo> selectHjblCount(@Param("deptId") Long countyDeptIdByName, @Param("date") String date,@Param("startTime") String startTime,@Param("endTime") String endTime);

    /**
     * 获取化解不力详情
     * @param countyDeptIdByName
     * @param date
     * @return
     */
    List<InstructionInfo> selectHjblDetails(@Param("deptId") Long countyDeptIdByName, @Param("date") String date,@Param("startTime") String startTime,@Param("endTime") String endTime);

    /**
     * 查询县市区化解不力
     * @param countyDeptIdByName
     * @param date
     * @return
     */
    List<InstructionInfo> selectHjblDetailsForCounty(@Param("deptId") Long countyDeptIdByName, @Param("date") String date,@Param("startTime") String startTime,@Param("endTime") String endTime);

    /**
     * 获取跟踪不力统计数据
     * @param countyDeptIdByName
     * @param date
     * @return
     */
    List<InstructionInfo> getPoorTrackNew(@Param("deptId") Long countyDeptIdByName, @Param("date") String date,@Param("startTime") String startTime,@Param("endTime") String endTime,@Param("instructionType") Integer instructionType);

    /**
     * 获取跟踪不力统计数据For县市区
     * @param countyDeptIdByName
     * @param date
     * @return
     */
    List<InstructionInfo> getPoorTrackNewForCounty(@Param("deptId") Long countyDeptIdByName, @Param("date") String date,@Param("startTime") String startTime,@Param("endTime") String endTime,@Param("instructionType") Integer instructionType);

    /**
     * 获取双排双办、矛盾风险预警 化解中、超期化解数、销号数
     * @param info
     * @return
     */
    List<InstructionInfo> getReportOne(InstructionInfo info);

    /**
     * 获取县（市、区）化解率
     * @param countyDeptIdByName
     * @param startTime
     * @param endTime
     * @param instructionType
     * @return
     */
    List<InstructionInfo> getCountyHjl(@Param("deptId") Long countyDeptIdByName, @Param("startTime")String startTime, @Param("endTime")String endTime,@Param("instructionType") Integer instructionType);

    /**
     * 获取接收不力集合
     * @param deptId
     * @return
     */
    List<InstructionInfo> getReceivePoor(@Param("deptId") Long deptId,@Param("deptName") String deptName,@Param("params") Map<String, Object> params);

    /**
     * 获取矛盾纠纷数据
     * @param info
     * @return
     */
    List<InstructionInfo> getContradictoryRisk(InstructionInfo info);

    /**
     * 查询处置不力数据
     * @param params
     * @param i
     * @return
     */
    List<FeedbackPoorRsp> getFeedbackPoor(@Param("params") Map<String, Object> params, @Param("isFilt") final int i);

    /**
     * 获取mzx(预警)接收单位数据
     * @param params
     * @return
     */
    List<PoorReceiveOneRsp> getReceiveData(@Param("params") Map<String, Object> params);

    /**
     * 查询我的待办
     * @param startTime
     * @param endTime
     * @return
     */
    Map<String, Object> findMyTodo(@Param("startTime") String startTime,@Param("endTime") String endTime);

    /**
     * 查询我的关注
     * @param followIds
     * @return
     */
    List<InstructionInfo> findMyFollow(@Param("myFollowIds") List<String> followIds,@Param("wkMzxIds") List<Long> yjh);

    /**
     * 查询案例列表
     * @return
     */
    List<InstructionInfo> getCaseList(InstructionInfo info);

    /**
     * 查询应接收未接收数据
     *
     * @return
     */
    List<ToReceiveRsp> getNoReceive(@Param("cityFlag") Boolean cityFlag,
                                    @Param("countyType") String countyType,
                                    @Param("deptId") Long deptId, @Param("startTime") String startTime,
                                    @Param("endTime") String endTime);

    List<Long> getSpsbAndyjIds(@Param("cityFlag") Boolean cityFlag, @Param("deptId") Long deptId,
                               @Param("startTime") String startTime, @Param("endTime") String endTime);

    List<ToFeedbackRsp> getToFeedbackList(@Param("cityFlag") Boolean cityFlag, @Param("ids") List<Long> ids);

    void updateCase(@Param("id") Long id);

    List<InstructionInfo> findFeedbackTodo(@Param("deptId") Long deptId);

    /**
     * 获取金安稳待销号数据
     * @return 待销号数量
     */
    Integer getPendingCloseCount();

    /**
     * 指令概况统计
     * @return 已交办、已销号、处置中数量统计
     */
    Map<String, Object> getInstructionOverview();

    /**
     * 指令概况统计（支持时间筛选）
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 已交办、已销号、处置中数量统计
     */
    Map<String, Object> getInstructionOverview(@Param("startTime") String startTime, @Param("endTime") String endTime);

    /**
     * 查询提醒时效（未按时接收的指令）
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 未按时接收的指令列表
     */
    List<Map<String, Object>> getRemindTimeoutList(@Param("startTime") String startTime, @Param("endTime") String endTime);

    /**
     * 查询提醒时效统计（未按时接收和未按时反馈统计数据）
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 未按时接收和未按时反馈的部门数、指令数统计
     */
    Map<String, Object> getRemindTimeoutStatistics(@Param("startTime") String startTime, @Param("endTime") String endTime);

    /**
     * 查询未按时反馈的指令列表
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 未按时反馈的指令列表
     */
    List<Map<String, Object>> getOvertimeFeedbackList(@Param("startTime") String startTime, @Param("endTime") String endTime);

    /**
     * 查询未按时接收按部门分组统计
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 各部门未按时接收统计
     */
    List<Map<String, Object>> getReceiveTimeoutByDept(@Param("startTime") String startTime, @Param("endTime") String endTime);

    /**
     * 查询未按时反馈按部门分组统计
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 各部门未按时反馈统计
     */
    List<Map<String, Object>> getFeedbackTimeoutByDept(@Param("startTime") String startTime, @Param("endTime") String endTime);

    /**
     * 更新指令的移交状态
     * @param instructionId 指令ID
     *
     */
    int updateTransferStatus(@Param("instructionId") Long instructionId);

    /**
     * 查询重点人员类型统计
     * @param pLevel 可选参数，人员等级筛选
     * @return 人员类型统计数据
     */
    List<Map<String, Object>> getPersonTypeStatistics(@Param("pLevel") Integer pLevel);

    /**
     * 查询人员走访记录统计
     * @param timeRange 时间范围筛选
     * @return 人员走访记录统计数据
     */
    List<Map<String, Object>> getPersonVisitStatistics(@Param("timeRange") String timeRange);

    /**
     * 查询人员走访记录统计汇总（按属地分组）
     * @return 按属地分组的各超时类型人数统计
     */
    List<Map<String, Object>> getPersonVisitStatisticsSummary();

    /**
     * 查询人员走访记录按等级分组统计
     * @param timeRange 时间范围筛选
     * @return 按人员等级分组的统计数据
     */
    List<Map<String, Object>> getPersonVisitStatisticsByLevel(@Param("timeRange") String timeRange);

    /**
     * 查询指定时间后的所有person_ids并连接
     * @param startTime 开始时间
     * @return GROUP_CONCAT后的person_ids字符串
     */
    String getPersonIdsConcat(@Param("startTime") String startTime);

    /**
     * 根据人员ID列表查询人员信息
     * @param personIds 人员ID列表
     * @return 人员信息列表
     */
    List<Map<String, Object>> getPersonInfoByIds(@Param("personIds") List<String> personIds);


    Map<String, Object> getCityIssuedStatistics(@Param("deptId") Long deptId, @Param("startTime") String startTime, @Param("endTime") String endTime);

    List<Map<String,Object>> getCountyIssuedStatistics(@Param("deptId") Long deptId,@Param("startTime") String startTime,@Param("endTime") String endTime,@Param("deptIds") List<Long> deptIds);

    List<Map<String, Object>> getEmergencyDegreeStatistics(@Param("deptName") String deptName, @Param("startTime") String startTime,@Param("endTime") String endTime);
}
