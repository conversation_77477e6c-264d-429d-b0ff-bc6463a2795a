package com.ruoyi.instruction.controller;

import java.util.List;
import java.util.stream.Collectors;
import javax.servlet.http.HttpServletResponse;

import com.ruoyi.common.constant.Constants;
import com.ruoyi.common.constant.InstructionRolesConstants;
import com.ruoyi.common.core.domain.entity.SysDept;
import com.ruoyi.common.core.domain.entity.SysRole;
import com.ruoyi.common.utils.DeptUtils;
import com.ruoyi.common.utils.SecurityUtils;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.instruction.domain.DayMeet;
import com.ruoyi.instruction.service.IDayMeetService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 日会商Controller
 *
 * <AUTHOR>
 * @date 2023-09-02
 */
@RestController
@RequestMapping("/day/meet")
public class DayMeetController extends BaseController {
    @Autowired
    private IDayMeetService dayMeetService;

    /**
     * 查询日会商列表
     */
    @PreAuthorize("@ss.hasPermi('day:meet:list')")
    @GetMapping("/list")
    public TableDataInfo list(DayMeet dayMeet) {
        //进行权限判断 县市区只能看到自己的会商列表,拥有角色则可以
        List<SysRole> roles = SecurityUtils.getLoginUser().getUser().getRoles();
        List<String> roleList = roles.stream().map(SysRole::getRoleKey).collect(Collectors.toList());
        if (!SecurityUtils.isAdmin(SecurityUtils.getUserId())) {
            if (!roleList.contains(InstructionRolesConstants.DAY_MEET_ADMIN)) {
                //查询用户父级部门所在县市区 县市区仅能查看自己所属区域的人员
                SysDept dept = SecurityUtils.getLoginUser().getUser().getDept();
                String dutyPlace = DeptUtils.getDutyPlaceByDept(dept);
                dayMeet.setCounty(dutyPlace);
            }
        }
        startPage();
        List<DayMeet> list = dayMeetService.selectDayMeetList(dayMeet);
        return getDataTable(list);
    }

    /**
     * 导出日会商列表
     */
    @PreAuthorize("@ss.hasPermi('day:meet:export')")
    @Log(title = "日会商", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, DayMeet dayMeet) {
        List<DayMeet> list = dayMeetService.selectDayMeetList(dayMeet);
        ExcelUtil<DayMeet> util = new ExcelUtil<DayMeet>(DayMeet.class);
        util.exportExcel(response, list, "日会商数据");
    }

    /**
     * 获取日会商详细信息
     */
    @PreAuthorize("@ss.hasPermi('day:meet:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id) {
        return success(dayMeetService.selectDayMeetById(id));
    }

    /**
     * 新增日会商
     */
    @PreAuthorize("@ss.hasPermi('day:meet:add')")
    @Log(title = "日会商", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody DayMeet dayMeet) {
        return toAjax(dayMeetService.insertDayMeet(dayMeet));
    }

    /**
     * 修改日会商
     */
    @PreAuthorize("@ss.hasPermi('day:meet:edit')")
    @Log(title = "日会商", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody DayMeet dayMeet) {
        return toAjax(dayMeetService.updateDayMeet(dayMeet));
    }

    /**
     * 删除日会商
     */
    @PreAuthorize("@ss.hasPermi('day:meet:remove')")
    @Log(title = "日会商", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids) {
        return toAjax(dayMeetService.deleteDayMeetByIds(ids));
    }
}
