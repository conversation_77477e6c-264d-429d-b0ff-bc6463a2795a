package com.ruoyi.instruction.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.instruction.domain.GroupDispose;
import com.ruoyi.instruction.service.IGroupDisposeService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 群体处置记录Controller
 *
 * <AUTHOR>
 * @date 2024-12-19
 */
@RestController
@RequestMapping("/group/dispose")
public class GroupDisposeController extends BaseController
{
    @Autowired
    private IGroupDisposeService groupDisposeService;

    /**
     * 查询群体处置记录列表
     */
    // @PreAuthorize("@ss.hasPermi('group:dispose:list')")
    @GetMapping("/list")
    public TableDataInfo list(GroupDispose groupDispose)
    {
        startPage();
        List<GroupDispose> list = groupDisposeService.selectGroupDisposeList(groupDispose);
        return getDataTable(list);
    }

    /**
     * 导出群体处置记录列表
     */
    // @PreAuthorize("@ss.hasPermi('group:dispose:export')")
    @Log(title = "群体处置记录", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, GroupDispose groupDispose)
    {
        List<GroupDispose> list = groupDisposeService.selectGroupDisposeList(groupDispose);
        ExcelUtil<GroupDispose> util = new ExcelUtil<GroupDispose>(GroupDispose.class);
        util.exportExcel(response, list, "群体处置记录数据");
    }

    /**
     * 获取群体处置记录详细信息
     */
    // @PreAuthorize("@ss.hasPermi('group:dispose:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(groupDisposeService.selectGroupDisposeById(id));
    }

    /**
     * 新增群体处置记录
     */
    @PreAuthorize("@ss.hasPermi('group:dispose:add')")
    @Log(title = "群体处置记录", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody GroupDispose groupDispose)
    {
        return toAjax(groupDisposeService.insertGroupDispose(groupDispose));
    }

    /**
     * 修改群体处置记录
     */
    // @PreAuthorize("@ss.hasPermi('group:dispose:edit')")
    @Log(title = "群体处置记录", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody GroupDispose groupDispose)
    {
        return toAjax(groupDisposeService.updateGroupDispose(groupDispose));
    }

    /**
     * 删除群体处置记录
     */
    // @PreAuthorize("@ss.hasPermi('group:dispose:remove')")
    @Log(title = "群体处置记录", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(groupDisposeService.deleteGroupDisposeByIds(ids));
    }
}
