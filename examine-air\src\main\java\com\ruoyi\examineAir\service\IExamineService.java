package com.ruoyi.examineAir.service;

import java.util.List;
import com.ruoyi.examineAir.domain.Examine;
import com.ruoyi.examineAir.vo.BigScreenExamineVo;
import com.ruoyi.examineAir.vo.BigScreenPageRegionStatisticsVo;
import com.ruoyi.examineAir.vo.ExamineVo;

/**
 * 考核晾晒列Service接口
 * 
 * <AUTHOR>
 * @date 2023-04-19
 */
public interface IExamineService 
{
    /**
     * 查询考核晾晒列
     * 
     * @param id 考核晾晒列主键
     * @return 考核晾晒列
     */
    public ExamineVo selectExamineById(Long id);

    /**
     * 查询考核晾晒列列表
     * 
     * @param examine 考核晾晒列
     * @return 考核晾晒列集合
     */
    public List<Examine> selectExamineList(Examine examine);

    /**
     * 新增考核晾晒列
     * 
     * @param examine 考核晾晒列
     * @return 结果
     */
    public int insertExamine(Examine examine);

    /**
     * 修改考核晾晒列
     * 
     * @param examine 考核晾晒列
     * @return 结果
     */
    public int updateExamine(Examine examine);

    /**
     * 批量删除考核晾晒列
     * 
     * @param ids 需要删除的考核晾晒列主键集合
     * @return 结果
     */
    public int deleteExamineByIds(Long[] ids);

    /**
     * 删除考核晾晒列信息
     * 
     * @param id 考核晾晒列主键
     * @return 结果
     */
    public int deleteExamineById(Long id);

    /**
     * 新增考核晾晒列
     *
     * @param examineVo 考核晾晒列
     * @return 结果
     */
    public int insertExamine(ExamineVo examineVo);

    /**
     * 发布考核晾晒
     * @param examine
     * @return
     */
    int release(Examine examine);


    /**
     * 修改考核晾晒列
     *
     * @param examine 考核晾晒列
     * @return 结果
     */
    public int updateExamine(ExamineVo examine);

    BigScreenExamineVo bigScreenExamineDetails(Long id);

    /**
     * 获取最新一条发布的数据
     * @return
     */
    List<BigScreenPageRegionStatisticsVo> getNewAreaCount();

    /**
     * 按区域获取最新一条的考核评价
     * @param area
     * @return
     */
    BigScreenExamineVo  regionExamine(String area);

}
