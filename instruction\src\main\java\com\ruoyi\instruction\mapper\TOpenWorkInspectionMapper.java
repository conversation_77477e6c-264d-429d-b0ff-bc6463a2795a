package com.ruoyi.instruction.mapper;

import com.ruoyi.instruction.domain.TOpenWorkInspection;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * 工作督查数据对接Mapper接口
 *
 * <AUTHOR>
 * @date 2024-08-08
 */
public interface TOpenWorkInspectionMapper
{
    /**
     * 查询工作督查数据对接
     *
     * @param id 工作督查数据对接主键
     * @return 工作督查数据对接
     */
    public TOpenWorkInspection selectTOpenWorkInspectionById(Long id);

    /**
     * 查询工作督查数据对接列表
     *
     * @param tOpenWorkInspection 工作督查数据对接
     * @return 工作督查数据对接集合
     */
    public List<TOpenWorkInspection> selectTOpenWorkInspectionList(TOpenWorkInspection tOpenWorkInspection);

    /**
     * 新增工作督查数据对接
     *
     * @param tOpenWorkInspection 工作督查数据对接
     * @return 结果
     */
    public int insertTOpenWorkInspection(TOpenWorkInspection tOpenWorkInspection);

    /**
     * 修改工作督查数据对接
     *
     * @param tOpenWorkInspection 工作督查数据对接
     * @return 结果
     */
    public int updateTOpenWorkInspection(TOpenWorkInspection tOpenWorkInspection);

    /**
     * 删除工作督查数据对接
     *
     * @param id 工作督查数据对接主键
     * @return 结果
     */
    public int deleteTOpenWorkInspectionById(Long id);

    /**
     * 批量删除工作督查数据对接
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteTOpenWorkInspectionByIds(Long[] ids);

    /**
     * 根据ids获取工作督查集合
     * @param ids
     * @return
     */
    List<TOpenWorkInspection> selectOpenWorkByIds(Long[] ids);

    /**
     * 将工作督查状态置为已接收
     * @param workId
     */
    void updateTOpenWorkTurnDown(@Param("workId") Long workId);

    /**
     * 获取统计数
     * @param tOpenWorkInspection
     * @return
     */
    Map<String, Object> getStatistics(TOpenWorkInspection tOpenWorkInspection);

    /**
     * 根据id更新处理状态
     * @param workId
     */
    void updateDealStepById(@Param("id") Long workId);

    /**
     * 根据id更新工作督办乡镇
     * @param workId
     * @param transferDept
     */
    void updateTownByWorkId(@Param("id") Long workId, @Param("town") String transferDept);
}
