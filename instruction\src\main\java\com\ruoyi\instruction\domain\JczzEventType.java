package com.ruoyi.instruction.domain;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 基层智治事件类型对象 t_jczz_event_type
 * 
 * <AUTHOR>
 * @date 2023-08-03
 */
public class JczzEventType extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键 */
    private Long id;

    /** 一级事件名称 */
    @Excel(name = "一级事件名称")
    private String firstName;

    /** 一级事件code */
    @Excel(name = "一级事件code")
    private String firstCode;

    /** 二级事件名称 */
    @Excel(name = "二级事件名称")
    private String secondName;

    /** 二级事件code */
    @Excel(name = "二级事件code")
    private String secondCode;

    /** 三级事件名称 */
    @Excel(name = "三级事件名称")
    private String thirdName;

    /** 三级事件code */
    @Excel(name = "三级事件code")
    private String thirdCode;

    /** 状态 1：正常 9：删除 */
    @Excel(name = "状态 1：正常 9：删除")
    private String status;

    public void setId(Long id) 
    {
        this.id = id;
    }

    public Long getId() 
    {
        return id;
    }
    public void setFirstName(String firstName) 
    {
        this.firstName = firstName;
    }

    public String getFirstName() 
    {
        return firstName;
    }
    public void setFirstCode(String firstCode) 
    {
        this.firstCode = firstCode;
    }

    public String getFirstCode() 
    {
        return firstCode;
    }
    public void setSecondName(String secondName) 
    {
        this.secondName = secondName;
    }

    public String getSecondName() 
    {
        return secondName;
    }
    public void setSecondCode(String secondCode) 
    {
        this.secondCode = secondCode;
    }

    public String getSecondCode() 
    {
        return secondCode;
    }
    public void setThirdName(String thirdName) 
    {
        this.thirdName = thirdName;
    }

    public String getThirdName() 
    {
        return thirdName;
    }
    public void setThirdCode(String thirdCode) 
    {
        this.thirdCode = thirdCode;
    }

    public String getThirdCode() 
    {
        return thirdCode;
    }
    public void setStatus(String status) 
    {
        this.status = status;
    }

    public String getStatus() 
    {
        return status;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("firstName", getFirstName())
            .append("firstCode", getFirstCode())
            .append("secondName", getSecondName())
            .append("secondCode", getSecondCode())
            .append("thirdName", getThirdName())
            .append("thirdCode", getThirdCode())
            .append("status", getStatus())
            .toString();
    }
}
