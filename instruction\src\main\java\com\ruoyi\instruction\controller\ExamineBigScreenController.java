package com.ruoyi.instruction.controller;

import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.examineAir.domain.Examine;
import com.ruoyi.examineAir.domain.ExamineBillboard;
import com.ruoyi.examineAir.service.IExamineBillboardService;
import com.ruoyi.examineAir.service.IExamineService;
import com.ruoyi.examineAir.vo.BigScreenExamineVo;
import com.ruoyi.examineAir.vo.ExamineVo;
import com.ruoyi.instruction.domain.InstructionGroup;
import com.ruoyi.instruction.domain.rspVo.BigScreenBillboardVo;
import com.ruoyi.instruction.domain.rspVo.BigScreenGroupDetailsVo;
import com.ruoyi.instruction.domain.rspVo.BigScreenGroupStatisticalResultsVo;
import com.ruoyi.instruction.service.IInstrucationPersonService;
import com.ruoyi.instruction.service.IInstructionEndService;
import com.ruoyi.instruction.service.IInstructionGroupService;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.*;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 考核晾晒驾驶舱接口
 * <AUTHOR>
 * @version 1.0
 * @date 2023/4/17 14:48
 * 群体工作台controller
 */
@RestController
@RequestMapping("/bigScreen/examine")
public class ExamineBigScreenController {

    @Autowired
    private IExamineBillboardService iExamineBillboardService;

    @Autowired
    private IExamineService examineService;

    /**
     * 红黄榜
     * @return
     */
    @GetMapping("/billboard")
    public AjaxResult billboard(){
        BigScreenBillboardVo bigScreenBillboardVo=new BigScreenBillboardVo();
        ExamineBillboard examineBillboard=new ExamineBillboard();
        examineBillboard.setType(0L);
        examineBillboard.setStatus(1L);
        List<ExamineBillboard> red = iExamineBillboardService.selectExamineBillboardList(examineBillboard);
        bigScreenBillboardVo.setRedList(red);
        examineBillboard.setType(1L);
        examineBillboard.setStatus(1L);
        List<ExamineBillboard> yellow = iExamineBillboardService.selectExamineBillboardList(examineBillboard);
        bigScreenBillboardVo.setYellowList(yellow);
        return  AjaxResult.success(bigScreenBillboardVo);
    }

    /**
     * 考核晾晒列表
     * @param examine
     * @return
     */
    @GetMapping("/list")
    public AjaxResult list(Examine examine)
    {
        examine.setStatus(1L);
        List<Examine> list = examineService.selectExamineList(examine);
        return  AjaxResult.success(list);
    }

    /**
     * 获取考核晾晒列详细信息
     */
    @GetMapping(value = "/{id}")
    public AjaxResult bigScreenExamineDetails(@PathVariable("id") Long id)
    {
        BigScreenExamineVo examineVo = examineService.bigScreenExamineDetails(id);

        return AjaxResult.success(examineVo);
    }




}
