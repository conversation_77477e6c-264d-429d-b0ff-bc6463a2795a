package com.ruoyi.instruction.controller;

import cn.hutool.http.HttpResponse;
import cn.hutool.http.HttpUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.utils.AESUtil;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.instruction.domain.InstructionFollow;
import com.ruoyi.instruction.domain.InstructionInfo;
import com.ruoyi.instruction.domain.rspVo.ToFeedbackRsp;
import com.ruoyi.instruction.domain.rspVo.ToReceiveRsp;
import com.ruoyi.instruction.mapper.InstructionInfoMapper;
import com.ruoyi.instruction.service.IInstructionFollowService;
import com.ruoyi.instruction.service.IYjEventService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.net.URLEncoder;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 金安治工作台
 * <AUTHOR>
 * @version 1.0
 * @date 2025/5/6 9:32
 */
@RestController
@RequestMapping("/jaz/workbench")
public class JazWorkbenchController extends BaseController {

    @Autowired
    private InstructionInfoMapper infoMapper;

    @Autowired
    private IYjEventService yjEventService;

    @Autowired
    private IInstructionFollowService instructionFollowService;

    /**
     * 我的待办
     * @param startTime
     * @param endTime
     * @return
     */
    @GetMapping("/myTodo")
    public AjaxResult myTodo(String startTime, String endTime) throws Exception {
        Map<String, Object> map = infoMapper.findMyTodo(startTime, endTime);
        Map<String, Object> mapNoTime = infoMapper.findMyTodo(null, null);
        Long yjb = (Long) map.get("yjb");
        Long yxh = (Long) map.get("yxh");
        map.put("czz", yjb - yxh);
        //查询待交办
        Long djb = toBeAssign(startTime, endTime);
        //查询矛盾纠纷预警待交办数据
        Long toAssign = yjEventService.findToAssign(startTime, endTime);
        map.put("djb", djb + toAssign);
        map.put("djb-yj", toAssign);
        map.put("djb-spsb", djb);
        Long yqdsh = (Long) mapNoTime.get("yqdsh");
        Long dxh = (Long) mapNoTime.get("dxh");
        map.put("dxh", dxh);
        map.put("yqdsh", yqdsh);
        map.put("dcl", yqdsh + dxh + toAssign+ djb);
        return AjaxResult.success(map);
    }

    /**
     * 查询待交办
     * @param startTime
     * @param endTime
     * @return
     */
    private Long toBeAssign(final String startTime, final String endTime) throws Exception {
        long timestamp = System.currentTimeMillis();
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("timestemp",timestamp);
        //json转字符串
        String json = JSON.toJSONString(jsonObject);
        String encrypt = AESUtil.encrypt(json);
        HttpResponse response = HttpUtil.createPost("https://jazz.swzfw.jinhua.gov.cn:5443/shujinApi/fulEvent/getSpsbUnAssignData").body(encrypt,"text/plain").execute();
        String body = response.body();
        System.out.println(body);
        //字符串转json
        JSONObject jsonObject1 = JSON.parseObject(body);
        String data = (String) jsonObject1.get("data");
        if (data == null){
            return null;
        }
        String decrypt = AESUtil.decrypt(data);
        return Long.valueOf(decrypt);
    }

    /**
     * 查询我的关注
     * type 1:我的关注 2:需跟进
     * @return
     */
    @GetMapping("/myFollow")
    public AjaxResult myFollow(Integer type) {
        List<InstructionInfo> infoList = new ArrayList<>();
        if (type != null && type == 1) {
            InstructionFollow instructionFollow = instructionFollowService.selectInstructionFollowById(SecurityUtils.getUserId());
            if (instructionFollow != null) {
                List<String> followIds = Arrays.asList(instructionFollow.getInstructionIds().split(","));
                if (followIds.size() > 0) {
                    infoList = infoMapper.findMyFollow(followIds, null);
                }
            }
        } else if (type != null && type == 2) {
            //查询出易激化集合
            List<Long> wkMzxIds = infoMapper.selectWkMzxIds();
            if (wkMzxIds.size() > 0) {
                infoList = infoMapper.findMyFollow(null, wkMzxIds);
            }
        }
        return AjaxResult.success(infoList);
    }

    /**
     * 获取市级矛盾纠纷上报
     * @param startTime
     * @param endTime
     * @return
     * @throws Exception
     */
    @GetMapping("/getCityContradiction")
    public AjaxResult getCityContradiction(String startTime, String endTime,Integer type) throws Exception {
        long timestamp = System.currentTimeMillis();
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("timestemp",timestamp);
        jsonObject.put("searchStartTime",startTime);
        jsonObject.put("searchEndTime",endTime);
        //json转字符串
        String json = JSON.toJSONString(jsonObject);
        String encrypt = AESUtil.encrypt(json);
        String url = "https://jazz.swzfw.jinhua.gov.cn:5443/shujinApi/fulEvent/getSpsbDeptData";
        if (type != null && type == 2) {
            url = "https://jazz.swzfw.jinhua.gov.cn:5443/shujinApi/fulEvent/getSpsbAreaData";
        }
        HttpResponse response = HttpUtil.createPost(url).body(encrypt,"text/plain").execute();
        String body = response.body();
        //字符串转json
        JSONObject jsonObject1 = JSON.parseObject(body);
        String data = (String) jsonObject1.get("data");
        if (data == null){
            return AjaxResult.error("未查询到数据");
        }
        String decrypt = AESUtil.decrypt(data);
        //字符串转json
        JSONObject jsonObject2 = JSON.parseObject(decrypt);

        return AjaxResult.success(jsonObject2);

    }

    @GetMapping("/caseList")
    public TableDataInfo caseList(InstructionInfo info) {
        startPage();
        List<InstructionInfo> list = infoMapper.getCaseList(info);
        return getDataTable(list);
    }

    /**
     * 更新案例状态
     * @param id
     * @return
     */
    @GetMapping("/updateCase")
    public AjaxResult updateCase(Long id) {
        infoMapper.updateCase(id);
        return AjaxResult.success();
    }

    /**
     * 预警研判情况
     * @param startTime
     * @param endTime
     * @return
     */
    @GetMapping("/warnSituation")
    public AjaxResult warnSituation(String startTime, String endTime) {
        List<Map<String,Object>> map = yjEventService.warnSituation(startTime,endTime);
        return AjaxResult.success(map);
    }

    /**
     * 查询应接收未接收数据
     * @return
     */
    @GetMapping("/noReceive")
    public AjaxResult noReveive() {
        List<ToReceiveRsp> list = infoMapper.getNoReceive(true, null, null, null, null);
        Map<String, Object> map = new HashMap<>();
        map.put("instructionCount", 0);
        map.put("deptCount", 0);
        //获取应接收未接收数据
        if (list.size() > 0) {
            long count = list.stream().map(ToReceiveRsp::getId).distinct().count();
            map.put("instructionCount", count);
            map.put("deptCount", list.size());
        }
        //获取应处置未处置数据
        //1、查询出市级双排双办、预警指令id
        List<Long> ids = infoMapper.getSpsbAndyjIds(true, null, null, null);
        //查询出详细数据
        List<ToFeedbackRsp> toFeedbackRspList = infoMapper.getToFeedbackList(true, ids);
        // map.put("toFeedback", toFeedbackRspList);
        map.put("feedbackCount", 0);
        map.put("feedbackDeptCount", 0);
        if (toFeedbackRspList.size() != 0) {
            long count = toFeedbackRspList.stream().filter(item -> item.getFeedbackTime() != null).map(ToFeedbackRsp::getId).distinct().count();
            long deptCount = toFeedbackRspList.stream().filter(item -> item.getFeedbackTime() != null).count();
            map.put("feedbackCount", count);
            map.put("feedbackDeptCount", deptCount);
        }
        return AjaxResult.success(map);
    }

    /**
     * 导出应接收未接收应处置未处置数据
     * @param response
     * @throws IOException
     */
    @PostMapping("/exportNoReceiveNoFeedback")
    public void export(HttpServletResponse response) throws IOException {
        response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
        response.setCharacterEncoding("utf-8");
        // 这里URLEncoder.encode可以防止中文乱码 当然和easyexcel没有关系
        String fileName = URLEncoder.encode("指令办理情况", "UTF-8").replaceAll("\\+", "%20");
        response.setHeader("Content-disposition", "attachment;filename*=utf-8''" + fileName + ".xlsx");
        ExcelWriter excelWriter = EasyExcel.write(response.getOutputStream()).build();
        //查询应接收未接收数据
        List<ToReceiveRsp> list = infoMapper.getNoReceive(true, null, null, null, null);

        WriteSheet writeSheet = EasyExcel.writerSheet(0, "应接收未接收").head(ToReceiveRsp.class).build();
        excelWriter.write(list, writeSheet);
        //查询应处置未处置数据
        //1、查询出市级双排双办、预警指令id
        List<Long> ids = infoMapper.getSpsbAndyjIds(true, null, null, null);
        //查询出详细数据
        List<ToFeedbackRsp> toFeedbackRspList = infoMapper.getToFeedbackList(true, ids);
        List<ToFeedbackRsp> collect = toFeedbackRspList.stream().filter(item -> item.getFeedbackTime() != null).collect(Collectors.toList());
        WriteSheet feedbackSheet = EasyExcel.writerSheet(1, "应处置未处置").head(ToFeedbackRsp.class).build();
        excelWriter.write(collect, feedbackSheet);
        // 关闭 ExcelWriter
        excelWriter.finish();
    }

}
