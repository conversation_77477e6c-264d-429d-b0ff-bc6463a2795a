package com.ruoyi.instruction.domain.rspVo;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * 风险隐患
 * 
 * <AUTHOR>
 * @date 2023-08-07
 */
@Data
public class RiskHiddenDangerRsp implements Serializable
{
    private static final long serialVersionUID = 1L;
    /**
     * 总数
     */
    private Integer total;
    /**
     * 未化解数
     */
    private Integer whjzs;
    /**
     * 化解数
     */
    private Integer hjs;
    /**
     * 重大风险
     */
    private Integer zdfx;
    /**
     * 高风险
     */
    private Integer gfx;
    /**
     * 中风险
     */
    private Integer zfx;
    /**
     * 低风险
     */
    private Integer dfx;
    /**
     * 已解决重大风险
     */
    private Integer jjzdfx;
    /**
     * 已解决高风险
     */
    private Integer jjgfx;
    /**
     * 已解决中风险
     */
    private Integer jjzfx;
    /**
     * 已解决低风险
     */
    private Integer jjdfx;
    /**
     * 化解率
     */
    private BigDecimal hjl;
    /**
     * 重大风险占比率
     */
    private BigDecimal zdfxzbl;
    /**
     * 高风险占比率
     */
    private BigDecimal gfxzbl;
    /**
     * 中风险占比率
     */
    private BigDecimal zfxzbl;
    /**
     * 低大风险占比率
     */
    private BigDecimal dfxzbl;
    /**
     * 重大风险化解率
     */
    private BigDecimal zdfxhjl;
    /**
     * 高风险化解率
     */
    private BigDecimal gfxhjl;
    /**
     * 中风险化解率
     */
    private BigDecimal zfxhjl;
    /**
     * 低大风险化解率
     */
    private BigDecimal dfxhjl;
    /**
     * 重大风险前5
     */
    private List<RiskTypeTopRsp> list;
    /**
     * 季度排查情况
     */
    private List<FxpcjdpcqkRspVp> jdpcqk;

}
