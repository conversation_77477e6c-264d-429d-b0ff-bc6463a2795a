package com.ruoyi.instruction.domain;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 研判阈值信息表对象 t_threshold_info
 * 
 * <AUTHOR>
 * @date 2023-05-05
 */
public class ThresholdInfo extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键 */
    private Long id;

    /** 阈值名称 */
    @Excel(name = "阈值名称")
    private String thresholdName;

    /** 阈值类型 */
    @Excel(name = "阈值类型")
    private String thresholdType;

    /** 阈值规则 */
    @Excel(name = "阈值规则")
    private String thresholdRule;

    /** 阈值数 */
    @Excel(name = "阈值数")
    private String thresholdValue;

    /** 分析情况 */
    @Excel(name = "分析情况")
    private String analyseSituation;

    /** 更新频率 */
    @Excel(name = "更新频率")
    private Long frequency;

    /** 更新频率单位 */
    @Excel(name = "更新频率单位")
    private String frequencyUnit;

    /** 预警内容 */
    @Excel(name = "预警内容")
    private String content;

    /** 1:正常 9：删除 */
    @Excel(name = "1:正常 9：删除")
    private String status;

    /** 定时任务id */
    @Excel(name = "定时任务id")
    private Long jobId;

    /** 统计数据时间范围 */
    @Excel(name = "统计数据时间范围")
    private Long timeRange;

    /** 统计时间单位 */
    @Excel(name = "统计时间单位")
    private String timeUnit;

    public void setId(Long id) 
    {
        this.id = id;
    }

    public Long getId() 
    {
        return id;
    }
    public void setThresholdName(String thresholdName) 
    {
        this.thresholdName = thresholdName;
    }

    public String getThresholdName() 
    {
        return thresholdName;
    }
    public void setThresholdType(String thresholdType) 
    {
        this.thresholdType = thresholdType;
    }

    public String getThresholdType() 
    {
        return thresholdType;
    }
    public void setThresholdRule(String thresholdRule) 
    {
        this.thresholdRule = thresholdRule;
    }

    public String getThresholdRule() 
    {
        return thresholdRule;
    }
    public void setThresholdValue(String thresholdValue) 
    {
        this.thresholdValue = thresholdValue;
    }

    public String getThresholdValue() 
    {
        return thresholdValue;
    }
    public void setAnalyseSituation(String analyseSituation) 
    {
        this.analyseSituation = analyseSituation;
    }

    public String getAnalyseSituation() 
    {
        return analyseSituation;
    }
    public void setFrequency(Long frequency) 
    {
        this.frequency = frequency;
    }

    public Long getFrequency() 
    {
        return frequency;
    }
    public void setFrequencyUnit(String frequencyUnit) 
    {
        this.frequencyUnit = frequencyUnit;
    }

    public String getFrequencyUnit() 
    {
        return frequencyUnit;
    }
    public void setContent(String content) 
    {
        this.content = content;
    }

    public String getContent() 
    {
        return content;
    }
    public void setStatus(String status) 
    {
        this.status = status;
    }

    public String getStatus() 
    {
        return status;
    }
    public void setJobId(Long jobId) 
    {
        this.jobId = jobId;
    }

    public Long getJobId() 
    {
        return jobId;
    }
    public void setTimeRange(Long timeRange) 
    {
        this.timeRange = timeRange;
    }

    public Long getTimeRange() 
    {
        return timeRange;
    }
    public void setTimeUnit(String timeUnit) 
    {
        this.timeUnit = timeUnit;
    }

    public String getTimeUnit() 
    {
        return timeUnit;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("thresholdName", getThresholdName())
            .append("thresholdType", getThresholdType())
            .append("thresholdRule", getThresholdRule())
            .append("thresholdValue", getThresholdValue())
            .append("analyseSituation", getAnalyseSituation())
            .append("frequency", getFrequency())
            .append("frequencyUnit", getFrequencyUnit())
            .append("content", getContent())
            .append("status", getStatus())
            .append("createBy", getCreateBy())
            .append("createTime", getCreateTime())
            .append("updateBy", getUpdateBy())
            .append("updateTime", getUpdateTime())
            .append("jobId", getJobId())
            .append("timeRange", getTimeRange())
            .append("timeUnit", getTimeUnit())
            .toString();
    }
}
