package com.ruoyi.instruction.mapper;

import java.util.List;

import com.ruoyi.instruction.domain.rspVo.BigScreenJcjcSpecialWorkRspVo;
import org.apache.ibatis.annotations.Mapper;
import com.ruoyi.instruction.domain.TJazzJcjcSpecialWork;
import org.apache.ibatis.annotations.Param;

/**
 * 基层基础-专项工作Mapper接口
 * 
 * <AUTHOR>
 * @date 2023-07-17
 */
@Mapper
public interface TJazzJcjcSpecialWorkMapper
{
    /**
     * 查询基层基础-专项工作
     * 
     * @param id 基层基础-专项工作ID
     * @return 基层基础-专项工作
     */
    public TJazzJcjcSpecialWork selectTJazzJcjcSpecialWorkById(Long id);

    /**
     * 查询基层基础-专项工作列表
     * 
     * @param tJazzJcjcSpecialWork 基层基础-专项工作
     * @return 基层基础-专项工作集合
     */
    public List<TJazzJcjcSpecialWork> selectTJazzJcjcSpecialWorkList(TJazzJcjcSpecialWork tJazzJcjcSpecialWork);

    /**
     * 新增基层基础-专项工作
     * 
     * @param tJazzJcjcSpecialWork 基层基础-专项工作
     * @return 结果
     */
    public int insertTJazzJcjcSpecialWork(TJazzJcjcSpecialWork tJazzJcjcSpecialWork);

    /**
     * 修改基层基础-专项工作
     * 
     * @param tJazzJcjcSpecialWork 基层基础-专项工作
     * @return 结果
     */
    public int updateTJazzJcjcSpecialWork(TJazzJcjcSpecialWork tJazzJcjcSpecialWork);

    /**
     * 删除基层基础-专项工作
     * 
     * @param id 基层基础-专项工作ID
     * @return 结果
     */
    public int deleteTJazzJcjcSpecialWorkById(Long id);

    /**
     * 批量删除基层基础-专项工作
     * 
     * @param ids 需要删除的数据ID
     * @return 结果
     */
    public int deleteTJazzJcjcSpecialWorkByIds(Long[] ids);

    /**
     * 根据区域查询，按级别分组
     * @param area
     */
    public List<BigScreenJcjcSpecialWorkRspVo> selectByArea(@Param("area") String area);
}
