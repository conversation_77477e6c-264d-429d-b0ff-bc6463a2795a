package com.ruoyi.instruction.domain.rspVo;


import lombok.Data;


@Data
public class RiskSummaryRspVo {

    /**
     * 排查总数
     */
    private Long totalCount = 0L;

    /**
     * 未化解总数
     */
    private Long totalUnresolvedCount = 0L;

    /**
     * 高风险以上(重大+高)总件数
     */
    private Long highRiskOrAboveCount = 0L;

    /**
     * 高风险以上(重大+高)未化解件数
     */
    private Long unresolvedHighRiskOrAboveCount = 0L;
}