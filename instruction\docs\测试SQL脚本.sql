-- 市级指令办理情况测试SQL脚本

-- 1. 测试超时接收数量查询
-- 查询超时接收的指令数量（以东阳市为例）
SELECT COUNT(DISTINCT a.id) as timeout_receive_count
FROM t_instruction_info a
INNER JOIN t_instruction_receive b ON a.id = b.instrucation_id
WHERE a.status = 1 
  AND a.create_dept_id = 202  -- 市级部门ID
  AND a.instruction_type IN (3, 6)  -- 双排双办和预警研判
  AND b.status = 1
  AND b.receive_time IS NULL  -- 未接收
  AND DATEDIFF(NOW(), a.assign_time) > 
      CASE a.emergency_degree
          WHEN '特急' THEN 1
          WHEN '紧急' THEN 2
          WHEN '一般' THEN 3
          ELSE 3
      END
  AND b.receive_dept LIKE '东阳市%';  -- 东阳市相关部门

-- 2. 测试超时处置数量查询
-- 查询超时处置的指令数量
SELECT COUNT(DISTINCT a.id) as timeout_dispose_count
FROM t_instruction_info a
INNER JOIN t_instruction_receive b ON a.id = b.instrucation_id
LEFT JOIN (
    SELECT instruction_id, receive_id, MAX(feedback_time) as last_feedback_time
    FROM t_instruction_feedback 
    WHERE status = 1 
    GROUP BY instruction_id, receive_id
) f ON a.id = f.instruction_id AND b.id = f.receive_id
WHERE a.status = 1 
  AND a.create_dept_id = 202
  AND a.instruction_type IN (3, 6)
  AND a.end_time IS NULL  -- 未销号
  AND b.status = 1
  AND b.receive_time IS NOT NULL  -- 已接收
  AND (f.last_feedback_time IS NULL OR 
       DATEDIFF(NOW(), f.last_feedback_time) > 
       CASE a.emergency_degree
           WHEN '特急' THEN 7
           WHEN '紧急' THEN 15
           WHEN '一般' THEN 30
           ELSE 30
       END)
  AND b.receive_dept LIKE '东阳市%';

-- 3. 测试应处置未处置数据查询
-- 查询应处置未处置的单位数和部门数
SELECT 
    COUNT(DISTINCT a.id) as unit_count,
    COUNT(DISTINCT b.receive_dept) as dept_count
FROM t_instruction_info a
INNER JOIN t_instruction_receive b ON a.id = b.instrucation_id
LEFT JOIN (
    SELECT instruction_id, receive_id, MAX(feedback_time) as last_feedback_time
    FROM t_instruction_feedback 
    WHERE status = 1 AND feedback_is_end != 1  -- 未办结
    GROUP BY instruction_id, receive_id
) f ON a.id = f.instruction_id AND b.id = f.receive_id
WHERE a.status = 1 
  AND a.create_dept_id = 202
  AND a.instruction_type IN (3, 6)
  AND a.end_time IS NULL
  AND b.status = 1
  AND b.receive_time IS NOT NULL
  AND (f.last_feedback_time IS NULL OR 
       DATEDIFF(NOW(), COALESCE(f.last_feedback_time, b.receive_time)) > 
       CASE a.emergency_degree
           WHEN '特急' THEN 7
           WHEN '紧急' THEN 15
           WHEN '一般' THEN 30
           ELSE 30
       END)
  AND b.receive_dept LIKE '东阳市%';

-- 4. 测试待反馈数据查询（按紧急程度分类）
-- 查询待反馈的指令数量，按紧急程度分类
SELECT 
    SUM(CASE WHEN a.emergency_degree = '特急' THEN 1 ELSE 0 END) as days7,
    SUM(CASE WHEN a.emergency_degree = '紧急' THEN 1 ELSE 0 END) as days15,
    SUM(CASE WHEN a.emergency_degree = '一般' THEN 1 ELSE 0 END) as days30
FROM t_instruction_info a
INNER JOIN t_instruction_receive b ON a.id = b.instrucation_id
INNER JOIN t_instruction_transfer t ON b.id = t.receive_id
LEFT JOIN (
    SELECT instruction_id, receive_id, MAX(feedback_time) as last_feedback_time,
           MAX(CASE WHEN feedback_is_end = 1 THEN 1 ELSE 0 END) as is_completed
    FROM t_instruction_feedback 
    WHERE status = 1 
    GROUP BY instruction_id, receive_id
) f ON a.id = f.instruction_id AND b.id = f.receive_id
WHERE a.status = 1 
  AND a.create_dept_id = 202
  AND a.instruction_type IN (3, 6)
  AND a.end_time IS NULL
  AND b.status = 1
  AND t.status = 1
  AND b.receive_time IS NOT NULL
  AND (f.is_completed IS NULL OR f.is_completed = 0)  -- 未完成
  AND (
      (f.last_feedback_time IS NOT NULL AND 
       DATEDIFF(NOW(), f.last_feedback_time) >= 
       CASE a.emergency_degree
           WHEN '特急' THEN 7
           WHEN '紧急' THEN 15
           WHEN '一般' THEN 30
           ELSE 30
       END)
      OR 
      (f.last_feedback_time IS NULL AND t.transfer_time IS NOT NULL AND
       DATEDIFF(NOW(), t.transfer_time) >= 
       CASE a.emergency_degree
           WHEN '特急' THEN 7
           WHEN '紧急' THEN 15
           WHEN '一般' THEN 30
           ELSE 30
       END)
  )
  AND b.receive_dept LIKE '东阳市%';

-- 5. 测试超时接收详细列表查询
-- 查询超时接收的指令详细信息
SELECT 
    a.id,
    a.instruction_title,
    a.emergency_degree,
    CASE a.instruction_type
        WHEN 3 THEN '双排双办'
        WHEN 6 THEN '预警研判'
        ELSE '其他'
    END as instruction_type,
    a.assign_time,
    a.handle_time,
    b.receive_dept,
    CASE b.is_mzx
        WHEN 1 THEN '协办单位'
        WHEN 2 THEN '主办单位'
        ELSE '未知'
    END as unit_type,
    DATEDIFF(NOW(), a.assign_time) as overdue_days
FROM t_instruction_info a
INNER JOIN t_instruction_receive b ON a.id = b.instrucation_id
WHERE a.status = 1 
  AND a.create_dept_id = 202
  AND a.instruction_type IN (3, 6)
  AND b.status = 1
  AND b.receive_time IS NULL
  AND DATEDIFF(NOW(), a.assign_time) > 
      CASE a.emergency_degree
          WHEN '特急' THEN 1
          WHEN '紧急' THEN 2
          WHEN '一般' THEN 3
          ELSE 3
      END
  AND b.receive_dept LIKE '东阳市%'
ORDER BY a.assign_time DESC
LIMIT 10;

-- 6. 验证数据完整性的查询
-- 检查相关表的数据情况
SELECT 
    '指令信息表' as table_name,
    COUNT(*) as total_count,
    COUNT(CASE WHEN status = 1 THEN 1 END) as active_count,
    COUNT(CASE WHEN create_dept_id = 202 THEN 1 END) as city_level_count,
    COUNT(CASE WHEN instruction_type IN (3, 6) THEN 1 END) as target_type_count
FROM t_instruction_info

UNION ALL

SELECT 
    '指令接收表' as table_name,
    COUNT(*) as total_count,
    COUNT(CASE WHEN status = 1 THEN 1 END) as active_count,
    COUNT(CASE WHEN receive_time IS NOT NULL THEN 1 END) as received_count,
    COUNT(CASE WHEN receive_dept LIKE '%东阳市%' THEN 1 END) as dongyang_count
FROM t_instruction_receive

UNION ALL

SELECT 
    '指令反馈表' as table_name,
    COUNT(*) as total_count,
    COUNT(CASE WHEN status = 1 THEN 1 END) as active_count,
    COUNT(CASE WHEN feedback_is_end = 1 THEN 1 END) as completed_count,
    COUNT(CASE WHEN feedback_time IS NOT NULL THEN 1 END) as feedback_count
FROM t_instruction_feedback

UNION ALL

SELECT 
    '指令转交表' as table_name,
    COUNT(*) as total_count,
    COUNT(CASE WHEN status = 1 THEN 1 END) as active_count,
    COUNT(CASE WHEN transfer_time IS NOT NULL THEN 1 END) as transferred_count,
    0 as extra_count
FROM t_instruction_transfer;
