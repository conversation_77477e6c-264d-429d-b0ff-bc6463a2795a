package com.ruoyi.instruction.controller;

import java.util.ArrayList;
import java.util.List;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;

import com.ruoyi.common.core.domain.entity.SysDept;
import com.ruoyi.common.core.domain.entity.SysUser;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.instruction.domain.Risk;
import com.ruoyi.instruction.domain.RiskDetails;
import com.ruoyi.instruction.domain.rspVo.ProblemBuildExport;
import org.springframework.beans.BeanUtils;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.instruction.domain.ThreeSenses;
import com.ruoyi.instruction.service.IThreeSensesService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;
import org.springframework.web.multipart.MultipartFile;

/**
 * 三感三率Controller
 *
 * <AUTHOR>
 * @date 2024-04-02
 */
@RestController
@RequestMapping("/system/senses")
public class ThreeSensesController extends BaseController
{
    @Autowired
    private IThreeSensesService threeSensesService;

    /**
     * 查询三感三率列表
     */
    @PreAuthorize("@ss.hasPermi('system:senses:list')")
    @GetMapping("/list")
    public TableDataInfo list(ThreeSenses threeSenses)
    {
        startPage();
        List<ThreeSenses> list = threeSensesService.selectThreeSensesList(threeSenses);
        return getDataTable(list);
    }

    /**
     * 导出三感三率列表
     */
    @PreAuthorize("@ss.hasPermi('system:senses:export')")
    @Log(title = "三感三率", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, ThreeSenses threeSenses)
    {
        List<ThreeSenses> list = threeSensesService.selectThreeSensesList(threeSenses);
        ExcelUtil<ThreeSenses> util = new ExcelUtil<ThreeSenses>(ThreeSenses.class);
        util.exportExcel(response, list, "三感三率数据");
    }

    /**
     * 获取三感三率详细信息
     */
    @PreAuthorize("@ss.hasPermi('system:senses:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(threeSensesService.selectThreeSensesById(id));
    }

    /**
     * 新增三感三率
     */
    @PreAuthorize("@ss.hasPermi('system:senses:add')")
    @Log(title = "三感三率", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody @Valid ThreeSenses threeSenses)
    {
        return threeSensesService.insertThreeSenses(threeSenses);
    }

    /**
     * 修改三感三率
     */
    @PreAuthorize("@ss.hasPermi('system:senses:edit')")
    @Log(title = "三感三率", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody @Valid ThreeSenses threeSenses)
    {
        return threeSensesService.updateThreeSenses(threeSenses);
    }

    /**
     * 删除三感三率
     */
    @PreAuthorize("@ss.hasPermi('system:senses:remove')")
    @Log(title = "三感三率", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(threeSensesService.deleteThreeSensesByIds(ids));
    }

    /**
     * 下载导入模板
     * @param response
     */
    @PostMapping("/importTemplate")
    public void importTemplate(HttpServletResponse response) {
        ExcelUtil<ThreeSenses> util = new ExcelUtil<ThreeSenses>(ThreeSenses.class);
        util.importTemplateExcel(response, "三感三率模板");
    }


    /**
     * 导入三感三率数据
     * @param file
     * @return
     * @throws Exception
     */
    @Log(title = "三感三率", businessType = BusinessType.IMPORT)
    @PostMapping("/importThreeSensesData")
    public AjaxResult importRiskDetailData(MultipartFile file) throws Exception {
        ExcelUtil<ThreeSenses> util = new ExcelUtil<ThreeSenses>(ThreeSenses.class);
        List<ThreeSenses> threeSensesList = util.importExcel(file.getInputStream());
        return threeSensesService.importThreeSensesData(threeSensesList);
    }

}
