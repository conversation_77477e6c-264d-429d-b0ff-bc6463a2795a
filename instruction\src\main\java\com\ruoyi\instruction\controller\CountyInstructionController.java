package com.ruoyi.instruction.controller;

import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.domain.entity.SysDept;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.instruction.mapper.InstructionInfoMapper;
import com.ruoyi.instruction.service.impl.InstructionInfoServiceImpl;
import com.ruoyi.system.mapper.SysDeptMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2025/7/30 17:31
 * 县市区指令
 */
@RestController
@RequestMapping("/countyInstruction/info")
public class CountyInstructionController extends BaseController {

    @Autowired
    private InstructionInfoMapper infoMapper;

    @Autowired
    private InstructionInfoServiceImpl instructionInfoService;

    @Autowired
    private SysDeptMapper deptMapper;

    /**
     * 获取上级下发指令统计
     * @param startTime
     * @param endTime
     * @return
     */
    @GetMapping("/getCityIssuedStatistics")
    public AjaxResult getCityIssuedStatistics(@RequestParam(required = false) String startTime, @RequestParam(required = false) String endTime) {

        SysDept dept = SecurityUtils.getLoginUser().getUser().getDept();
        dept = instructionInfoService.getCountyDeptInfo(dept);
        Long deptId = dept.getDeptId();
        Map<String,Object> map = infoMapper.getCityIssuedStatistics(deptId,startTime, endTime);
        return AjaxResult.success(map);
    }

    /**
     * 获取县级下发指令
     * @param startTime
     * @param endTime
     * @return
     */
    @GetMapping("/getCountyIssuedStatistics")
    public AjaxResult getCountyIssuedStatistics(@RequestParam(required = false) String startTime, @RequestParam(required = false) String endTime) {

        SysDept dept = SecurityUtils.getLoginUser().getUser().getDept();
        dept = instructionInfoService.getCountyDeptInfo(dept);
        Long deptId = dept.getDeptId();
        //获取政府机构部门id
        List<Long> deptIds = deptMapper.getZfjgDeptId(dept.getParentId());
        List<Map<String,Object>> map = infoMapper.getCountyIssuedStatistics(deptId,startTime, endTime, deptIds);
        return AjaxResult.success(map);
    }

    /**
     * 获取紧急程度统计
     * @param startTime
     * @param endTime
     * @return
     */
    @GetMapping("/getEmergencyDegreeStatistics")
    public AjaxResult getEmergencyDegreeStatistics(@RequestParam(required = false) String startTime, @RequestParam(required = false) String endTime) {

        SysDept dept = SecurityUtils.getLoginUser().getUser().getDept();
        dept = instructionInfoService.getCountyDeptInfo(dept);
        String deptName = dept.getDeptName();

        List<Map<String,Object>> map = infoMapper.getEmergencyDegreeStatistics(deptName,startTime, endTime);
        return AjaxResult.success(map);
    }





}
