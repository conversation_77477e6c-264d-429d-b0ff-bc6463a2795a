package com.ruoyi.instruction.excellistener;

import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.ruoyi.instruction.domain.InstrucationPerson;
import com.ruoyi.instruction.domain.rspVo.EventPersonRspVo;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/2/9 14:28
 */
public class InstructionPersonListener extends AnalysisEventListener<EventPersonRspVo> {

    List<EventPersonRspVo> personList = new ArrayList<>();

    @Override
    public void invoke(final EventPersonRspVo person, final AnalysisContext analysisContext) {
        personList.add(person);
    }

    @Override
    public void doAfterAllAnalysed(final AnalysisContext analysisContext) {

    }

    public List<EventPersonRspVo> getDataList() {
        return personList;
    }
}
