package com.ruoyi.instruction.controller;

import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.instruction.domain.InstructionTransferWarning;
import com.ruoyi.instruction.service.IInstructionInfoService;
import com.ruoyi.instruction.service.IInstructionTransferWarningService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("/instruction/transferWarning")
public class InstructionTransferWarningController extends BaseController {
    @Autowired
    private IInstructionTransferWarningService warningService;

    // @PreAuthorize("@ss.hasPermi('instruction:transferWarning:list')")
    @GetMapping("/list")
    public TableDataInfo list(InstructionTransferWarning warning) {
        startPage();
        List<InstructionTransferWarning> list = warningService.selectWarningList(warning);
        return getDataTable(list);
    }

    // @PreAuthorize("@ss.hasPermi('instruction:transferWarning:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id) {
        return AjaxResult.success(warningService.selectWarningById(id));
    }

    @Log(title = "指令移交预警", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody InstructionTransferWarning warning) {
        return toAjax(warningService.insertWarning(warning));
    }

    // @PreAuthorize("@ss.hasPermi('instruction:transferWarning:edit')")
    @Log(title = "指令移交预警", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody InstructionTransferWarning warning) {
        return toAjax(warningService.updateWarning(warning));
    }

    // @PreAuthorize("@ss.hasPermi('instruction:transferWarning:remove')")
    @Log(title = "指令移交预警", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids) {
        return toAjax(warningService.deleteWarningByIds(ids));
    }
}
