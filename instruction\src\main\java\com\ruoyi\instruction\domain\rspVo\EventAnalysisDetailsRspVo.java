package com.ruoyi.instruction.domain.rspVo;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class EventAnalysisDetailsRspVo {

  /**
   * 创建时间
   */
  private String createTime;
  /**
   * 事件标题
   */
  private  String eventTitle;
  /**
   * 涉事人数
   */
  private Integer respPersonNum;

  /**
   * 所属地
   */
  private String region;
  /**
   * 是否实地上访
   */
  private String isSiteVisits;

  /**
   * 实地上访人数
   */
  private Integer siteVisitsCount;
  /**
   * 实地上访类型
   */
  private String   siteVisitsType;
  /**
   * 序号
   */
  private  Integer num;

  /**
   * 事件属性
   */
  private String   eventProperties;

  /**
   * 群体名
   */
  private String   groupName;

  private String typeName;

  private Long id;

  private  Integer eventNum;
}
