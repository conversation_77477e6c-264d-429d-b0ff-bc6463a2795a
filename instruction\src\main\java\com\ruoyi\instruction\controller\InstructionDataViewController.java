package com.ruoyi.instruction.controller;

import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.instruction.service.InstructionDataViewService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/4/12 8:43
 * 指令模块数据可视化相关接口
 */
@RestController
@RequestMapping("/instruction/dateView")
public class InstructionDataViewController {

    @Autowired
    private InstructionDataViewService instructionDataViewService;

    /**
     * 获取指令下发、接收、处置、销号数量(展示市本级指令下发数)
     * @return
     */
    @PostMapping("/getProcessData")
    public AjaxResult getProcessData(@RequestBody(required = false) Map<String,Object> map){
        return instructionDataViewService.getProcessData(map);
    }

    /**
     * 获取市本级指令下发各县市区接收排名
     * @return
     */
    @GetMapping("/getIssueRank")
    public AjaxResult getIssueRank(){
        return instructionDataViewService.getIssueRank();
    }

    /**
     * 获取县市区下发指令
     * @return
     */
    @GetMapping("/getCountyIssueRank")
    public AjaxResult getCountyIssueRank(){
        return instructionDataViewService.getCountyIssueRank();
    }


    /**
     * 获取指令到访类型
     * @return
     */
    @GetMapping("/getTypeCount")
    public AjaxResult getTypeCount(){
        return instructionDataViewService.getTypeCount();
    }

    /**
     * 获取指令新增数据及占比
     * @param type 1:今日 2：本周 3：本月 4：全年 5：指定时间
     * @param createTime
     * @param endTime
     * @return
     */
    @GetMapping("/getAddDataProportion")
    public AjaxResult getAddDataProportion(@RequestParam("type")Integer type, @RequestParam(value = "startTime",required = false)String createTime,@RequestParam(value = "endTime",required = false)String endTime){
        return instructionDataViewService.getAddDataProportion(type,createTime,endTime);
    }

    /**
     * 获取县市区平均处置时长排名
     * @param type 1：今日 2：本周 3：本月
     * @return
     */
    @GetMapping("/getCountyDealHour/{type}")
    public AjaxResult getCountyDealHour(@PathVariable String type){
        List<Map<String,Integer>> maps = instructionDataViewService.getCountyDealHour(type);
        return AjaxResult.success(maps);
    }
}
