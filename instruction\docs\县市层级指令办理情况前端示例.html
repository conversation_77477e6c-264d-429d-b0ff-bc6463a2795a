<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>县市层级指令办理情况前端示例</title>
    <script src="https://cdn.jsdelivr.net/npm/axios/dist/axios.min.js"></script>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background-color: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .form-group {
            margin-bottom: 15px;
        }
        label {
            display: inline-block;
            width: 120px;
            font-weight: bold;
        }
        input, select, button {
            padding: 8px;
            margin: 5px;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
        button {
            background-color: #007bff;
            color: white;
            cursor: pointer;
            padding: 10px 20px;
        }
        button:hover {
            background-color: #0056b3;
        }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-top: 20px;
        }
        .stat-card {
            background-color: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            text-align: center;
            border-left: 4px solid #007bff;
        }
        .stat-number {
            font-size: 24px;
            font-weight: bold;
            color: #007bff;
        }
        .stat-label {
            font-size: 14px;
            color: #666;
            margin-top: 5px;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 20px;
        }
        th, td {
            border: 1px solid #ddd;
            padding: 8px;
            text-align: left;
        }
        th {
            background-color: #f2f2f2;
            font-weight: bold;
        }
        .loading {
            text-align: center;
            color: #666;
            font-style: italic;
        }
        .error {
            color: #dc3545;
            background-color: #f8d7da;
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>县市层级指令办理情况前端示例</h1>
        
        <!-- 查询条件 -->
        <div class="section">
            <h2>查询条件</h2>
            <div class="form-group">
                <label>县市区：</label>
                <select id="countyName">
                    <option value="">全部</option>
                    <option value="磐安县">磐安县</option>
                    <option value="兰溪市">兰溪市</option>
                    <option value="东阳市">东阳市</option>
                    <option value="义乌市">义乌市</option>
                    <option value="浦江县">浦江县</option>
                    <option value="永康市">永康市</option>
                    <option value="金东区">金东区</option>
                    <option value="婺城区">婺城区</option>
                    <option value="开发区">开发区</option>
                    <option value="武义县">武义县</option>
                </select>
            </div>
            <div class="form-group">
                <label>开始时间：</label>
                <input type="date" id="startTime">
            </div>
            <div class="form-group">
                <label>结束时间：</label>
                <input type="date" id="endTime">
            </div>
            <button onclick="loadStats()">查询统计数据</button>
            <button onclick="exportData()">导出数据</button>
        </div>

        <!-- 统计数据展示 -->
        <div class="section">
            <h2>统计数据</h2>
            <div id="statsContainer">
                <div class="loading">点击"查询统计数据"按钮加载数据</div>
            </div>
        </div>

        <!-- 详细列表查询 -->
        <div class="section">
            <h2>详细列表查询</h2>
            <div class="form-group">
                <label>列表类型：</label>
                <select id="listType">
                    <option value="noReceive">应接收未接收</option>
                    <option value="noDispose">应处置未处置</option>
                </select>
            </div>
            <div class="form-group">
                <label>部门类型：</label>
                <select id="deptType">
                    <option value="county">县级部门</option>
                    <option value="town">乡镇街道</option>
                </select>
            </div>
            <button onclick="loadList()">查询详细列表</button>
        </div>

        <!-- 列表数据展示 -->
        <div class="section">
            <h2>详细列表</h2>
            <div id="listContainer">
                <div class="loading">点击"查询详细列表"按钮加载数据</div>
            </div>
        </div>
    </div>

    <script>
        // 基础配置
        const BASE_URL = '/instruction/cityHandling/county';
        
        // 加载统计数据
        async function loadStats() {
            const container = document.getElementById('statsContainer');
            container.innerHTML = '<div class="loading">加载中...</div>';
            
            try {
                const params = getQueryParams();
                const response = await axios.get(`${BASE_URL}/data`, { params });
                
                if (response.data.code === 200) {
                    displayStats(response.data.data);
                } else {
                    container.innerHTML = `<div class="error">加载失败：${response.data.msg}</div>`;
                }
            } catch (error) {
                container.innerHTML = `<div class="error">请求失败：${error.message}</div>`;
            }
        }

        // 加载详细列表
        async function loadList() {
            const container = document.getElementById('listContainer');
            container.innerHTML = '<div class="loading">加载中...</div>';
            
            try {
                const params = {
                    ...getQueryParams(),
                    listType: document.getElementById('listType').value,
                    deptType: document.getElementById('deptType').value
                };
                
                const response = await axios.get(`${BASE_URL}/list`, { params });
                
                if (response.data.code === 200) {
                    displayList(response.data.data);
                } else {
                    container.innerHTML = `<div class="error">加载失败：${response.data.msg}</div>`;
                }
            } catch (error) {
                container.innerHTML = `<div class="error">请求失败：${error.message}</div>`;
            }
        }

        // 导出数据
        async function exportData() {
            try {
                const params = getQueryParams();
                const response = await axios.post(`${BASE_URL}/export`, params, {
                    responseType: 'blob'
                });
                
                // 创建下载链接
                const url = window.URL.createObjectURL(new Blob([response.data]));
                const link = document.createElement('a');
                link.href = url;
                link.setAttribute('download', '县市层级指令办理情况.xlsx');
                document.body.appendChild(link);
                link.click();
                link.remove();
                window.URL.revokeObjectURL(url);
                
                alert('导出成功！');
            } catch (error) {
                alert(`导出失败：${error.message}`);
            }
        }

        // 获取查询参数
        function getQueryParams() {
            return {
                countyName: document.getElementById('countyName').value,
                startTime: document.getElementById('startTime').value,
                endTime: document.getElementById('endTime').value
            };
        }

        // 显示统计数据
        function displayStats(data) {
            const container = document.getElementById('statsContainer');
            container.innerHTML = `
                <div class="stats-grid">
                    <div class="stat-card">
                        <div class="stat-number">${data.noReceiveCountyDeptCount || 0}</div>
                        <div class="stat-label">应接收未接收<br>县级部门数</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number">${data.noReceiveTownDeptCount || 0}</div>
                        <div class="stat-label">应接收未接收<br>乡镇街道数</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number">${data.noReceiveCountyInstructionCount || 0}</div>
                        <div class="stat-label">应接收未接收<br>县级部门指令数</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number">${data.noReceiveTownInstructionCount || 0}</div>
                        <div class="stat-label">应接收未接收<br>乡镇街道指令数</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number">${data.noDisposeCountyDeptCount || 0}</div>
                        <div class="stat-label">应处置未处置<br>县级部门数</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number">${data.noDisposeTownDeptCount || 0}</div>
                        <div class="stat-label">应处置未处置<br>乡镇街道数</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number">${data.noDisposeCountyInstructionCount || 0}</div>
                        <div class="stat-label">应处置未处置<br>县级部门指令数</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number">${data.noDisposeTownInstructionCount || 0}</div>
                        <div class="stat-label">应处置未处置<br>乡镇街道指令数</div>
                    </div>
                </div>
            `;
        }

        // 显示列表数据
        function displayList(data) {
            const container = document.getElementById('listContainer');
            
            if (!data || data.length === 0) {
                container.innerHTML = '<div class="loading">暂无数据</div>';
                return;
            }

            let tableHtml = `
                <table>
                    <thead>
                        <tr>
                            <th>指令ID</th>
                            <th>指令标题</th>
                            <th>紧急程度</th>
                            <th>指令类型</th>
                            <th>交办时间</th>
                            <th>部门名称</th>
                            <th>部门类型</th>
                            <th>单位类型</th>
                            <th>所属县市区</th>
                            <th>超期天数</th>
                        </tr>
                    </thead>
                    <tbody>
            `;

            data.forEach(item => {
                tableHtml += `
                    <tr>
                        <td>${item.id || ''}</td>
                        <td>${item.instructionTitle || ''}</td>
                        <td>${item.emergencyDegree || ''}</td>
                        <td>${item.instructionType || ''}</td>
                        <td>${item.assignTime ? new Date(item.assignTime).toLocaleString() : ''}</td>
                        <td>${item.deptName || ''}</td>
                        <td>${item.deptType || ''}</td>
                        <td>${item.unitType || ''}</td>
                        <td>${item.countyName || ''}</td>
                        <td>${item.overdueDays || 0}</td>
                    </tr>
                `;
            });

            tableHtml += '</tbody></table>';
            container.innerHTML = tableHtml;
        }

        // 页面加载完成后设置默认时间
        document.addEventListener('DOMContentLoaded', function() {
            const today = new Date();
            const oneMonthAgo = new Date(today.getFullYear(), today.getMonth() - 1, today.getDate());
            
            document.getElementById('startTime').value = oneMonthAgo.toISOString().split('T')[0];
            document.getElementById('endTime').value = today.toISOString().split('T')[0];
        });
    </script>
</body>
</html>
