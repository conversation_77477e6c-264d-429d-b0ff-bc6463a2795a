package com.ruoyi.instruction.controller;

import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;
import javax.servlet.http.HttpServletResponse;

import com.ruoyi.common.constant.InstructionRolesConstants;
import com.ruoyi.common.core.domain.entity.SysDept;
import com.ruoyi.common.core.domain.entity.SysRole;
import com.ruoyi.common.utils.DeptUtils;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.instruction.domain.PetitionInfo;
import com.ruoyi.instruction.mapper.PetitionInfoMapper;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.instruction.domain.PetitionPerson;
import com.ruoyi.instruction.service.IPetitionPersonService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 信访人员库Controller
 *
 * <AUTHOR>
 * @date 2024-04-29
 */
@RestController
@RequestMapping("/petition/person")
public class PetitionPersonController extends BaseController
{
    @Autowired
    private IPetitionPersonService petitionPersonService;

    @Autowired
    private PetitionInfoMapper petitionInfoMapper;

    /**
     * 查询信访人员库列表
     */
    @PreAuthorize("@ss.hasPermi('petition:person:list')")
    @GetMapping("/list")
    public TableDataInfo list(PetitionPerson petitionPerson)
    {
        Map<String, Object> params = petitionPerson.getParams();
        if (petitionPerson.getContentType() != null && !petitionPerson.getContentType().isEmpty()) {
            List<String> contentTypes = Arrays.asList(petitionPerson.getContentType().split(","));
            params.put("contentTypes", contentTypes);
        }
        if (petitionPerson.getGroupIds() != null && !petitionPerson.getGroupIds().isEmpty()){
            List<String> groupIds = Arrays.asList(petitionPerson.getGroupIds().split(","));
            params.put("groupIds", groupIds);
        }
        if (params.get("gradeBeginTime") != null && params.get("gradeEndTime") != null) {
            PetitionInfo petitionInfo = new PetitionInfo();
            petitionInfo.setParams(params);
            List<PetitionInfo> petitionInfoList = petitionInfoMapper.selectPetitionInfoList(petitionInfo);
            List<String> personNames = petitionInfoList.stream().map(PetitionInfo::getPersonName).collect(Collectors.toList());
            if (personNames.size() == 0) {
                return getDataTable(new ArrayList<>());
            }
            params.put("personNames", personNames);
        }
        petitionPerson.setParams(params);
        //县市区账号进行过滤
        List<SysRole> roles = SecurityUtils.getLoginUser().getUser().getRoles();
        List<String> roleList = roles.stream().map(SysRole::getRoleKey).collect(Collectors.toList());
        if (!roleList.contains(InstructionRolesConstants.CITY_INSTRUCTION)) {
            //查询用户父级部门所在县市区 县市区仅能查看自己所属区域的人员
            SysDept dept = SecurityUtils.getLoginUser().getUser().getDept();
            String dutyPlace = DeptUtils.getDutyPlaceByDept(dept);
            petitionPerson.setDutyPlace(dutyPlace);
            if (dept.getParentId() == 213L || dept.getParentId() == 214L || dept.getParentId() == 215L || dept.getParentId() == 216L || dept.getParentId() == 217L || dept.getParentId() == 218L || dept.getParentId() == 219L || dept.getParentId() == 220L || dept.getParentId() == 221L || dept.getParentId() == 262L) {

            }else {
                petitionPerson.setDutyTown(dept.getDeptName());
            }
        }
        startPage();
        List<PetitionPerson> list = petitionPersonService.selectPetitionPersonList(petitionPerson);
        return getDataTable(list);
    }


    /**
     * 获取统计数据
     * @param petitionPerson
     * @return
     */
    @GetMapping("/getStatistics")
    public AjaxResult getStatistics(PetitionPerson petitionPerson) {
        AjaxResult ajaxResult = new AjaxResult();
        Map<String, Object> params = petitionPerson.getParams();
        if (petitionPerson.getContentType() != null && !petitionPerson.getContentType().isEmpty()) {
            List<String> contentTypes = Arrays.asList(petitionPerson.getContentType().split(","));
            params.put("contentTypes", contentTypes);
        }
        if (petitionPerson.getGroupIds() != null && !petitionPerson.getGroupIds().isEmpty()) {
            List<String> groupIds = Arrays.asList(petitionPerson.getGroupIds().split(","));
            params.put("groupIds", groupIds);
        }
        if (params.get("gradeBeginTime") != null && params.get("gradeEndTime") != null) {
            PetitionInfo petitionInfo = new PetitionInfo();
            petitionInfo.setParams(params);
            List<PetitionInfo> petitionInfoList = petitionInfoMapper.selectPetitionInfoList(petitionInfo);
            if (petitionInfoList.size() > 0){
                List<String> personNames = petitionInfoList.stream().map(PetitionInfo::getPersonName).collect(Collectors.toList());
                params.put("personNames", personNames);
            }else {
                // 创建一个HashMap实例
                Map<String, Integer> regionCounts = new HashMap<>();
                // 插入数据
                regionCounts.put("武义县", 0);
                regionCounts.put("义乌市", 0);
                regionCounts.put("永康市", 0);
                regionCounts.put("金东区", 0);
                regionCounts.put("开发区", 0);
                regionCounts.put("磐安县", 0);
                regionCounts.put("兰溪市", 0);
                regionCounts.put("婺城区", 0);
                regionCounts.put("东阳市", 0);
                regionCounts.put("浦江县", 0);

                ajaxResult.put("dutyPlaceCounts", regionCounts);
                ajaxResult.put("provinceNum", 0);
                ajaxResult.put("capitalNum", 0);
                ajaxResult.put("total", 0);
                return ajaxResult;
            }
        }
        petitionPerson.setParams(params);
        //县市区账号进行过滤
        List<SysRole> roles = SecurityUtils.getLoginUser().getUser().getRoles();
        List<String> roleList = roles.stream().map(SysRole::getRoleKey).collect(Collectors.toList());
        if (!roleList.contains(InstructionRolesConstants.CITY_INSTRUCTION)) {
            //查询用户父级部门所在县市区 县市区仅能查看自己所属区域的人员
            SysDept dept = SecurityUtils.getLoginUser().getUser().getDept();
            String dutyPlace = DeptUtils.getDutyPlaceByDept(dept);
            petitionPerson.setDutyPlace(dutyPlace);
            if (dept.getParentId() == 213L || dept.getParentId() == 214L || dept.getParentId() == 215L || dept.getParentId() == 216L || dept.getParentId() == 217L || dept.getParentId() == 218L || dept.getParentId() == 219L || dept.getParentId() == 220L || dept.getParentId() == 221L || dept.getParentId() == 262L) {

            }else {
                petitionPerson.setDutyTown(dept.getDeptName());
            }
        }
        List<PetitionPerson> list = petitionPersonService.selectPetitionPersonList(petitionPerson);
        if (list.size() > 0) {
            long provinceNum = list.stream()
                    .mapToLong(PetitionPerson::getProvinceNum)
                    .sum();
            long capitalNum = list.stream()
                    .mapToLong(PetitionPerson::getCapitalNum)
                    .sum();
            // 使用Stream API进行分组并计算每个dutyPlace的数量
            Map<String, Long> dutyPlaceCounts = list.stream()
                    .filter(person -> person.getDutyPlace() != null && !person.getDutyPlace().isEmpty())
                    .collect(Collectors.groupingBy(
                            PetitionPerson::getDutyPlace,
                            Collectors.counting()));
            ajaxResult.put("dutyPlaceCounts", dutyPlaceCounts);
            ajaxResult.put("provinceNum", provinceNum);
            ajaxResult.put("capitalNum", capitalNum);
            ajaxResult.put("total", list.size());
        }else {
            // 创建一个HashMap实例
            Map<String, Integer> regionCounts = new HashMap<>();
            // 插入数据
            regionCounts.put("武义县", 0);
            regionCounts.put("义乌市", 0);
            regionCounts.put("永康市", 0);
            regionCounts.put("金东区", 0);
            regionCounts.put("开发区", 0);
            regionCounts.put("磐安县", 0);
            regionCounts.put("兰溪市", 0);
            regionCounts.put("婺城区", 0);
            regionCounts.put("东阳市", 0);
            regionCounts.put("浦江县", 0);

            ajaxResult.put("dutyPlaceCounts", regionCounts);
            ajaxResult.put("provinceNum", 0);
            ajaxResult.put("capitalNum", 0);
            ajaxResult.put("total", 0);
        }
        return ajaxResult;
    }

    /**
     * 导出信访人员库列表
     */
    @PreAuthorize("@ss.hasPermi('petition:person:export')")
    @Log(title = "信访人员库", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, PetitionPerson petitionPerson)
    {
        //县市区账号进行过滤
        List<SysRole> roles = SecurityUtils.getLoginUser().getUser().getRoles();
        List<String> roleList = roles.stream().map(SysRole::getRoleKey).collect(Collectors.toList());
        if (!roleList.contains(InstructionRolesConstants.CITY_INSTRUCTION)) {
            //查询用户父级部门所在县市区 县市区仅能查看自己所属区域的人员
            SysDept dept = SecurityUtils.getLoginUser().getUser().getDept();
            String dutyPlace = DeptUtils.getDutyPlaceByDept(dept);
            petitionPerson.setDutyPlace(dutyPlace);
            if (dept.getParentId() == 213L || dept.getParentId() == 214L || dept.getParentId() == 215L || dept.getParentId() == 216L || dept.getParentId() == 217L || dept.getParentId() == 218L || dept.getParentId() == 219L || dept.getParentId() == 220L || dept.getParentId() == 221L || dept.getParentId() == 262L) {

            }else {
                petitionPerson.setDutyTown(dept.getDeptName());
            }
        }
        List<PetitionPerson> list = petitionPersonService.selectPetitionPersonList(petitionPerson);
        ExcelUtil<PetitionPerson> util = new ExcelUtil<PetitionPerson>(PetitionPerson.class);
        util.exportExcel(response, list, "信访人员库数据");
    }

    /**
     * 获取信访人员库详细信息
     */
    @PreAuthorize("@ss.hasPermi('petition:person:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(petitionPersonService.selectPetitionPersonById(id));
    }

    /**
     * 新增信访人员库
     */
    @PreAuthorize("@ss.hasPermi('petition:person:add')")
    @Log(title = "信访人员库", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody PetitionPerson petitionPerson)
    {
        return toAjax(petitionPersonService.insertPetitionPerson(petitionPerson));
    }

    /**
     * 修改信访人员库
     */
    @PreAuthorize("@ss.hasPermi('petition:person:edit')")
    @Log(title = "信访人员库", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody PetitionPerson petitionPerson)
    {
        return toAjax(petitionPersonService.updatePetitionPerson(petitionPerson));
    }

    /**
     * 删除信访人员库
     */
    @PreAuthorize("@ss.hasPermi('petition:person:remove')")
    @Log(title = "信访人员库", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(petitionPersonService.deletePetitionPersonByIds(ids));
    }
}
