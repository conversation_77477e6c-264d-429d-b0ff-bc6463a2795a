package com.ruoyi.instruction.mapper;

import java.util.List;
import com.ruoyi.instruction.domain.YjEventPerson;

/**
 * 基层治理-人员关系Mapper接口
 * 
 * <AUTHOR>
 * @date 2024-12-10
 */
public interface YjEventPersonMapper 
{
    /**
     * 查询基层治理-人员关系
     * 
     * @param id 基层治理-人员关系主键
     * @return 基层治理-人员关系
     */
    public YjEventPerson selectYjEventPersonById(Long id);

    /**
     * 查询基层治理-人员关系列表
     * 
     * @param yjEventPerson 基层治理-人员关系
     * @return 基层治理-人员关系集合
     */
    public List<YjEventPerson> selectYjEventPersonList(YjEventPerson yjEventPerson);

    /**
     * 新增基层治理-人员关系
     * 
     * @param yjEventPerson 基层治理-人员关系
     * @return 结果
     */
    public int insertYjEventPerson(YjEventPerson yjEventPerson);

    /**
     * 修改基层治理-人员关系
     * 
     * @param yjEventPerson 基层治理-人员关系
     * @return 结果
     */
    public int updateYjEventPerson(YjEventPerson yjEventPerson);

    /**
     * 删除基层治理-人员关系
     * 
     * @param id 基层治理-人员关系主键
     * @return 结果
     */
    public int deleteYjEventPersonById(Long id);

    /**
     * 批量删除基层治理-人员关系
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteYjEventPersonByIds(Long[] ids);
}
