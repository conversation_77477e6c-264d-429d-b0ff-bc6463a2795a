package com.ruoyi.instruction.domain;

import java.util.Date;

import com.fasterxml.jackson.annotation.JsonFormat;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 政法委转情指（流程）对象 czjl
 *
 * <AUTHOR>
 * @date 2023-06-07
 */
public class Czjl extends BaseEntity {
    private static final long serialVersionUID = 1L;

    private Long id;

    /**
     * 情指数据Id
     */
    private String qzid;

    /**
     * 操作人姓名
     */
    @Excel(name = "操作人姓名")
    private String czrxm;

    /**
     * 操作人部门
     */
    @Excel(name = "操作人部门")
    private String czrbm;

    /**
     * 操作时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "操作时间", width = 30, dateFormat = "yyyy-MM-dd")
    private String czsj;

    /**
     * 操作内容
     */
    @Excel(name = "操作内容")
    private String cznr;


    /**
     * 操作状态(0-通过，1-不通过)
     */
    @Excel(name = "操作状态(0-通过，1-不通过)")
    private String czlx;

    /**
     * 部门id
     */
    @Excel(name = "部门id")
    private String bmid;

    /** 操作状态（0-通过 ,1-不通过） */
    @Excel(name = "操作状态", readConverterExp = "0=-通过,,=1-不通过")
    private String czzt;

    public Long getId() {
        return id;
    }

    public void setId(final Long id) {
        this.id = id;
    }

    public void setQzid(String qzid) {
        this.qzid = qzid;
    }

    public String getQzid() {
        return qzid;
    }

    public void setCzrxm(String czrxm) {
        this.czrxm = czrxm;
    }

    public String getCzrxm() {
        return czrxm;
    }

    public void setCzrbm(String czrbm) {
        this.czrbm = czrbm;
    }

    public String getCzrbm() {
        return czrbm;
    }

    public String getCzsj() {
        return czsj;
    }

    public void setCzsj(final String czsj) {
        this.czsj = czsj;
    }

    public void setCznr(String cznr) {
        this.cznr = cznr;
    }

    public String getCznr() {
        return cznr;
    }

    public void setCzlx(String czlx) {
        this.czlx = czlx;
    }

    public String getCzlx() {
        return czlx;
    }

    public void setBmid(String bmid) {
        this.bmid = bmid;
    }

    public String getBmid() {
        return bmid;
    }

    public String getCzzt() {
        return czzt;
    }

    public void setCzzt(final String czzt) {
        this.czzt = czzt;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this, ToStringStyle.MULTI_LINE_STYLE)
                .append("qzid", getQzid())
                .append("czrxm", getCzrxm())
                .append("czrbm", getCzrbm())
                .append("czsj", getCzsj())
                .append("cznr", getCznr())
                .append("czlx", getCzlx())
                .append("bmid", getBmid())
                .toString();
    }
}
