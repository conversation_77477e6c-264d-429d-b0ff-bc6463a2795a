package com.ruoyi.instruction.domain.rspVo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;
import com.ruoyi.instruction.domain.InstrucationPerson;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.util.Date;
import java.util.List;

/**
 * 指令基本信息对象 中间表
 *
 * <AUTHOR>
 * @date 2022-12-19
 */
@Data
public class InstructionInfomiddleResVo extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 指令主键 */
    private Long id;

    /** 指令编码（自动生成唯一uuid） */
    @Excel(name = "指令编码", readConverterExp = "自动生成唯一uuid")
    private String instructionCode;

    /** 指令标题 */
    @Excel(name = "指令标题")
    private String instructionTitle;

    /** 办理期限 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "办理期限", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date handleTime;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date pushTime;

    /** 接收单位 */
    @Excel(name = "接收单位")
    private String receiveUnit;

    /** 紧急程度 */
    @Excel(name = "紧急程度")
    private String emergencyDegree;

    /** 关联群体id */
    @Excel(name = "关联群体id")
    private Long groupId;

    /** 事件类型 */
    @Excel(name = "事件类型")
    private String type;

    /** 信息来源 */
    @Excel(name = "信息来源")
    private String sourceInfo;

    /** 指令内容 */
    @Excel(name = "指令内容")
    private String instructionContent;

    /** 基本情况 */
    @Excel(name = "基本情况")
    private String baseInfo;

    /** 创建者 */
    @Excel(name = "创建者")
    private String creatorBy;

    /** 状态 1:正常  9：删除 */
    @Excel(name = "状态 1:正常  9：删除")
    private String status;

    /** 指令状态 1:交办 2:暂存 */
    @Excel(name = "指令状态 1:交办 2:暂存")
    private String instructionStatus;

    /** 反馈要求 */
    @Excel(name = "反馈要求")
    private String feedback;

    /** 人员ids */
    @Excel(name = "人员ids")
    private String personIds;

    /**
     * 人员信息
     */
    private List<InstrucationPerson> personList;

    /**
     * 接收单位
     */
    private String[] unit;

    /**
     * 页码
     */
    private Integer start;

    /**
     * 每页显示条数
     */
    private Integer end;

    /**
     * 事件类型id
     */
    private Long typeId;

    /**
     * 事件类型
     */
    private String typeName;

    /**
     * 事件id(用于判断创建指令的同时是否创建事件信息)
     */
    private Long eventId;

    /**
     * 交办时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date assignTime;

    /**
     * 接收时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date reveiveTime;

    /**
     * mzx接收时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date mzxReveiveTime;

    /**
     * 处置时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date disposeTime;

    /**
     * 反馈时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date feedbackTime;

    /**
     * mzx反馈时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date mzxFeedbackTime;

    /**
     * 销号时间时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date endTime;

    /**
     * 县市区反馈时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date countyFeedbackTime;

    /**
     * mzx县市区反馈时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date mzxCountyFeedbackTime;

    /**
     * 县市区已反馈部门
     */
    private Integer countDeptNum;

    /**
     * 接收部门个数
     */
    private Integer receiveUnitCount;

    private Integer unitCount;

    /** 指令创建者部门id */
    private Long createDeptId;

    /**
     * 牵头人员ids
     */
    private String leadPersonIds;


    /** 是否销号 1:销号 2：未销号 */
    private Integer instrucationIsEnd;

    /**
     * 销号时选择反馈部门
     */
    private String feedbackDept;

    /** 是否发布 1：发布 2：未发布 */
    private String isRelease;

    /**
     * 督办次数
     */
    private Integer handleNumber;

    /**
     * 提醒次数
     */
    private Integer remindNumber;

    /** 指令创建id */
    private Long instructionCreateDeptId;


    /** 暗访任务名称 */
    private String missionName;

    /** 暗访点位名称 */
    private String companyName;

    /** 县市区 */
    private String county;

    /** 乡镇街道 */
    private String town;

    /** 点位详细信息 */
    private String companyAddress;

    /** 是否审核 0:待审核1：审核 2：未审核 */
    private String isAudit;

    /** 审核人员 */
    private String auditPerson;

    /** 审核时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date auditTime;

    /**
     * 部门协同str
     */
    private String departmentStr;


    private String mzxId;

    /**
     * 市外人员
     */
    private Integer outsidePerson;

    /** 备用统计数字段 */
    private Integer spareNum;

    /** 纠纷所在地 */
    private String mzxPlace;

    /** 纠纷性质 */
    private String mzxNature;

    /** 纠纷人员关系 */
    private String mzxRelation;

    /** 纠纷发生时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date mzxOccurTime;

    /** 纠纷化解责任单位 */
    private String mzxDutyDept;

    /** 纠纷类型 */
    private String mzxType;

    /** 纠纷上报来源 */
    private String mzxSourceInfo;

    /**
     * 指令类型 1：维稳 2：平安 3：基层  4：工作督查  5:失联失控 6：wz预警事件类型
     */
    private Integer instructionType;

    /**
     * 处置部门
     */
    private String transferDept;

    /**
     * 电信案例
     */
    private String caseType;

    /**
     * 化解状态
     */
    private String resolveStatus;

    /**
     * 是否已移交
     */
    private Integer isTransferred;

    /**
     * 是否可移交
     */
    private boolean canBeTransferred;

    /**
     * 移交id
     */
    private Long transferId;
}
