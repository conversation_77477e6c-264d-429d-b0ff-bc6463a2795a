package com.ruoyi.instruction.domain.rspVo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.common.utils.StringUtils;
import lombok.Data;

import java.util.Date;

/**
 * 重点群体处置流程列表
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2023/4/23 9:24
 */
@Data
public class DisposalProcessEventVo {

    /**
     * 事件标题
     */
    private String eventTitle;
    /**
     * 责任单位
     */
    private String dutyUnit;

    /**
     * 指令id
     */
    private Long instructionId;


    private Integer status;
    /**
     * 时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date pushTime;

    public String getDutyUnit() {
        if (!StringUtils.isEmpty(dutyUnit)){
            return StringUtils.strip(dutyUnit,"[]");
        }
        return dutyUnit;
    }


}
