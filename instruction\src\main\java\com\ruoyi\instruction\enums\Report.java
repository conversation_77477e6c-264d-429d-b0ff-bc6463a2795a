package com.ruoyi.instruction.enums;

import com.ruoyi.instruction.domain.FeedbackAnalysis;
import com.ruoyi.instruction.domain.InspectionReport;
import com.ruoyi.instruction.domain.StreetFeedback;

/**
 * <AUTHOR>
 */

public enum Report {
    ZTJCFX("整体检查分析", InspectionReport.class),
    FKLFX("反馈率分析", FeedbackAnalysis.class),
    XZJDFKFX("乡镇街道反馈分析", StreetFeedback.class),
    DWLBHGFX("点位类别合格分析", InspectionReport.class),
    DWLBWTFX("点位类别问题分析", InspectionReport.class),
    WTLBFX("问题类别分析", InspectionReport.class);

    private String type;
    private Class className;

    Report(String type, Class className) {
        this.type = type;
        this.className = className;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public Class getClassName() {
        return className;
    }

    public void setClassName(Class className) {
        this.className = className;
    }
}
