package com.ruoyi.instruction.mapper;

import java.util.List;
import com.ruoyi.instruction.domain.ProvinceYjDetails;

/**
 * 省级预警详情Mapper接口
 * 
 * <AUTHOR>
 * @date 2023-09-21
 */
public interface ProvinceYjDetailsMapper 
{
    /**
     * 查询省级预警详情
     * 
     * @param id 省级预警详情主键
     * @return 省级预警详情
     */
    public ProvinceYjDetails selectProvinceYjDetailsById(Long id);

    /**
     * 查询省级预警详情列表
     * 
     * @param provinceYjDetails 省级预警详情
     * @return 省级预警详情集合
     */
    public List<ProvinceYjDetails> selectProvinceYjDetailsList(ProvinceYjDetails provinceYjDetails);

    /**
     * 新增省级预警详情
     * 
     * @param provinceYjDetails 省级预警详情
     * @return 结果
     */
    public int insertProvinceYjDetails(ProvinceYjDetails provinceYjDetails);

    /**
     * 修改省级预警详情
     * 
     * @param provinceYjDetails 省级预警详情
     * @return 结果
     */
    public int updateProvinceYjDetails(ProvinceYjDetails provinceYjDetails);

    /**
     * 删除省级预警详情
     * 
     * @param id 省级预警详情主键
     * @return 结果
     */
    public int deleteProvinceYjDetailsById(Long id);

    /**
     * 批量删除省级预警详情
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteProvinceYjDetailsByIds(Long[] ids);
}
