package com.ruoyi.instruction.domain;

import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;
import lombok.Data;

@Data
public class CityPetitionPerson extends BaseEntity {

    private Long id;

    /** 姓名 */
    @Excel(name = "姓名")
    private String personName;

    /** 内容分类 */
    @Excel(name = "内容分类")
    private String contentType;

    /** 属地 */
    @Excel(name = "属地")
    private String dutyPlace;

    /** 来市次数 */
    @Excel(name = "来市次数")
    private Long cityNum;

    private Long cityNumMin;

    private Long cityNumMax;

    /** 问题归属 */
    private String problemPlace;

    private Long acount;

    private String personCard;
}
