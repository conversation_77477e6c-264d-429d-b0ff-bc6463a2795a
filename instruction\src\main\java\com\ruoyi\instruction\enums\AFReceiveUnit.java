package com.ruoyi.instruction.enums;

import com.ruoyi.common.utils.StringUtils;

/**
 * 暗访督察-接收单位
 *
 * <AUTHOR>
 */
public enum AFReceiveUnit
{
    LANXI("330781000000","[兰溪市委政法委]","兰溪市"),
    YIWU("330782000000","[义乌市委政法委]","义乌市"),
    PUJIANG("330726000000","[浦江县委政法委]","浦江县"),
    WUCHENG("330702000000","[婺城区政法委]","婺城区"),
    JIDONG("330703000000","[金东区政法委]","金东区"),
    WUYI("330723000000","[武义县委政法委]","武义县"),
    YONGKANG("330784000000","[永康市委政法委]","永康市"),
    DONGYANG("330783000000","[东阳市委政法委]","东阳市"),
    KAIFA("330704000000","[开发区政法办]","开发区"),
    PANAN("330727000000","[磐安县委政法委]","磐安县"),
    JINHUA("330700000000","[金华市委政法委]","金华市");

    private final String name;
    private final String areaCode;
    private final String area;

    AFReceiveUnit(String areaCode,String name,String area)
    {
        this.name = name;
        this.areaCode = areaCode;
        this.area = area;
    }

    public String getName() {
        return name;
    }

    public String getAreaCode() {
        return areaCode;
    }

    public String getArea() {
        return area;
    }

    //通过ID获取枚举值
    public static AFReceiveUnit getByAreaCode(String  areaCode) {
        if(StringUtils.isEmpty(areaCode)){
            return null;
        }
        for(AFReceiveUnit v : values()) {
            if(v.areaCode.equals(areaCode)) {
                return v;
            }
        }
        return null;
    }

    public static AFReceiveUnit getByArea(String  area) {
        if(StringUtils.isEmpty(area)){
            return null;
        }
        for(AFReceiveUnit v : values()) {
            if(v.area.equals(area)) {
                return v;
            }
        }
        return null;
    }
}
