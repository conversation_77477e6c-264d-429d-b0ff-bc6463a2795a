-- SQL优化建议：为getPoorTrackNew查询创建索引
-- 这些索引将显著提高查询性能

-- 1. t_instruction_info表的复合索引
-- 主要用于WHERE条件过滤和JOIN操作
CREATE INDEX idx_instruction_info_main ON t_instruction_info (
    status, 
    instruction_type, 
    create_dept_id, 
    handle_time
);

-- 2. t_instruction_info表的时间范围查询索引
-- 用于handle_time的范围查询优化
CREATE INDEX idx_instruction_info_handle_time ON t_instruction_info (handle_time);

-- 3. t_instruction_info表的紧急程度索引
-- 用于CASE语句中的emergency_degree条件判断
CREATE INDEX idx_instruction_info_emergency ON t_instruction_info (emergency_degree);

-- 4. t_instruction_receive表的复合索引
-- 用于JOIN和WHERE条件过滤
CREATE INDEX idx_instruction_receive_main ON t_instruction_receive (
    status, 
    instrucation_id, 
    receive_dept
);

-- 5. t_instruction_receive表的指令ID索引
-- 用于与主表的JOIN操作
CREATE INDEX idx_instruction_receive_instruction_id ON t_instruction_receive (instrucation_id);

-- 6. t_instruction_county_feedback表的复合索引
-- 用于GROUP BY和WHERE条件过滤
CREATE INDEX idx_county_feedback_main ON t_instruction_county_feedback (
    status, 
    is_end, 
    instruction_id, 
    feedback_dept
);

-- 7. t_instruction_county_feedback表的接收时间索引
-- 用于MAX(receive_time)聚合函数优化
CREATE INDEX idx_county_feedback_receive_time ON t_instruction_county_feedback (
    instruction_id, 
    feedback_dept, 
    receive_time
);

-- 8. 如果表数据量很大，考虑创建分区表（可选）
-- 按时间分区可以进一步提高查询性能
-- ALTER TABLE t_instruction_info PARTITION BY RANGE (YEAR(handle_time)) (
--     PARTITION p2022 VALUES LESS THAN (2023),
--     PARTITION p2023 VALUES LESS THAN (2024),
--     PARTITION p2024 VALUES LESS THAN (2025),
--     PARTITION p_future VALUES LESS THAN MAXVALUE
-- );

-- 查看索引创建后的执行计划
-- EXPLAIN SELECT ... (完整的优化后查询)

-- 性能监控查询
-- 用于监控索引使用情况
SELECT 
    TABLE_SCHEMA,
    TABLE_NAME,
    INDEX_NAME,
    CARDINALITY,
    SUB_PART,
    PACKED,
    NULLABLE,
    INDEX_TYPE
FROM INFORMATION_SCHEMA.STATISTICS 
WHERE TABLE_SCHEMA = DATABASE() 
AND TABLE_NAME IN ('t_instruction_info', 't_instruction_receive', 't_instruction_county_feedback')
ORDER BY TABLE_NAME, INDEX_NAME;
