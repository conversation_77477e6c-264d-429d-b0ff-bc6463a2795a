package com.ruoyi.instruction.controller;

import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.instruction.domain.InstructionEvent;
import com.ruoyi.instruction.service.IInstructionEventService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 金安智治驾驶舱金安大数据生产安全
 * <AUTHOR> @version 1.0
 * @date 2
 *
 */
@RestController
@RequestMapping("/bigScreen/jazz/produceSafe")
public class BigScreenJazzProduceSafeController extends BaseController {
    @Autowired
    private IInstructionEventService instructionEventService;
    /**
     * 金安大数据生产安全板块  事件详情
     * <AUTHOR>
     */
    @GetMapping("/EventInfo")
    public AjaxResult getEventInfo(){
        List<InstructionEvent> list= instructionEventService.getProduceEventInfo();
        return AjaxResult.success(list);
    }

}
