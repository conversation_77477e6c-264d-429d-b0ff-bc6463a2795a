package com.ruoyi.instruction.domain.reqVo;

import com.ruoyi.common.core.domain.BaseEntity;
import com.ruoyi.instruction.domain.*;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import java.util.List;

/**
 * 平安金华
 * <AUTHOR>
 * @version 1.0
 * @date 2023/1/6 10:25
 */
@Data
public class JazzPajhAreaStatisticsVo extends BaseEntity {

    /**
     * 类型，1地区，2部门,3地级市，4乡镇街道
     */
    private Long type;

    /**
     * 年度
     */
    private String year;
    /**
     * 月份
     */
    private String month;

    private  List<JazzPajhAreaStatistics> list;


    /**
     * 对应数据的areaId或者townId
     */
    private  Long dateId;
    private  Long townId;
    /**
     *
     */
    private  Integer dateType;

    /**
     * 排序类型，1省排序
     */
    private  Integer orderType;


    public String getMonth() {
        return   StringUtils.isBlank(month)?null:Integer.parseInt(month)+"";
    }


}
