package com.ruoyi.instruction.controller.Dashboard;

import com.ruoyi.common.constant.InstructionRolesConstants;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.domain.entity.SysRole;
import com.ruoyi.common.core.domain.entity.SysUser;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.utils.DeptUtils;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.common.utils.easyexcel.EasyExcelUtil;
import com.ruoyi.instruction.domain.*;
import com.ruoyi.instruction.domain.reqVo.InspectionReportParam;
import com.ruoyi.instruction.enums.Report;
import com.ruoyi.instruction.mapper.*;
import com.ruoyi.instruction.service.*;
import com.ruoyi.instruction.service.impl.InstructionInfoServiceImpl;
import com.ruoyi.instruction.service.impl.TOpenUndercoverInspectionServiceImpl;
import org.apache.ibatis.jdbc.SQL;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.time.temporal.TemporalAdjusters;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * 驾驶舱大屏统计接口
 * <AUTHOR>
 * @version 1.0
 * @date 2024/12/14 9:27
 */
@RestController
@RequestMapping("/dashboard/Statistics")
public class StatisticsController extends BaseController {

    @Autowired
    private InstrucationPersonMapper personMapper;


    @Autowired
    private IInstrucationPersonService personService;

    @Autowired
    private InstructionEventMapper eventMapper;

    @Autowired
    private InstructionGroupMapper groupMapper;

    @Autowired
    private IPersonAuditService personAuditService;

    @Autowired
    private InstructionInfoMapper infoMapper;

    @Autowired
    private JazsMapper jazsMapper;

    @Autowired
    private DeployControlMapper deployControlMapper;

    @Autowired
    private IPersonLevelWarnService personLevelWarnService;

    @GetMapping("/getStatisticsOne")
    public AjaxResult getStatisticsOne(){
        List<Map> list = personMapper.getStatisticsOne();
        return success(list);
    }

    /**
     * 获取群体列表
     * @param instructionGroup
     * @return
     */
    @GetMapping("/listForGroup")
    public TableDataInfo listForWZ(InstructionGroup instructionGroup) {

        String countyName = "";
        Long deptId = null;
        SysUser user = SecurityUtils.getLoginUser().getUser();
        List<String> roleList = user.getRoles().stream().map(SysRole::getRoleKey).collect(Collectors.toList());
        if (roleList.contains(InstructionRolesConstants.COUNTY_COCKPIT)) {
            countyName = DeptUtils.getDutyPlaceByDept(user.getDept());
        }
        Map<String, Object> params = instructionGroup.getParams();
        if (instructionGroup.getBeginTime()!=null){
            params.put("beginTime",instructionGroup.getBeginTime());
        }
        if (instructionGroup.getEndTime()!=null){
            params.put("endTime",instructionGroup.getEndTime());
        }
        instructionGroup.setParams(params);
        startPage();
        List<InstructionGroup> list = groupMapper.getBigScreenStatistics(instructionGroup);
        int uniqueCount = 0;
        if (list != null && list.size() > 0) {
            //1、查询事件
            // List<InstructionEvent> eventList = eventMapper.selectEventList();
            List<InstructionEvent> eventList = eventMapper.selectEventListForTime(instructionGroup);

            for (InstructionGroup group : list) {
                //查询群体关联事件
                String finallyPersonIds = eventList.stream().filter(event -> event.getGroupId() != null && event.getGroupId().equals(group.getId())).map(InstructionEvent::getPersonIds).collect(Collectors.joining(","));
                //所有人员ids
                group.setPersonCount(0);
                if (group.getPersonIds() == null) {
                    group.setPersonIds("");
                }
                if (finallyPersonIds.length() > 0 || group.getPersonIds().length() > 0) {
                    String ids = finallyPersonIds + "," + group.getPersonIds();
                    group.setPersonIds(ids);
                    List<String> collect = Arrays.stream(ids.split(",")).collect(Collectors.toList());
                    //查询人员数量
                    // int count = 0;
                    // count = personService.findPeronCountById(collect);
                    // count = personMapper.findPersonCountByIdAndDeptName(collect, countyName);
                    group.setPersonCount(collect.size());
                }
            }
            //过滤list中personCount为0的数据
            list = list.stream().filter(group -> group.getPersonCount() > 0).collect(Collectors.toList());
            String groupIds = list.stream().filter(group -> group.getLeadPersonIds() != null).map(InstructionGroup::getLeadPersonIds).collect(Collectors.joining(","));
            // 按逗号分隔并去重
            Set<String> uniquePersonIds = new HashSet<>();
            if (groupIds != null && !groupIds.isEmpty()) {
                uniquePersonIds.addAll(Arrays.asList(groupIds.split(",")));
            }

            // 计算唯一 lead_person_ids 的个数
            uniqueCount = uniquePersonIds.size();
        }

        return getDataTable(list,uniqueCount);
    }

    @GetMapping("/getGroupControllevel")
    public AjaxResult getGroupControllevel(){
        List<Map<String, Object>>  map = groupMapper.getGroupControllevel();
        return success(map);
    }

    /**
     * 获取群体类型（只取前top5,剩余归为其他）
     * @return
     */
    @GetMapping("/getGroupType")
    public AjaxResult getGroupType(){
        //前五条数据
        List<Map<String, Object>> list = groupMapper.getGroupType();
        List<String> typeIds = list.stream().filter(map -> map.get("type") != null).map(map -> map.get("type").toString()).collect(Collectors.toList());
        Long count = groupMapper.getGroupTypeCount(typeIds);
        HashMap<String, Object> map = new HashMap<>();
        map.put("type_name", "其他");
        map.put("amount", count);
        map.put("type","99");
        list.add(map);
        return success(list);
    }

    @GetMapping("/getPersonType")
    public AjaxResult getPersonType(){
        String countyName = "";
                SysUser user = SecurityUtils.getLoginUser().getUser();
        List<String> roleList = user.getRoles().stream().map(SysRole::getRoleKey).collect(Collectors.toList());
        if (roleList.contains(InstructionRolesConstants.COUNTY_COCKPIT)) {
            countyName = DeptUtils.getDutyPlaceByDept(user.getDept());
        }
        List<Map<String, Object>> list = personMapper.getPersonType(countyName);
        return success(list);
    }


    /**
     * 获取人员异动情况
     * @return
     */
    @GetMapping("getPersonTransaction")
    public AjaxResult getPersonTransaction(InstrucationPerson person){
        String countyName = "";
        Long deptId = null;
        SysUser user = SecurityUtils.getLoginUser().getUser();
        List<String> roleList = user.getRoles().stream().map(SysRole::getRoleKey).collect(Collectors.toList());
        if (roleList.contains(InstructionRolesConstants.COUNTY_COCKPIT)) {
            countyName = DeptUtils.getDutyPlaceByDept(user.getDept());
            person.setDutyPlace(countyName);
        }
        Map<String, Object> params = person.getParams();
        if (person.getBeginTime()!=null){
            params.put("beginTime",person.getBeginTime());
        }
        if (person.getEndTime()!=null){
            params.put("endTime",person.getEndTime());
        }
        if (params.size()==0) {
            LocalDate monday = LocalDate.now().with(TemporalAdjusters.previousOrSame(java.time.DayOfWeek.MONDAY));
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
            String formattedMonday = monday.format(formatter);
            params.put("beginTime", formattedMonday);
            person.setParams(params);
        }
        List<InstrucationPerson> personList = new ArrayList<>();
        //查询事件数
        String personIds = eventMapper.selectPersonIdsByTime(person);
        if (personIds != null && !personIds.equals("")) {
            List<String> ids = Arrays.asList(personIds.split(","));
            params.put("personIds", ids);
            person.setParams(params);

            personList = personMapper.getPersonTransaction(person);
            if (personList != null && personList.size() != 0) {
                //按照eventNum降序排序
                personList.sort(Comparator.comparingInt(InstrucationPerson::getEventNum).reversed());
            }
        }
        return success(personList).put("count", personList.size());
    }


    @GetMapping("/getStatistics")
    public AjaxResult getStatistics(PersonAudit personAudit) {
        Map<String, Object> params = personAudit.getParams();
        if (personAudit.getBeginAuditTime()!=null){
            params.put("beginAuditTime",personAudit.getBeginAuditTime());
        }
        if (personAudit.getEndAuditTime()!=null){
            params.put("endAuditTime",personAudit.getEndAuditTime());
        }
        personAudit.setParams(params);
        SysUser user = SecurityUtils.getLoginUser().getUser();
        List<String> roleList = user.getRoles().stream().map(SysRole::getRoleKey).collect(Collectors.toList());
        if (roleList.contains(InstructionRolesConstants.COUNTY_COCKPIT)) {
            String countyName = DeptUtils.getDutyPlaceByDept(user.getDept());
            personAudit.setDutyPlace(countyName);
        }
        return personAuditService.getStatistics(personAudit);
    }

    /**
     * 获取市级指令数
     * @return
     */
    @GetMapping("/getInstrcutionCount")
    public AjaxResult getInstrcutionCount(){
        //获取指令数
        Long acount = infoMapper.getCityCount();
        return AjaxResult.success().put("count",acount);
    }

    /**
     * 获取人员数
     * @return
     */
    @GetMapping("/getPersonCount")
    public AjaxResult getPersonCount(){
        List<Map<String,Object>> maps = personMapper.getPersonCount();
        return AjaxResult.success(maps);
    }

    @GetMapping("/getJazsTownTop")
    public AjaxResult getJazsTownTop(@RequestParam(name = "month") Integer month){
        String str = "";
        if (month == 1) {
            str = "jan";
        } else if (month == 2) {
            str = "feb";
        } else if (month == 3) {
            str = "mar";
        } else if (month == 4) {
            str = "apr";
        } else if (month == 5) {
            str = "may";
        } else if (month == 6) {
            str = "jun";
        } else if (month == 7) {
            str = "jul";
        } else if (month == 8) {
            str = "aug";
        } else if (month == 9) {
            str = "sep";
        } else if (month == 10) {
            str = "oct";
        } else if (month == 11) {
            str = "nov";
        } else if (month == 12) {
            str = "dec";
        }
        String str1 = str+"_rank";
        String desc = selectTownTop(str, str1," desc");
        String aes = selectTownTop(str, str1,"");
        List<Map<String,Object>> mapsDesc = jazsMapper.selectTownTop(desc);
        List<Map<String,Object>> mapsAES = jazsMapper.selectTownTop(aes);
        return AjaxResult.success().put("desc", mapsDesc).put("aes",mapsAES);
    }


    public String selectTownTop(String str, String str1, String str2) {
        return new SQL() {{
            SELECT("county, street, " + str + " AS score, " + str1 + " AS rank");
            FROM("t_jazs");
            WHERE("`year` = 2024");
            WHERE("status = 1");
            ORDER_BY(str + str2);
            LIMIT(10);
        }}.toString();
    }


    /**
     * 查询金安指数乡镇街道排名
     * @return
     */
    @GetMapping("/getJazsNewTop")
    public AjaxResult getJazsNewTop(){
        Jazs jazs = new Jazs();
        jazs.setYear(2024);
        List<Jazs> list = jazsMapper.selectJazsList(jazs);
        for (Jazs jazsNew : list) {
            int top = 0;
            int low = 0;
            Long[] ranks = {
                    jazsNew.getJanRank(),
                    jazsNew.getFebRank(),
                    jazsNew.getMarRank(),
                    jazsNew.getAprRank(),
                    jazsNew.getMayRank(),
                    jazsNew.getJunRank(),
                    jazsNew.getJulRank(),
                    jazsNew.getAugRank(),
                    jazsNew.getSepRank(),
                    jazsNew.getOctRank(),
                    jazsNew.getNovRank(),
                    jazsNew.getDecRank()
            };

            for (Long rank : ranks) {
                if (rank != null) {
                    if (rank < 11) {
                        top++;
                    } else if (rank > 137) {
                        low++;
                    }
                }
            }
            jazsNew.setTopNum(top);
            jazsNew.setLowNum(low);
        }
        List<Jazs> topTenList = list.stream()
                .filter(j -> j.getTopNum() != null)
                .sorted(Comparator.comparingInt(Jazs::getTopNum).reversed())
                .limit(10)
                .collect(Collectors.toList());
        List<Jazs> lowTenList = list.stream()
                .filter(j -> j.getLowNum() != null)
                .sorted(Comparator.comparingInt(Jazs::getLowNum).reversed())
                .limit(10)
                .collect(Collectors.toList());
        return AjaxResult.success().put("top", topTenList).put("low",lowTenList);
    }


    @GetMapping("/getPointClass")
    public AjaxResult getPointClass(String date){
        List<Map<String,Object>> maps = jazsMapper.selectPointClass(date);
        return AjaxResult.success(maps);
    }

    /**
     * 获取人员走访情况
     * @return
     */
    @GetMapping("/personInterviewCondition")
    public AjaxResult personInterviewCondition() {
        Map<String, Object> map = new HashMap<>();
        //查询人员走访情况
        String countyName = "";
        SysUser user = SecurityUtils.getLoginUser().getUser();
        List<String> roleList = user.getRoles().stream().map(SysRole::getRoleKey).collect(Collectors.toList());
        if (roleList.contains(InstructionRolesConstants.COUNTY_COCKPIT)) {
            countyName = DeptUtils.getDutyPlaceByDept(user.getDept());
        }
        List<InstrucationPerson> personList = personMapper.selectPersonInterviewCondition(countyName);
        //当日应走访数量  查询出pLevel=3且 interviewCount大于等于1的数量 或 (pLevel=2且 interviewCount大于6的数量) 或 (pLevel=1且 interviewCount大于29的数量)
        long count = personList.stream().filter(p -> (p.getpLevel().equals(3) && p.getInterviewCount() > 0) || (p.getpLevel().equals(2) && p.getInterviewCount() > 6) || (p.getpLevel().equals(1) && p.getInterviewCount() > 29)).count();
        //当日已走访数量
        long count1 = personList.stream().filter(p -> (p.getpLevel().equals(3) && p.getInterviewCount() > 0 && p.getStrikeCount()==0) || (p.getpLevel().equals(2) && p.getInterviewCount() > 6&& p.getStrikeCount()==0) || (p.getpLevel().equals(1) && p.getInterviewCount() > 29&& p.getStrikeCount()==0)).count();
        //应走访人数
        map.put("shouldInterview", count);
        map.put("interviewed", count1);
        map.put("noInterview", count-count1);
        return AjaxResult.success(map);
    }

    /**
     * 获取人员稳控预警数据
     * @return
     */
    @GetMapping("/getStabilityControl")
    public AjaxResult getStabilityControl() {
        //获取失联失控数据
        List<DeployControl> deployControlList = deployControlMapper.getDeployControlListForDp();
        //获取controlStatus为失联的数量
        Long lostNum = deployControlList.stream().filter(d -> d.getControlStatus().equals("失联")).count();
        //查询重点人员的数量
        Long importmentNum = personMapper.getImportanceCount();
        Map<String, Object> map = new HashMap<>();
        map.put("lossNum", lostNum);
        map.put("inBeiAndHang", deployControlList.size());
        map.put("inControl", importmentNum - lostNum);
        return AjaxResult.success(map);
    }

    /**
     * 获取人员异动预警
     * @return
     */
    @GetMapping("/getPersonWarn")
    public AjaxResult getPersonWarn() {
        //查询近一个月异动人员ids
        String personIds = eventMapper.findPersonIdsByMonth();
        if (personIds == null || personIds.equals("")) {
            return AjaxResult.warn("暂无数据");
        }

        String[] split = personIds.split(",");
        Map<String, Integer> map = StringUtils.countIDs(split);
        List<InstrucationPerson> instrucationPeople = personMapper.selectImportancePersonByIds(split);
        //遍历instrucationPeople 集合 并统计出每个人员的异动次数 ，异动次数是从map中取，并统计出各个县市区的异动次数，县市区对应字段为dutyPlace
        Map<String, Integer> countyCounts = new HashMap<>();
        countyCounts.put("婺城区", 0);
        countyCounts.put("开发区", 0);
        countyCounts.put("磐安县", 0);
        countyCounts.put("兰溪市", 0);
        countyCounts.put("东阳市", 0);
        countyCounts.put("义乌市", 0);
        countyCounts.put("浦江县", 0);
        countyCounts.put("永康市", 0);
        countyCounts.put("金东区", 0);
        countyCounts.put("武义县", 0);

        for (InstrucationPerson instrucationPerson : instrucationPeople) {
            String dutyPlace = instrucationPerson.getDutyPlace();
            Integer count = map.get(instrucationPerson.getId().toString());
            instrucationPerson.setEventNum(count);
            if (dutyPlace != null) {
                countyCounts.put(dutyPlace, countyCounts.get(dutyPlace) + count);
            }
        }
        //筛选出instrucationPeople集合中eventNum前10天数据
        List<InstrucationPerson> topTenList = instrucationPeople.stream()
                .sorted(Comparator.comparingInt(InstrucationPerson::getEventNum).reversed())
                .limit(3)
                .collect(Collectors.toList());
        // 按照 value 降序排序
        List<Map.Entry<String, Integer>> sortedCountyCounts = countyCounts.entrySet()
                .stream()
                .sorted(Map.Entry.<String, Integer>comparingByValue(Comparator.reverseOrder()))
                .limit(3)
                .collect(Collectors.toList());
        List<InstrucationPerson> countyCountList = sortedCountyCounts.stream()
                .map(entry -> new InstrucationPerson(entry.getKey(), entry.getValue()))
                .collect(Collectors.toList());

        return AjaxResult.success().put("county",countyCountList).put("person",topTenList);

    }


    /**
     * 获取人员走访预警
     * @return
     */
    @GetMapping("/getPersonInterviewWarn")
    public AjaxResult getPersonInterviewWarn() {
        String countyName = "";
        SysUser user = SecurityUtils.getLoginUser().getUser();
        List<String> roleList = user.getRoles().stream().map(SysRole::getRoleKey).collect(Collectors.toList());
        if (roleList.contains(InstructionRolesConstants.COUNTY_COCKPIT)) {
            countyName = DeptUtils.getDutyPlaceByDept(user.getDept());
        }
        Map<String, Object> map = new HashMap<>();
        //查询人员走访情况
        List<InstrucationPerson> personList = personMapper.selectPersonInterviewCondition(countyName);
        //查询出应走访
        List<InstrucationPerson> collect = personList.stream().filter(p -> (p.getpLevel().equals(3) && p.getInterviewCount() > 0) || (p.getpLevel().equals(2) && p.getInterviewCount() > 6) || (p.getpLevel().equals(1) && p.getInterviewCount() > 29)).collect(Collectors.toList());
        //当日已走访数据
        List<InstrucationPerson> collect1 = personList.stream().filter(p -> (p.getpLevel().equals(3) && p.getInterviewCount() > 0 && p.getStrikeCount() == 0) || (p.getpLevel().equals(2) && p.getInterviewCount() > 6 && p.getStrikeCount() == 0) || (p.getpLevel().equals(1) && p.getInterviewCount() > 29 && p.getStrikeCount() == 0)).collect(Collectors.toList());
        //应访未访数据
        List<InstrucationPerson> collect2 = personList.stream().filter(p -> (p.getpLevel().equals(3) && p.getInterviewCount() > 0 && p.getStrikeCount() != 0) || (p.getpLevel().equals(2) && p.getInterviewCount() > 6 && p.getStrikeCount() != 0) || (p.getpLevel().equals(1) && p.getInterviewCount() > 29 && p.getStrikeCount() != 0)).collect(Collectors.toList());

        map.put("shouldInterview", collect);
        map.put("interviewed", collect1);
        map.put("noInterview", collect2);
        return AjaxResult.success(map);
    }

    /**
     * 获取人员管控预警
     * @return
     */
    @GetMapping("/getPersonControlWarn")
    public AjaxResult getPersonControlWarn() {
        //获取失联失控数据
        List<DeployControl> deployControlList = deployControlMapper.getDeployControlListForDp();
        //获取controlStatus为失联的数据集合
        List<DeployControl> slList = deployControlList.stream().filter(d -> d.getControlStatus().equals("失联")).collect(Collectors.toList());
        //获取controlStatus为在控且
        List<DeployControl> controlList = deployControlList.stream().filter(d -> !d.getControlStatus().equals("失联")).collect(Collectors.toList());
        Map<String, Object> map = new HashMap<>();
        map.put("sl", slList);
        map.put("inHangAndJin", controlList);
        return AjaxResult.success(map);
    }

    /**
     * 获取人员调档信息
     * @return
     */
    @GetMapping("/getPersonWarnTransaction")
    public AjaxResult getPersonTransaction() {
        PersonLevelWarn personLevelWarn = new PersonLevelWarn();
        String countyName = "";
        SysUser user = SecurityUtils.getLoginUser().getUser();
        List<String> roleList = user.getRoles().stream().map(SysRole::getRoleKey).collect(Collectors.toList());
        if (roleList.contains(InstructionRolesConstants.COUNTY_COCKPIT)) {
            countyName = DeptUtils.getDutyPlaceByDept(user.getDept());
            personLevelWarn.setDutyPlace(countyName);
        }
        List<PersonLevelWarn> list = personLevelWarnService.selectPersonLevelWarnList(personLevelWarn);
        //查询调档人数，disposeStatus不为null
        List<PersonLevelWarn> collect = list.stream().filter(p -> p.getDisposeStatus() != null).collect(Collectors.toList());
        Map map = new HashMap();
        map.put("totalCount", list.size());
        map.put("dispose", collect.size());
        map.put("noDispose", list.size() - collect.size());
        return AjaxResult.success().put("data",map);
    }




}
