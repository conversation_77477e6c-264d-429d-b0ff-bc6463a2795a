package com.ruoyi.instruction.mapper;

import com.ruoyi.instruction.domain.rspVo.CountyInstructionHandlingRsp;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * 市级指令办理情况Mapper接口
 *
 * <AUTHOR>
 * @date 2025-01-20
 */
public interface CityInstructionHandlingMapper {

    /**
     * 获取超时接收数量
     *
     * @param params 查询参数
     * @return 超时接收数量
     */
    Long getTimeoutReceiveCount(@Param("params") Map<String, Object> params);

    /**
     * 获取超时处置数量
     *
     * @param params 查询参数
     * @return 超时处置数量
     */
    Long getTimeoutDisposeCount(@Param("params") Map<String, Object> params);

    /**
     * 获取应处置未处置数据
     *
     * @param params 查询参数
     * @return 应处置未处置数据（包含单位数和部门数）
     */
    Map<String, Object> getUnprocessedData(@Param("params") Map<String, Object> params);

    /**
     * 获取应处置未处置数据（区分部门和镇街）
     *
     * @param params 查询参数
     * @return 应处置未处置数据（区分县级部门和乡镇街道）
     */
    Map<String, Object> getUnprocessedDataDetailed(@Param("params") Map<String, Object> params);

    /**
     * 获取待反馈数据（按紧急程度分类）
     *
     * @param params 查询参数
     * @return 待反馈数据（包含7天、15天、30天的数量）
     */
    Map<String, Object> getPendingFeedbackData(@Param("params") Map<String, Object> params);

    /**
     * 获取超时接收指令列表
     *
     * @param params 查询参数
     * @return 超时接收指令列表
     */
    List<Map<String, Object>> getTimeoutReceiveList(@Param("params") Map<String, Object> params);

    /**
     * 获取超时处置指令列表
     *
     * @param params 查询参数
     * @return 超时处置指令列表
     */
    List<Map<String, Object>> getTimeoutDisposeList(@Param("params") Map<String, Object> params);

    /**
     * 获取应处置未处置指令列表
     *
     * @param params 查询参数
     * @return 应处置未处置指令列表
     */
    List<Map<String, Object>> getUnprocessedList(@Param("params") Map<String, Object> params);

    /**
     * 获取待反馈指令列表（按紧急程度分类）
     *
     * @param params 查询参数
     * @return 待反馈指令列表
     */
    List<Map<String, Object>> getPendingFeedbackList(@Param("params") Map<String, Object> params);

    // ==================== 县市层级指令办理情况 ====================

    /**
     * 获取县市层级应接收未接收统计数据
     *
     * @param params 查询参数
     * @return 应接收未接收统计数据
     */
    Map<String, Object> getCountyNoReceiveStats(@Param("params") Map<String, Object> params);

    /**
     * 获取县市层级应处置未处置统计数据
     *
     * @param params 查询参数
     * @return 应处置未处置统计数据
     */
    Map<String, Object> getCountyNoDisposeStats(@Param("params") Map<String, Object> params);

    /**
     * 获取县市层级应接收未接收详细列表
     *
     * @param params 查询参数
     * @return 应接收未接收详细列表
     */
    List<CountyInstructionHandlingRsp> getCountyNoReceiveList(@Param("params") Map<String, Object> params);

    /**
     * 获取县市层级应处置未处置详细列表
     *
     * @param params 查询参数
     * @return 应处置未处置详细列表
     */
    List<CountyInstructionHandlingRsp> getCountyNoDisposeList(@Param("params") Map<String, Object> params);
}
