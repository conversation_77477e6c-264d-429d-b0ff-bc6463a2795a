package com.ruoyi.instruction.controller;

import java.util.List;
import java.util.Map;
import javax.servlet.http.HttpServletResponse;

import com.ruoyi.instruction.domain.InstrucationPerson;
import com.ruoyi.instruction.domain.Jazs;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.instruction.domain.Pazs;
import com.ruoyi.instruction.service.IPazsService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;
import org.springframework.web.multipart.MultipartFile;

/**
 * 平安指数Controller
 *
 * <AUTHOR>
 * @date 2023-08-09
 */
@RestController
@RequestMapping("/system/pazs")
public class PazsController extends BaseController
{
    @Autowired
    private IPazsService pazsService;

    /**
     * 查询平安指数列表
     */
    @PreAuthorize("@ss.hasPermi('system:pazs:list')")
    @GetMapping("/list")
    public TableDataInfo list(Pazs pazs)
    {
        startPage();
        List<Pazs> list = pazsService.selectPazsList(pazs);
        return getDataTable(list);
    }

    /**
     * 导出平安指数列表
     */
    @PreAuthorize("@ss.hasPermi('system:pazs:export')")
    @Log(title = "平安指数", businessType = BusinessType.EXPORT)
    @GetMapping("/export")
    public AjaxResult export(Pazs pazs)
    {
        String type = pazs.getType();
        String year = pazs.getYear()!=null?pazs.getYear().toString():"";
        String conditon = pazs.getCondition();
        String rankName = pazs.getRankName();
        String rankType = pazs.getRankType();
        List<Pazs> list = pazsService.selectPazsNewList(type,year,conditon,rankName,rankType);
//        List<Pazs> list = pazsService.selectPazsList(pazs);
        ExcelUtil<Pazs> util = new ExcelUtil<Pazs>(Pazs.class);
        return util.exportExcel(list, "pazs");
    }

    /**
     * 获取平安指数详细信息
     */
    @PreAuthorize("@ss.hasPermi('system:pazs:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(pazsService.selectPazsById(id));
    }

    /**
     * 新增平安指数
     */
    @PreAuthorize("@ss.hasPermi('system:pazs:add')")
    @Log(title = "平安指数", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody Pazs pazs)
    {
        return toAjax(pazsService.insertPazs(pazs));
    }

    /**
     * 修改平安指数
     */
    @PreAuthorize("@ss.hasPermi('system:pazs:edit')")
    @Log(title = "平安指数", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody Pazs pazs)
    {
        return toAjax(pazsService.updatePazs(pazs));
    }

    /**
     * 删除平安指数
     */
    @PreAuthorize("@ss.hasPermi('system:pazs:remove')")
    @Log(title = "平安指数", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(pazsService.deletePazsByIds(ids));
    }

    /**
     * 下载平安指数导入模板
     *
     * @param response
     */
    @PostMapping("/importTemplate")
    public void importTemplate(HttpServletResponse response) {
        ExcelUtil<Pazs> util = new ExcelUtil<Pazs>(Pazs.class);
        util.importTemplateExcel(response, "平安指数导入模板");
    }

    /**
     * 导入平安指数数据
     */
    @Log(title = "平安指数", businessType = BusinessType.IMPORT)
    @PostMapping("/importPazsData")
    public AjaxResult importPazsData(MultipartFile file) throws Exception {
        ExcelUtil<Pazs> util = new ExcelUtil<Pazs>(Pazs.class);
        List<Pazs> pazsList = util.importExcel(file.getInputStream());
        //获取操作用户
        String operName = getUsername();
        //存入数据
        String msg = pazsService.importPazs(pazsList, operName);
        return AjaxResult.success(msg);
    }

    /**
     * 查询平安指数列表（新）
     */
    @GetMapping("/newlist")
    public TableDataInfo newlist(String type,String year,String condition,String rankName,String rankType)
    {
        startPage();
        List<Pazs> list = pazsService.selectPazsNewList(type,year,condition,rankName,rankType);
        return getDataTable(list);
    }
}
