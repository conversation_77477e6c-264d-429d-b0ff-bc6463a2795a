package com.ruoyi.instruction.mapper;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import com.ruoyi.instruction.domain.GkPerson;
import org.apache.ibatis.annotations.Param;

/**
 * 管控人员Mapper接口
 * 
 * <AUTHOR>
 * @date 2023-06-01
 */
public interface GkPersonMapper 
{
    /**
     * 查询管控人员
     * 
     * @param id 管控人员主键
     * @return 管控人员
     */
    public GkPerson selectGkPersonById(Long id);

    /**
     * 查询管控人员列表
     * 
     * @param gkPerson 管控人员
     * @return 管控人员集合
     */
    public List<GkPerson> selectGkPersonList(GkPerson gkPerson);

    /**
     * 新增管控人员
     * 
     * @param gkPerson 管控人员
     * @return 结果
     */
    public int insertGkPerson(GkPerson gkPerson);

    /**
     * 修改管控人员
     * 
     * @param gkPerson 管控人员
     * @return 结果
     */
    public int updateGkPerson(GkPerson gkPerson);

    /**
     * 删除管控人员
     * 
     * @param id 管控人员主键
     * @return 结果
     */
    public int deleteGkPersonById(Long id);

    /**
     * 批量删除管控人员
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteGkPersonByIds(Long[] ids);

    /**
     * 查询已处置或已交办人员预警事件
     * @return
     */
    List<String> findDisposeIds();

    /**
     * 通过身份证号查询布控最后一条信息
     * @param idCard
     * @return
     */
    GkPerson selectGkPersonBySfzhLast(@Param("idCard") String idCard);

    /**
     * 根据管控人员ids查询管控人员信息
     * @param ids
     * @return
     */
    List<GkPerson> selectGkPersonByIds(String[] ids);

    /**
     * 查询已处置预警对应类型
     * @return
     */
    List<Map<String, Integer>> findDisposeMap();

    /**
     * 驾驶舱查询人员布控列表
     * @param gkPerson
     * @return
     */
    List<HashMap> selectGkPersonListForBigScreen(GkPerson gkPerson);

    /**
     * 获取基层智治数据
     * @return
     */
    List<Map<String,Object>> getJczzEventType();

    /**
     * 查询已布控人员id
     * @return
     */
    List<Long> findPersonIds();

}
