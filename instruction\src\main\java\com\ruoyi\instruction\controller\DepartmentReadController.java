package com.ruoyi.instruction.controller;

import java.util.*;
import java.util.stream.Collectors;
import javax.servlet.http.HttpServletResponse;

import com.ruoyi.common.constant.Constants;
import com.ruoyi.common.constant.InstructionRolesConstants;
import com.ruoyi.common.core.domain.entity.SysRole;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.instruction.domain.InstructionInfo;
import com.ruoyi.instruction.domain.rspVo.InstructionInfoRspVo;
import com.ruoyi.instruction.mapper.DepartmentReadMapper;
import com.ruoyi.instruction.service.impl.InstructionInfoServiceImpl;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.instruction.domain.DepartmentRead;
import com.ruoyi.instruction.service.IDepartmentReadService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 部门协同已读/未读Controller
 *
 * <AUTHOR>
 * @date 2023-10-10
 */
@RestController
@RequestMapping("/department/read")
public class DepartmentReadController extends BaseController
{
    @Autowired
    private IDepartmentReadService departmentReadService;

    @Autowired
    private DepartmentReadMapper departmentReadMapper;

    @Autowired
    private InstructionInfoServiceImpl instructionInfoService;

    /**
     * 查询部门协同已读/未读列表
     */
    @GetMapping("/list")
    public TableDataInfo list(DepartmentRead departmentRead)
    {
        startPage();
        List<DepartmentRead> list = departmentReadService.selectDepartmentReadList(departmentRead);
        return getDataTable(list);
    }

    /**
     * 导出部门协同已读/未读列表
     */
    @Log(title = "部门协同已读/未读", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, DepartmentRead departmentRead)
    {
        List<DepartmentRead> list = departmentReadService.selectDepartmentReadList(departmentRead);
        ExcelUtil<DepartmentRead> util = new ExcelUtil<DepartmentRead>(DepartmentRead.class);
        util.exportExcel(response, list, "部门协同已读/未读数据");
    }

    /**
     * 获取部门协同已读/未读详细信息
     */
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(departmentReadService.selectDepartmentReadById(id));
    }

    /**
     * 新增部门协同已读/未读
     */
    @Log(title = "部门协同已读/未读", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody DepartmentRead departmentRead)
    {
        return toAjax(departmentReadService.insertDepartmentRead(departmentRead));
    }

    /**
     * 修改部门协同已读/未读
     */
    @Log(title = "部门协同已读/未读", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody DepartmentRead departmentRead)
    {
        return toAjax(departmentReadService.updateDepartmentRead(departmentRead));
    }

    /**
     * 删除部门协同已读/未读
     */
    @Log(title = "部门协同已读/未读", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(departmentReadService.deleteDepartmentReadByIds(ids));
    }


    /**
     * 根据用户部门id获取未读数
     * @return
     */
    @GetMapping("/getUnRead")
    public AjaxResult getUnRead(){
        Long deptId = SecurityUtils.getDeptId();
        Long count = departmentReadMapper.getUnRead(deptId);
        return AjaxResult.success(count);
    }

    /**
     * 根据部门id将记录设为已读
     * @return
     */
    @GetMapping("/ReadDepartment")
    public AjaxResult ReadDepartment(){
        Long deptId = SecurityUtils.getDeptId();
        int row = departmentReadMapper.ReadDepartmentRecord(deptId);
        return AjaxResult.success(row);
    }

    /**
     * 获取部门协同集合
     * @param departmentRead
     * @return
     */
    @GetMapping("/getDepartmentList")
    public TableDataInfo getDepartmentList(DepartmentRead departmentRead){
        //进行权限判断 县市区只能看到自己的会商列表,拥有角色则可以
        List<SysRole> roles = SecurityUtils.getLoginUser().getUser().getRoles();
        List<String> roleList = roles.stream().map(SysRole::getRoleKey).collect(Collectors.toList());
        List<Long> instructionIds = new ArrayList<>();
        List<InstructionInfoRspVo> collect = new ArrayList<>();
        if (!roleList.contains(InstructionRolesConstants.CITY_LEADER)) {
            //部门协同
            Long deptId = SecurityUtils.getDeptId();
            departmentRead.setDeptId(deptId);
            // startPage();
            List<DepartmentRead> departmentReads = departmentReadMapper.selectDepartmentReadList(departmentRead);
            instructionIds = departmentReads.stream().map(DepartmentRead::getInstructionId).collect(Collectors.toList());
        }else {
            //领导角色 可查看所有未读记录
            // startPage();
            instructionIds = departmentReadMapper.selectInstructionIdForLeader(departmentRead);
        }
        if (instructionIds.size()>0){
            //去查询指令 根据市政法委权限去查询
            InstructionInfo info = new InstructionInfo();
            info.setCreateDeptId(Constants.JINHUA_CITY_DEPT_ID);
            info.setStatus("1");
            Map<String, Object> params = info.getParams();
            params.put("ids",instructionIds);
            info.setParams(params);
            // startPage();
            collect = instructionInfoService.getDepartmentInstructionInfo(info);
            //根据指令ids查询部门协同已读记录
            List<Map<String,Object>> readStr = departmentReadMapper.findReadStrByInstructionIds(instructionIds);
            for (InstructionInfoRspVo rspVo:collect) {
                for (Map<String,Object> map:readStr) {
                    Long instructionId = (Long) map.get("instruction_id");
                    if (instructionId.equals(rspVo.getId())){
                        String readStr1 = (String) map.get("readStr");
                        String departmentStr = (String) map.get("deptNames");
                        rspVo.setDepartmentStr(departmentStr);
                        rspVo.setReadUnits(readStr1);
                        break;
                    }
                }
            }
        }

        TableDataInfo dataTable = getDataTable(instructionIds);
        if (collect.size()!=0){
            int start = departmentRead.getEnd() * (departmentRead.getStart() - 1);
            collect = collect.stream().sorted(Comparator.comparing(InstructionInfoRspVo::getAssignTime, Comparator.nullsFirst(Date::compareTo)).reversed()).skip(start).limit(departmentRead.getEnd()).collect(Collectors.toList());
        }
        dataTable.setRows(collect);
        return dataTable;
    }

    /**
     * 根据指令id查询该指令已读/未读数
     * @param instructionId
     * @return
     */
    @GetMapping("/getReadRecordsByInstructionId/{id}")
    public AjaxResult getReadRecordsByInstructionId(@PathVariable("id") Long instructionId){
        Map<String, Object> map = new HashMap<>();
        //查询已读记录
        List<Map<String,Object>> readRecord = departmentReadMapper.selectReadRecord(instructionId);
        //查询未读记录
        List<String> noReadRecord = departmentReadMapper.selectNoReadRecord(instructionId);
        map.put("readRecord",readRecord);
        map.put("noReadRecord",noReadRecord);
        map.put("reads",readRecord.size());
        map.put("noReads",noReadRecord.size());
        return AjaxResult.success(map);
    }



}
