# 县市层级指令办理情况8个数据字段验证

## 修改内容总结

根据用户要求，已将`getCountyHandlingData`方法的返回数据从6个字段扩展为8个字段，将应接收未接收和应处置未处置的指令数按照县级部门和乡镇街道分别统计。

## 修改前后对比

### 修改前（6个字段）
```json
{
  "noReceiveCountyDeptCount": 5,        // 应接收未接收的县级部门数量
  "noReceiveTownDeptCount": 12,         // 应接收未接收的乡镇街道数量
  "noReceiveInstructionCount": 8,       // 应接收未接收的指令数量（总计）
  "noDisposeCountyDeptCount": 3,        // 应处置未处置的县级部门数量
  "noDisposeTownDeptCount": 7,          // 应处置未处置的乡镇街道数量
  "noDisposeInstructionCount": 6        // 应处置未处置的指令数量（总计）
}
```

### 修改后（8个字段）
```json
{
  "noReceiveCountyDeptCount": 5,           // 应接收未接收的县级部门数量
  "noReceiveTownDeptCount": 12,            // 应接收未接收的乡镇街道数量
  "noReceiveCountyInstructionCount": 8,    // 应接收未接收的县级部门指令数量
  "noReceiveTownInstructionCount": 15,     // 应接收未接收的乡镇街道指令数量
  "noDisposeCountyDeptCount": 3,           // 应处置未处置的县级部门数量
  "noDisposeTownDeptCount": 7,             // 应处置未处置的乡镇街道数量
  "noDisposeCountyInstructionCount": 6,    // 应处置未处置的县级部门指令数量
  "noDisposeTownInstructionCount": 10      // 应处置未处置的乡镇街道指令数量
}
```

## 具体修改内容

### 1. Controller层修改
**文件**: `instruction/src/main/java/com/ruoyi/instruction/controller/CityInstructionHandlingController.java`

**修改内容**:
- 在`getCountyHandlingData`方法中，将原来的6个返回字段扩展为8个
- 新增了`noReceiveCountyInstructionCount`、`noReceiveTownInstructionCount`、`noDisposeCountyInstructionCount`、`noDisposeTownInstructionCount`四个字段

### 2. SQL查询修改
**文件**: `instruction/src/main/resources/mapper/instruction/CityInstructionHandlingMapper.xml`

#### 应接收未接收统计查询修改
```sql
-- 修改前
SELECT
    COUNT(DISTINCT CASE WHEN dept_level = 'county' THEN receive_dept END) as countyDeptCount,
    COUNT(DISTINCT CASE WHEN dept_level = 'town' THEN receive_dept END) as townDeptCount,
    COUNT(DISTINCT instruction_id) as instructionCount

-- 修改后
SELECT
    COUNT(DISTINCT CASE WHEN dept_level = 'county' THEN receive_dept END) as countyDeptCount,
    COUNT(DISTINCT CASE WHEN dept_level = 'town' THEN receive_dept END) as townDeptCount,
    COUNT(DISTINCT CASE WHEN dept_level = 'county' THEN instruction_id END) as countyInstructionCount,
    COUNT(DISTINCT CASE WHEN dept_level = 'town' THEN instruction_id END) as townInstructionCount
```

#### 应处置未处置统计查询修改
```sql
-- 修改前
SELECT
    COUNT(DISTINCT CASE WHEN dept_level = 'county' THEN feedback_dept END) as countyDeptCount,
    COUNT(DISTINCT CASE WHEN dept_level = 'town' THEN feedback_dept END) as townDeptCount,
    COUNT(DISTINCT instruction_id) as instructionCount

-- 修改后
SELECT
    COUNT(DISTINCT CASE WHEN dept_level = 'county' THEN feedback_dept END) as countyDeptCount,
    COUNT(DISTINCT CASE WHEN dept_level = 'town' THEN feedback_dept END) as townDeptCount,
    COUNT(DISTINCT CASE WHEN dept_level = 'county' THEN instruction_id END) as countyInstructionCount,
    COUNT(DISTINCT CASE WHEN dept_level = 'town' THEN instruction_id END) as townInstructionCount
```

### 3. 文档更新
已同步更新以下文档：
- `县市层级指令办理情况API文档.md` - 更新了响应示例和字段说明
- `县市层级指令办理情况前端示例.html` - 更新了统计数据显示
- `县市层级指令办理情况测试脚本.js` - 更新了数据验证函数
- `县市层级指令办理情况开发总结.md` - 更新了接口说明

## 业务逻辑说明

### 数据统计逻辑
1. **部门数量统计**：统计涉及的不同部门数量，按县级部门和乡镇街道分类
2. **指令数量统计**：统计涉及的不同指令数量，按县级部门和乡镇街道分类

### 部门分类规则
- **县级部门**：磐安县、兰溪市、东阳市、义乌市、浦江县、永康市、金东区、婺城区、开发区、武义县
- **乡镇街道**：除县级部门外的其他部门

### 数据去重处理
- 使用`COUNT(DISTINCT ...)`确保同一部门或指令不会被重复统计
- 通过`CASE WHEN`条件分别统计县级部门和乡镇街道的数据

## 验证要点

### 1. 数据一致性验证
- 验证县级部门指令数 + 乡镇街道指令数 = 总指令数
- 验证统计数据与详细列表数据的一致性

### 2. 分类准确性验证
- 验证部门分类是否正确（县级部门 vs 乡镇街道）
- 验证指令归属是否正确

### 3. 接口响应验证
- 验证返回的8个字段都存在且为数字类型
- 验证字段命名符合预期

## 测试建议

### 1. 单元测试
```javascript
// 验证返回数据结构
function validateResponse(data) {
    const expectedFields = [
        'noReceiveCountyDeptCount',
        'noReceiveTownDeptCount',
        'noReceiveCountyInstructionCount',
        'noReceiveTownInstructionCount',
        'noDisposeCountyDeptCount',
        'noDisposeTownDeptCount',
        'noDisposeCountyInstructionCount',
        'noDisposeTownInstructionCount'
    ];
    
    return expectedFields.every(field => 
        data.hasOwnProperty(field) && typeof data[field] === 'number'
    );
}
```

### 2. 集成测试
- 调用`/county/data`接口，验证返回8个字段
- 调用`/county/list`接口，验证详细数据与统计数据的一致性
- 测试不同筛选条件下的数据准确性

### 3. 业务逻辑测试
- 验证县级部门和乡镇街道的分类逻辑
- 验证应接收未接收和应处置未处置的判断逻辑
- 验证时间和区域筛选的准确性

## 总结

此次修改成功将县市层级指令办理情况统计数据从6个字段扩展为8个字段，实现了按部门类型（县级部门/乡镇街道）分别统计指令数量的需求。修改涉及Controller层、SQL查询层和相关文档，确保了数据的准确性和一致性。
