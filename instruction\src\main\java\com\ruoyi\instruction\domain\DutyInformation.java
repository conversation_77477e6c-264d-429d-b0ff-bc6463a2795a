package com.ruoyi.instruction.domain;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.util.Date;

/**
 * 今日值班信息对象 t_duty_information
 * 
 * <AUTHOR>
 * @date 2023-08-04
 */
public class DutyInformation extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 自增主键id */
    private Long id;

    /** 区域名称 */
    @Excel(name = "区域名称")
    private String areaName;

    /** 总指挥长id */
    @Excel(name = "总指挥长id")
    private String zzhzId;

    /** 值班领导id */
    @Excel(name = "值班领导id")
    private String zbldId;

    /** 值班长id */
    @Excel(name = "值班长id")
    private String zbzId;

    /** 值班日期 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "值班日期", width = 30, dateFormat = "yyyy-MM-dd")
    private Date dutyTime;

    /** 周几 */
    @Excel(name = "周几")
    private String xq;

    /** 创建时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "创建时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date cTime;

    /** 修改时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "修改时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date uTime;

    /** 总指挥长状态，1永久，2临时 */
    @Excel(name = "总指挥长状态，1永久，2临时")
    private Integer zzhzStatus;

    /** 值班领导状态，1永久，2临时 */
    @Excel(name = "值班领导状态，1永久，2临时")
    private Integer zbldStatus;

    /** 值班长状态，1永久，2临时 */
    @Excel(name = "值班长状态，1永久，2临时")
    private Integer zbzStatus;
    /**
     * 开始时间
     */
    private String startTime;
    /**
     * 结束时间
     */
    private String endTime;

    private String zzhzxm;
    private String zzhzdh;
    private String zzhztx;

    private String zbldxm;
    private String zblddh;
    private String zbldtx;

    private String zbzxm;
    private String zbzdh;
    private String zbztx;

    /** 值班领导id */
    @Excel(name = "值班领导1id")
    private String zbldId1;

    /** 值班长id */
    @Excel(name = "值班长1id")
    private String zbzId1;

    private String zbldxm1;
    private String zblddh1;
    private String zbldtx1;

    private String zbzxm1;
    private String zbzdh1;
    private String zbztx1;

    /** 值班领导id */
    @Excel(name = "值班领导2id")
    private String zbldId2;

    /** 值班长id */
    @Excel(name = "值班长2id")
    private String zbzId2;

    private String zbldxm2;
    private String zblddh2;
    private String zbldtx2;

    private String zbzxm2;
    private String zbzdh2;
    private String zbztx2;
    /**
     * 风险等级1一级，2二级
     */
    private  Integer level;
    public void setId(Long id) 
    {
        this.id = id;
    }

    public Long getId() 
    {
        return id;
    }
    public void setAreaName(String areaName) 
    {
        this.areaName = areaName;
    }

    public String getAreaName() 
    {
        return areaName;
    }
    public void setZzhzId(String zzhzId) 
    {
        this.zzhzId = zzhzId;
    }

    public String getZzhzId() 
    {
        return zzhzId;
    }
    public void setZbldId(String zbldId) 
    {
        this.zbldId = zbldId;
    }

    public String getZbldId() 
    {
        return zbldId;
    }
    public void setZbzId(String zbzId) 
    {
        this.zbzId = zbzId;
    }

    public String getZbzId() 
    {
        return zbzId;
    }
    public void setDutyTime(Date dutyTime) 
    {
        this.dutyTime = dutyTime;
    }

    public Date getDutyTime() 
    {
        return dutyTime;
    }
    public void setXq(String xq) 
    {
        this.xq = xq;
    }

    public String getXq() 
    {
        return xq;
    }
    public void setcTime(Date cTime) 
    {
        this.cTime = cTime;
    }

    public Date getcTime() 
    {
        return cTime;
    }
    public void setuTime(Date uTime) 
    {
        this.uTime = uTime;
    }

    public Date getuTime() 
    {
        return uTime;
    }
    public void setZzhzStatus(Integer zzhzStatus) 
    {
        this.zzhzStatus = zzhzStatus;
    }

    public Integer getZzhzStatus() 
    {
        return zzhzStatus;
    }
    public void setZbldStatus(Integer zbldStatus) 
    {
        this.zbldStatus = zbldStatus;
    }

    public Integer getZbldStatus() 
    {
        return zbldStatus;
    }
    public void setZbzStatus(Integer zbzStatus) 
    {
        this.zbzStatus = zbzStatus;
    }

    public Integer getZbzStatus() 
    {
        return zbzStatus;
    }

    public String getStartTime() {
        return startTime;
    }

    public void setStartTime(String startTime) {
        this.startTime = startTime;
    }

    public String getEndTime() {
        return endTime;
    }

    public void setEndTime(String endTime) {
        this.endTime = endTime;
    }

    public String getZzhzxm() {
        return zzhzxm;
    }

    public void setZzhzxm(String zzhzxm) {
        this.zzhzxm = zzhzxm;
    }

    public String getZzhzdh() {
        return zzhzdh;
    }

    public void setZzhzdh(String zzhzdh) {
        this.zzhzdh = zzhzdh;
    }

    public String getZzhztx() {
        return zzhztx;
    }

    public void setZzhztx(String zzhztx) {
        this.zzhztx = zzhztx;
    }

    public String getZbldxm() {
        return zbldxm;
    }

    public void setZbldxm(String zbldxm) {
        this.zbldxm = zbldxm;
    }

    public String getZblddh() {
        return zblddh;
    }

    public void setZblddh(String zblddh) {
        this.zblddh = zblddh;
    }

    public String getZbldtx() {
        return zbldtx;
    }

    public void setZbldtx(String zbldtx) {
        this.zbldtx = zbldtx;
    }

    public String getZbzxm() {
        return zbzxm;
    }

    public void setZbzxm(String zbzxm) {
        this.zbzxm = zbzxm;
    }

    public String getZbzdh() {
        return zbzdh;
    }

    public void setZbzdh(String zbzdh) {
        this.zbzdh = zbzdh;
    }

    public String getZbztx() {
        return zbztx;
    }

    public void setZbztx(String zbztx) {
        this.zbztx = zbztx;
    }

    public String getZbldId1() {
        return zbldId1;
    }

    public void setZbldId1(String zbldId1) {
        this.zbldId1 = zbldId1;
    }

    public String getZbzId1() {
        return zbzId1;
    }

    public void setZbzId1(String zbzId1) {
        this.zbzId1 = zbzId1;
    }

    public String getZbldxm1() {
        return zbldxm1;
    }

    public void setZbldxm1(String zbldxm1) {
        this.zbldxm1 = zbldxm1;
    }

    public String getZblddh1() {
        return zblddh1;
    }

    public void setZblddh1(String zblddh1) {
        this.zblddh1 = zblddh1;
    }

    public String getZbldtx1() {
        return zbldtx1;
    }

    public void setZbldtx1(String zbldtx1) {
        this.zbldtx1 = zbldtx1;
    }

    public String getZbzxm1() {
        return zbzxm1;
    }

    public void setZbzxm1(String zbzxm1) {
        this.zbzxm1 = zbzxm1;
    }

    public String getZbzdh1() {
        return zbzdh1;
    }

    public void setZbzdh1(String zbzdh1) {
        this.zbzdh1 = zbzdh1;
    }

    public String getZbztx1() {
        return zbztx1;
    }

    public void setZbztx1(String zbztx1) {
        this.zbztx1 = zbztx1;
    }

    public Integer getLevel() {
        return level;
    }

    public void setLevel(Integer level) {
        this.level = level;
    }

    public String getZbldId2() {
        return zbldId2;
    }

    public void setZbldId2(String zbldId2) {
        this.zbldId2 = zbldId2;
    }

    public String getZbzId2() {
        return zbzId2;
    }

    public void setZbzId2(String zbzId2) {
        this.zbzId2 = zbzId2;
    }

    public String getZbldxm2() {
        return zbldxm2;
    }

    public void setZbldxm2(String zbldxm2) {
        this.zbldxm2 = zbldxm2;
    }

    public String getZblddh2() {
        return zblddh2;
    }

    public void setZblddh2(String zblddh2) {
        this.zblddh2 = zblddh2;
    }

    public String getZbldtx2() {
        return zbldtx2;
    }

    public void setZbldtx2(String zbldtx2) {
        this.zbldtx2 = zbldtx2;
    }

    public String getZbzxm2() {
        return zbzxm2;
    }

    public void setZbzxm2(String zbzxm2) {
        this.zbzxm2 = zbzxm2;
    }

    public String getZbzdh2() {
        return zbzdh2;
    }

    public void setZbzdh2(String zbzdh2) {
        this.zbzdh2 = zbzdh2;
    }

    public String getZbztx2() {
        return zbztx2;
    }

    public void setZbztx2(String zbztx2) {
        this.zbztx2 = zbztx2;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("areaName", getAreaName())
            .append("zzhzId", getZzhzId())
            .append("zbldId", getZbldId())
            .append("zbzId", getZbzId())
            .append("dutyTime", getDutyTime())
            .append("xq", getXq())
            .append("cTime", getcTime())
            .append("uTime", getuTime())
            .append("zzhzStatus", getZzhzStatus())
            .append("zbldStatus", getZbldStatus())
            .append("zbzStatus", getZbzStatus())
            .toString();
    }
}
