package com.ruoyi.instruction.controller;

import java.util.*;
import java.util.stream.Collectors;
import javax.servlet.http.HttpServletResponse;

import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.instruction.domain.DahuaVideo;
import com.ruoyi.instruction.service.IDahuaVideoService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 大华视频信息
 * Controller
 *
 * <AUTHOR>
 * @date 2023-08-30
 */
@RestController
@RequestMapping("/instruction/video")
public class DahuaVideoController extends BaseController {
    @Autowired
    private IDahuaVideoService dahuaVideoService;

    /**
     * 获取视频场景及点位
     * @return
     */
    @GetMapping("/getScene")
    public AjaxResult getScene(){
        List<Map<String,Object>> mapList = dahuaVideoService.getSene();
        Map<Object, List<Map<String, Object>>> scene = mapList.stream().collect(Collectors.groupingBy(map -> map.get("scene"), LinkedHashMap::new, Collectors.toList()));
        List<Map<Object, Object>> finallyMap = new ArrayList<>();
        int num = 0;
        for (Object s : scene.keySet()) {
            Map<Object, Object> map = new HashMap<>();
            map.put("name", s);
            map.put("id",num++);

            List<Map<String, Object>> mapList1 = scene.get(s);
            for (Map<String,Object> mps1:mapList1) {
                mps1.put("id",num++);
            }
            map.put("list", mapList1);
            finallyMap.add(map);
        }
        return AjaxResult.success(finallyMap);
    }

    /**
     * 返回随机的六个视频
     * @return
     */
    @GetMapping("/getRandomVideos")
    public AjaxResult getRandomVideos() {
        List<String> videos = dahuaVideoService.getRandomVideos();
        //*/ 对list进行随机排序，然后选取前6个元素
        Collections.shuffle(videos);
        List<String> finalVideos = videos.subList(0, 6);
        //*/
        return AjaxResult.success(finalVideos);
    }

    /**
     * 根据id查询视频
     * @param ids
     * @return
     */
    @GetMapping("/getVideoByIds/{ids}")
    public AjaxResult getVideoByIds(@PathVariable Long[] ids){
        List<DahuaVideo> list = dahuaVideoService.getVideoByIds(ids);
        return AjaxResult.success(list);
    }

    /**
     * 查询大华视频信息
     * 列表
     */
    @PreAuthorize("@ss.hasPermi('instruction:video:list')")
    @GetMapping("/list")
    public TableDataInfo list(DahuaVideo dahuaVideo) {
        startPage();
        List<DahuaVideo> list = dahuaVideoService.selectDahuaVideoList(dahuaVideo);
        return getDataTable(list);
    }

    /**
     * 导出大华视频信息
     * 列表
     */
    @PreAuthorize("@ss.hasPermi('instruction:video:export')")
    @Log(title = "大华视频信息", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, DahuaVideo dahuaVideo) {
        List<DahuaVideo> list = dahuaVideoService.selectDahuaVideoList(dahuaVideo);
        ExcelUtil<DahuaVideo> util = new ExcelUtil<DahuaVideo>(DahuaVideo.class);
        util.exportExcel(response, list, "大华视频信息数据");
    }

    /**
     * 获取大华视频信息
     * 详细信息
     */
    @PreAuthorize("@ss.hasPermi('instruction:video:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id) {
        return success(dahuaVideoService.selectDahuaVideoById(id));
    }

    /**
     * 新增大华视频信息
     */
    @PreAuthorize("@ss.hasPermi('instruction:video:add')")
    @Log(title = "大华视频信息", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody DahuaVideo dahuaVideo) {
        return toAjax(dahuaVideoService.insertDahuaVideo(dahuaVideo));
    }

    /**
     * 修改大华视频信息
     */
    @PreAuthorize("@ss.hasPermi('instruction:video:edit')")
    @Log(title = "大华视频信息", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody DahuaVideo dahuaVideo) {
        return toAjax(dahuaVideoService.updateDahuaVideo(dahuaVideo));
    }

    /**
     * 删除大华视频信息
     */
    @PreAuthorize("@ss.hasPermi('instruction:video:remove')")
    @Log(title = "大华视频信息", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids) {
        return toAjax(dahuaVideoService.deleteDahuaVideoByIds(ids));
    }
}
