package com.ruoyi.instruction.domain.rspVo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.common.annotation.Excel;
import lombok.Data;

import java.util.Date;

/**
 * 命案分析
 * <AUTHOR> @version 1.0
 * @date 2023/3/6 9:18
 *
 */
@Data
public class DailyNewsMonthVo {

    public Long   id;


    /** 要闻时间 */
    @JsonFormat(pattern = "MM月dd日")
    @Excel(name = "要闻时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date newsTime;

    /** 期数 */
    @Excel(name = "期数")
    private String newsNum;

}
