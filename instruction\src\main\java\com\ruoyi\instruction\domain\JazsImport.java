package com.ruoyi.instruction.domain;

import com.ruoyi.common.annotation.Excel;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

/**
 * 金安指数导入模板
 */
public class JazsImport {

    private static final long serialVersionUID = 1L;

    /** 序号 */
    private Long id;

    /** 年份 */
    @Excel(name = "年份")
    private Integer year;

    /** 月份 */
    @Excel(name = "月份",combo = {"1","2","3","4","5","6","7","8","9","10","11","12"})
    private Integer month;

    /** 县市区 */
    @Excel(name = "县市区", combo = {"婺城区","金东区","兰溪市","东阳市","义乌市","永康市","浦江县","武义县","磐安县","开发区"})
    private String county;

    /** 乡镇街道 */
    @Excel(name = "乡镇街道")
    private String street;

    /** 得分 */
    @Excel(name = "得分")
    private Double score;

    /** 排名   */
    @Excel(name = "排名")
    private Long rank;



    public void setId(Long id)
    {
        this.id = id;
    }

    public Long getId()
    {
        return id;
    }
    public void setYear(Integer year)
    {
        this.year = year;
    }

    public Integer getYear()
    {
        return year;
    }
    public void setMonth(Integer month)
    {
        this.month = month;
    }

    public Integer getMonth()
    {
        return month;
    }
    public void setCounty(String county)
    {
        this.county = county;
    }

    public String getCounty()
    {
        return county;
    }
    public void setStreet(String street)
    {
        this.street = street;
    }

    public String getStreet()
    {
        return street;
    }
    public void setScore(Double score)
    {
        this.score = score;
    }

    public Double getScore()
    {
        return score;
    }
    public void setRank(Long rank)
    {
        this.rank = rank;
    }

    public Long getRank()
    {
        return rank;
    }


    @Override
    public String toString() {
        return new ToStringBuilder(this, ToStringStyle.MULTI_LINE_STYLE)
                .append("id", getId())
                .append("year", getYear())
                .append("month", getMonth())
                .append("county", getCounty())
                .append("street", getStreet())
                .append("score", getScore())
                .append("rank", getRank())
                .toString();
    }
}
