package com.ruoyi.instruction.domain.rspVo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.common.annotation.Excel;
import lombok.Data;
import org.apache.poi.ss.usermodel.IndexedColors;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2023/9/12 16:32
 * 大屏督察位点返回类
 */
@Data
public class BigScreenMcafVo {

    /**
     * 主键id
     */
    private Long id;

    /**
     * 企业店招名称
     */
    private String companyName;

    /**
     * 企业经营地址
     */
    private String companyAddress;

    /**
     * 企业所属行业
     */
    private String companyCategory;

    /**
     * 暗访任务开始时间
     */
    private String missionStartTime;

    /**
     * 已发现问题数量
     */
    private Integer issueCount;

    /**
     * 解决数量数量
     */
    private Integer resolvedCount;

    /**
     * 经度
     */
    private String lon;

    /**
     * 纬度
     */
    private String lat;
}
