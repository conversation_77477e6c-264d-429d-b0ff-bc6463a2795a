package com.ruoyi.instruction.domain;

import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 信访人员库对象 t_petition_person
 *
 * <AUTHOR>
 * @date 2024-04-29
 */
@Data
public class PetitionPerson extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键id */
    private Long id;

    /** 信访人员姓名 */
    @Excel(name = "信访人员姓名")
    private String personName;

    /** 责任属地 */
    @Excel(name = "责任属地")
    private String dutyPlace;

    /** 责任乡镇 */
    @Excel(name = "责任乡镇")
    private String dutyTown;

    /** 内容分类 */
    @Excel(name = "内容分类")
    private String contentType;

    /** 赴省次数 */
    @Excel(name = "赴省次数")
    private Long provinceNum;

    /** 进京次数 */
    @Excel(name = "进京次数")
    private Long capitalNum;

    /** 关联群体名称 */
    @Excel(name = "关联群体名称")
    private String groupName;

    /** 关联群体ids */
    @Excel(name = "关联群体ids")
    private String groupIds;

    /** 问题属地 */
    @Excel(name = "问题属地")
    private String problemPlace;

    /**
     * 上报单位
     */
    private String rankUnit;

    /**
     * 上访总次数
     */
    private Long acount;


}
