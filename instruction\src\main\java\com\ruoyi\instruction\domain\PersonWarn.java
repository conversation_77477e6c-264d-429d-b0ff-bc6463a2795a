package com.ruoyi.instruction.domain;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 人员预警对象 t_person_warn
 *
 * <AUTHOR>
 * @date 2024-01-29
 */
public class PersonWarn extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键id */
    private Long id;

    /** 人员id */
    @Excel(name = "人员id")
    private Long personId;

    /** 人员姓名 */
    @Excel(name = "人员姓名")
    private String personName;

    /** 当前等级 -1：无 0：撤档1：黄 2：橙  3：红
 */
    @Excel(name = "当前等级 -1：无 0：撤档1：黄 2：橙  3：红 ")
    private Integer nowLevel;

    /** 申请等级 -1：无 0：撤档1：黄 2：橙  3：红
 */
    @Excel(name = "申请等级 -1：无 0：撤档1：黄 2：橙  3：红 ")
    private Integer applyLevel;

    /** 责任所属地 */
    @Excel(name = "责任所属地")
    private String dutyPlace;

    /** 乡镇街道 */
    @Excel(name = "乡镇街道")
    private String personTown;

    /** 调整理由 */
    @Excel(name = "调整理由")
    private String adjustReason;

    /** 状态 1：正常 9：删除 */
    @Excel(name = "状态 1：正常 9：删除")
    private String status;

    /** 预警状态1：待处理 2：已申请  3：已知晓 */
    @Excel(name = "预警状态1：待处理 2：已申请  3：已知晓")
    private Integer warnStatus;

    /** 预警时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "预警时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date warnTime;

    public void setId(Long id)
    {
        this.id = id;
    }

    public Long getId()
    {
        return id;
    }
    public void setPersonId(Long personId)
    {
        this.personId = personId;
    }

    public Long getPersonId()
    {
        return personId;
    }
    public void setPersonName(String personName)
    {
        this.personName = personName;
    }

    public String getPersonName()
    {
        return personName;
    }
    public void setNowLevel(Integer nowLevel)
    {
        this.nowLevel = nowLevel;
    }

    public Integer getNowLevel()
    {
        return nowLevel;
    }
    public void setApplyLevel(Integer applyLevel)
    {
        this.applyLevel = applyLevel;
    }

    public Integer getApplyLevel()
    {
        return applyLevel;
    }
    public void setDutyPlace(String dutyPlace)
    {
        this.dutyPlace = dutyPlace;
    }

    public String getDutyPlace()
    {
        return dutyPlace;
    }
    public void setPersonTown(String personTown)
    {
        this.personTown = personTown;
    }

    public String getPersonTown()
    {
        return personTown;
    }
    public void setAdjustReason(String adjustReason)
    {
        this.adjustReason = adjustReason;
    }

    public String getAdjustReason()
    {
        return adjustReason;
    }
    public void setStatus(String status)
    {
        this.status = status;
    }

    public String getStatus()
    {
        return status;
    }
    public void setWarnStatus(Integer warnStatus)
    {
        this.warnStatus = warnStatus;
    }

    public Integer getWarnStatus()
    {
        return warnStatus;
    }
    public void setWarnTime(Date warnTime)
    {
        this.warnTime = warnTime;
    }

    public Date getWarnTime()
    {
        return warnTime;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("personId", getPersonId())
            .append("personName", getPersonName())
            .append("nowLevel", getNowLevel())
            .append("applyLevel", getApplyLevel())
            .append("dutyPlace", getDutyPlace())
            .append("personTown", getPersonTown())
            .append("adjustReason", getAdjustReason())
            .append("status", getStatus())
            .append("warnStatus", getWarnStatus())
            .append("warnTime", getWarnTime())
            .toString();
    }
}
