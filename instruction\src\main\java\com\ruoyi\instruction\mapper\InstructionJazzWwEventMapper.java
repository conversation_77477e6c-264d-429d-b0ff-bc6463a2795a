package com.ruoyi.instruction.mapper;

import com.ruoyi.instruction.domain.InstructionEcologicalEnv;
import com.ruoyi.instruction.domain.InstructionEvent;
import com.ruoyi.instruction.domain.InstructionEventInfos;
import com.ruoyi.instruction.domain.rspVo.*;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.time.LocalDate;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 金安智治驾驶舱统计维稳的事件
 *
 * <AUTHOR> @date 2022-12-16
 */
@Mapper
public interface InstructionJazzWwEventMapper {
    /**
     * 金安大数据基础数据
     * @return
     */
    List<BigScreenJazzCommonVo> basicData();

    /**
     * 统计某一类事件的数量
     * @return
     */
    List<String> selectCountByType(@Param("type") Integer type);

    /**
     * 统计某一类事件的详情
     * @param instructionEvent
     * @return
     */
    List<InstructionEvent> selectCountByTypeForDetails(InstructionEvent instructionEvent);

    /**
     *
     * @param instructionEvent
     * @return
     */
    List<HashMap> countTypeNum(InstructionEvent instructionEvent);
    /**
     *
     * @param type
     * @return
     */
    BigScreenJazzCommonVo countTypeBig(Integer type);
}
