package com.ruoyi.instruction.mapper;

import java.util.List;
import java.util.Map;

import com.ruoyi.instruction.domain.PersonAudit;
import org.apache.ibatis.annotations.Param;

/**
 * 人员申请记录Mapper接口
 *
 * <AUTHOR>
 * @date 2024-01-05
 */
public interface PersonAuditMapper
{
    /**
     * 查询人员申请记录
     *
     * @param id 人员申请记录主键
     * @return 人员申请记录
     */
    public PersonAudit selectPersonAuditById(Long id);

    /**
     * 查询人员申请记录列表
     *
     * @param personAudit 人员申请记录
     * @return 人员申请记录集合
     */
    public List<PersonAudit> selectPersonAuditList(PersonAudit personAudit);

    /**
     * 新增人员申请记录
     *
     * @param personAudit 人员申请记录
     * @return 结果
     */
    public int insertPersonAudit(PersonAudit personAudit);

    /**
     * 修改人员申请记录
     *
     * @param personAudit 人员申请记录
     * @return 结果
     */
    public int updatePersonAudit(PersonAudit personAudit);

    /**
     * 删除人员申请记录
     *
     * @param id 人员申请记录主键
     * @return 结果
     */
    public int deletePersonAuditById(Long id);

    /**
     * 批量删除人员申请记录
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deletePersonAuditByIds(Long[] ids);

    /**
     * 根据人员id获取人员最后申请id
     * @param personAudit
     * @return
     */
    PersonAudit getFinallyRecordByPersonId(PersonAudit personAudit);

    /**
     * 根据人员id更新人员调级审核状态
     * @param ids
     */
    void updatePersonAuditStatus(Long[] ids);

    /**
     * 查询人员等级变更列表
     * @param personAudit
     * @return
     */
    List<PersonAudit> selectGradeChangeList(PersonAudit personAudit);

    /**
     * 查询人员等级变更统计
     * @param personAudit
     * @return
     */
    List<Map<Object, Object>> getStatistics(PersonAudit personAudit);

    /**
     * 根据时间查询统计
     * @param personAudit
     * @return
     */
    List<Map<Object, Object>> getStatisticsByDate(PersonAudit personAudit);

    /**
     * 统计人员变更
     * @param personAudit
     * @return
     */
    List<Map<Object, Object>> getGradeChangeStatistics(PersonAudit personAudit);

    /**
     * 获取人员初始等级
     * @param personAudit
     * @return
     */
    List<PersonAudit> getPersonInitalLevel(PersonAudit personAudit);

    /**
     * 查询人员类型集合
     * @param personAudit
     * @return
     */
    List<PersonAudit> getPersonTypeList(PersonAudit personAudit);

}
