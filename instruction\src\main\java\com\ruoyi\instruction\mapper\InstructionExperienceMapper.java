package com.ruoyi.instruction.mapper;



import com.ruoyi.instruction.domain.InstructionExperience;

import java.util.List;

/**
 * 【经验创新】Mapper接口
 *
 * <AUTHOR>
 * @date 2023-05-17
 */
public interface InstructionExperienceMapper
{
    /**
     * 查询【经验创新】
     *
     * @param id 【经验创新】主键
     * @return
     */
    public InstructionExperience selectInstructionExperienceById(Long id);

    /**
     * 查询【经验创新】列表
     *
     * @param instructionExperience
     * @return 【经验创新】集合
     */
    public List<InstructionExperience> selectInstructionExperienceList(InstructionExperience instructionExperience);

    /**
     * 新增【经验创新】
     *
     * @param instructionExperience
     * @return 结果
     */
    public int insertInstructionExperience(InstructionExperience instructionExperience);

    /**
     * 修改【经验创新】
     *
     * @param instructionExperience
     * @return 结果
     */
    public int updateInstructionExperience(InstructionExperience instructionExperience);

    /**
     * 删除【经验创新】
     *
     * @param id 【经验创新】主键
     * @return 结果
     */
    public int deleteInstructionExperienceById(Long id);

    /**
     * 批量删除【经验创新】
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteInstructionExperienceByIds(Long[] ids);
    /**
     * 基层安全板块的 经验创新小模块
     * @return 结果
     */
    public List<InstructionExperience> selectExpInGrassRoots();
}
