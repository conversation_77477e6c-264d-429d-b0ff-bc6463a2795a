<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.examineAir.mapper.ExamineBillboardMapper">

    <resultMap type="ExamineBillboard" id="ExamineBillboardResult">
            <result property="id" column="id"/>
            <result property="content" column="content"/>
            <result property="cTime" column="c_time"/>
            <result property="uTime" column="u_time"/>
            <result property="status" column="status"/>
            <result property="creatorUserId" column="creator_user_id"/>
            <result property="creatorName" column="creator_name"/>
            <result property="type" column="type"/>
    </resultMap>

    <sql id="selectExamineBillboardVo">
        select id, content, c_time, u_time, status, creator_user_id, creator_name, type
        from t_examine_billboard
    </sql>

    <select id="selectExamineBillboardList" parameterType="ExamineBillboard" resultMap="ExamineBillboardResult">
        <include refid="selectExamineBillboardVo"/>
        <where>
            <if test="content != null  and content != ''">and content like concat('%', #{content}, '%')</if>
            <if test="cTime != null ">and c_time = #{cTime}</if>
            <if test="uTime != null ">and u_time = #{uTime}</if>
            <if test="status != null ">and status = #{status}</if>
            <if test="creatorUserId != null ">and creator_user_id = #{creatorUserId}</if>
            <if test="creatorName != null  and creatorName != ''">and creator_name like concat('%', #{creatorName},
                '%')
            </if>
            <if test="type != null ">and type = #{type}</if>
            <if test="startTime != null  and endTime != null">and c_time  between #{startTime} and #{endTime}</if>
            order by  c_time desc
        </where>
    </select>

    <select id="selectExamineBillboardById" parameterType="Long"
            resultMap="ExamineBillboardResult">
            <include refid="selectExamineBillboardVo"/>
            where status = 1 and id = #{id}
    </select>

    <insert id="insertExamineBillboard" parameterType="ExamineBillboard" useGeneratedKeys="true"
            keyProperty="id">
        insert into t_examine_billboard
        <trim prefix="(" suffix=")" suffixOverrides=",">
                    <if test="content != null">content,</if>
                    <if test="cTime != null">c_time,</if>
                    <if test="uTime != null">u_time,</if>
                    <if test="status != null">status,</if>
                    <if test="creatorUserId != null">creator_user_id,</if>
                    <if test="creatorName != null">creator_name,</if>
                    <if test="type != null">type,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
                    <if test="content != null">#{content},</if>
                    <if test="cTime != null">#{cTime},</if>
                    <if test="uTime != null">#{uTime},</if>
                    <if test="status != null">#{status},</if>
                    <if test="creatorUserId != null">#{creatorUserId},</if>
                    <if test="creatorName != null">#{creatorName},</if>
                    <if test="type != null">#{type},</if>
        </trim>
    </insert>

    <update id="updateExamineBillboard" parameterType="ExamineBillboard">
        update t_examine_billboard
        <trim prefix="SET" suffixOverrides=",">
                    <if test="content != null">content = #{content},</if>
                    <if test="cTime != null">c_time = #{cTime},</if>
                    <if test="uTime != null">u_time = #{uTime},</if>
                    <if test="status != null">status = #{status},</if>
                    <if test="creatorUserId != null">creator_user_id = #{creatorUserId},</if>
                    <if test="creatorName != null">creator_name = #{creatorName},</if>
                    <if test="type != null">type = #{type},</if>
        </trim>
        where id = #{id}
    </update>

    <update id="deleteExamineBillboardById" parameterType="Long">
        delete from t_examine_billboard
        where id = #{id}
    </update>

    <update id="deleteExamineBillboardByIds" parameterType="String">
        delete from t_examine_billboard  where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>
</mapper>