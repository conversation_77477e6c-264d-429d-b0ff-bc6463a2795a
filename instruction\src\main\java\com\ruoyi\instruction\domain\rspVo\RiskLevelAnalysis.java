package com.ruoyi.instruction.domain.rspVo;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.format.NumberFormat;
import lombok.Data;
import lombok.Getter;

import java.lang.reflect.Field;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;

/**
 * 增加风险等级判断
 */
@Data
public class RiskLevelAnalysis {

    @ExcelProperty({"县市区域"})
    private String countyName;

    @ExcelIgnore
    @Getter
    private static HashMap<String, List<String>> fieldMap;

    static {
        fieldMap = new HashMap<>();
        fieldMap.put("1", Arrays.asList("level1Count", "level1SolvedCount", "level1OverTimeCount", "level1DissolutionRate"));
        fieldMap.put("2", Arrays.asList("level2Count", "level2SolvedCount", "level2OverTimeCount", "level2DissolutionRate"));
        fieldMap.put("3", Arrays.asList("level3Count", "level3SolvedCount", "level3OverTimeCount", "level3DissolutionRate"));
        fieldMap.put("4", Arrays.asList("level4Count", "level4SolvedCount", "level4OverTimeCount", "level4DissolutionRate"));
    }

    @ExcelProperty({"重大风险", "排查数"})
    private int level1Count;
    @ExcelProperty({"重大风险", "化解数"})
    private int level1SolvedCount;
    @ExcelProperty({"重大风险", "化解率"})
    @NumberFormat("0.00%")
    private float level1DissolutionRate;
    @ExcelProperty({"重大风险", "超期未化解"})
    private int level1OverTimeCount;


    @ExcelProperty({"高风险", "排查数"})
    private int level2Count;
    @ExcelProperty({"高风险", "化解数"})
    private int level2SolvedCount;
    @ExcelProperty({"高风险", "化解率"})
    @NumberFormat("0.00%")
    private float level2DissolutionRate;
    @ExcelProperty({"高风险", "超期未化解"})
    private int level2OverTimeCount;


    @ExcelProperty({"中风险", "排查数"})
    private int level3Count;
    @ExcelProperty({"中风险", "化解数"})
    private int level3SolvedCount;
    @ExcelProperty({"中风险", "化解率"})
    @NumberFormat("0.00%")
    private float level3DissolutionRate;
    @ExcelProperty({"中风险", "超期未化解"})
    private int level3OverTimeCount;

    @ExcelProperty({"低风险", "排查数"})
    private int level4Count;
    @ExcelProperty({"低风险", "化解数"})
    private int level4SolvedCount;
    @ExcelProperty({"低风险", "化解率"})
    @NumberFormat("0.00%")
    private float level4DissolutionRate;
    @ExcelProperty({"低风险", "超期未化解"})
    private int level4OverTimeCount;


    public RiskLevelAnalysis(String countyName) {
        this.countyName = countyName;

    }


    /**
     * 根据属性名获取属性值
     *
     * @param fieldName
     * @return
     */
    public Object getFieldByName(String fieldName) {
        try {
            Field field = this.getClass().getDeclaredField(fieldName);
            field.setAccessible(true);
            return field.get(this);
        } catch (NoSuchFieldException | IllegalAccessException e) {
            e.printStackTrace();
            return null;
        }
    }

    /**
     * 根据属性名设置属性值
     *
     * @param fieldName
     * @param newValue
     */

    public void setFieldByName(String fieldName, Object newValue) {
        try {
            Field field = this.getClass().getDeclaredField(fieldName);
            field.setAccessible(true);
            field.set(this, newValue);
        } catch (NoSuchFieldException | IllegalAccessException e) {
            e.printStackTrace();
        }
    }

}



