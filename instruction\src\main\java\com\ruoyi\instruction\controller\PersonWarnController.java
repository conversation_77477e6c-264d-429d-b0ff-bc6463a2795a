package com.ruoyi.instruction.controller;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import javax.servlet.http.HttpServletResponse;

import com.ruoyi.common.constant.Constants;
import com.ruoyi.common.core.domain.entity.SysDept;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.system.mapper.SysDeptMapper;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.instruction.domain.PersonWarn;
import com.ruoyi.instruction.service.IPersonWarnService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 人员预警Controller
 *
 * <AUTHOR>
 * @date 2024-01-29
 */
@RestController
@RequestMapping("/person/warn")
public class PersonWarnController extends BaseController
{
    @Autowired
    private IPersonWarnService personWarnService;

    @Autowired
    private SysDeptMapper sysDeptMapper;

    /**
     * 查询人员预警列表
     */
    // @PreAuthorize("@ss.hasPermi('person:warn:list')")
    @GetMapping("/list")
    public TableDataInfo list(PersonWarn personWarn)
    {
        SysDept dept = SecurityUtils.getLoginUser().getUser().getDept();
        Long parentId = dept.getParentId();

        if (parentId.equals(Constants.JINHUA_CITY_DEPT_ID) || dept.getDeptId().equals(Constants.JINHUA_CITY_DEPT_ID)) {

        } else if (parentId == 213L || parentId == 214L || parentId == 215L || parentId == 216L || parentId == 217L || parentId == 218L || parentId == 219L || parentId == 220L || parentId == 221L || parentId == 262L) {
            SysDept parentDept = sysDeptMapper.selectDeptById(parentId);
            String countyName = parentDept.getDeptName().replace("委政法委", "").replace("政法委", "").replace("政法办", "");
            personWarn.setDutyPlace(countyName);
        }else{
            personWarn.setPersonTown(dept.getDeptName());
        }
        startPage();
        List<PersonWarn> list = personWarnService.selectPersonWarnList(personWarn);
        return getDataTable(list);
    }

    /**
     * 导出人员预警列表
     */
    // @PreAuthorize("@ss.hasPermi('person:warn:export')")
    @Log(title = "人员预警", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, PersonWarn personWarn)
    {
        List<PersonWarn> list = personWarnService.selectPersonWarnList(personWarn);
        ExcelUtil<PersonWarn> util = new ExcelUtil<PersonWarn>(PersonWarn.class);
        util.exportExcel(response, list, "人员预警数据");
    }

    /**
     * 获取人员预警详细信息
     */
    // @PreAuthorize("@ss.hasPermi('person:warn:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(personWarnService.selectPersonWarnById(id));
    }

    /**
     * 新增人员预警
     */
    // @PreAuthorize("@ss.hasPermi('person:warn:add')")
    @Log(title = "人员预警", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody PersonWarn personWarn)
    {
        return toAjax(personWarnService.insertPersonWarn(personWarn));
    }

    /**
     * 修改人员预警
     */
    // @PreAuthorize("@ss.hasPermi('person:warn:edit')")
    @Log(title = "人员预警", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody PersonWarn personWarn)
    {
        return toAjax(personWarnService.updatePersonWarn(personWarn));
    }

    /**
     * 删除人员预警
     */
    // @PreAuthorize("@ss.hasPermi('person:warn:remove')")
    @Log(title = "人员预警", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(personWarnService.deletePersonWarnByIds(ids));
    }
}
