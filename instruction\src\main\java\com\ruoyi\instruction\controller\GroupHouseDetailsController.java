package com.ruoyi.instruction.controller;

import java.util.ArrayList;
import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.instruction.domain.GroupHouseDetails;
import com.ruoyi.instruction.service.IGroupHouseDetailsService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 群体涉及户数、金额Controller
 *
 * <AUTHOR>
 * @date 2024-12-19
 */
@RestController
@RequestMapping("/group/house")
public class GroupHouseDetailsController extends BaseController
{
    @Autowired
    private IGroupHouseDetailsService groupHouseDetailsService;

    /**
     * 查询群体涉及户数、金额列表
     */
    @PreAuthorize("@ss.hasPermi('group:house:list')")
    @GetMapping("/list")
    public AjaxResult list(GroupHouseDetails groupHouseDetails)
    {
        List<GroupHouseDetails> groupHouseDetailsList = new ArrayList<>();
        if (groupHouseDetails.getGroupId() != null) {
            groupHouseDetailsList = groupHouseDetailsService.selectGroupHouseDetailsList(groupHouseDetails);
        }
        if (groupHouseDetails.getGroupId() == null || groupHouseDetailsList.size() == 0) {
            groupHouseDetails.setGroupId(-1L);
            groupHouseDetailsList = groupHouseDetailsService.selectGroupHouseDetailsList(groupHouseDetails);
            for (GroupHouseDetails details : groupHouseDetailsList) {
                details.setId(null);
                details.setGroupId(null);
            }
        }
        return AjaxResult.success(groupHouseDetailsList);
    }

    /**
     * 导出群体涉及户数、金额列表
     */
    @PreAuthorize("@ss.hasPermi('group:house:export')")
    @Log(title = "群体涉及户数、金额", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, GroupHouseDetails groupHouseDetails)
    {
        List<GroupHouseDetails> list = groupHouseDetailsService.selectGroupHouseDetailsList(groupHouseDetails);
        ExcelUtil<GroupHouseDetails> util = new ExcelUtil<GroupHouseDetails>(GroupHouseDetails.class);
        util.exportExcel(response, list, "群体涉及户数、金额数据");
    }

    /**
     * 获取群体涉及户数、金额详细信息
     */
    @PreAuthorize("@ss.hasPermi('group:house:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(groupHouseDetailsService.selectGroupHouseDetailsById(id));
    }

    /**
     * 新增群体涉及户数、金额
     */
    @PreAuthorize("@ss.hasPermi('group:house:add')")
    @Log(title = "群体涉及户数、金额", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody GroupHouseDetails groupHouseDetails)
    {
        return toAjax(groupHouseDetailsService.insertGroupHouseDetails(groupHouseDetails));
    }

    /**
     * 修改群体涉及户数、金额
     */
    @PreAuthorize("@ss.hasPermi('group:house:edit')")
    @Log(title = "群体涉及户数、金额", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody GroupHouseDetails groupHouseDetails)
    {
        return toAjax(groupHouseDetailsService.updateGroupHouseDetails(groupHouseDetails));
    }

    /**
     * 删除群体涉及户数、金额
     */
    @PreAuthorize("@ss.hasPermi('group:house:remove')")
    @Log(title = "群体涉及户数、金额", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(groupHouseDetailsService.deleteGroupHouseDetailsByIds(ids));
    }
}
