package com.ruoyi.instruction.domain;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;
import lombok.Data;

import java.util.Date;

@Data
public class EasyCauseTrouble extends BaseEntity {

    private Long id;

    /**
     * 姓名
     */
    @Excel(name = "姓名")
    private String name;

    /**
     * 电话号码
     */
    @Excel(name = "电话号码")
    private String phone;

    /**
     * 区县
     */
    @Excel(name = "属地")
    private String county;

    /**
     * 乡镇街道
     */
    @Excel(name = "责任乡镇")
    private String street;

    /**
     * 身份证号
     */
    private String personCard;



    /**
     * 户籍地址
     */
    @Excel(name = "户籍地址")
    private String residenceAddress;

    /**
     * 居住地址
     */
    @Excel(name = "居住地址")
    private String residentialAddress;

    /**
     * 危险性
     */
    @Excel(name = "类型(管控)")
    private String risk;

    /**
     * 目前诊断
     */
    @Excel(name = "目前诊断")
    private String currentDiagnosis;

    /**
     * 纳入管理时间
     */
    @Excel(name = "纳入管理时间", dateFormat = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date manageTime;

    /**
     * 危险性等级
     */
    @Excel(name = "管控级别(公安)")
    private String riskLevel;

    /**
     * 警情次数
     */
    @Excel(name = "警情次数(2023年以来)")
    private int policeNum;



    /**
     * 治疗情况
     */
    @Excel(name = "治疗情况")
    private String treatment;

    /**
     * 走访次数
     */
    @Excel(name = "走访次数")
    private int visitNum;

    /**
     * 随访日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @Excel(name = "最新走访时间", dateFormat = "yyyy-MM-dd")
    private Date followDate;

}
