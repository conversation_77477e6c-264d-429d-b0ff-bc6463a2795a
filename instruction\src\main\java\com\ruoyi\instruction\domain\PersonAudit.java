package com.ruoyi.instruction.domain;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 人员申请记录对象 t_person_audit
 *
 * <AUTHOR>
 * @date 2024-01-05
 */
@Data
public class PersonAudit extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键id */
    private Long id;

    /** 人员id */
    // @Excel(name = "人员id")
    private Long personId;

    /** 人员姓名 */
    @Excel(name = "人员姓名")
    private String personName;


    /** 责任所属地 */
    @Excel(name = "属地")
    private String dutyPlace;


    /** 乡镇街道 */
    @Excel(name = "责任乡镇")
    private String pTown;


    /** 申请等级 1：高  2：中  3：低  4：撤档 */
    @Excel(name = "初始等级",readConverterExp = "-1=无,0=撤档,1=黄,2=橙,3=红")
    private Integer applyLevel;

    /** 当前等级 1：高  2：中  3：低  4：撤档 */
    @Excel(name = "当前等级",readConverterExp = "-1=无,0=撤档,1=黄,2=橙,3=红")
    private Integer nowLevel;

    /** 申请时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm")
    @Excel(name = "申请入库时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm")
    private Date sqTime;

    /** 调整理由 */
    @Excel(name = "申请理由")
    private String adjustReason;

    /** 申请单位 */
    @Excel(name = "申请部门")
    private String sqdw;


    /** 审核时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm")
    @Excel(name = "申请审核时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm")
    private Date auditTime;

    /** 审核单位 */
    @Excel(name = "审核部门")
    private String auditDw;

    /** 申请单位部门id */
    // @Excel(name = "申请单位部门id")
    private Long sqdwDeptId;


    /** 市级审核意见 0：待审核 1：同意   2：驳回 */
    private Integer isAuditCity;

    /** 审核理由 */
    private String auditReason;

    /** 县级审核意见0：待审核 1：同意  2：驳回 */
    private Integer isAuditCounty;


    /** 状态 1：正常 9：删除 */
    private String status;


    /** 是否结束 0：未结束  1: 已结束 */
    private Integer isFinish;

    /** 审核人 */
    private String auditPerson;

    /** 申请人 */
    private String sqPerson;

    /** 审核人部门id */
    private Long auditDeptId;

    /**
     * 审核类型 1:需我审核  2:我发起的审核
     */
    private Integer auditType;

    /**
     * 账号类型 1:市级  2:县市区 3:乡镇街道
     */
    private Integer accountType;

    /**
     * 当前部门id
     */
    private Long deptId;


    /**
     * 排序类型 1:按申请时间降序 2:按申请时间升序  3:按审核时间降序 4:按审核时间升序
     */
    private Integer sortType;

    /** 县市区审核理由 */
    private String auditReasonCounty;

    /** 县市区审核理由 */
    private String auditPersonCounty;

    /** 县市区审核时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm")
    private Date auditTimeCounty;


    /** 县市区审核单位 */
    private String auditDwCounty;

    /** 县市区审核单位id */
    private Long auditDeptIdCounty;

    /**
     * 是否跨级 0：不是 1：是
     */
    private Integer isSkipLevel;

    /**
     * 县市区查询字段
     */
    private String countyParam;

    /**
     * 乡镇街道查询字段
     */
    private String townParam;

    /**
     * 升降等级 1：升  2：降
     */
    private Integer type;

    /**
     * 人员类型
     */
    private String personType;

    /**
     * 升降等级总数
     */
    private Long aCount;

    /**
     * 是否包含易肇事肇祸人员
     */
    private Integer isTrouble;

    /** 附件ids */
    private String fileIds;


    /** 人员类型  */
    private String pType;

    private String beginAuditTime;

    private String endAuditTime;

    public String getpType() {
        return pType;
    }

    public void setpType(final String pType) {
        this.pType = pType;
    }

    public String getpTown() {
        return pTown;
    }

    public void setpTown(final String pTown) {
        this.pTown = pTown;
    }
}
