package com.ruoyi.instruction.domain;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 网络舆情对象 t_network_yq
 * 
 * <AUTHOR>
 * @date 2023-09-14
 */
@Data
public class NetworkYq extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** id  */
    private String id;

    /** 编号 */
    @Excel(name = "编号")
    private String code;

    /** 标题 */
    @Excel(name = "标题")
    private String title;

    /** 内容 */
    @Excel(name = "内容")
    private String content;

    /** 时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date issueTime;

    /** 部门名称 */
    @Excel(name = "部门名称")
    private String deptName;

    /** 风险等级 */
    @Excel(name = "风险等级")
    private Integer infoType;

    /** 风险等级名称 */
    @Excel(name = "风险等级名称")
    private String infoTypeName;

    /** 来源 */
    @Excel(name = "来源")
    private Integer source;

    /** 来源名称 */
    @Excel(name = "来源名称")
    private String sourceName;

    /** 状态 1：正常 9：异常 */
    @Excel(name = "状态 1：正常 9：异常")
    private String status;

    /** 添加类型 1：手动 2：数仓对接 */
    @Excel(name = "添加类型 1：手动 2：数仓对接")
    private Integer addType;

}
