package com.ruoyi.instruction.domain.rspVo;

import com.alibaba.excel.annotation.ExcelProperty;
import com.ruoyi.common.annotation.Excel;
import lombok.Data;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/10/9 11:24
 */
@Data
public class InstructionPersonControlExportVo {

    private Long personId;

    @Excel(name = "管控人员")
    private String name;

    /** 单位 */
    @Excel(name = "管控人员单位")
    private String unit;

    /** 职位 */
    @Excel(name = "管控人员职位")
    private String position;


    /** 是否是责任人 1：责任人 2：非责任人 */
    @Excel(name = "是否为组长",combo = {"是","否"},readConverterExp ="1=是,2=否")
    private String dutyer;

    /** 电话 */
    @Excel(name = "电话")
    private String telephone;


}
