package com.ruoyi.instruction.mapper;

import java.util.List;
import com.ruoyi.instruction.domain.UndercoverInspection;
import com.ruoyi.instruction.domain.reqVo.RiskHiddenDangerReq;
import com.ruoyi.instruction.domain.rspVo.AfdcTjVo;
import com.ruoyi.instruction.domain.rspVo.PmTopRsp;
import org.apache.ibatis.annotations.Mapper;

/**
 * 暗访督察Mapper接口
 * 
 * <AUTHOR>
 * @date 2023-05-23
 */
@Mapper
public interface UndercoverInspectionMapper 
{
    /**
     * 查询暗访督察
     * 
     * @param id 暗访督察主键
     * @return 暗访督察
     */
    public UndercoverInspection selectUndercoverInspectionById(Long id);

    /**
     * 查询暗访督察列表
     * 
     * @param undercoverInspection 暗访督察
     * @return 暗访督察集合
     */
    public List<UndercoverInspection> selectUndercoverInspectionList(UndercoverInspection undercoverInspection);

    /**
     * 新增暗访督察
     * 
     * @param undercoverInspection 暗访督察
     * @return 结果
     */
    public int insertUndercoverInspection(UndercoverInspection undercoverInspection);

    /**
     * 修改暗访督察
     * 
     * @param undercoverInspection 暗访督察
     * @return 结果
     */
    public int updateUndercoverInspection(UndercoverInspection undercoverInspection);

    /**
     * 删除暗访督察
     * 
     * @param id 暗访督察主键
     * @return 结果
     */
    public int deleteUndercoverInspectionById(Long id);

    /**
     * 批量删除暗访督察
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteUndercoverInspectionByIds(Long[] ids);

    /**
     * 领域问题top10
     * @param riskHiddenDangerReq
     * @return
     */
    List<PmTopRsp> lywtTop10(RiskHiddenDangerReq riskHiddenDangerReq);

    /**
     * 各县市问题排名
     * @param riskHiddenDangerReq
     * @return
     */
    List<PmTopRsp> xswtpm(RiskHiddenDangerReq riskHiddenDangerReq);

    /**
     * 暗访督察统计
     * @param riskHiddenDangerReq
     * @return
     */
    AfdcTjVo afdcTj(RiskHiddenDangerReq riskHiddenDangerReq);
}
