package com.ruoyi.instruction.controller.openapi;

import com.github.pagehelper.PageInfo;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.constant.HttpStatus;
import com.ruoyi.common.constant.InstructionRolesConstants;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.domain.entity.SysDept;
import com.ruoyi.common.core.domain.entity.SysRole;
import com.ruoyi.common.core.domain.entity.SysUser;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.instruction.domain.InstructionFile;
import com.ruoyi.instruction.domain.TOpenWorkInspection;
import com.ruoyi.instruction.domain.rspVo.InstructionInfoRspVo;
import com.ruoyi.instruction.domain.rspVo.OpenWorkImage;
import com.ruoyi.instruction.enums.AFReceiveUnit;
import com.ruoyi.instruction.mapper.TOpenWorkInspectionMapper;
import com.ruoyi.instruction.service.IInstructionFileService;
import com.ruoyi.instruction.service.ITOpenUndercoverInspectionService;
import com.ruoyi.instruction.service.ITOpenWorkInspectionService;
import com.ruoyi.instruction.service.impl.InstructionInfoServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 工作督查数据对接Controller
 *
 * <AUTHOR>
 * @date 2024-08-08
 */
@RestController
@RequestMapping("/instruction/TOpenWorkInspection")
public class TOpenWorkInspectionController extends BaseController
{
    @Autowired
    private ITOpenWorkInspectionService tOpenWorkInspectionService;

    @Autowired
    private IInstructionFileService instructionFileService;

    @Autowired
    private TOpenWorkInspectionMapper tOpenWorkInspectionMapper;

    @Autowired
    private ITOpenUndercoverInspectionService openUndercoverInspectionService;

    @Autowired
    private InstructionInfoServiceImpl infoService;

    /**
     * 查询工作督查数据对接列表
     */
    @PreAuthorize("@ss.hasPermi('instruction:TOpenWorkInspection:list')")
    @GetMapping("/list")
    public TableDataInfo list(TOpenWorkInspection tOpenWorkInspection)
    {

        SysUser user = SecurityUtils.getLoginUser().getUser();
        List<SysRole> roles = user.getRoles();
        List<String> roleList = roles.stream().map(SysRole::getRoleKey).collect(Collectors.toList());
        if (roleList.contains(InstructionRolesConstants.COUNTY_INSTRUCTION)) {
            //角色中包含县市区指令 根据祖级部门ID判断属于哪个县市区
            SysDept sysdept = infoService.getDeptIdByAncestors(user.getDept());
            String deptName = sysdept.getDeptName();
            String replace = deptName.replace("委政法委", "").replace("政法委", "").replace("政法办", "");
            tOpenWorkInspection.setCounty(replace);
        }
        startPage();
        List<TOpenWorkInspection> list = tOpenWorkInspectionService.selectTOpenWorkInspectionList(tOpenWorkInspection);
        return getDataTable(list);
        // startPage();
        // List<TOpenWorkInspection> list = tOpenWorkInspectionService.selectTOpenWorkInspectionList(tOpenWorkInspection);
        // // 使用 Stream API 根据 unitName 进行分组
        // Map<String, List<TOpenWorkInspection>> collect2 = list.stream()
        //         .collect(Collectors.groupingBy(TOpenWorkInspection::getUnitName));
        // List<Map<Object, Object>> finallyList = new ArrayList<>();
        // for (Object s : collect2.keySet()) {
        //     List<TOpenWorkInspection> rspVoList = collect2.get(s);
        //     Map<Object, Object> map = new HashMap<>();
        //     map.put("name", s);
        //     map.put("list", rspVoList);
        //     finallyList.add(map);
        //
        // }
        // TableDataInfo rspData = new TableDataInfo();
        // rspData.setCode(HttpStatus.SUCCESS);
        // rspData.setMsg("查询成功");
        // rspData.setRows(finallyList);
        // rspData.setTotal(new PageInfo(list).getTotal());
        // return rspData;
    }

    @PreAuthorize("@ss.hasPermi('instruction:TOpenWorkInspection:createInfo')")
    @Log(title = "工作督查批量生成指令", businessType = BusinessType.INSERT)
    @PostMapping("/creatInstructionInfo/{inspectionIds}")
    public AjaxResult creatInstructionInfo(@PathVariable Long[] inspectionIds) {
        return toAjax(tOpenWorkInspectionService.creatInstructionInfo(inspectionIds));
    }

    /**
     * 导出工作督查数据对接列表
     */
    @PreAuthorize("@ss.hasPermi('instruction:TOpenWorkInspection:export')")
    @Log(title = "工作督查数据对接", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, TOpenWorkInspection tOpenWorkInspection)
    {
        List<TOpenWorkInspection> list = tOpenWorkInspectionService.selectTOpenWorkInspectionList(tOpenWorkInspection);
        ExcelUtil<TOpenWorkInspection> util = new ExcelUtil<TOpenWorkInspection>(TOpenWorkInspection.class);
        util.exportExcel(response, list, "工作督查数据对接数据");
    }

    /**
     * 获取工作督查数据对接详细信息
     */
    @PreAuthorize("@ss.hasPermi('instruction:TOpenWorkInspection:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(tOpenWorkInspectionService.selectTOpenWorkInspectionById(id));
    }

    /**
     * 新增工作督查数据对接
     */
    // @PreAuthorize("@ss.hasPermi('instruction:TOpenWorkInspection:add')")
    @Log(title = "工作督查数据对接", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody TOpenWorkInspection tOpenWorkInspection) {

        AFReceiveUnit byAreaCode = AFReceiveUnit.getByAreaCode(tOpenWorkInspection.getAreaCode().substring(0, 6) + "000000");
        tOpenWorkInspection.setReceiveUnit(byAreaCode == null ? null : byAreaCode.getName());
        tOpenWorkInspection.setCounty(byAreaCode == null ? null : byAreaCode.getArea());
        if (!AFReceiveUnit.JINHUA.getAreaCode().equals(tOpenWorkInspection.getAreaCode()) && !tOpenWorkInspection.getAreaCode().contains("000000")) {
            //获取乡镇
            tOpenWorkInspection.setTown(openUndercoverInspectionService.getTown(tOpenWorkInspection.getAreaCode()));
        }
        if (tOpenWorkInspection.getCreateDate()!=null){
            // 将时间戳转换为 Date 对象
            Date date = new Date(tOpenWorkInspection.getCreateDate());
            tOpenWorkInspection.setCreateTime(date);
        }

        if (tOpenWorkInspection.getImgList() != null) {
            List<OpenWorkImage> imageList = tOpenWorkInspection.getImgList();
            if (imageList.size() != 0) {
                List<String> ids = new ArrayList<>();
                for (OpenWorkImage openWorkImage : imageList) {
                    String url = openWorkImage.getUrl();
                    InstructionFile instructionFile = new InstructionFile();
                    instructionFile.setFileUrl(openWorkImage.getUrl());
                    int i1 = url.lastIndexOf("/");
                    instructionFile.setFileName(url.substring(i1 == -1 ? 0 : (i1 + 1)));
                    instructionFileService.insertInstructionFile(instructionFile);
                    ids.add(instructionFile.getId() + "");
                }
                String str = ids.stream().collect(Collectors.joining(","));
                tOpenWorkInspection.setImgIds(str);
            }
        }
        return toAjax(tOpenWorkInspectionService.insertTOpenWorkInspection(tOpenWorkInspection));
    }

    /**
     * 修改工作督查数据对接
     */
    @PreAuthorize("@ss.hasPermi('instruction:TOpenWorkInspection:edit')")
    @Log(title = "工作督查数据对接", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody TOpenWorkInspection tOpenWorkInspection)
    {
        AFReceiveUnit byAreaCode = AFReceiveUnit.getByArea(tOpenWorkInspection.getCounty());
        tOpenWorkInspection.setReceiveUnit(byAreaCode == null ? null : byAreaCode.getName());
        return toAjax(tOpenWorkInspectionService.updateTOpenWorkInspection(tOpenWorkInspection));
    }

    /**
     * 删除工作督查数据对接
     */
    @PreAuthorize("@ss.hasPermi('instruction:TOpenWorkInspection:remove')")
    @Log(title = "工作督查数据对接", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(tOpenWorkInspectionService.deleteTOpenWorkInspectionByIds(ids));
    }

    /**
     * 获取统计（督查总数、问题总数、已整改数、未整改数）
     * @param tOpenWorkInspection
     * @return
     */
    @GetMapping("/getStatistics")
    public AjaxResult getStatistics(TOpenWorkInspection tOpenWorkInspection)
    {
        Map<String, Object> map = tOpenWorkInspectionMapper.getStatistics(tOpenWorkInspection);
        return AjaxResult.success(map);
    }


}
