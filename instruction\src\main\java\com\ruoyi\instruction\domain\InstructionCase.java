package com.ruoyi.instruction.domain;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.util.Date;

/**
 * 【经典案例】对象 t_instruction_case
 *
 * <AUTHOR>
 * @date 2023-05-12
 */
public class InstructionCase extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 案例表主键 */
    private Long id;

    /** 案例名称 */
    @Excel(name = "案例名称")
    private String caseTitle;

    /** 领域类型(由类型表中取出) */
    @Excel(name = "领域类型(由类型表中取出)")
    private String type;

    /** 报送单位 */
    @Excel(name = "报送单位")
    private String dutyUnit;

    /** 基本情况 */
    @Excel(name = "基本情况")
    private String baseSituation;

    /** 1:正常  9：删除 */
    @Excel(name = "1:正常  9：删除")
    private String status;

    /** 1：发布  2：未发布 */
    @Excel(name = "1：发布  2：未发布")
    private String isRelease;

    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date cTime;

    /**
     * 指令类型 (用于筛选)
     */
    private Integer instructionType;

    public void setId(Long id)
    {
        this.id = id;
    }

    public Long getId()
    {
        return id;
    }
    public void setCaseTitle(String caseTitle)
    {
        this.caseTitle = caseTitle;
    }

    public String getCaseTitle()
    {
        return caseTitle;
    }
    public void setType(String type)
    {
        this.type = type;
    }

    public String getType()
    {
        return type;
    }
    public void setDutyUnit(String dutyUnit)
    {
        this.dutyUnit = dutyUnit;
    }

    public String getDutyUnit()
    {
        return dutyUnit;
    }
    public void setBaseSituation(String baseSituation)
    {
        this.baseSituation = baseSituation;
    }

    public String getBaseSituation()
    {
        return baseSituation;
    }
    public void setStatus(String status)
    {
        this.status = status;
    }

    public String getStatus()
    {
        return status;
    }
    public void setIsRelease(String isRelease)
    {
        this.isRelease = isRelease;
    }

    public String getIsRelease()
    {
        return isRelease;
    }

    public Date getcTime() {
        return cTime;
    }

    public void setcTime(Date cTime) {
        this.cTime = cTime;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("caseTitle", getCaseTitle())
            .append("type", getType())
            .append("dutyUnit", getDutyUnit())
            .append("baseSituation", getBaseSituation())
            .append("status", getStatus())
            .append("isRelease", getIsRelease())
            .append("createTime", getCreateTime())
            .toString();
    }

    public Integer getInstructionType() {
        return instructionType;
    }

    public void setInstructionType(Integer instructionType) {
        this.instructionType = instructionType;
    }
}
