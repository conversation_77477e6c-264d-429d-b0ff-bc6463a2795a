package com.ruoyi.instruction.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.instruction.domain.InstructionTransfer;
import com.ruoyi.instruction.service.IInstructionTransferService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 指令转接Controller
 * 
 * <AUTHOR>
 * @date 2022-12-19
 */
@RestController
@RequestMapping("/instruction/transfer")
public class InstructionTransferController extends BaseController
{
    @Autowired
    private IInstructionTransferService instructionTransferService;

    /**
     * 查询指令转接列表
     */
    @PreAuthorize("@ss.hasPermi('instruction:transfer:list')")
    @GetMapping("/list")
    public TableDataInfo list(InstructionTransfer instructionTransfer)
    {
        startPage();
        List<InstructionTransfer> list = instructionTransferService.selectInstructionTransferList(instructionTransfer);
        return getDataTable(list);
    }

    /**
     * 导出指令转接列表
     */
    @PreAuthorize("@ss.hasPermi('instruction:transfer:export')")
    @Log(title = "指令转接", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, InstructionTransfer instructionTransfer)
    {
        List<InstructionTransfer> list = instructionTransferService.selectInstructionTransferList(instructionTransfer);
        ExcelUtil<InstructionTransfer> util = new ExcelUtil<InstructionTransfer>(InstructionTransfer.class);
        util.exportExcel(response, list, "指令转接数据");
    }

    /**
     * 获取指令转接详细信息
     */
    @PreAuthorize("@ss.hasPermi('instruction:transfer:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(instructionTransferService.selectInstructionTransferById(id));
    }

    /**
     * 新增指令转接
     */
    @PreAuthorize("@ss.hasPermi('instruction:transfer:add')")
    @Log(title = "指令转接", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody InstructionTransfer instructionTransfer)
    {
        return toAjax(instructionTransferService.insertInstructionTransfer(instructionTransfer));
    }

    /**
     * 修改指令转接
     */
    @PreAuthorize("@ss.hasPermi('instruction:transfer:edit')")
    @Log(title = "指令转接", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody InstructionTransfer instructionTransfer)
    {
        return toAjax(instructionTransferService.updateInstructionTransfer(instructionTransfer));
    }

    /**
     * 删除指令转接
     */
    @PreAuthorize("@ss.hasPermi('instruction:transfer:remove')")
    @Log(title = "指令转接", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(instructionTransferService.deleteInstructionTransferByIds(ids));
    }
}
