package com.ruoyi.examineAir.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;
import com.ruoyi.examineAir.domain.ExamineDetails;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * 考核晾晒列
 * 
 * <AUTHOR>
 * @date 2023-04-19
 */
@Data
public class ExamineVo extends BaseEntity
{
    private static final long serialVersionUID = 1L;


    /**  */
    private Long id;


    /** 标题 */
    @Excel(name = "标题")
    private String title;

    /** 类型，1.除险保安，2.亚运赛道，3.维稳工作 */
    @Excel(name = "类型，1.除险保安，2.亚运赛道，3.维稳工作")
    private Integer type;

    /** 发布时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "发布时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date releaseTime;

    /** 状态，0未发布，1已发布 */
    @Excel(name = "状态，0未发布，1已发布")
    private Long status;

    private List<ExamineDetails> examineDetailsList;

    /** 表头内容 */
    private String headContent;

    /** 磐安得分 */
    @Excel(name = "磐安得分")
    private BigDecimal panan;

    /** 武义得分 */
    @Excel(name = "武义得分")
    private BigDecimal wuyi;

    /** 东阳得分 */
    @Excel(name = "东阳得分")
    private BigDecimal dongyang;

    /** 永康得分 */
    @Excel(name = "永康得分")
    private BigDecimal yongkang;

    /** 浦江得分 */
    @Excel(name = "浦江得分")
    private BigDecimal pujiang;

    /** 义乌得分 */
    @Excel(name = "义乌得分")
    private BigDecimal yiwu;

    /** 兰溪得分 */
    @Excel(name = "兰溪得分")
    private BigDecimal lanxi;

    /** 金东得分 */
    @Excel(name = "金东得分")
    private BigDecimal jindong;

    /** 婺城得分 */
    @Excel(name = "婺城得分")
    private BigDecimal wucheng;

    /** 开发区得分 */
    @Excel(name = "开发区得分")
    private BigDecimal kaifa;

}
