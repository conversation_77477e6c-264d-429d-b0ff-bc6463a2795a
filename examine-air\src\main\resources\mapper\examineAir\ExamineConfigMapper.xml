<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.examineAir.mapper.ExamineConfigMapper">

    <resultMap type="ExamineConfig" id="ExamineConfigResult">
            <result property="id" column="id"/>
            <result property="type" column="type"/>
            <result property="fieldName" column="field_name"/>
            <result property="fieldIntroduce" column="field_introduce"/>
            <result property="maxValue" column="max_value"/>
            <result property="summation" column="summation"/>
    </resultMap>

    <sql id="selectExamineConfigVo">
        select id, type, field_name, field_introduce, max_value, summation
        from t_examine_config
    </sql>

    <select id="selectExamineConfigList" parameterType="ExamineConfig" resultMap="ExamineConfigResult">
        <include refid="selectExamineConfigVo"/>
        <where>
            <if test="type != null  and type != ''">and type = #{type}</if>
            <if test="fieldName != null  and fieldName != ''">and field_name like concat('%', #{fieldName}, '%')</if>
            <if test="fieldIntroduce != null  and fieldIntroduce != ''">and field_introduce = #{fieldIntroduce}</if>
            <if test="maxValue != null ">and max_value = #{maxValue}</if>
            <if test="summation != null ">and summation = #{summation}</if>
        </where>
    </select>

    <select id="selectExamineConfigById" parameterType="Long"
            resultMap="ExamineConfigResult">
            <include refid="selectExamineConfigVo"/>
            where  id = #{id}
    </select>

    <insert id="insertExamineConfig" parameterType="ExamineConfig" useGeneratedKeys="true"
            keyProperty="id">
        insert into t_examine_config
        <trim prefix="(" suffix=")" suffixOverrides=",">
                    <if test="type != null">type,</if>
                    <if test="fieldName != null">field_name,</if>
                    <if test="fieldIntroduce != null">field_introduce,</if>
                    <if test="maxValue != null">max_value,</if>
                    <if test="summation != null">summation,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
                    <if test="type != null">#{type},</if>
                    <if test="fieldName != null">#{fieldName},</if>
                    <if test="fieldIntroduce != null">#{fieldIntroduce},</if>
                    <if test="maxValue != null">#{maxValue},</if>
                    <if test="summation != null">#{summation},</if>
        </trim>
    </insert>

    <update id="updateExamineConfig" parameterType="ExamineConfig">
        update t_examine_config
        <trim prefix="SET" suffixOverrides=",">
                    <if test="type != null">type = #{type},</if>
                    <if test="fieldName != null">field_name = #{fieldName},</if>
                    <if test="fieldIntroduce != null">field_introduce = #{fieldIntroduce},</if>
                    <if test="maxValue != null">max_value = #{maxValue},</if>
                    <if test="summation != null">summation = #{summation},</if>
        </trim>
        where id = #{id}
    </update>

    <update id="deleteExamineConfigById" parameterType="Long">
        update t_examine_config set status = 9
        where id = #{id}
    </update>

    <update id="deleteExamineConfigByIds" parameterType="String">
        update t_examine_config set status = 9 where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>
</mapper>