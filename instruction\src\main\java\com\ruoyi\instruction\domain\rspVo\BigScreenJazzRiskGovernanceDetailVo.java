package com.ruoyi.instruction.domain.rspVo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.instruction.domain.InstrucationPerson;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * 重点风险治理通过id获取详情
 * <AUTHOR> @version 1.0
 * @date 2023/3/6 9:18
 *
 */
@Data
public class BigScreenJazzRiskGovernanceDetailVo {

    /**
     * 指令主键
     */
    private Long id;


    /**
     * 指令标题
     */
    @Excel(name = "指令标题")
    private String instructionTitle;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private  Date createTime;

    /**
     * 接收单位
     */
    @Excel(name = "接收单位")
    private String receiveUnit;

    /**
     * 紧急程度
     */
    @Excel(name = "紧急程度")
    private String emergencyDegree;



    /**
     * 指令内容
     */
    @Excel(name = "指令内容")
    private String instructionContent;

    /**
     * 基本情况
     */
    @Excel(name = "基本情况")
    private String baseInfo;



    /**
     * 状态 1:正常  9：删除
     */
    @Excel(name = "状态 1:正常  9：删除")
    private String status;



    /**
     * 接收单位
     */
    private String[] unit;


    /**
     * 交办开始时间
     */
    private  Date assignStartTime;
    /**
     * 交办结束时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm")
    private  Date endTime;

    /**
     * 是否销号 1:销号 2：未销号
     */
   private Integer instrucationIsEnd;

   List<String> list;

}
