package com.ruoyi.instruction.controller;

import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.instruction.domain.CityPetition;
import com.ruoyi.instruction.mapper.CityPetitionPersonMapper;
import com.ruoyi.instruction.service.ICityPentitionService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.Arrays;
import java.util.List;
import java.util.Map;

/**
 * 来市信访信息Controller
 *
 * <AUTHOR>
 * @date 2024-05-28
 */
@RestController
@RequestMapping("/citypetition")
public class CityPetitionController extends BaseController {
    @Autowired
    private ICityPentitionService cityPentitionService;
    @Autowired
    private CityPetitionPersonMapper cityPetitionPersonMapper;

    /**
     * 查询到市信访信息列表
     */
    @GetMapping("/list")
    public TableDataInfo list(CityPetition cityPetition)
    {
        if (cityPetition.getContentType() != null && !cityPetition.getContentType().isEmpty()) {
            Map<String, Object> params = cityPetition.getParams();
            List<String> contentTypes = Arrays.asList(cityPetition.getContentType().split(","));
            params.put("contentTypes", contentTypes);
            cityPetition.setParams(params);
        }

        startPage();
        List<CityPetition> list = cityPentitionService.selectCityPetitionList(cityPetition);
        return getDataTable(list);
    }

    /**
     * 下载导入模板
     *
     * @param response
     */
    @PostMapping("/importTemplate")
    public void importTemplate(HttpServletResponse response) {
        ExcelUtil<CityPetition> util = new ExcelUtil<CityPetition>(CityPetition.class);
        util.importTemplateExcel(response, "信访到市事件导入模板");
    }

    /**
     * 导入数据
     * @param file
     * @return
     * @throws Exception
     */
//    @Log(title = "来市信访数据导入", businessType = BusinessType.IMPORT)
    @PostMapping("/importCityPetition")
    public AjaxResult importPetitionInfo(MultipartFile file) throws Exception {
        ExcelUtil<CityPetition> util = new ExcelUtil<CityPetition>(CityPetition.class);
        List<CityPetition> cityPetitionList = util.importExcel(file.getInputStream());
        return cityPentitionService.importCityPetitionData(cityPetitionList);
    }

    /**
     * 根据人员查询到市信访信息列表
     */
    @GetMapping("/listByName")
    public TableDataInfo listByName(CityPetition cityPetition)
    {
        startPage();
        List<CityPetition> list = cityPentitionService.selectCityPetitionListByName(cityPetition);
        return getDataTable(list);
    }


    /**
     * 批量加密身份证
     */
//    @PostMapping("/encryptionIDcard")
//    public void encryptionIDcard(){
//        List<CityPetition> list = cityPentitionService.selectCityPetitionList1();
//        for (int i = 0; i < list.size(); i++) {
//            CityPetition c = list.get(i);
//            // 身份证加密
//            if(StringUtils.isNotEmpty(c.getPersonCard())){
//                c.setPersonCard(IDCardUtils.encryptIDCard(c.getPersonCard().toLowerCase()));
//            }
//        }
//        cityPentitionService.batchUpdateCityPetition(list);
//    }



}
