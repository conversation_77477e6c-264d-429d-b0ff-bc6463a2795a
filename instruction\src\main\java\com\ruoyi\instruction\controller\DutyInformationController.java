package com.ruoyi.instruction.controller;

import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.domain.entity.SysDept;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.enums.AreaDept;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.DeptUtils;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.instruction.domain.reqVo.DutyInformationDeleteReq;
import com.ruoyi.system.mapper.SysDeptMapper;
import com.ruoyi.instruction.domain.DutyInformation;
import com.ruoyi.instruction.service.IDutyInformationService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 今日值班信息Controller
 * 
 * <AUTHOR>
 * @date 2023-08-04
 */
@RestController
@RequestMapping("/xzzfj/dutyInformation")
public class DutyInformationController extends BaseController
{
    @Autowired
    private IDutyInformationService dutyInformationService;

    @Resource
    private SysDeptMapper sysDeptMapper;

    /**
     * 查询今日值班信息列表
     */
    @PreAuthorize("@ss.hasPermi('xzzfj:dutyInformation:list')")
    @GetMapping("/list")
    public TableDataInfo list(DutyInformation dutyInformation)
    {

        Long deptId = SecurityUtils.getDeptId();
        SysDept sysDept = sysDeptMapper.selectDeptById(deptId);
//        AreaDept byDept = AreaDept.getByDept(sysDept.getDeptName());
//        if (!StringUtils.isEmpty(byDept.getArea())&&!byDept.getArea().equals("金华市")){
//            dutyInformation.setAreaName(byDept.getArea());
//        }
        String dutyPlaceByDept = DeptUtils.getDutyPlaceByDept1(sysDept);
        if (!StringUtils.isEmpty(dutyPlaceByDept)&&!dutyPlaceByDept.equals("金华市")){
            dutyInformation.setAreaName(dutyPlaceByDept);
        }
        startPage();
        List<DutyInformation> list = dutyInformationService.selectDutyInformationList(dutyInformation);
        return getDataTable(list);
    }

    /**
     * 导出今日值班信息列表
     */
    @PreAuthorize("@ss.hasPermi('xzzfj:dutyInformation:export')")
    @Log(title = "今日值班信息", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, DutyInformation dutyInformation)
    {
        List<DutyInformation> list = dutyInformationService.selectDutyInformationList(dutyInformation);
        ExcelUtil<DutyInformation> util = new ExcelUtil<DutyInformation>(DutyInformation.class);
        util.exportExcel(response, list, "今日值班信息数据");
    }

    /**
     * 获取今日值班信息详细信息
     */
    @PreAuthorize("@ss.hasPermi('xzzfj:dutyInformation:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(dutyInformationService.selectDutyInformationById(id));
    }

    /**
     * 新增今日值班信息
     */
    @PreAuthorize("@ss.hasPermi('xzzfj:dutyInformation:add')")
    @Log(title = "今日值班信息", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody DutyInformation dutyInformation)
    {
        Long deptId = SecurityUtils.getDeptId();
        SysDept sysDept = sysDeptMapper.selectDeptById(deptId);
//        AreaDept byDept = AreaDept.getByDept(sysDept.getDeptName());
//        if (!StringUtils.isEmpty(byDept.getArea())&&!byDept.getArea().equals("金华市")){
//            dutyInformation.setAreaName(byDept.getArea());
//        }
        String dutyPlaceByDept = DeptUtils.getDutyPlaceByDept1(sysDept);
        if (!StringUtils.isEmpty(dutyPlaceByDept)&&!dutyPlaceByDept.equals("金华市")){
            dutyInformation.setAreaName(dutyPlaceByDept);
        }
        return toAjax(dutyInformationService.insertDutyInformation(dutyInformation));
    }

    /**
     * 修改今日值班信息
     */
    @PreAuthorize("@ss.hasPermi('xzzfj:dutyInformation:edit')")
    @Log(title = "今日值班信息", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody DutyInformation dutyInformation)
    {
        return toAjax(dutyInformationService.updateDutyInformation(dutyInformation));
    }

    /**
     * 删除今日值班信息
     */
    @PreAuthorize("@ss.hasPermi('xzzfj:dutyInformation:remove')")
    @Log(title = "今日值班信息", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(dutyInformationService.deleteDutyInformationByIds(ids));
    }


    /**
     * 新增或编辑今日值班信息
     */
    @PreAuthorize("@ss.hasPermi('xzzfj:dutyInformation:add')")
    @Log(title = "今日值班信息", businessType = BusinessType.INSERT)
    @PostMapping("addAndUpdate")
    public AjaxResult addAndUpdate(@RequestBody DutyInformation dutyInformation)
    {
        Long deptId = SecurityUtils.getDeptId();
        SysDept sysDept = sysDeptMapper.selectDeptById(deptId);
//        AreaDept byDept = AreaDept.getByDept(sysDept.getDeptName());
//        if (!StringUtils.isEmpty(byDept.getArea())&&!byDept.getArea().equals("金华市")){
//            dutyInformation.setAreaName(byDept.getArea());
//        }
        String dutyPlaceByDept = DeptUtils.getDutyPlaceByDept1(sysDept);
        if (!StringUtils.isEmpty(dutyPlaceByDept)&&!dutyPlaceByDept.equals("金华市")){
            dutyInformation.setAreaName(dutyPlaceByDept);
        }
        if (dutyInformation.getAreaName().equals("市本级")){
            dutyInformation.setAreaName("金华市");
        }
        return toAjax(dutyInformationService.addAndUpdate(dutyInformation));
    }

    /**
     * 查询今日值班信息（新）
     */
    @GetMapping("/listForArea")
    public AjaxResult listForArea(DutyInformation dutyInformation)
    {
        Long deptId = SecurityUtils.getDeptId();
        SysDept sysDept = sysDeptMapper.selectDeptById(deptId);
//        AreaDept byDept = AreaDept.getByDept(sysDept.getDeptName());
//        if (!StringUtils.isEmpty(byDept.getArea())&&!byDept.getArea().equals("金华市")){
//            dutyInformation.setAreaName(byDept.getArea());
//        }
        String dutyPlaceByDept = DeptUtils.getDutyPlaceByDept1(sysDept);
        if (!StringUtils.isEmpty(dutyPlaceByDept)&&!dutyPlaceByDept.equals("金华市")){
            dutyInformation.setAreaName(dutyPlaceByDept);
        }
        if (dutyInformation.getAreaName().equals("市本级")){
            dutyInformation.setAreaName("金华市");
        }
        List<DutyInformation> list = dutyInformationService.selectDutyInformationList(dutyInformation);
        return AjaxResult.success(list);
    }

    /**
     * 删除今日值班
     */
    @PostMapping("/delete")
    public AjaxResult delete(@RequestBody DutyInformationDeleteReq dutyInformation)
    {
        Integer blank = dutyInformationService.blank(dutyInformation);
        return toAjax(blank);
    }

    /**
     * 修改风险等级
     */
    @Log(title = "今日值班信息", businessType = BusinessType.INSERT)
    @PostMapping("updateRiskLevel")
    public AjaxResult updateRiskLevel(@RequestBody DutyInformation dutyInformation)
    {
        Long deptId = SecurityUtils.getDeptId();
        SysDept sysDept = sysDeptMapper.selectDeptById(deptId);
//        AreaDept byDept = AreaDept.getByDept(sysDept.getDeptName());
//        if (!StringUtils.isEmpty(byDept.getArea())&&!byDept.getArea().equals("金华市")){
//            dutyInformation.setAreaName(byDept.getArea());
//        }
        String dutyPlaceByDept = DeptUtils.getDutyPlaceByDept1(sysDept);
        if (!StringUtils.isEmpty(dutyPlaceByDept)&&!dutyPlaceByDept.equals("金华市")){
            dutyInformation.setAreaName(dutyPlaceByDept);
        }
        if (dutyInformation.getAreaName().equals("市本级")){
            dutyInformation.setAreaName("金华市");
        }
        return AjaxResult.success(dutyInformationService.updateRiskLevel(dutyInformation));
    }
    /**
     * 获取风险等级
     */
    @GetMapping("selectRiskLevel")
    public AjaxResult selectRiskLevel(String area)
    {
        Long deptId = SecurityUtils.getDeptId();
        SysDept sysDept = sysDeptMapper.selectDeptById(deptId);
//        AreaDept byDept = AreaDept.getByDept(sysDept.getDeptName());
//        if (!StringUtils.isEmpty(byDept.getArea())&&!byDept.getArea().equals("金华市")){
//            area=byDept.getArea();
//        }
        String dutyPlaceByDept = DeptUtils.getDutyPlaceByDept1(sysDept);
        if (!StringUtils.isEmpty(dutyPlaceByDept)&&!dutyPlaceByDept.equals("金华市")){
            area=dutyPlaceByDept;
        }
        if (StringUtils.isEmpty(area)){
            area=dutyPlaceByDept;
        }
        if (area.equals("市本级")){
            area="金华市";
        }
        return AjaxResult.success(dutyInformationService.selectRiskLevel(area));
    }
}
