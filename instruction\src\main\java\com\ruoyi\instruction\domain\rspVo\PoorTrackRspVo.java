package com.ruoyi.instruction.domain.rspVo;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.ruoyi.common.annotation.Excel;
import lombok.Data;

import java.util.Date;

/**
 * 跟踪不力(含接收单位) 导出实体类
 * <AUTHOR>
 * @version 1.0
 * @date 2025/4/22 10:31
 */
@Data
public class PoorTrackRspVo {

    @ExcelProperty("序号")
    private Long id;

    @ExcelProperty(value = "指令标题")
    @ColumnWidth(20)
    private String instructionTitle;

    @ColumnWidth(15)
    @ExcelProperty("紧急程度")
    private String emergencyDegree;


    /** 指令类型 1：维稳 2：平安 3：基层 */
    @ColumnWidth(15)
    @ExcelProperty("指令类型")
    private String instructionTypeStr;

    @ExcelProperty("交办时间")
    @ColumnWidth(20)
    private Date assignTime;

    @ColumnWidth(20)
    @ExcelProperty("办理期限")
    private Date handleTime;

    @ColumnWidth(20)
    @ExcelProperty("接收不力单位")
    private String receiveStr;

    @ExcelProperty("类型")
    private String type;

    @ColumnWidth(20)
    @ExcelProperty("接收时间")
    private Date updateTime;



}
