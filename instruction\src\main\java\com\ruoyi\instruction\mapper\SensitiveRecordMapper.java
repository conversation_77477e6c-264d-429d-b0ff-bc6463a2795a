package com.ruoyi.instruction.mapper;

import java.util.List;
import com.ruoyi.instruction.domain.SensitiveRecord;

/**
 * 敏感信息Mapper接口
 * 
 * <AUTHOR>
 * @date 2024-07-10
 */
public interface SensitiveRecordMapper 
{
    /**
     * 查询敏感信息
     * 
     * @param id 敏感信息主键
     * @return 敏感信息
     */
    public SensitiveRecord selectSensitiveRecordById(Long id);

    /**
     * 查询敏感信息列表
     * 
     * @param sensitiveRecord 敏感信息
     * @return 敏感信息集合
     */
    public List<SensitiveRecord> selectSensitiveRecordList(SensitiveRecord sensitiveRecord);

    /**
     * 新增敏感信息
     * 
     * @param sensitiveRecord 敏感信息
     * @return 结果
     */
    public int insertSensitiveRecord(SensitiveRecord sensitiveRecord);

    /**
     * 修改敏感信息
     * 
     * @param sensitiveRecord 敏感信息
     * @return 结果
     */
    public int updateSensitiveRecord(SensitiveRecord sensitiveRecord);

    /**
     * 删除敏感信息
     * 
     * @param id 敏感信息主键
     * @return 结果
     */
    public int deleteSensitiveRecordById(Long id);

    /**
     * 批量删除敏感信息
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteSensitiveRecordByIds(Long[] ids);
}
