package com.ruoyi.instruction.domain;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.common.annotation.Excel;
import lombok.Data;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 社会治安-命案分析对象 t_jazz_homicide_case
 * 
 * <AUTHOR>
 * @date 2023-07-19
 */
@Data
public class TJazzHomicideCase extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** $column.columnComment */
    private Long id;

    /** 区域 */
    @Excel(name = "区域")
    private String area;

    /** 发生时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "发生时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date ariseTime;

    /** 嫌疑人姓名 */
    @Excel(name = "嫌疑人姓名")
    private String suspectName;

    /** 嫌疑人性别 */
    @Excel(name = "嫌疑人性别")
    private String suspectSex;

    /** 嫌疑人户籍 */
    @Excel(name = "嫌疑人户籍")
    private String suspectHousePlace;

    /** 嫌疑人省份 */
    @Excel(name = "嫌疑人省份")
    private String suspectProvince;

    /** 被害人姓名 */
    @Excel(name = "被害人姓名")
    private String victimName;

    /** 被害人性别 */
    @Excel(name = "被害人性别")
    private String victimSex;

    /** 被害人户籍 */
    @Excel(name = "被害人户籍")
    private String victimHousePlace;

    /** 类型 */
    @Excel(name = "类型")
    private String type;


}
