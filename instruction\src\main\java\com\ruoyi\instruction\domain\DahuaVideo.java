package com.ruoyi.instruction.domain;

import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 大华视频信息
对象 t_dahua_video
 * 
 * <AUTHOR>
 * @date 2023-08-30
 */
@Data
public class DahuaVideo extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键 */
    private Long id;

    /** 经度 */
    @Excel(name = "经度")
    private String esX;

    /** 纬度 */
    @Excel(name = "纬度")
    private String esY;

    /** key */
    @Excel(name = "key")
    private String dkey;

    /** 所属组织 */
    @Excel(name = "所属组织")
    private String orgname;

    /** 值 */
    @Excel(name = "值")
    private String dvalue;

    /** 1:正常 9:删除 */
    @Excel(name = "1:正常 9:删除")
    private String status;


}
