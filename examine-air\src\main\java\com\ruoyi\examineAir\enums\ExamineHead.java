package com.ruoyi.examineAir.enums;

/**
 * 考核晾晒默认表头
 * 
 * <AUTHOR>
 */
public enum ExamineHead
{
    CXBA(1, "除险保安","[{\"name\":\"工作机制建设\",\"value\":\"score1\"},{\"name\":\"风险隐患处置\",\"value\":\"score2\"},{\"name\":\"平安金华建设\",\"value\":\"score3\"},{\"name\":\"基层社会治理\",\"value\":\"score4\"},{\"name\":\"反邪教专项工作\",\"value\":\"score5\"},{\"name\":\"信访积案化解\",\"value\":\"score6\"},{\"name\":\"流动人口管理\",\"value\":\"score7\"},{\"name\":\"安全生产监管\",\"value\":\"score8\"},{\"name\":\"网络舆情监管\",\"value\":\"score9\"},{\"name\":\"数字化成果运用\",\"value\":\"score10\"}]"),
    YYSD(2, "亚运赛道","[{\"name\":\"专班运行\",\"value\":\"score1\"},{\"name\":\"方案迭代\",\"value\":\"score2\"},{\"name\":\"信息预警\",\"value\":\"score3\"},{\"name\":\"人员管控\",\"value\":\"score4\"},{\"name\":\"交办反馈\",\"value\":\"score5\"},{\"name\":\"风险处置\",\"value\":\"score6\"},{\"name\":\"特色亮点\",\"value\":\"score7\"}}]"),
    WWGZ(3, "维稳工作","[{\"name\":\"风险隐患化解\",\"value\":\"score1\"},{\"name\":\"突发事件处置\",\"value\":\"score2\"},{\"name\":\"情报信息报送\",\"value\":\"score3\"},{\"name\":\"重点人员管控\",\"value\":\"score4\"},{\"name\":\"工作制度落实\",\"value\":\"score5\"},{\"name\":\"工作任务交办\",\"value\":\"score6\"},{\"name\":\"社会风险评估\",\"value\":\"score7\"},{\"name\":\"网络舆论监控\",\"value\":\"score8\"},{\"name\":\"特色亮点工作\",\"value\":\"score9\"}]");

    private final Integer type;
    private final String content;
    private final String template;

    ExamineHead(Integer type, String content,String template)
    {
        this.type = type;
        this.content = content;
        this.template = template;
    }

    public Integer getType() {
        return type;
    }

    public String getContent() {
        return content;
    }

    public String getTemplate() {
        return template;
    }

    public static ExamineHead findEnumByType(int type) {

        for (ExamineHead statusEnum : ExamineHead.values()) {
            if (statusEnum.getType() == type) {
                return statusEnum;
            }

        }
        throw new IllegalArgumentException("code is not support");

    }


}
