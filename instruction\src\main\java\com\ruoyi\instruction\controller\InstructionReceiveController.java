package com.ruoyi.instruction.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.instruction.domain.InstructionReceive;
import com.ruoyi.instruction.service.IInstructionReceiveService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 指令接收Controller
 * 
 * <AUTHOR>
 * @date 2022-12-19
 */
@RestController
@RequestMapping("/instruction/receive")
public class InstructionReceiveController extends BaseController
{
    @Autowired
    private IInstructionReceiveService instructionReceiveService;

    /**
     * 查询指令接收列表
     */
    @PreAuthorize("@ss.hasPermi('instruction:receive:list')")
    @GetMapping("/list")
    public TableDataInfo list(InstructionReceive instructionReceive)
    {
        startPage();
        List<InstructionReceive> list = instructionReceiveService.selectInstructionReceiveList(instructionReceive);
        return getDataTable(list);
    }

    /**
     * 导出指令接收列表
     */
    @PreAuthorize("@ss.hasPermi('instruction:receive:export')")
    @Log(title = "指令接收", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, InstructionReceive instructionReceive)
    {
        List<InstructionReceive> list = instructionReceiveService.selectInstructionReceiveList(instructionReceive);
        ExcelUtil<InstructionReceive> util = new ExcelUtil<InstructionReceive>(InstructionReceive.class);
        util.exportExcel(response, list, "指令接收数据");
    }

    /**
     * 获取指令接收详细信息
     */
    @PreAuthorize("@ss.hasPermi('instruction:receive:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(instructionReceiveService.selectInstructionReceiveById(id));
    }

    /**
     * 新增指令接收
     */
    @PreAuthorize("@ss.hasPermi('instruction:receive:add')")
    @Log(title = "指令接收", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody InstructionReceive instructionReceive)
    {
        return toAjax(instructionReceiveService.insertInstructionReceive(instructionReceive));
    }

    /**
     * 修改指令接收
     */
    @PreAuthorize("@ss.hasPermi('instruction:receive:edit')")
    @Log(title = "指令接收", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody InstructionReceive instructionReceive)
    {
        return toAjax(instructionReceiveService.updateInstructionReceive(instructionReceive));
    }

    /**
     * 删除指令接收
     */
    @PreAuthorize("@ss.hasPermi('instruction:receive:remove')")
    @Log(title = "指令接收", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(instructionReceiveService.deleteInstructionReceiveByIds(ids));
    }
}
