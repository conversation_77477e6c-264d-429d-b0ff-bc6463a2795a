-- getPoorTrackNew性能测试脚本
-- 用于对比优化前后的查询性能

-- 1. 开启查询分析
SET profiling = 1;

-- 2. 清除查询缓存（确保测试准确性）
RESET QUERY CACHE;

-- 3. 原始查询性能测试
SELECT '=== 原始查询性能测试 ===' as test_phase;

-- 原始查询（简化版，用于测试）
SELECT COUNT(*) as original_query_count
FROM (
    SELECT
    a.id,
    a.emergency_degree,
    a.assign_time,
    CASE
    WHEN a.emergency_degree = '一般' THEN DATE_ADD(a.create_time, INTERVAL 24 HOUR)
    WHEN a.emergency_degree = '紧急' THEN DATE_ADD(a.create_time, INTERVAL 12 HOUR)
    WHEN a.emergency_degree = '特急' THEN DATE_ADD(a.create_time, INTERVAL 2 HOUR) 
    ELSE a.create_time
    END AS create_time,
    CASE
    WHEN a.emergency_degree = '一般' THEN DATE_ADD(a.assign_time, INTERVAL 1 MONTH)
    WHEN a.emergency_degree = '紧急' THEN DATE_ADD(a.assign_time, INTERVAL 2 WEEK)
    WHEN a.emergency_degree = '特急' THEN DATE_ADD(a.assign_time, INTERVAL 1 WEEK) 
    ELSE a.assign_time
    END AS update_time
    FROM t_instruction_info a
    WHERE a.status = 1
    AND a.instruction_type IN (3, 6)
    AND DATE_FORMAT(a.handle_time, '%Y-%m') = '2024-01'
) sub;

-- 4. 优化后查询性能测试
SELECT '=== 优化后查询性能测试 ===' as test_phase;

-- 优化后查询
SELECT COUNT(*) as optimized_query_count
FROM (
    SELECT
    a.id,
    a.emergency_degree,
    a.assign_time,
    a.create_time,
    CASE
    WHEN a.emergency_degree = '一般' THEN DATE_ADD(a.create_time, INTERVAL 24 HOUR)
    WHEN a.emergency_degree = '紧急' THEN DATE_ADD(a.create_time, INTERVAL 12 HOUR)
    WHEN a.emergency_degree = '特急' THEN DATE_ADD(a.create_time, INTERVAL 2 HOUR) 
    ELSE a.create_time
    END AS create_time_calc,
    CASE
    WHEN a.emergency_degree = '一般' THEN DATE_ADD(a.assign_time, INTERVAL 1 MONTH)
    WHEN a.emergency_degree = '紧急' THEN DATE_ADD(a.assign_time, INTERVAL 2 WEEK)
    WHEN a.emergency_degree = '特急' THEN DATE_ADD(a.assign_time, INTERVAL 1 WEEK) 
    ELSE a.assign_time
    END AS update_time_calc
    FROM t_instruction_info a
    WHERE a.status = 1
    AND a.instruction_type IN (3, 6)
    AND a.handle_time >= '2024-01-01'
    AND a.handle_time < '2024-02-01'
) sub;

-- 5. 显示查询分析结果
SHOW PROFILES;

-- 6. 详细的执行计划分析
SELECT '=== 执行计划分析 ===' as analysis_phase;

-- 原始查询执行计划
EXPLAIN FORMAT=JSON
SELECT a.id
FROM t_instruction_info a
WHERE a.status = 1
AND a.instruction_type IN (3, 6)
AND DATE_FORMAT(a.handle_time, '%Y-%m') = '2024-01';

-- 优化后查询执行计划
EXPLAIN FORMAT=JSON
SELECT a.id
FROM t_instruction_info a
WHERE a.status = 1
AND a.instruction_type IN (3, 6)
AND a.handle_time >= '2024-01-01'
AND a.handle_time < '2024-02-01';

-- 7. 索引使用情况分析
SELECT '=== 索引使用情况分析 ===' as index_analysis;

-- 检查表的索引情况
SHOW INDEX FROM t_instruction_info;
SHOW INDEX FROM t_instruction_receive;
SHOW INDEX FROM t_instruction_county_feedback;

-- 8. 表统计信息
SELECT '=== 表统计信息 ===' as table_stats;

SELECT 
    TABLE_NAME,
    TABLE_ROWS,
    DATA_LENGTH,
    INDEX_LENGTH,
    (DATA_LENGTH + INDEX_LENGTH) as TOTAL_SIZE
FROM information_schema.TABLES 
WHERE TABLE_SCHEMA = DATABASE() 
AND TABLE_NAME IN ('t_instruction_info', 't_instruction_receive', 't_instruction_county_feedback');

-- 9. 完整的JOIN查询性能测试
SELECT '=== 完整JOIN查询性能测试 ===' as join_test;

-- 测试完整的JOIN查询（原始版本）
SELECT COUNT(*)
FROM (
    SELECT
    a.id,
    a.emergency_degree,
    a.assign_time
    FROM t_instruction_info a
    WHERE a.status = 1
    AND a.instruction_type IN (3, 6)
    AND DATE_FORMAT(a.handle_time, '%Y-%m') = '2024-01'
) sub_a
LEFT JOIN (
    SELECT id, receive_dept, receive_time, instrucation_id 
    FROM t_instruction_receive 
    WHERE status = 1
) sub_b ON sub_a.id = sub_b.instrucation_id
LEFT JOIN (
    SELECT instruction_id, feedback_dept, MAX(receive_time) as feedback_time
    FROM t_instruction_county_feedback 
    WHERE status = 1 AND is_end = 1 
    GROUP BY instruction_id, feedback_dept
) sub_c ON sub_b.instrucation_id = sub_c.instruction_id 
AND sub_b.receive_dept = sub_c.feedback_dept;

-- 测试完整的JOIN查询（优化版本）
SELECT COUNT(*)
FROM (
    SELECT
    a.id,
    a.emergency_degree,
    a.assign_time
    FROM t_instruction_info a
    WHERE a.status = 1
    AND a.instruction_type IN (3, 6)
    AND a.handle_time >= '2024-01-01'
    AND a.handle_time < '2024-02-01'
) sub_a
LEFT JOIN (
    SELECT id, receive_dept, receive_time, instrucation_id 
    FROM t_instruction_receive 
    WHERE status = 1
) sub_b ON sub_a.id = sub_b.instrucation_id
LEFT JOIN (
    SELECT instruction_id, feedback_dept, MAX(receive_time) as feedback_time
    FROM t_instruction_county_feedback 
    WHERE status = 1 AND is_end = 1 
    GROUP BY instruction_id, feedback_dept
) sub_c ON sub_b.instrucation_id = sub_c.instruction_id 
AND sub_b.receive_dept = sub_c.feedback_dept;

-- 10. 显示最终的性能分析结果
SHOW PROFILES;

-- 11. 关闭查询分析
SET profiling = 0;

-- 12. 性能测试总结查询
SELECT 
    '性能测试完成' as status,
    '请查看上述SHOW PROFILES结果对比查询性能' as note,
    '建议在生产环境类似数据量下进行测试' as recommendation;
