package com.ruoyi.instruction.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.instruction.domain.Fkjl;
import com.ruoyi.instruction.service.IFkjlService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 政法委转情指（签收、反馈）Controller
 * 
 * <AUTHOR>
 * @date 2023-06-07
 */
@RestController
@RequestMapping("/instruction/fkjl")
public class FkjlController extends BaseController
{
    @Autowired
    private IFkjlService fkjlService;

    /**
     * 查询政法委转情指（签收、反馈）列表
     */
    @PreAuthorize("@ss.hasPermi('instruction:fkjl:list')")
    @GetMapping("/list")
    public TableDataInfo list(Fkjl fkjl)
    {
        startPage();
        List<Fkjl> list = fkjlService.selectFkjlList(fkjl);
        return getDataTable(list);
    }

    /**
     * 导出政法委转情指（签收、反馈）列表
     */
    @PreAuthorize("@ss.hasPermi('instruction:fkjl:export')")
    @Log(title = "政法委转情指（签收、反馈）", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, Fkjl fkjl)
    {
        List<Fkjl> list = fkjlService.selectFkjlList(fkjl);
        ExcelUtil<Fkjl> util = new ExcelUtil<Fkjl>(Fkjl.class);
        util.exportExcel(response, list, "政法委转情指（签收、反馈）数据");
    }

    /**
     * 获取政法委转情指（签收、反馈）详细信息
     */
    @PreAuthorize("@ss.hasPermi('instruction:fkjl:query')")
    @GetMapping(value = "/{qzid}")
    public AjaxResult getInfo(@PathVariable("qzid") String qzid)
    {
        return success(fkjlService.selectFkjlByQzid(qzid));
    }

    /**
     * 新增政法委转情指（签收、反馈）
     */
    @PreAuthorize("@ss.hasPermi('instruction:fkjl:add')")
    @Log(title = "政法委转情指（签收、反馈）", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody Fkjl fkjl)
    {
        return toAjax(fkjlService.insertFkjl(fkjl));
    }

    /**
     * 修改政法委转情指（签收、反馈）
     */
    @PreAuthorize("@ss.hasPermi('instruction:fkjl:edit')")
    @Log(title = "政法委转情指（签收、反馈）", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody Fkjl fkjl)
    {
        return toAjax(fkjlService.updateFkjl(fkjl));
    }

    /**
     * 删除政法委转情指（签收、反馈）
     */
    @PreAuthorize("@ss.hasPermi('instruction:fkjl:remove')")
    @Log(title = "政法委转情指（签收、反馈）", businessType = BusinessType.DELETE)
	@DeleteMapping("/{qzids}")
    public AjaxResult remove(@PathVariable String[] qzids)
    {
        return toAjax(fkjlService.deleteFkjlByQzids(qzids));
    }
}
