package com.ruoyi.instruction.domain;

import java.util.Date;
import java.util.List;

import com.fasterxml.jackson.annotation.JsonFormat;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 每日要闻列对象 t_daily_news
 * 
 * <AUTHOR>
 * @date 2023-08-16
 */
public class DailyNews extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** $column.columnComment */
    private Long id;

    /** $column.columnComment */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "${comment}", readConverterExp = "$column.readConverterExp()")
    private Date cTime;

    /** $column.columnComment */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "${comment}", readConverterExp = "$column.readConverterExp()")
    private Date uTime;

    /** 要闻时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "要闻时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date newsTime;

    /** 期数 */
    @Excel(name = "期数")
    private String newsNum;

    /** 标题 */
    @Excel(name = "标题")
    private String title;

    /** 概要 */
    @Excel(name = "概要")
    private String summary;

    List<DailyNewsDetails> dailyNewsDetailsList;

    public void setId(Long id) 
    {
        this.id = id;
    }

    public Long getId() 
    {
        return id;
    }
    public void setcTime(Date cTime) 
    {
        this.cTime = cTime;
    }

    public Date getcTime() 
    {
        return cTime;
    }
    public void setuTime(Date uTime) 
    {
        this.uTime = uTime;
    }

    public Date getuTime() 
    {
        return uTime;
    }
    public void setNewsTime(Date newsTime) 
    {
        this.newsTime = newsTime;
    }

    public Date getNewsTime() 
    {
        return newsTime;
    }
    public void setNewsNum(String newsNum) 
    {
        this.newsNum = newsNum;
    }

    public String getNewsNum() 
    {
        return newsNum;
    }
    public void setTitle(String title) 
    {
        this.title = title;
    }

    public String getTitle() 
    {
        return title;
    }
    public void setSummary(String summary) 
    {
        this.summary = summary;
    }

    public String getSummary() 
    {
        return summary;
    }

    public List<DailyNewsDetails> getDailyNewsDetailsList() {
        return dailyNewsDetailsList;
    }

    public void setDailyNewsDetailsList(List<DailyNewsDetails> dailyNewsDetailsList) {
        this.dailyNewsDetailsList = dailyNewsDetailsList;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("cTime", getcTime())
            .append("uTime", getuTime())
            .append("newsTime", getNewsTime())
            .append("newsNum", getNewsNum())
            .append("title", getTitle())
            .append("summary", getSummary())
            .toString();
    }
}
