package com.ruoyi.instruction.mapper;

import java.util.List;
import com.ruoyi.instruction.domain.RiskDetailWork;

/**
 * 风险排查详情-工作进展Mapper接口
 * 
 * <AUTHOR>
 * @date 2024-01-16
 */
public interface RiskDetailWorkMapper 
{
    /**
     * 查询风险排查详情-工作进展
     * 
     * @param id 风险排查详情-工作进展主键
     * @return 风险排查详情-工作进展
     */
    public RiskDetailWork selectRiskDetailWorkById(Long id);

    /**
     * 查询风险排查详情-工作进展列表
     * 
     * @param riskDetailWork 风险排查详情-工作进展
     * @return 风险排查详情-工作进展集合
     */
    public List<RiskDetailWork> selectRiskDetailWorkList(RiskDetailWork riskDetailWork);

    /**
     * 新增风险排查详情-工作进展
     * 
     * @param riskDetailWork 风险排查详情-工作进展
     * @return 结果
     */
    public int insertRiskDetailWork(RiskDetailWork riskDetailWork);

    /**
     * 修改风险排查详情-工作进展
     * 
     * @param riskDetailWork 风险排查详情-工作进展
     * @return 结果
     */
    public int updateRiskDetailWork(RiskDetailWork riskDetailWork);

    /**
     * 删除风险排查详情-工作进展
     * 
     * @param id 风险排查详情-工作进展主键
     * @return 结果
     */
    public int deleteRiskDetailWorkById(Long id);

    /**
     * 批量删除风险排查详情-工作进展
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteRiskDetailWorkByIds(Long[] ids);
}
