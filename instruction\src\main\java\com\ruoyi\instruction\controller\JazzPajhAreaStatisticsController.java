package com.ruoyi.instruction.controller;

import java.io.InputStream;
import java.time.LocalDate;
import java.time.Month;
import java.util.ArrayList;
import java.util.List;
import javax.servlet.http.HttpServletResponse;

import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.instruction.domain.JazzInstructionEvent;
import com.ruoyi.instruction.domain.reqVo.JazzPajhAreaStatisticsVo;
import org.springframework.beans.BeanUtils;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.instruction.domain.JazzPajhAreaStatistics;
import com.ruoyi.instruction.service.IJazzPajhAreaStatisticsService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;
import org.springframework.web.multipart.MultipartFile;

/**
 * 平安金华区域统计Controller
 * 
 * <AUTHOR>
 * @date 2023-05-16
 */
@RestController
@RequestMapping("/instruction/areaStatistics")
public class JazzPajhAreaStatisticsController extends BaseController
{
    @Autowired
    private IJazzPajhAreaStatisticsService jazzPajhAreaStatisticsService;

    /**
     * 查询平安金华区域统计列表
     */
//    @PreAuthorize("@ss.hasPermi('instruction:areaStatistics:list')")
    @GetMapping("/list")
    public TableDataInfo list(JazzPajhAreaStatisticsVo jazzPajhAreaStatistics)
    {
        String year=jazzPajhAreaStatistics.getYear();
        String month=jazzPajhAreaStatistics.getMonth();
        if (StringUtils.isBlank(year)&&StringUtils.isBlank(month)){
            JazzPajhAreaStatistics jazzPajhAreaStatistics1 = jazzPajhAreaStatisticsService.selectNew(null);
            //获取最近的时间
            if (jazzPajhAreaStatistics1!=null){
                year=jazzPajhAreaStatistics1.getYear();
                month=jazzPajhAreaStatistics1.getMonth();
            }else{
                //获取当前年月
                LocalDate current_date = LocalDate.now();
                year = current_date.getYear()+"";
                month = current_date.getMonth().getValue()+"";
            }
            jazzPajhAreaStatistics.setYear(year);
            jazzPajhAreaStatistics.setMonth(month);


        }
        startPage();
        List<JazzPajhAreaStatistics> list = jazzPajhAreaStatisticsService.selectList(jazzPajhAreaStatistics);
        JazzPajhAreaStatisticsVo jj=new JazzPajhAreaStatisticsVo();
        BeanUtils.copyProperties(jazzPajhAreaStatistics,jj);
        for (JazzPajhAreaStatistics j:list){
           j.setYear(year);
           if (!StringUtils.isEmpty(month)){
               j.setMonth(month);
           }
           if (jazzPajhAreaStatistics.getDateType()==2&&jazzPajhAreaStatistics.getType()==1){
               jj.setType(4L);
               jj.setDateId(j.getAreaId());
               List<JazzPajhAreaStatistics> list1 = jazzPajhAreaStatisticsService.selectList(jj);
               for (JazzPajhAreaStatistics j1:list1){
                   j1.setYear(year);
                   j1.setMonth(month);
               }
               j.setList(list1);
           }
       }
        return getDataTable(list);
    }

    /**
     * 导出平安金华区域统计列表
     */
//    @PreAuthorize("@ss.hasPermi('instruction:areaStatistics:export')")
    @Log(title = "平安金华区域统计", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, JazzPajhAreaStatisticsVo jazzPajhAreaStatistics)
    {
        String year=jazzPajhAreaStatistics.getYear();
        String month=jazzPajhAreaStatistics.getMonth();
        if (StringUtils.isEmpty(year)||StringUtils.isEmpty(month)){
            JazzPajhAreaStatistics jazzPajhAreaStatistics1 = jazzPajhAreaStatisticsService.selectNew(null);
            //获取最近的时间
            if (jazzPajhAreaStatistics1!=null){
                year=jazzPajhAreaStatistics1.getYear();
                month=jazzPajhAreaStatistics1.getMonth();
            }else{
                //获取当前年月
                LocalDate current_date = LocalDate.now();
                year = current_date.getYear()+"";
                month = current_date.getMonth().getValue()+"";
            }
            jazzPajhAreaStatistics.setYear(year);
            jazzPajhAreaStatistics.setMonth(month);


        }
        List<JazzPajhAreaStatistics> list = jazzPajhAreaStatisticsService.selectList(jazzPajhAreaStatistics);
        ExcelUtil<JazzPajhAreaStatistics> util = new ExcelUtil<JazzPajhAreaStatistics>(JazzPajhAreaStatistics.class);
        util.exportExcel(response, list, "平安金华区域统计数据");
    }

    /**
     * 获取平安金华区域统计详细信息
     */
    @PreAuthorize("@ss.hasPermi('instruction:areaStatistics:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(jazzPajhAreaStatisticsService.selectJazzPajhAreaStatisticsById(id));
    }

//    /**
//     * 新增平安金华区域统计
//     */
//    @PreAuthorize("@ss.hasPermi('instruction:areaStatistics:add')")
//    @Log(title = "平安金华区域统计", businessType = BusinessType.INSERT)
//    @PostMapping
//    public AjaxResult add(@RequestBody JazzPajhAreaStatistics jazzPajhAreaStatistics)
//    {
//        return toAjax(jazzPajhAreaStatisticsService.insertJazzPajhAreaStatistics(jazzPajhAreaStatistics));
//    }
//
//    /**
//     * 修改平安金华区域统计
//     */
//    @PreAuthorize("@ss.hasPermi('instruction:areaStatistics:edit')")
//    @Log(title = "平安金华区域统计", businessType = BusinessType.UPDATE)
//    @PutMapping
//    public AjaxResult edit(@RequestBody JazzPajhAreaStatistics jazzPajhAreaStatistics)
//    {
//        return toAjax(jazzPajhAreaStatisticsService.updateJazzPajhAreaStatistics(jazzPajhAreaStatistics));
//    }

    /**
     * 删除平安金华区域统计
     */
    @PreAuthorize("@ss.hasPermi('instruction:areaStatistics:remove')")
    @Log(title = "平安金华区域统计", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(jazzPajhAreaStatisticsService.deleteJazzPajhAreaStatisticsByIds(ids));
    }

    /**
     * 新增平安金华区域统计
     */
//    @PreAuthorize("@ss.hasPermi('instruction:areaStatistics:add')")
    @Log(title = "平安金华区域统计", businessType = BusinessType.INSERT)
    @PostMapping("addList")
    public AjaxResult addList(@RequestBody JazzPajhAreaStatisticsVo jazzPajhAreaStatisticsVo)
    {
        return toAjax(jazzPajhAreaStatisticsService.addList(jazzPajhAreaStatisticsVo));
    }

    /**
     * 修改平安金华区域统计
     */
    @Log(title = "平安金华区域统计", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody JazzPajhAreaStatistics jazzPajhAreaStatistics)
    {
//        List<JazzPajhAreaStatistics> jazzPajhAreaStatisticsList=new ArrayList<>();
//        jazzPajhAreaStatisticsList.add(jazzPajhAreaStatistics);
        return toAjax(jazzPajhAreaStatisticsService.update(jazzPajhAreaStatistics));
    }

    /**
     * 下载平安金华导入模板
     *
     * @param response
     */
    @PostMapping("/importPersonTemplate")
    public void importPersonTemplate(HttpServletResponse response) {
        ExcelUtil<JazzPajhAreaStatistics> util = new ExcelUtil<>(JazzPajhAreaStatistics.class);
        util.importTemplateExcel(response, "金安大事件导入模板");
    }

    /**
     * 导入多张sheet数据
     */
    @Log(title = "导入平安金华数据", businessType = BusinessType.IMPORT)
    @PostMapping("/importMoreSheetData")
    public AjaxResult importMoreSheetData(MultipartFile file) throws Exception {
        String msg = "";
        // 获取文件名
        if (file == null) {
            return AjaxResult.error("文件不能为空");
        }
        String fileName = file.getOriginalFilename();
        if (StringUtils.isEmpty(fileName)) {
            return AjaxResult.error("文件名不能为空");
        }
        // 获取文件后缀
        String prefix = fileName.substring(fileName.lastIndexOf("."));
        if (!prefix.toLowerCase().contains("xls") && !prefix.toLowerCase().contains("xlsx")) {
            return AjaxResult.error("文件格式异常，请上传Excel文件格式");
        }
        //由于2003和2007的版本所使用的接口不一样，所以这里统一用Workbook做兼容
        boolean isExcel2003 = prefix.toLowerCase().endsWith("xls") ? true : false;
        InputStream inputStream = null;
        try {
            inputStream = file.getInputStream();
            msg = jazzPajhAreaStatisticsService.uploadPajhAreaStatistics(inputStream, isExcel2003);
        } catch (Exception e) {
            return AjaxResult.error("上传失败,原因如下：" + e.getMessage());
        } finally {
            if (inputStream != null) {
                inputStream.close();
            }
        }

        return AjaxResult.success(msg);
    }
}
