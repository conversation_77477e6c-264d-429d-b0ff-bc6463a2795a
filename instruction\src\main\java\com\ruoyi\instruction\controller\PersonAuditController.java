package com.ruoyi.instruction.controller;

import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;
import javax.servlet.http.HttpServletResponse;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.ruoyi.common.constant.Constants;
import com.ruoyi.common.core.domain.entity.SysDept;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.instruction.domain.PersonInterview;
import com.ruoyi.instruction.domain.excelVo.PersonLevelChangeExcelVo;
import com.ruoyi.instruction.domain.excelVo.PersonTypeChangeExcelVo;
import com.ruoyi.instruction.mapper.PersonAuditMapper;
import com.ruoyi.system.mapper.SysDeptMapper;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.instruction.domain.PersonAudit;
import com.ruoyi.instruction.service.IPersonAuditService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 人员申请记录Controller
 *
 * <AUTHOR>
 * @date 2024-01-05
 */
@RestController
@RequestMapping("/person/audit")
public class PersonAuditController extends BaseController {
    @Autowired
    private IPersonAuditService personAuditService;

    @Autowired
    private SysDeptMapper deptMapper;

    @Autowired
    private PersonAuditMapper personAuditMapper;

    /**
     * 查询人员申请记录列表
     */
    // @PreAuthorize("@ss.hasPermi('system:audit:list')")
    @GetMapping("/list")
    public TableDataInfo list(PersonAudit personAudit) {
        if (personAudit.getPersonId() == null) {
            if (personAudit.getAuditType() == 1) {
                //进行是市级、县市区、乡镇街道账号判断
                SysDept dept = SecurityUtils.getLoginUser().getUser().getDept();
                Long parentId = dept.getParentId();
                personAudit.setDeptId(dept.getDeptId());
                if (parentId.equals(Constants.JINHUA_CITY_DEPT_ID) || dept.getDeptId().equals(Constants.JINHUA_CITY_DEPT_ID)) {
                    personAudit.setAccountType(1);
                } else if (parentId == 213L || parentId == 214L || parentId == 215L || parentId == 216L || parentId == 217L || parentId == 218L || parentId == 219L || parentId == 220L || parentId == 221L || parentId == 262L) {
                    personAudit.setAccountType(2);
                    //查询县市区下的乡镇街道deptId
                    List<SysDept> deptList = deptMapper.selectTownListByParentId(parentId);
                    List<Long> deptIds = deptList.stream().map(SysDept::getDeptId).collect(Collectors.toList());
                    deptIds.add(dept.getDeptId());
                    Map<String, Object> params = personAudit.getParams();
                    params.put("deptIds", deptIds);
                }
            } else {
                personAudit.setSqdwDeptId(SecurityUtils.getDeptId());
            }
        }
        if (personAudit.getCountyParam() != null && !personAudit.getCountyParam().isEmpty()) {
            String[] countyParam = personAudit.getCountyParam().split(",");
            Map<String, Object> params = personAudit.getParams();
            params.put("countyParam", countyParam);
            personAudit.setParams(params);
        }
        if (personAudit.getTownParam() != null && !personAudit.getTownParam().isEmpty()) {
            String[] townParam = personAudit.getTownParam().split(",");
            Map<String, Object> params = personAudit.getParams();
            params.put("townParam", townParam);
            personAudit.setParams(params);
        }
        startPage();
        List<PersonAudit> list = personAuditService.selectPersonAuditList(personAudit);
        return getDataTable(list);
    }

    /**
     * 根据人员id获取最后处理记录
     *
     * @param p
     * @return
     */
    @GetMapping("/getFinallyRecordByPersonId")
    public AjaxResult getFinallyRecordByPersonId(PersonAudit p) {
        PersonAudit personAudit = personAuditMapper.getFinallyRecordByPersonId(p);
        //判断是否跨级
        Long sqdwDeptId = personAudit.getSqdwDeptId();
        SysDept dept = deptMapper.selectDeptById(sqdwDeptId);
        Long parentId = dept.getParentId();
        //判断差值
        // int nowLevel = personAudit.getNowLevel();
        // int applyLevel = personAudit.getApplyLevel();
        // int absDiff = Math.abs(nowLevel - applyLevel);
        if (!parentId.equals(Constants.JINHUA_CITY_DEPT_ID) && (personAudit.getNowLevel().equals(3) || personAudit.getApplyLevel().equals(3))) {
            personAudit.setIsSkipLevel(1);
            if (personAudit.getIsFinish() != 1) {
                //是跨级别调整且未流程未结束
                if (personAudit.getAuditDwCounty() == null) {
                    personAudit.setAuditDwCounty(personAudit.getDutyPlace());
                }
                if (personAudit.getAuditDw() == null) {
                    personAudit.setAuditDw("市委政法委");
                }
            }

        } else {
            //非跨级
            personAudit.setIsSkipLevel(0);
            if (personAudit.getIsFinish() != 1) {
                if (parentId == 213L || parentId == 214L || parentId == 215L || parentId == 216L || parentId == 217L || parentId == 218L || parentId == 219L || parentId == 220L || parentId == 221L || parentId == 262L) {
                    personAudit.setAuditDw("市委政法委");
                } else {
                    personAudit.setAuditDw(personAudit.getDutyPlace());
                }
            }
        }
        return AjaxResult.success(personAudit);
    }

    /**
     * 获取待审核数、审核中数
     *
     * @param personAudit
     * @return
     */
    @GetMapping("/getAuditNumber")
    public AjaxResult getAuditNumber(PersonAudit personAudit) {

        SysDept dept = SecurityUtils.getLoginUser().getUser().getDept();
        Map<String, Object> map = new HashMap<>();
        //查询我发起的处理
        personAudit.setSqdwDeptId(dept.getDeptId());
        List<PersonAudit> list = personAuditService.selectPersonAuditList(personAudit);
        map.put("otherDeal", list.size());

        personAudit.setSqdwDeptId(null);
        //查询待审核数
        personAudit.setIsAuditCity(0);
        //进行是市级、县市区、乡镇街道账号判断
        Long parentId = dept.getParentId();
        if (parentId.equals(Constants.JINHUA_CITY_DEPT_ID) || dept.getDeptId().equals(Constants.JINHUA_CITY_DEPT_ID)) {
            personAudit.setAccountType(1);
            List<PersonAudit> alist = personAuditService.selectPersonAuditList(personAudit);
            map.put("myDeal", alist.size());
        } else if (parentId == 213L || parentId == 214L || parentId == 215L || parentId == 216L || parentId == 217L || parentId == 218L || parentId == 219L || parentId == 220L || parentId == 221L || parentId == 262L) {
            personAudit.setAccountType(2);
            //查询县市区下的乡镇街道deptId
            List<SysDept> deptList = deptMapper.selectTownListByParentId(parentId);
            List<Long> deptIds = deptList.stream().map(SysDept::getDeptId).collect(Collectors.toList());
            deptIds.add(dept.getDeptId());
            Map<String, Object> params = personAudit.getParams();
            params.put("deptIds", deptIds);
            List<PersonAudit> alist = personAuditService.selectPersonAuditList(personAudit);
            map.put("myDeal", alist.size());
        } else {
            map.put("myDeal", 0);
        }
        return AjaxResult.success(map);
    }

    /**
     * 导出人员申请记录列表
     */
    // @PreAuthorize("@ss.hasPermi('system:audit:export')")
    @Log(title = "重点人员等级变更记录", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, PersonAudit personAudit) {
        List<PersonAudit> list = personAuditService.selectGradeChangeList(personAudit);
        ExcelUtil<PersonAudit> util = new ExcelUtil<PersonAudit>(PersonAudit.class);
        util.exportExcel(response, list, "重点人员等级变更记录");
    }

    /**
     * 获取人员申请记录详细信息
     */
    // @PreAuthorize("@ss.hasPermi('system:audit:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id) {
        return success(personAuditService.selectPersonAuditById(id));
    }

    /**
     * 新增人员申请记录
     */
    // @PreAuthorize("@ss.hasPermi('system:audit:add')")
    @Log(title = "人员申请记录", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody PersonAudit personAudit) {
        return personAuditService.insertPersonAudit(personAudit);
    }

    /**
     * 修改人员申请记录
     */
    // @PreAuthorize("@ss.hasPermi('system:audit:edit')")
    @Log(title = "人员申请记录", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody PersonAudit personAudit) {
        return toAjax(personAuditService.updatePersonAudit(personAudit));
    }

    /**
     * 删除人员申请记录
     */
    // @PreAuthorize("@ss.hasPermi('system:audit:remove')")
    @Log(title = "人员申请记录", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids) {
        return toAjax(personAuditService.deletePersonAuditByIds(ids));
    }

    /**
     * 人员等级变更列表
     * @param personAudit
     * @return
     */
    @GetMapping("/gradeChangeList")
    public TableDataInfo gradeChangeList(PersonAudit personAudit) {
        startPage();
        List<PersonAudit> list = personAuditService.selectGradeChangeList(personAudit);
        return getDataTable(list);
    }

    /**
     * 获取统计信息
     * @param personAudit
     * @return
     */
    @GetMapping("/getStatistics")
    public AjaxResult getStatistics(PersonAudit personAudit) {
        return personAuditService.getStatistics(personAudit);
    }

    @GetMapping("/getTypeByStr")
    public AjaxResult getTypeByStr(String str) {
        Integer type = 0;
        if (str.contains("预警")) {
            type = 1;
        } else if (str.contains("风险")) {
            type = 2;
        } else if (str.contains("涉稳")) {
            type = 3;
        } else if (str.contains("指挥")) {
            type = 4;
        } else if (str.contains("地图")) {
            type = 5;
        }
        return AjaxResult.success(type);
    }

    /**
     * 导出重点人员等级调整记录
     * @param response
     * @param personAudit
     */
    @PostMapping ("/exportPersonLevelChange")
    public void exportPersonLevelChange(HttpServletResponse response, PersonAudit personAudit) throws IOException {
        //查询初始值(截止3月31日排查的重点人员数据为准)
        personAudit.setType(1);
        List<PersonAudit> personAuditList = personAuditMapper.getPersonInitalLevel(personAudit);

        //查询人员类型分析
        List<PersonAudit> personTypeList = personAuditMapper.getPersonTypeList(personAudit);

        String[] countys = {"婺城区","金东区","兰溪市","东阳市","义乌市","永康市","浦江县","武义县","磐安县","开发区"};
        if (personAudit.getDutyPlace()!= null){
            String[] newCountys = {personAudit.getDutyPlace()};
            countys = newCountys;
        }
        List<PersonTypeChangeExcelVo> personTypeChangeExcelVoList = new ArrayList<>();

        List<PersonLevelChangeExcelVo> excelVoList = new ArrayList<>();
        for (int i = 0; i < countys.length; i++) {
            //查询县市区数据
            int finalI = i;
            String[] finalCountys = countys;
            Long nowLevel1 = personAuditList.stream().filter(item -> item.getDutyPlace().equals(finalCountys[finalI]) && item.getNowLevel() == 1).findFirst().map(PersonAudit::getACount).orElse(0L);
            Long nowLevel2 = personAuditList.stream().filter(item -> item.getDutyPlace().equals(finalCountys[finalI]) && item.getNowLevel() == 2).findFirst().map(PersonAudit::getACount).orElse(0L);
            Long nowLevel3 = personAuditList.stream().filter(item -> item.getDutyPlace().equals(finalCountys[finalI]) && item.getNowLevel() == 3).findFirst().map(PersonAudit::getACount).orElse(0L);

            PersonLevelChangeExcelVo personLevelChangeExcelVo = new PersonLevelChangeExcelVo();
            personLevelChangeExcelVo.setCounty(countys[i]);
            personLevelChangeExcelVo.setInitialNum1(nowLevel1);
            personLevelChangeExcelVo.setInitialNum2(nowLevel2);
            personLevelChangeExcelVo.setInitialNum3(nowLevel3);
            personLevelChangeExcelVo.setTotalInitialNum(nowLevel1+nowLevel2+nowLevel3);

            //查询人员等级调整
            personAudit.setType(null);
            personAudit.setDutyPlace(finalCountys[finalI]);
            List<Map<Object, Object>> filteredMaps = personAuditMapper.getGradeChangeStatistics(personAudit);

            // 过滤出 apply_level 等于 1 且 now_level 小于 apply_level 值的数据集合 统计 aCount 的总和值
            //新增等级1
            long addLevel1 = filteredMaps.stream()
                    .filter(map -> (Integer)map.get("apply_level") == 1 && ((Integer) map.get("now_level")) < (Integer) map.get("apply_level"))
                    .mapToLong(map -> (Long) map.get("aCount"))
                    .sum();
            personLevelChangeExcelVo.setAddedNum1(OptionalLong.of(addLevel1).orElse(0L));

            //新增等级2
            long addLevel2 = filteredMaps.stream()
                    .filter(map -> (Integer)map.get("apply_level") == 2 && ((Integer) map.get("now_level")) < (Integer) map.get("apply_level"))
                    .mapToLong(map -> (Long) map.get("aCount"))
                    .sum();
            personLevelChangeExcelVo.setAddedNum2(OptionalLong.of(addLevel2).orElse(0L));
            //降级等级2
            long downgradeNum2 = filteredMaps.stream()
                    .filter(map -> (Integer) map.get("now_level") == 2 && (Integer) map.get("apply_level") == 1)
                    .mapToLong(map -> (Long) map.get("aCount"))
                    .sum();
            personLevelChangeExcelVo.setDowngradeNum2(OptionalLong.of(downgradeNum2).orElse(0L));
            //新增等级3
            long addLevel3 = filteredMaps.stream()
                    .filter(map -> (Integer)map.get("apply_level") == 3  && ((Integer) map.get("now_level")) < (Integer) map.get("apply_level"))
                    .mapToLong(map -> (Long) map.get("aCount"))
                    .sum();
            personLevelChangeExcelVo.setAddedNum3(OptionalLong.of(addLevel3).orElse(0L));
            //降级等级3
            long downgradeNum3 = filteredMaps.stream()
                    .filter(map -> ((Integer) map.get("now_level") == 3 && (Integer) map.get("apply_level") == 2) || ((Integer) map.get("now_level") == 3 && (Integer) map.get("apply_level") == 1))
                    .mapToLong(map -> (Long) map.get("aCount"))
                    .sum();
            personLevelChangeExcelVo.setDowngradeNum3(OptionalLong.of(downgradeNum3).orElse(0L));
            //等级1销号
            long cancelLevel1 = filteredMaps.stream()
                    .filter(map -> (Integer)map.get("apply_level") == 0  && (Integer)map.get("now_level") == 1)
                    .mapToLong(map -> (Long) map.get("aCount"))
                    .sum();
            personLevelChangeExcelVo.setCancelledNum1(OptionalLong.of(cancelLevel1).orElse(0L));
            //等级2销号
            long cancelLevel2 = filteredMaps.stream()
                    .filter(map -> (Integer)map.get("apply_level") == 0  && (Integer)map.get("now_level") == 2 )
                    .mapToLong(map -> (Long) map.get("aCount"))
                    .sum();
            personLevelChangeExcelVo.setCancelledNum2(OptionalLong.of(cancelLevel2).orElse(0L));
            //等级3销号
            long cancelLevel3 = filteredMaps.stream()
                    .filter(map -> (Integer)map.get("apply_level") == 0  && (Integer)map.get("now_level") == 3 )
                    .mapToLong(map -> (Long) map.get("aCount"))
                    .sum();
            personLevelChangeExcelVo.setCancelledNum3(OptionalLong.of(cancelLevel3).orElse(0L));
            personLevelChangeExcelVo.setTotalAddedNum(OptionalLong.of(addLevel1).orElse(0L)+OptionalLong.of(addLevel2).orElse(0L)+OptionalLong.of(addLevel3).orElse(0L));
            personLevelChangeExcelVo.setTotalDowngradeNum(OptionalLong.of(downgradeNum2).orElse(0L)+OptionalLong.of(downgradeNum3).orElse(0L));
            personLevelChangeExcelVo.setTotalCancelledNum(OptionalLong.of(cancelLevel1).orElse(0L)+OptionalLong.of(cancelLevel2).orElse(0L)+OptionalLong.of(cancelLevel3).orElse(0L));
            excelVoList.add(personLevelChangeExcelVo);


            //添加人员类型数据
            PersonTypeChangeExcelVo personTypeChangeExcelVo = new PersonTypeChangeExcelVo();
            //涉稳警情类
            Long swjqRedNum = personTypeList.stream().filter(item -> item.getDutyPlace().equals(finalCountys[finalI]) && item.getNowLevel() == 3 && item.getPersonType().equals("涉稳警情类")).findFirst().map(PersonAudit::getACount).orElse(0L);
            Long swjqOrangeNum = personTypeList.stream().filter(item -> item.getDutyPlace().equals(finalCountys[finalI]) && item.getNowLevel() == 2 && item.getPersonType().equals("涉稳警情类")).findFirst().map(PersonAudit::getACount).orElse(0L);
            Long swjqYellowNum = personTypeList.stream().filter(item -> item.getDutyPlace().equals(finalCountys[finalI]) && item.getNowLevel() == 1 && item.getPersonType().equals("涉稳警情类")).findFirst().map(PersonAudit::getACount).orElse(0L);
            Long swjqTotalNum = swjqRedNum + swjqOrangeNum + swjqYellowNum;
            personTypeChangeExcelVo.setSwjqRedNum(swjqRedNum);
            personTypeChangeExcelVo.setSwjqOrangeNum(swjqOrangeNum);
            personTypeChangeExcelVo.setSwjqYellowNum(swjqYellowNum);
            personTypeChangeExcelVo.setSwjqTotalNum(swjqTotalNum);

            //诉求群体类
            Long sqqtRedNum = personTypeList.stream().filter(item -> item.getDutyPlace().equals(finalCountys[finalI]) && item.getNowLevel() == 3 && item.getPersonType().equals("诉求群体类")).findFirst().map(PersonAudit::getACount).orElse(0L);
            Long sqqtOrangeNum = personTypeList.stream().filter(item -> item.getDutyPlace().equals(finalCountys[finalI]) && item.getNowLevel() == 2 && item.getPersonType().equals("诉求群体类")).findFirst().map(PersonAudit::getACount).orElse(0L);
            Long sqqtYellowNum = personTypeList.stream().filter(item -> item.getDutyPlace().equals(finalCountys[finalI]) && item.getNowLevel() == 1 && item.getPersonType().equals("诉求群体类")).findFirst().map(PersonAudit::getACount).orElse(0L);
            Long sqqtTotalNum = sqqtRedNum + sqqtOrangeNum + sqqtYellowNum;
            personTypeChangeExcelVo.setSqqtRedNum(sqqtRedNum);
            personTypeChangeExcelVo.setSqqtOrangeNum(sqqtOrangeNum);
            personTypeChangeExcelVo.setSqqtYellowNum(sqqtYellowNum);
            personTypeChangeExcelVo.setSqqtTotalNum(sqqtTotalNum);

            //个人极端类
            Long grjdRedNum = personTypeList.stream().filter(item -> item.getDutyPlace().equals(finalCountys[finalI]) && item.getNowLevel() == 3 && item.getPersonType().equals("个人极端类")).findFirst().map(PersonAudit::getACount).orElse(0L);
            Long grjdOrangeNum = personTypeList.stream().filter(item -> item.getDutyPlace().equals(finalCountys[finalI]) && item.getNowLevel() == 2 && item.getPersonType().equals("个人极端类")).findFirst().map(PersonAudit::getACount).orElse(0L);
            Long grjdYellowNum = personTypeList.stream().filter(item -> item.getDutyPlace().equals(finalCountys[finalI]) && item.getNowLevel() == 1 && item.getPersonType().equals("个人极端类")).findFirst().map(PersonAudit::getACount).orElse(0L);
            Long grjdTotalNum = grjdRedNum + grjdOrangeNum + grjdYellowNum;
            personTypeChangeExcelVo.setGrjdRedNum(grjdRedNum);
            personTypeChangeExcelVo.setGrjdOrangeNum(grjdOrangeNum);
            personTypeChangeExcelVo.setGrjdYellowNum(grjdYellowNum);
            personTypeChangeExcelVo.setGrjdTotalNum(grjdTotalNum);

            //煽动串联
            Long sdclRedNum = personTypeList.stream().filter(item -> item.getDutyPlace().equals(finalCountys[finalI]) && item.getNowLevel() == 3 && item.getPersonType().equals("煽动串联类")).findFirst().map(PersonAudit::getACount).orElse(0L);
            Long sdclOrangeNum = personTypeList.stream().filter(item -> item.getDutyPlace().equals(finalCountys[finalI]) && item.getNowLevel() == 2 && item.getPersonType().equals("煽动串联类")).findFirst().map(PersonAudit::getACount).orElse(0L);
            Long sdclYellowNum = personTypeList.stream().filter(item -> item.getDutyPlace().equals(finalCountys[finalI]) && item.getNowLevel() == 1 && item.getPersonType().equals("煽动串联类")).findFirst().map(PersonAudit::getACount).orElse(0L);
            Long sdclTotalNum = sdclRedNum + sdclOrangeNum + sdclYellowNum;
            personTypeChangeExcelVo.setSdclRedNum(sdclRedNum);
            personTypeChangeExcelVo.setSdclOrangeNum(sdclOrangeNum);
            personTypeChangeExcelVo.setSdclYellowNum(sdclYellowNum);
            personTypeChangeExcelVo.setSdclTotalNum(sdclTotalNum);

            //易肇事肇祸精神病
            Long yzszhRedNum = personTypeList.stream().filter(item -> item.getDutyPlace().equals(finalCountys[finalI]) && item.getNowLevel() == 3 && item.getPersonType().equals("易肇事肇祸精神病")).findFirst().map(PersonAudit::getACount).orElse(0L);
            Long yzszhOrangeNum = personTypeList.stream().filter(item -> item.getDutyPlace().equals(finalCountys[finalI]) && item.getNowLevel() == 2 && item.getPersonType().equals("易肇事肇祸精神病")).findFirst().map(PersonAudit::getACount).orElse(0L);
            Long yzszhYellowNum = personTypeList.stream().filter(item -> item.getDutyPlace().equals(finalCountys[finalI]) && item.getNowLevel() == 1 && item.getPersonType().equals("易肇事肇祸精神病")).findFirst().map(PersonAudit::getACount).orElse(0L);
            Long yzszhTotalNum = yzszhRedNum + yzszhOrangeNum + yzszhYellowNum;
            personTypeChangeExcelVo.setYzszhjsbRedNum(yzszhRedNum);
            personTypeChangeExcelVo.setYzszhjsbOrangeNum(yzszhOrangeNum);
            personTypeChangeExcelVo.setYzszhjsbYellowNum(yzszhYellowNum);
            personTypeChangeExcelVo.setYzszhjsbTotalNum(yzszhTotalNum);

            //其他
            Long qtRedNum = personTypeList.stream().filter(item -> item.getDutyPlace().equals(finalCountys[finalI]) && item.getNowLevel() == 3 && item.getPersonType().equals("其他类")).findFirst().map(PersonAudit::getACount).orElse(0L);
            Long qtOrangeNum = personTypeList.stream().filter(item -> item.getDutyPlace().equals(finalCountys[finalI]) && item.getNowLevel() == 2 && item.getPersonType().equals("其他类")).findFirst().map(PersonAudit::getACount).orElse(0L);
            Long qtYellowNum = personTypeList.stream().filter(item -> item.getDutyPlace().equals(finalCountys[finalI]) && item.getNowLevel() == 1 && item.getPersonType().equals("其他类")).findFirst().map(PersonAudit::getACount).orElse(0L);
            Long qtTotalNum = qtRedNum + qtOrangeNum + qtYellowNum;
            personTypeChangeExcelVo.setOtherRedNum(qtRedNum);
            personTypeChangeExcelVo.setOtherOrangeNum(qtOrangeNum);
            personTypeChangeExcelVo.setOtherYellowNum(qtYellowNum);
            personTypeChangeExcelVo.setOtherTotalNum(qtTotalNum);

            //总计
            personTypeChangeExcelVo.setTotalRedNum(swjqRedNum+sqqtRedNum+grjdRedNum+sdclRedNum+yzszhRedNum+qtRedNum);
            personTypeChangeExcelVo.setTotalOrangeNum(swjqOrangeNum+sqqtOrangeNum+grjdOrangeNum+sdclOrangeNum+yzszhOrangeNum+qtOrangeNum);
            personTypeChangeExcelVo.setTotalYellowNum(swjqYellowNum+sqqtYellowNum+grjdYellowNum+sdclYellowNum+yzszhYellowNum+qtYellowNum);
            personTypeChangeExcelVo.setTotalTotalNum(swjqTotalNum+sqqtTotalNum+grjdTotalNum+sdclTotalNum+yzszhTotalNum+qtTotalNum);
            personTypeChangeExcelVo.setCounty(countys[i]);
            personTypeChangeExcelVoList.add(personTypeChangeExcelVo);


        }

        //添加合计
        PersonLevelChangeExcelVo totalVo = new PersonLevelChangeExcelVo();
        totalVo.setCounty("合计");
        totalVo.setInitialNum1(excelVoList.stream().mapToLong(PersonLevelChangeExcelVo::getInitialNum1).sum());
        totalVo.setInitialNum2(excelVoList.stream().mapToLong(PersonLevelChangeExcelVo::getInitialNum2).sum());
        totalVo.setInitialNum3(excelVoList.stream().mapToLong(PersonLevelChangeExcelVo::getInitialNum3).sum());
        totalVo.setAddedNum1(excelVoList.stream().mapToLong(PersonLevelChangeExcelVo::getAddedNum1).sum());
        totalVo.setAddedNum2(excelVoList.stream().mapToLong(PersonLevelChangeExcelVo::getAddedNum2).sum());
        totalVo.setAddedNum3(excelVoList.stream().mapToLong(PersonLevelChangeExcelVo::getAddedNum3).sum());
        totalVo.setDowngradeNum2(excelVoList.stream().mapToLong(PersonLevelChangeExcelVo::getDowngradeNum2).sum());
        totalVo.setDowngradeNum3(excelVoList.stream().mapToLong(PersonLevelChangeExcelVo::getDowngradeNum3).sum());
        totalVo.setCancelledNum1(excelVoList.stream().mapToLong(PersonLevelChangeExcelVo::getCancelledNum1).sum());
        totalVo.setCancelledNum2(excelVoList.stream().mapToLong(PersonLevelChangeExcelVo::getCancelledNum2).sum());
        totalVo.setCancelledNum3(excelVoList.stream().mapToLong(PersonLevelChangeExcelVo::getCancelledNum3).sum());
        totalVo.setTotalAddedNum(excelVoList.stream().mapToLong(PersonLevelChangeExcelVo::getTotalAddedNum).sum());
        totalVo.setTotalDowngradeNum(excelVoList.stream().mapToLong(PersonLevelChangeExcelVo::getTotalDowngradeNum).sum());
        totalVo.setTotalCancelledNum(excelVoList.stream().mapToLong(PersonLevelChangeExcelVo::getTotalCancelledNum).sum());
        totalVo.setTotalInitialNum(excelVoList.stream().mapToLong(PersonLevelChangeExcelVo::getTotalInitialNum).sum());
        excelVoList.add(totalVo);

        //添加人员类型合计
        PersonTypeChangeExcelVo personTypeChangeExcelVo = new PersonTypeChangeExcelVo();
        personTypeChangeExcelVo.setCounty("合计");
        personTypeChangeExcelVo.setSwjqRedNum(personTypeChangeExcelVoList.stream().mapToLong(PersonTypeChangeExcelVo::getSwjqRedNum).sum());
        personTypeChangeExcelVo.setSwjqOrangeNum(personTypeChangeExcelVoList.stream().mapToLong(PersonTypeChangeExcelVo::getSwjqOrangeNum).sum());
        personTypeChangeExcelVo.setSwjqYellowNum(personTypeChangeExcelVoList.stream().mapToLong(PersonTypeChangeExcelVo::getSwjqYellowNum).sum());
        personTypeChangeExcelVo.setSwjqTotalNum(personTypeChangeExcelVoList.stream().mapToLong(PersonTypeChangeExcelVo::getSwjqTotalNum).sum());
        personTypeChangeExcelVo.setSqqtRedNum(personTypeChangeExcelVoList.stream().mapToLong(PersonTypeChangeExcelVo::getSqqtRedNum).sum());
        personTypeChangeExcelVo.setSqqtOrangeNum(personTypeChangeExcelVoList.stream().mapToLong(PersonTypeChangeExcelVo::getSqqtOrangeNum).sum());
        personTypeChangeExcelVo.setSqqtYellowNum(personTypeChangeExcelVoList.stream().mapToLong(PersonTypeChangeExcelVo::getSqqtYellowNum).sum());
        personTypeChangeExcelVo.setSqqtTotalNum(personTypeChangeExcelVoList.stream().mapToLong(PersonTypeChangeExcelVo::getSqqtTotalNum).sum());
        personTypeChangeExcelVo.setGrjdRedNum(personTypeChangeExcelVoList.stream().mapToLong(PersonTypeChangeExcelVo::getGrjdRedNum).sum());
        personTypeChangeExcelVo.setGrjdOrangeNum(personTypeChangeExcelVoList.stream().mapToLong(PersonTypeChangeExcelVo::getGrjdOrangeNum).sum());
        personTypeChangeExcelVo.setGrjdYellowNum(personTypeChangeExcelVoList.stream().mapToLong(PersonTypeChangeExcelVo::getGrjdYellowNum).sum());
        personTypeChangeExcelVo.setGrjdTotalNum(personTypeChangeExcelVoList.stream().mapToLong(PersonTypeChangeExcelVo::getGrjdTotalNum).sum());
        personTypeChangeExcelVo.setSdclRedNum(personTypeChangeExcelVoList.stream().mapToLong(PersonTypeChangeExcelVo::getSdclRedNum).sum());
        personTypeChangeExcelVo.setSdclOrangeNum(personTypeChangeExcelVoList.stream().mapToLong(PersonTypeChangeExcelVo::getSdclOrangeNum).sum());
        personTypeChangeExcelVo.setSdclYellowNum(personTypeChangeExcelVoList.stream().mapToLong(PersonTypeChangeExcelVo::getSdclYellowNum).sum());
        personTypeChangeExcelVo.setSdclTotalNum(personTypeChangeExcelVoList.stream().mapToLong(PersonTypeChangeExcelVo::getSdclTotalNum).sum());
        personTypeChangeExcelVo.setYzszhjsbRedNum(personTypeChangeExcelVoList.stream().mapToLong(PersonTypeChangeExcelVo::getYzszhjsbRedNum).sum());
        personTypeChangeExcelVo.setYzszhjsbOrangeNum(personTypeChangeExcelVoList.stream().mapToLong(PersonTypeChangeExcelVo::getYzszhjsbOrangeNum).sum());
        personTypeChangeExcelVo.setYzszhjsbYellowNum(personTypeChangeExcelVoList.stream().mapToLong(PersonTypeChangeExcelVo::getYzszhjsbYellowNum).sum());
        personTypeChangeExcelVo.setYzszhjsbTotalNum(personTypeChangeExcelVoList.stream().mapToLong(PersonTypeChangeExcelVo::getYzszhjsbTotalNum).sum());
        personTypeChangeExcelVo.setOtherRedNum(personTypeChangeExcelVoList.stream().mapToLong(PersonTypeChangeExcelVo::getOtherRedNum).sum());
        personTypeChangeExcelVo.setOtherOrangeNum(personTypeChangeExcelVoList.stream().mapToLong(PersonTypeChangeExcelVo::getOtherOrangeNum).sum());
        personTypeChangeExcelVo.setOtherYellowNum(personTypeChangeExcelVoList.stream().mapToLong(PersonTypeChangeExcelVo::getOtherYellowNum).sum());
        personTypeChangeExcelVo.setOtherTotalNum(personTypeChangeExcelVoList.stream().mapToLong(PersonTypeChangeExcelVo::getOtherTotalNum).sum());
        personTypeChangeExcelVo.setTotalRedNum(personTypeChangeExcelVoList.stream().mapToLong(PersonTypeChangeExcelVo::getTotalRedNum).sum());
        personTypeChangeExcelVo.setTotalOrangeNum(personTypeChangeExcelVoList.stream().mapToLong(PersonTypeChangeExcelVo::getTotalOrangeNum).sum());
        personTypeChangeExcelVo.setTotalYellowNum(personTypeChangeExcelVoList.stream().mapToLong(PersonTypeChangeExcelVo::getTotalYellowNum).sum());
        personTypeChangeExcelVo.setTotalTotalNum(personTypeChangeExcelVoList.stream().mapToLong(PersonTypeChangeExcelVo::getTotalTotalNum).sum());
        personTypeChangeExcelVoList.add(personTypeChangeExcelVo);

        try {
            response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
            response.setCharacterEncoding("utf-8");
            // 这里URLEncoder.encode可以防止中文乱码 当然和easyexcel没有关系
            String fileName = URLEncoder.encode("测试", "UTF-8").replaceAll("\\+", "%20");
            response.setHeader("Content-disposition", "attachment;filename*=utf-8''" + fileName + ".xlsx");
            ExcelWriter excelWriter = EasyExcel.write(response.getOutputStream()).build();
            WriteSheet writeSheet = EasyExcel.writerSheet(0, "区县人员化解情况").head(PersonLevelChangeExcelVo.class).build();
            excelWriter.write(excelVoList, writeSheet);
            //写入
            WriteSheet twoSheet = EasyExcel.writerSheet(1, "人员类型分析").head(PersonTypeChangeExcelVo.class).build();
            excelWriter.write(personTypeChangeExcelVoList, twoSheet);
            // 关闭 ExcelWriter
            excelWriter.finish();
        } catch (UnsupportedEncodingException e) {
            // 处理编码异常
            System.err.println("编码错误: " + e.getMessage());
        }
    }
}
