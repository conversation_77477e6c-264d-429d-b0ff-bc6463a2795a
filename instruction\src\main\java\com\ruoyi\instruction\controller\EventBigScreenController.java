package com.ruoyi.instruction.controller;

import com.ruoyi.common.constant.Constants;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.domain.entity.SysDept;
import com.ruoyi.common.core.domain.entity.SysRole;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.instruction.domain.BdzyList;
import com.ruoyi.instruction.domain.InstructionEvent;
import com.ruoyi.instruction.domain.InstructionEventInfos;
import com.ruoyi.instruction.domain.MajorEventCommand;
import com.ruoyi.instruction.domain.rspVo.BigScreenEventInfo;
import com.ruoyi.instruction.domain.rspVo.BigScreenEventStatistics;
import com.ruoyi.instruction.domain.rspVo.BigScreenListEventRspVo;
import com.ruoyi.instruction.mapper.MajorEventCommandMapper;
import com.ruoyi.instruction.service.IBdzyListService;
import com.ruoyi.instruction.service.IInstructionEventService;
import com.ruoyi.instruction.service.IInstructionInfoService;
import com.ruoyi.instruction.service.impl.InstructionInfoServiceImpl;
import com.ruoyi.system.service.ISysDeptService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 重大事件驾驶舱接口
 * <AUTHOR>
 * @version 1.0
 * @date 2023/4/20 13:52
 */
@RestController
@RequestMapping("/bigScreen/event")
public class EventBigScreenController extends BaseController {

    @Autowired
    private IInstructionEventService eventService;


    @Autowired
    private MajorEventCommandMapper commandMapper;

    @Autowired
    private ISysDeptService sysDeptService;

    @Autowired
    private InstructionInfoServiceImpl infoService;
    @Autowired
    private IBdzyListService bdzyListService;
    /**
     * 获取已发布的重大事件/事件列表
     *
     * @param date
     * @return
     */
    @GetMapping("/getList")
    public AjaxResult getBigScreenEventList(@RequestParam("date") Integer date) {
        String dept = getDept();
        List<BigScreenListEventRspVo> eventList = eventService.getBigScreenEventList(date,dept);
        return AjaxResult.success(eventList);
    }

    /**
     * 获取事件统计信息
     * @param date
     * @return
     */
    @GetMapping("/getStatistics")
    public AjaxResult getStatistics(@RequestParam("date") Integer date){
        String dept = getDept();
        BigScreenEventStatistics statistics = eventService.getStatistics(date,dept);
        return AjaxResult.success(statistics);
    }

    /**
     * 获取事件统计-县市区事件数
     * @return
     */
    @GetMapping("/getCountyEventCount")
    public AjaxResult getCountyEventCount(@RequestParam("date") Integer date){
        List<Map<String,Integer>> maps = eventService.getCountyEventCount(date);
        String dept = getDept();
        if (!StringUtils.isEmpty(dept)&&dept.length()>3){
            dept=dept.substring(0,3);
        }
        for (Map m:maps){
            Object county = m.get("county");
            if (!county.toString().equals(dept)){
                m.put("isBold",false);
            }else {
                m.put("isBold",true);
            }
        }
        return AjaxResult.success(maps);
    }

    /**
     * 根据事件id获取事件详情
     * @param id
     * @return
     */
    @GetMapping("/getInfoById/{id}")
    public AjaxResult getInfoById(@PathVariable("id")Long id){
        BigScreenEventInfo eventInfo = eventService.getInfoById(id);
        return AjaxResult.success(eventInfo);
    }

    /**
     * 获取跟踪指挥列表数据
     * @param id
     * @return
     */
    @GetMapping("/getCommandById/{id}")
    public AjaxResult getCommandById(@PathVariable("id")Long id){
        //查询重大事件跟进指挥
        MajorEventCommand majorEventCommand = new MajorEventCommand();
        majorEventCommand.setMajorEventId(id);
        List<MajorEventCommand> majorEventCommands = commandMapper.selectMajorEventCommandList(majorEventCommand);
        return AjaxResult.success(majorEventCommands);
    }

    /**
     * 查询重大事件板块左下角的重大事件清单
     * <AUTHOR>
     * @return
     */
//    @GetMapping("/getEventListByYear")
//    public AjaxResult getImportantEventInfos(@RequestParam(value = "year") String year,@RequestParam(value = "distinct") String distinct){
//        List<InstructionEventInfos> list =eventService.getEventInfos(year,distinct);
//        return AjaxResult.success(list);
//    }
    @GetMapping("/getEventListByYear")
    public TableDataInfo getImportantEventInfos(@RequestParam(value = "year") String year, @RequestParam(value = "distinct") String distinct){
        startPage();
        List<InstructionEventInfos> list =eventService.getEventInfos(year,distinct);
        return getDataTable(list);
    }

    public  String getDept(){
        Long deptId = SecurityUtils.getDeptId();
        List<SysRole> roles = SecurityUtils.getLoginUser().getUser().getRoles();
        List<String> roleList = roles.stream().map(SysRole::getRoleKey).collect(Collectors.toList());
        if (roleList.contains("cityInstruction")) {
            deptId = Constants.JINHUA_CITY_DEPT_ID;
        } else if (roleList.contains("countyInstruction")) {
            SysDept dept = SecurityUtils.getLoginUser().getUser().getDept();
            dept = infoService.getCountyDeptInfo(dept);
            deptId = dept.getDeptId();
        }
        SysDept sysDept = sysDeptService.selectDeptForInstructionById(deptId);
        if (sysDept!=null&&(sysDept.getDeptName().contains("政法委")||sysDept.getDeptName().equals("开发区政法办"))){
            if (sysDept.getDeptName().contains("金华市委政法委")){
                return  null;
            }else {
                return  sysDept.getDeptName();
            }
        }else {
            return  "未知";
        }
    }

    /**
     * 获取八大战役列表
     * @return
     */
    @GetMapping("/bdzyList")
    public AjaxResult bdzyList(){
        BdzyList bdzyList=new BdzyList();
        List<BdzyList> bdzyLists = bdzyListService.selectBdzyListList(bdzyList);
        return  AjaxResult.success(bdzyLists);
    }

    /**
     * 获取八大战役详情
     * @param id
     * @return
     */
    @GetMapping("/bdzyDetails")
    public AjaxResult bdzyDetails(Long id){
        BdzyList bdzyListVo=bdzyListService.selectbdzyDetails(id);
        return  AjaxResult.success(bdzyListVo);
    }
}
