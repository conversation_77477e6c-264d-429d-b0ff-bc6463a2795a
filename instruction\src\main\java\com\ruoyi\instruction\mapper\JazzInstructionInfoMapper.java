package com.ruoyi.instruction.mapper;

import com.ruoyi.instruction.domain.JazzInstructionInfo;
import com.ruoyi.instruction.domain.rspVo.InstructionInfomiddleResVo;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * 指令基本信息Mapper接口
 *
 * <AUTHOR>
 * @date 2022-12-15
 */
@Repository
public interface JazzInstructionInfoMapper {
    /**
     * 查询指令基本信息
     *
     * @param id 指令基本信息主键
     * @return 指令基本信息
     */
    public JazzInstructionInfo selectInstructionInfoById(Long id);

    /**
     * 查询指令基本信息列表
     *
     * @param instructionInfo 指令基本信息
     * @return 指令基本信息集合
     */
    public List<JazzInstructionInfo> selectInstructionInfoList(JazzInstructionInfo instructionInfo);

    /**
     * 新增指令基本信息
     *
     * @param instructionInfo 指令基本信息
     * @return 结果
     */
    public int insertInstructionInfo(JazzInstructionInfo instructionInfo);

    /**
     * 修改指令基本信息
     *
     * @param instructionInfo 指令基本信息
     * @return 结果
     */
    public int updateInstructionInfo(JazzInstructionInfo instructionInfo);

    /**
     * 删除指令基本信息
     *
     * @param id 指令基本信息主键
     * @return 结果
     */
    public int deleteInstructionInfoById(Long id);

    /**
     * 批量删除指令基本信息
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteInstructionInfoByIds(Long[] ids);


    /**
     * 查询总数
     *
     * @param instructionInfo
     * @return
     */
    Long selectCount(JazzInstructionInfo instructionInfo);

    List<InstructionInfomiddleResVo> testInstructionListNew(JazzInstructionInfo instructionInfo);

    /**
     * 查询出未交办的指令ids
     *
     * @return
     */
    Set<Long> AssignNotHandleIds();

    /**
     * 查询出未接收的指令ids
     *
     * @return
     */
    Set<Long> ReceiveNotHandleIds(@Param("deptName") String depeName);

//    /**
//     * 查询出需要处置的指令ids
//     *
//     * @param deptName
//     * @return
//     */
//    Set<Long> DisposeNotHandleIds(@Param("deptName") String deptName);

    /**
     * 查询出需要反馈的指令ids
     *
     * @param deptName
     * @return
     */
    Set<Long> FeedBackNotHandleIds(@Param("deptName") String deptName);

    /**
     * 市本级下发接收单位排名
     *
     * @return
     */
    List<Map<String, Integer>> getIssueRank();

    /**
     * 获取县市区下发指令
     * @return
     */
    List<Map<String, Integer>> getCountyIssueRank();

    /**
     * 获取指令到访类型
     * @return
     */
    List<Map<String, Integer>> getTypeCount();

    /**
     * 获取县市区平均处置时长排名
     * @param type
     * @return
     */
    List<Map<String, Integer>> getCountyDealHour(@Param("type") String type);

    /**
     * 查询指令最后一条反馈时间
     * @param instructionId
     * @return
     */
    Date getLastFeedBackTimeById(@Param("instructionId") Long instructionId);
}
