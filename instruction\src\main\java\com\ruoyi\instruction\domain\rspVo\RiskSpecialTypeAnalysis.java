package com.ruoyi.instruction.domain.rspVo;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;
import lombok.Getter;

import java.lang.reflect.Field;
import java.util.Arrays;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;

/**
 * 专项风险判断
 */
@Data
public class RiskSpecialTypeAnalysis {

    @ExcelProperty({"县市区域"})
    private String countyName;

    @Getter
    @ExcelIgnore
    private static HashMap<String, List<String>> fieldMap;

    @Getter
    @ExcelIgnore
    private static HashSet<String> problemSet;

    static {
        fieldMap = new HashMap<>();
        fieldMap.put("合计", Arrays.asList("totalCount", "totalHouseholds", "totalSolvedCount"));
        fieldMap.put("逾期交付", Arrays.asList("problem1Count", "problem1Households", "problem1SolvedCount"));
        fieldMap.put("规划问题", Arrays.asList("problem2Count", "problem2Households", "problem2SolvedCount"));
        fieldMap.put("房屋质量", Arrays.asList("problem3Count", "problem3Households", "problem3SolvedCount"));
        fieldMap.put("虚假宣传", Arrays.asList("problem4Count", "problem4Households", "problem4SolvedCount"));
        fieldMap.put("学区问题", Arrays.asList("problem5Count", "problem5Households", "problem5SolvedCount"));
        fieldMap.put("房价问题", Arrays.asList("problem6Count", "problem6Households", "problem6SolvedCount"));
        fieldMap.put("欠薪问题", Arrays.asList("problem7Count", "problem7Households", "problem7SolvedCount"));
        fieldMap.put("车位问题", Arrays.asList("problem8Count", "problem8Households", "problem8SolvedCount"));
        fieldMap.put("托管经营", Arrays.asList("problem9Count", "problem9Households", "problem9SolvedCount"));
        fieldMap.put("资金债务问题", Arrays.asList("problem10Count", "problem10Households", "problem10SolvedCount"));
        fieldMap.put("产权无法办理问题", Arrays.asList("problem11Count", "problem11Households", "problem11SolvedCount"));
        fieldMap.put("其他", Arrays.asList("problem12Count", "problem12Households", "problem12SolvedCount"));

        problemSet = new HashSet<>(Arrays.asList(
                "逾期交付", "规划问题", "房屋质量", "虚假宣传",
                "学区问题", "房价问题", "欠薪问题", "车位问题",
                "托管经营", "资金债务问题", "产权无法办理问题"
        ));
    }


    @ExcelProperty(value = {"合计", "排查数"})
    private int totalCount;
    @ExcelProperty(value = {"合计", "涉及户数"})
    private long totalHouseholds;
    @ExcelProperty(value = {"合计", "化解数"})
    private int totalSolvedCount;


    @ExcelProperty({"逾期交付", "排查数"})
    private int problem1Count;
    @ExcelProperty({"逾期交付", "涉及户数"})
    private long problem1Households;
    @ExcelProperty({"逾期交付", "化解数"})
    private int problem1SolvedCount;


    @ExcelProperty({"规划问题", "排查数"})
    private int problem2Count;
    @ExcelProperty({"规划问题", "涉及户数"})
    private long problem2Households;
    @ExcelProperty({"规划问题", "化解数"})
    private int problem2SolvedCount;


    @ExcelProperty({"房屋质量", "排查数"})
    private int problem3Count;
    @ExcelProperty({"房屋质量", "涉及户数"})
    private long problem3Households;
    @ExcelProperty({"房屋质量", "化解数"})
    private int problem3SolvedCount;

    @ExcelProperty({"虚假宣传", "排查数"})
    private int problem4Count;
    @ExcelProperty({"虚假宣传", "涉及户数"})
    private long problem4Households;
    @ExcelProperty({"虚假宣传", "化解数"})
    private int problem4SolvedCount;

    @ExcelProperty({"学区问题", "排查数"})
    private int problem5Count;
    @ExcelProperty({"学区问题", "涉及户数"})
    private long problem5Households;
    @ExcelProperty({"学区问题", "化解数"})
    private int problem5SolvedCount;


    @ExcelProperty({"房价问题", "排查数"})
    private int problem6Count;
    @ExcelProperty({"房价问题", "涉及户数"})
    private long problem6Households;
    @ExcelProperty({"房价问题", "化解数"})
    private int problem6SolvedCount;


    @ExcelProperty({"欠薪问题", "排查数"})
    private int problem7Count;
    @ExcelProperty({"欠薪问题", "涉及户数"})
    private long problem7Households;
    @ExcelProperty({"欠薪问题", "化解数"})
    private int problem7SolvedCount;

    @ExcelProperty({"车位问题", "排查数"})
    private int problem8Count;
    @ExcelProperty({"车位问题", "涉及户数"})
    private long problem8Households;
    @ExcelProperty({"车位问题", "化解数"})
    private int problem8SolvedCount;

    @ExcelProperty({"托管经营", "排查数"})
    private int problem9Count;
    @ExcelProperty({"托管经营", "涉及户数"})
    private long problem9Households;
    @ExcelProperty({"托管经营", "化解数"})
    private int problem9SolvedCount;


    @ExcelProperty({"资金债务问题", "排查数"})
    private int problem10Count;
    @ExcelProperty({"资金债务问题", "涉及户数"})
    private long problem10Households;
    @ExcelProperty({"资金债务问题", "化解数"})
    private int problem10SolvedCount;


    @ExcelProperty({"产权无法办理问题", "排查数"})
    private int problem11Count;
    @ExcelProperty({"产权无法办理问题", "涉及户数"})
    private long problem11Households;
    @ExcelProperty({"产权无法办理问题", "化解数"})
    private int problem11SolvedCount;

    @ExcelProperty({"其他", "排查数"})
    private int problem12Count;
    @ExcelProperty({"其他", "涉及户数"})
    private long problem12Households;
    @ExcelProperty({"其他", "化解数"})
    private int problem12SolvedCount;

    public RiskSpecialTypeAnalysis(String countyName) {
        this.countyName = countyName;
    }

    /**
     * 根据属性名获取属性值
     *
     * @param fieldName
     * @return
     */
    public Object getFieldByName(String fieldName) {
        try {
            Field field = this.getClass().getDeclaredField(fieldName);
            field.setAccessible(true);
            return field.get(this);
        } catch (NoSuchFieldException | IllegalAccessException e) {
            e.printStackTrace();
            return null;
        }
    }

    /**
     * 根据属性名设置属性值
     *
     * @param fieldName
     * @param newValue
     */

    public void setFieldByName(String fieldName, Object newValue) {
        try {
            Field field = this.getClass().getDeclaredField(fieldName);
            field.setAccessible(true);
            field.set(this, newValue);
        } catch (NoSuchFieldException | IllegalAccessException e) {
            e.printStackTrace();
        }
    }

    public RiskSpecialTypeAnalysis() {
    }
}
