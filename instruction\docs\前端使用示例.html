<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>市级指令办理情况</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background-color: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
            color: #333;
        }
        .time-filter {
            margin-bottom: 20px;
            padding: 15px;
            background-color: #f8f9fa;
            border-radius: 5px;
        }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        .stat-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            border-radius: 8px;
            text-align: center;
            cursor: pointer;
            transition: transform 0.2s;
        }
        .stat-card:hover {
            transform: translateY(-2px);
        }
        .stat-number {
            font-size: 2em;
            font-weight: bold;
            margin-bottom: 5px;
        }
        .stat-label {
            font-size: 0.9em;
            opacity: 0.9;
        }
        .feedback-section {
            margin-top: 30px;
        }
        .feedback-grid {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 15px;
        }
        .feedback-card {
            background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
            color: white;
            padding: 15px;
            border-radius: 8px;
            text-align: center;
            cursor: pointer;
        }
        .export-btn {
            background-color: #28a745;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin-top: 20px;
        }
        .export-btn:hover {
            background-color: #218838;
        }
        input[type="date"] {
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            margin: 0 10px;
        }
        button {
            padding: 8px 16px;
            background-color: #007bff;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
        }
        button:hover {
            background-color: #0056b3;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>市级指令办理情况</h1>
            <p id="currentArea">当前区域：金华市（默认查询全市数据）</p>
            <p style="color: #666; font-size: 14px;">注意：所有接口默认查询金华市全部数据，不受当前用户部门限制</p>
        </div>

        <div class="time-filter">
            <label>时间筛选：</label>
            <input type="date" id="startTime" />
            <span>至</span>
            <input type="date" id="endTime" />
            <button onclick="loadData()">查询</button>
            <button onclick="clearFilter()">清空</button>
            <button onclick="loadDetailedLists()">获取详细列表</button>
        </div>

        <div class="stats-grid">
            <div class="stat-card" onclick="showTimeoutReceiveList()">
                <div class="stat-number" id="timeoutReceive">-</div>
                <div class="stat-label">超时接收</div>
            </div>
            <div class="stat-card" onclick="showTimeoutDisposeList()">
                <div class="stat-number" id="timeoutDispose">-</div>
                <div class="stat-label">超时处置</div>
            </div>
            <div class="stat-card" onclick="showUnprocessedList()">
                <div class="stat-number" id="unprocessedUnit">-</div>
                <div class="stat-label">应处置未处置单位数</div>
            </div>
            <div class="stat-card" onclick="showUnprocessedList()">
                <div class="stat-number" id="unprocessedDept">-</div>
                <div class="stat-label">应处置未处置部门数</div>
            </div>
        </div>

        <div class="feedback-section">
            <h3>待反馈情况</h3>
            <div class="feedback-grid">
                <div class="feedback-card" onclick="showPendingFeedbackList('7')">
                    <div class="stat-number" id="feedback7Days">-</div>
                    <div class="stat-label">7天（特急）</div>
                </div>
                <div class="feedback-card" onclick="showPendingFeedbackList('15')">
                    <div class="stat-number" id="feedback15Days">-</div>
                    <div class="stat-label">15天（紧急）</div>
                </div>
                <div class="feedback-card" onclick="showPendingFeedbackList('30')">
                    <div class="stat-number" id="feedback30Days">-</div>
                    <div class="stat-label">30天（一般）</div>
                </div>
            </div>
        </div>

        <button class="export-btn" onclick="exportData()">导出数据</button>
    </div>

    <script>
        // 页面加载时获取数据
        window.onload = function() {
            loadData();
        };

        // 加载数据
        function loadData() {
            const startTime = document.getElementById('startTime').value;
            const endTime = document.getElementById('endTime').value;

            const params = new URLSearchParams();
            if (startTime) params.append('startTime', startTime);
            if (endTime) params.append('endTime', endTime);

            // 获取统计数据
            fetch(`/instruction/cityHandling/data?${params}`)
                .then(response => response.json())
                .then(data => {
                    if (data.code === 200) {
                        updateUI(data.data);
                    } else {
                        alert('获取数据失败：' + data.msg);
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    alert('网络错误，请稍后重试');
                });
        }

        // 更新UI
        function updateUI(data) {
            document.getElementById('currentArea').textContent = '当前区域：' + (data.currentArea || '全部');
            document.getElementById('timeoutReceive').textContent = data.timeoutReceiveCount || 0;
            document.getElementById('timeoutDispose').textContent = data.timeoutDisposeCount || 0;
            document.getElementById('unprocessedUnit').textContent = data.unprocessedUnitCount || 0;
            document.getElementById('unprocessedDept').textContent = data.unprocessedDeptCount || 0;
            document.getElementById('feedback7Days').textContent = data.pendingFeedback7Days || 0;
            document.getElementById('feedback15Days').textContent = data.pendingFeedback15Days || 0;
            document.getElementById('feedback30Days').textContent = data.pendingFeedback30Days || 0;
        }

        // 清空筛选条件
        function clearFilter() {
            document.getElementById('startTime').value = '';
            document.getElementById('endTime').value = '';
            loadData();
        }

        // 获取并显示所有详细列表数据
        function loadDetailedLists() {
            const startTime = document.getElementById('startTime').value;
            const endTime = document.getElementById('endTime').value;
            const params = new URLSearchParams();
            if (startTime) params.append('startTime', startTime);
            if (endTime) params.append('endTime', endTime);

            fetch(`/instruction/cityHandling/list?${params}`)
                .then(response => response.json())
                .then(result => {
                    if (result.code === 200) {
                        console.log('详细列表数据：', result.data);
                        // 这里可以根据需要处理各个列表数据
                        // result.data.timeoutReceiveList - 超时接收列表
                        // result.data.timeoutDisposeList - 超时处置列表
                        // result.data.unprocessedList - 应处置未处置列表
                        // result.data.pendingFeedback7DaysList - 特急待反馈列表
                        // result.data.pendingFeedback15DaysList - 紧急待反馈列表
                        // result.data.pendingFeedback30DaysList - 一般待反馈列表
                        alert('详细列表数据已加载，请查看控制台输出');
                    } else {
                        alert('获取详细列表失败：' + result.msg);
                    }
                })
                .catch(error => {
                    console.error('获取详细列表出错：', error);
                    alert('获取详细列表出错，请查看控制台');
                });
        }

        // 显示超时接收列表（点击统计卡片时调用）
        function showTimeoutReceiveList() {
            loadDetailedLists();
        }

        // 显示超时处置列表
        function showTimeoutDisposeList() {
            loadDetailedLists();
        }

        // 显示应处置未处置列表
        function showUnprocessedList() {
            loadDetailedLists();
        }

        // 显示待反馈列表
        function showPendingFeedbackList(emergencyType) {
            loadDetailedLists();
        }

        // 导出数据
        function exportData() {
            const startTime = document.getElementById('startTime').value;
            const endTime = document.getElementById('endTime').value;
            
            const form = document.createElement('form');
            form.method = 'POST';
            form.action = '/instruction/cityHandling/export';
            
            if (startTime) {
                const startInput = document.createElement('input');
                startInput.type = 'hidden';
                startInput.name = 'startTime';
                startInput.value = startTime;
                form.appendChild(startInput);
            }
            
            if (endTime) {
                const endInput = document.createElement('input');
                endInput.type = 'hidden';
                endInput.name = 'endTime';
                endInput.value = endTime;
                form.appendChild(endInput);
            }
            
            document.body.appendChild(form);
            form.submit();
            document.body.removeChild(form);
        }
    </script>
</body>
</html>
