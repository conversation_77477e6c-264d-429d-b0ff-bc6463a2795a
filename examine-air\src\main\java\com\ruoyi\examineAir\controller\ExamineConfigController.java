package com.ruoyi.examineAir.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.examineAir.domain.ExamineConfig;
import com.ruoyi.examineAir.service.IExamineConfigService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 考核晾晒动态配置信息Controller
 * 
 * <AUTHOR>
 * @date 2023-04-19
 */
@RestController
@RequestMapping("/examineAir/examineConfig")
public class ExamineConfigController extends BaseController
{
    @Autowired
    private IExamineConfigService examineConfigService;

    /**
     * 查询考核晾晒动态配置信息列表
     */
    @PreAuthorize("@ss.hasPermi('examineAir:examineConfig:list')")
    @GetMapping("/list")
    public TableDataInfo list(ExamineConfig examineConfig)
    {
        startPage();
        List<ExamineConfig> list = examineConfigService.selectExamineConfigList(examineConfig);
        return getDataTable(list);
    }

    /**
     * 导出考核晾晒动态配置信息列表
     */
    @PreAuthorize("@ss.hasPermi('examineAir:examineConfig:export')")
    @Log(title = "考核晾晒动态配置信息", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, ExamineConfig examineConfig)
    {
        List<ExamineConfig> list = examineConfigService.selectExamineConfigList(examineConfig);
        ExcelUtil<ExamineConfig> util = new ExcelUtil<ExamineConfig>(ExamineConfig.class);
        util.exportExcel(response, list, "考核晾晒动态配置信息数据");
    }

    /**
     * 获取考核晾晒动态配置信息详细信息
     */
    @PreAuthorize("@ss.hasPermi('examineAir:examineConfig:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(examineConfigService.selectExamineConfigById(id));
    }

    /**
     * 新增考核晾晒动态配置信息
     */
    @PreAuthorize("@ss.hasPermi('examineAir:examineConfig:add')")
    @Log(title = "考核晾晒动态配置信息", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody ExamineConfig examineConfig)
    {
        return toAjax(examineConfigService.insertExamineConfig(examineConfig));
    }

    /**
     * 修改考核晾晒动态配置信息
     */
    @PreAuthorize("@ss.hasPermi('examineAir:examineConfig:edit')")
    @Log(title = "考核晾晒动态配置信息", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody ExamineConfig examineConfig)
    {
        return toAjax(examineConfigService.updateExamineConfig(examineConfig));
    }

    /**
     * 删除考核晾晒动态配置信息
     */
    @PreAuthorize("@ss.hasPermi('examineAir:examineConfig:remove')")
    @Log(title = "考核晾晒动态配置信息", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(examineConfigService.deleteExamineConfigByIds(ids));
    }
}
