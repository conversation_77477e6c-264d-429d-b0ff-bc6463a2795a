package com.ruoyi.instruction.domain;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.format.NumberFormat;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.ContentRowHeight;
import com.alibaba.excel.annotation.write.style.HeadRowHeight;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;

/**
 * 反馈率分析
 * <AUTHOR>
 */
@Getter
@Setter
@EqualsAndHashCode
@ColumnWidth(10)
@HeadRowHeight(20)
@ContentRowHeight(20)
public class FeedbackAnalysis {
    @ExcelProperty("县市区")
    private String county;
    @ColumnWidth(8)
    @ExcelProperty({"点位", "总数"})
    private Integer totalPoints;
    @ExcelProperty({"点位", "已反馈"})
    private Integer feedbackPoints;
    @NumberFormat("#.##%")
    @ExcelProperty({"点位", "整改率"})
    private Float pointFeedbackRate;
    @ColumnWidth(8)
    @ExcelProperty({"问题", "总数"})
    private Integer totalProblems;
    @ExcelProperty({"问题", "已反馈"})
    private Integer feedbackProblems;
    @NumberFormat("#.##%")
    @ExcelProperty({"问题", "整改率"})
    private Float problemFeedbackRate;
}
