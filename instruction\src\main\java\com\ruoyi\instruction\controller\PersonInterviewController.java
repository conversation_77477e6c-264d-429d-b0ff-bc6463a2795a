package com.ruoyi.instruction.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.instruction.domain.PersonInterview;
import com.ruoyi.instruction.service.IPersonInterviewService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 人员走访记录Controller
 *
 * <AUTHOR>
 * @date 2024-01-05
 */
@RestController
@RequestMapping("/system/interview")
public class PersonInterviewController extends BaseController
{
    @Autowired
    private IPersonInterviewService personInterviewService;

    /**
     * 根据人员id获取走访记录列表
     */
    // @PreAuthorize("@ss.hasPermi('system:interview:list')")
    @GetMapping("/list")
    public AjaxResult list(PersonInterview personInterview)
    {
        List<PersonInterview> list = personInterviewService.selectPersonInterviewList(personInterview);
        return AjaxResult.success(list);
    }

    /**
     * 导出人员走访记录列表
     */
    // @PreAuthorize("@ss.hasPermi('system:interview:export')")
    @Log(title = "人员走访记录", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, PersonInterview personInterview)
    {
        List<PersonInterview> list = personInterviewService.selectPersonInterviewList(personInterview);
        ExcelUtil<PersonInterview> util = new ExcelUtil<PersonInterview>(PersonInterview.class);
        util.exportExcel(response, list, "人员走访记录数据");
    }

    /**
     * 获取人员走访记录详细信息
     */
    // @PreAuthorize("@ss.hasPermi('system:interview:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(personInterviewService.selectPersonInterviewById(id));
    }

    /**
     * 新增人员走访记录
     */
    // @PreAuthorize("@ss.hasPermi('system:interview:add')")
    @Log(title = "人员走访记录", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody PersonInterview personInterview)
    {
        return toAjax(personInterviewService.insertPersonInterview(personInterview));
    }

    /**
     * 修改人员走访记录
     */
    // @PreAuthorize("@ss.hasPermi('system:interview:edit')")
    @Log(title = "人员走访记录", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody PersonInterview personInterview)
    {
        return toAjax(personInterviewService.updatePersonInterview(personInterview));
    }

    /**
     * 删除人员走访记录
     */
    // @PreAuthorize("@ss.hasPermi('system:interview:remove')")
    @Log(title = "人员走访记录", businessType = BusinessType.DELETE)
	@DeleteMapping("/{id}")
    public AjaxResult remove(@PathVariable Long id)
    {
        return toAjax(personInterviewService.deletePersonInterviewById(id));
    }
}
