package com.ruoyi.instruction.domain;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

import java.util.Date;
import java.util.List;

/**
 * 指令关联人员信息对象 t_instruction_person
 *
 * <AUTHOR>
 * @date 2022-12-15
 */
@Data
public class InstrucationPerson extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /**
     * 指令人员id
     */
    private Long id;

    /**
     * 人员姓名
     */
    @Excel(name = "人员姓名")
    private String personName;

    /**
     * 证件号码
     */
    @Excel(name = "证件号码")
    private String idCard;

    /**
     * 户籍所在地
     */
    @Excel(name = "户籍所在地")
    private String housePlace;

    /**
     * 当前居住地
     */
    @Excel(name = "当前居住地")
    private String currentPlace;

    /**
     * 责任所在地
     */
    @Excel(name = "责任所在地", combo = {"婺城区", "金东区", "兰溪市", "东阳市", "义乌市", "永康市", "浦江县", "武义县", "磐安县", "开发区"})
    private String dutyPlace;

    /**
     * 电话号码
     */
    @Excel(name = "电话号码")
    private String personPhone;

    /**
     * 人员状态 1：正常 9：删除
     */
    private String status;

    /**
     * 群体id（用作关联）
     */
    private Long groupId;

    private String ids;

    /**
     * 是否是牵头人 0：非牵头人 1：牵头人
     */
    private Integer isLead;

    /**
     * 管控级别 低、中、高 从字典表中取出
     */
    @Excel(name = "管控级别", type = Excel.Type.EXPORT)
    private String controlLevel;

    private String controlLevelName;

    /**
     * 关联事件数
     */
    @Excel(name = "活跃次数", type = Excel.Type.EXPORT)
    private Integer eventNum;

    /**
     * 关联群体数
     */
    private Integer groupNum;

    /**
     * 关联事件详细信息
     */
    private List<InstructionEvent> eventList;

    /** 管控策略1：五包一 */
    private String controlStrategy;

    /** 用户性别0：男 1：女 2：未知 */
    // @Excel(name = "性别0：男 1：女 2：未知")
    private String sex;


    /**
     * 重点人员管控列表
     */
    private List<InstructionPersonControl> personControlList;

    /**
     * 关联群体名称
     */
    private String groupName;

    /**
     * 人员最后一次类型名称
     */
    @Excel(name = "人员类型", type = Excel.Type.EXPORT)
    private String typeName;

    /**
     * 排序类型 1:根据关联事件降序 2:根据关联事件升序 3:挑头次数降序 4:挑头次数升序 5:进京次数降序 6:进京次数升序 7:赴省次数降序 8:赴省次数升序
     */
    private Integer sortType;

    /** 关联群体id */
    private String groupIds;

    /**
     * 人员关联群体集合
     */
    private List<InstructionGroup> groupList;
    /**
     * 查询人员类型
     */
    private  Integer peopleType;
    /**
     * 人员管理状态，1在控，2失联
     */
    private  Integer manageStatus;

    /**
     * 管控人员列表
     */
    private List<GkPerson> gkPersonList;

    /** 原身份证 */
    private String originalIdCard;

    /**
     * 审核转态
     */
    private String shzt;
    /**
     *挑头次数
     */
    @Excel(name = "挑头次数", type = Excel.Type.EXPORT)
    private Integer leaderNum;

    /**
     * 响应次数
     */
    @Excel(name = "响应次数", type = Excel.Type.EXPORT)
    private Integer respondNum;

    /** 人员类型 1:异动人员  2：关注人员 */
    private Long personType;

    /** 人员来源 1:系统变更  2：新增 */
    @Excel(name = "来源", type = Excel.Type.EXPORT,readConverterExp = "1=系统变更,2=新增")
    private Long personSource;


    /** 一级标题 */
    private String firstTitle;

    /** 二级标题 */
    private String secondTitle;

    /**
     * 乡镇街道
     */
    private String town;

    /**
     * 五包一责任人
     */
    private String dutyer;

    /**
     * 五包一联系方式
     */
    private String telephone;

    /** 新管控等级 1：红 2：橙  3：黄  4：撤档 */
    private Integer pLevel;

    /** 人员类型  */
    private String pType;

    /** 人员所属乡镇 */
    private String pTown;

    /** 主要诉求 */
    private String mainApply;

    /** 纳管理由 */
    private String adjustReason;

    /** 申请单位意见 */
    private String sqdwReason;

    /** 审核单位意见 */
    private String auditReason;

    /** 赴省次数 */
    private Long provinceNum;

    /** 进京次数 */
    private Long capitalNum;

    /** 是否为mzx人员 1:是 */
    private Long isMzx;

    /** 走访时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm")
    private Date interviewTime;

    /** 走访次数 */
    private Long interviewCount;

    /** 打击次数 */
    @Excel(name = "打击次数")
    private Long strikeCount;

    /** 赴省次数-新 */
    private Long provinceNumNew;

    /** 进京次数-新 */
    private Long capitalNumNew;

    /** 是否为新关注人员 1：是 0：否 */
    private Integer isFollow;

    /** 具体描述 */
    private String specificDescription;

    /**
     * 是否包含包含易肇事肇祸人员
     */
    private Integer isTrouble;


    private String beginTime;

    private String endTime;

    public Integer getpLevel() {
        return pLevel;
    }

    public void setpLevel(final Integer pLevel) {
        this.pLevel = pLevel;
    }

    public String getpType() {
        return pType;
    }

    public void setpType(final String pType) {
        this.pType = pType;
    }

    public String getpTown() {
        return pTown;
    }

    public void setpTown(final String pTown) {
        this.pTown = pTown;
    }

    public InstrucationPerson(){}

    public InstrucationPerson(String dutyPlace,Integer eventNum){
        this.dutyPlace=dutyPlace;
        this.eventNum=eventNum;
    }
}
