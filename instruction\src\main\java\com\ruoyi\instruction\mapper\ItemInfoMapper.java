package com.ruoyi.instruction.mapper;

import java.util.List;
import com.ruoyi.instruction.domain.ItemInfo;
import com.ruoyi.instruction.domain.rspVo.BigScreenItemInfoRspVo;
import com.ruoyi.instruction.domain.rspVo.BigScreenItemRspVo;
import org.apache.ibatis.annotations.Param;

/**
 * 事项基本信息Mapper接口
 *
 * <AUTHOR>
 * @date 2023-04-21
 */
public interface ItemInfoMapper
{
    /**
     * 查询事项基本信息
     *
     * @param id 事项基本信息主键
     * @return 事项基本信息
     */
    public ItemInfo selectItemInfoById(Long id);

    /**
     * 查询事项基本信息列表
     *
     * @param itemInfo 事项基本信息
     * @return 事项基本信息集合
     */
    public List<ItemInfo> selectItemInfoList(ItemInfo itemInfo);

    /**
     * 新增事项基本信息
     *
     * @param itemInfo 事项基本信息
     * @return 结果
     */
    public int insertItemInfo(ItemInfo itemInfo);

    /**
     * 修改事项基本信息
     *
     * @param itemInfo 事项基本信息
     * @return 结果
     */
    public int updateItemInfo(ItemInfo itemInfo);

    /**
     * 删除事项基本信息
     *
     * @param id 事项基本信息主键
     * @return 结果
     */
    public int deleteItemInfoById(Long id);

    /**
     * 批量删除事项基本信息
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteItemInfoByIds(Long[] ids);

    /**
     * 获取挂牌整治数据
     * @return
     */
    List<BigScreenItemRspVo> getDealList(ItemInfo itemInfo);


    BigScreenItemInfoRspVo getCountyInfo(ItemInfo itemInfo);

    /**
     * 获取任务总数、事项总数、整治完成率
     * @return
     */
    BigScreenItemRspVo getItemData(ItemInfo itemInfo);

}
