package com.ruoyi.instruction.mapper;

import java.util.List;
import com.ruoyi.instruction.domain.InstructionRemind;

/**
 * 提醒单-信息Mapper接口
 * 
 * <AUTHOR>
 * @date 2023-05-18
 */
public interface InstructionRemindMapper 
{
    /**
     * 查询提醒单-信息
     * 
     * @param id 提醒单-信息主键
     * @return 提醒单-信息
     */
    public InstructionRemind selectInstructionRemindById(Long id);

    /**
     * 查询提醒单-信息列表
     * 
     * @param instructionRemind 提醒单-信息
     * @return 提醒单-信息集合
     */
    public List<InstructionRemind> selectInstructionRemindList(InstructionRemind instructionRemind);

    /**
     * 新增提醒单-信息
     * 
     * @param instructionRemind 提醒单-信息
     * @return 结果
     */
    public int insertInstructionRemind(InstructionRemind instructionRemind);

    /**
     * 修改提醒单-信息
     * 
     * @param instructionRemind 提醒单-信息
     * @return 结果
     */
    public int updateInstructionRemind(InstructionRemind instructionRemind);

    /**
     * 删除提醒单-信息
     * 
     * @param id 提醒单-信息主键
     * @return 结果
     */
    public int deleteInstructionRemindById(Long id);

    /**
     * 批量删除提醒单-信息
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteInstructionRemindByIds(Long[] ids);
}
