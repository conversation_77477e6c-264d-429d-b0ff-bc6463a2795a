package com.ruoyi.instruction.mapper;

import java.util.List;
import com.ruoyi.instruction.domain.JczzEventType;

/**
 * 基层智治事件类型Mapper接口
 * 
 * <AUTHOR>
 * @date 2023-08-03
 */
public interface JczzEventTypeMapper 
{
    /**
     * 查询基层智治事件类型
     * 
     * @param id 基层智治事件类型主键
     * @return 基层智治事件类型
     */
    public JczzEventType selectJczzEventTypeById(Long id);

    /**
     * 查询基层智治事件类型列表
     * 
     * @param jczzEventType 基层智治事件类型
     * @return 基层智治事件类型集合
     */
    public List<JczzEventType> selectJczzEventTypeList(JczzEventType jczzEventType);

    /**
     * 新增基层智治事件类型
     * 
     * @param jczzEventType 基层智治事件类型
     * @return 结果
     */
    public int insertJczzEventType(JczzEventType jczzEventType);

    /**
     * 修改基层智治事件类型
     * 
     * @param jczzEventType 基层智治事件类型
     * @return 结果
     */
    public int updateJczzEventType(JczzEventType jczzEventType);

    /**
     * 删除基层智治事件类型
     * 
     * @param id 基层智治事件类型主键
     * @return 结果
     */
    public int deleteJczzEventTypeById(Long id);

    /**
     * 批量删除基层智治事件类型
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteJczzEventTypeByIds(Long[] ids);
}
