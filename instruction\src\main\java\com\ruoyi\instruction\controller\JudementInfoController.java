package com.ruoyi.instruction.controller;

import java.util.List;
import java.util.Map;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.instruction.domain.JudementInfo;
import com.ruoyi.instruction.service.IJudementInfoService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 分析研判Controller
 * 
 * <AUTHOR>
 * @date 2023-05-04
 */
@RestController
@RequestMapping("/instruction/judement")
public class JudementInfoController extends BaseController
{
    @Autowired
    private IJudementInfoService judementInfoService;

    /**
     * 查询分析研判列表
     */
    @PreAuthorize("@ss.hasPermi('instruction:judement:list')")
    @GetMapping("/list")
    public TableDataInfo list(JudementInfo judementInfo)
    {
        startPage();
        List<JudementInfo> list = judementInfoService.selectJudementInfoList(judementInfo);
        return getDataTable(list);
    }

    /**
     * 导出分析研判列表
     */
    @PreAuthorize("@ss.hasPermi('instruction:judement:export')")
    @Log(title = "分析研判", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, JudementInfo judementInfo)
    {
        List<JudementInfo> list = judementInfoService.selectJudementInfoList(judementInfo);
        ExcelUtil<JudementInfo> util = new ExcelUtil<JudementInfo>(JudementInfo.class);
        util.exportExcel(response, list, "分析研判数据");
    }

    /**
     * 获取分析研判详细信息
     */
    @PreAuthorize("@ss.hasPermi('instruction:judement:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(judementInfoService.selectJudementInfoById(id));
    }

    /**
     * 新增分析研判
     */
    @PreAuthorize("@ss.hasPermi('instruction:judement:add')")
    @Log(title = "分析研判", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody JudementInfo judementInfo)
    {
        return toAjax(judementInfoService.insertJudementInfo(judementInfo));
    }

    /**
     * 修改分析研判
     */
    @PreAuthorize("@ss.hasPermi('instruction:judement:edit')")
    @Log(title = "分析研判", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody JudementInfo judementInfo)
    {
        return toAjax(judementInfoService.updateJudementInfo(judementInfo));
    }

    /**
     * 删除分析研判
     */
    @PreAuthorize("@ss.hasPermi('instruction:judement:remove')")
    @Log(title = "分析研判", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(judementInfoService.deleteJudementInfoByIds(ids));
    }

    /**
     * 获取分析研判数、待研判数、已研判数
     * @return
     */
    @GetMapping("/getData")
    public AjaxResult getData(){
        Map<String,Object> map = judementInfoService.getData();
        return AjaxResult.success(map);
    }


}
