package com.ruoyi.instruction.domain;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.format.NumberFormat;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.ContentRowHeight;
import com.alibaba.excel.annotation.write.style.HeadRowHeight;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;

/**
 * 整体检查分析
 * <AUTHOR>
 */
@Getter
@Setter
@EqualsAndHashCode
@ColumnWidth(15)
@HeadRowHeight(20)
@ContentRowHeight(20)
public class InspectionReport{
    @ColumnWidth(10)
    @ExcelProperty("县市区")
    private String county;
    @ColumnWidth(12)
    @ExcelProperty({"检查单位", "检查点位"})
    private Integer inspectionPoints;
    @ExcelProperty({"检查单位", "问题点位"})
    private Integer unqualifiedPoints;
    @NumberFormat("#.##%")
    @ColumnWidth(12)
    @ExcelProperty({"检查单位", "不合格率"})
    private Float unqualifiedRate;
}
