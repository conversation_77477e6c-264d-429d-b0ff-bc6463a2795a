package com.ruoyi.instruction.domain;

import java.util.Date;
import java.util.List;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 指令接收对象 t_instruction_receive
 *
 * <AUTHOR>
 * @date 2022-12-19
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class InstructionReceive extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 接收表主键id */
    private Long id;

    /** 接收部门 */
    @Excel(name = "接收部门")
    private String receiveDept;

    /** 接收时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "接收时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date receiveTime;

    /** 是否接收 1:是 2：否 */
    @Excel(name = "是否接收 1:是 2：否")
    private Integer isReceive;

    /** 接收人 */
    @Excel(name = "接收人")
    private String receiveBy;

    /** 指令id */
    @Excel(name = "指令id")
    private Long instrucationId;

    /** 1:正常 9：删除 */
    @Excel(name = "1:正常 9：删除")
    private Integer status;

    /** 接收部门id */
    @Excel(name = "接收部门id")
    private Long receiveDeptId;


    /** 是否为mzx 1:是 2:不是 */
    private Long isMzx;

    /**
     * 转交部门实体类
     */
    private List<InstructionTransfer> transferList;


}
