package com.ruoyi.instruction.excellistener;

import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.ruoyi.instruction.domain.InstructionEvent;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/2/9 14:15
 */
public class InstructionEventListener extends AnalysisEventListener<InstructionEvent> {

    List<InstructionEvent> dataList = new ArrayList<>();

    @Override
    public void invoke(final InstructionEvent instructionEvent, final AnalysisContext analysisContext) {
        dataList.add(instructionEvent);
    }

    @Override
    public void doAfterAllAnalysed(final AnalysisContext analysisContext) {

    }

    public List<InstructionEvent> getDataList() {
        return dataList;
    }

}
