package com.ruoyi.examineAir.service.impl;

import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.examineAir.mapper.ExamineBillboardMapper;
import com.ruoyi.examineAir.domain.ExamineBillboard;
import com.ruoyi.examineAir.service.IExamineBillboardService;

/**
 * 考核晾晒-红黄榜Service业务层处理
 * 
 * <AUTHOR>
 * @date 2023-04-19
 */
@Service
public class ExamineBillboardServiceImpl implements IExamineBillboardService 
{
    @Autowired
    private ExamineBillboardMapper examineBillboardMapper;

    /**
     * 查询考核晾晒-红黄榜
     * 
     * @param id 考核晾晒-红黄榜主键
     * @return 考核晾晒-红黄榜
     */
    @Override
    public ExamineBillboard selectExamineBillboardById(Long id)
    {
        return examineBillboardMapper.selectExamineBillboardById(id);
    }

    /**
     * 查询考核晾晒-红黄榜列表
     * 
     * @param examineBillboard 考核晾晒-红黄榜
     * @return 考核晾晒-红黄榜
     */
    @Override
    public List<ExamineBillboard> selectExamineBillboardList(ExamineBillboard examineBillboard)
    {
        return examineBillboardMapper.selectExamineBillboardList(examineBillboard);
    }

    /**
     * 新增考核晾晒-红黄榜
     * 
     * @param examineBillboard 考核晾晒-红黄榜
     * @return 结果
     */
    @Override
    public int insertExamineBillboard(ExamineBillboard examineBillboard)
    {
        return examineBillboardMapper.insertExamineBillboard(examineBillboard);
    }

    /**
     * 修改考核晾晒-红黄榜
     * 
     * @param examineBillboard 考核晾晒-红黄榜
     * @return 结果
     */
    @Override
    public int updateExamineBillboard(ExamineBillboard examineBillboard)
    {
        return examineBillboardMapper.updateExamineBillboard(examineBillboard);
    }

    /**
     * 批量删除考核晾晒-红黄榜
     * 
     * @param ids 需要删除的考核晾晒-红黄榜主键
     * @return 结果
     */
    @Override
    public int deleteExamineBillboardByIds(Long[] ids)
    {
        return examineBillboardMapper.deleteExamineBillboardByIds(ids);
    }

    /**
     * 删除考核晾晒-红黄榜信息
     * 
     * @param id 考核晾晒-红黄榜主键
     * @return 结果
     */
    @Override
    public int deleteExamineBillboardById(Long id)
    {
        return examineBillboardMapper.deleteExamineBillboardById(id);
    }
}
