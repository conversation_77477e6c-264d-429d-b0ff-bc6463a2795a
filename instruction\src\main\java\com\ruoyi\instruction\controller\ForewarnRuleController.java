package com.ruoyi.instruction.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.instruction.domain.ForewarnRule;
import com.ruoyi.instruction.service.IForewarnRuleService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 预警提醒规则Controller
 *
 * <AUTHOR>
 * @date 2024-07-30
 */
@RestController
@RequestMapping("/forewarn/rule")
public class ForewarnRuleController extends BaseController
{
    @Autowired
    private IForewarnRuleService forewarnRuleService;

    /**
     * 查询预警提醒规则列表
     */
    // @PreAuthorize("@ss.hasPermi('forewarn:rule:list')")
    @GetMapping("/list")
    public TableDataInfo list(ForewarnRule forewarnRule)
    {
        startPage();
        List<ForewarnRule> list = forewarnRuleService.selectForewarnRuleList(forewarnRule);
        return getDataTable(list);
    }

    /**
     * 导出预警提醒规则列表
     */
    @PreAuthorize("@ss.hasPermi('forewarn:rule:export')")
    @Log(title = "预警提醒规则", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, ForewarnRule forewarnRule)
    {
        List<ForewarnRule> list = forewarnRuleService.selectForewarnRuleList(forewarnRule);
        ExcelUtil<ForewarnRule> util = new ExcelUtil<ForewarnRule>(ForewarnRule.class);
        util.exportExcel(response, list, "预警提醒规则数据");
    }

    /**
     * 获取预警提醒规则详细信息
     */
    @PreAuthorize("@ss.hasPermi('forewarn:rule:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(forewarnRuleService.selectForewarnRuleById(id));
    }

    /**
     * 新增预警提醒规则
     */
    @PreAuthorize("@ss.hasPermi('forewarn:rule:add')")
    @Log(title = "预警提醒规则", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody ForewarnRule forewarnRule)
    {
        return toAjax(forewarnRuleService.insertForewarnRule(forewarnRule));
    }

    /**
     * 修改预警提醒规则
     */
    @PreAuthorize("@ss.hasPermi('forewarn:rule:edit')")
    @Log(title = "预警提醒规则", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody ForewarnRule forewarnRule)
    {
        return forewarnRuleService.updateForewarnRule(forewarnRule);
    }

    /**
     * 删除预警提醒规则
     */
    @PreAuthorize("@ss.hasPermi('forewarn:rule:remove')")
    @Log(title = "预警提醒规则", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(forewarnRuleService.deleteForewarnRuleByIds(ids));
    }



}
