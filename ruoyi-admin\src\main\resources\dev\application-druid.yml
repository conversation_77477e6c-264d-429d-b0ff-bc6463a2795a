# 数据源配置
spring:
  datasource:
    type: com.alibaba.druid.pool.DruidDataSource
    driverClassName: com.mysql.cj.jdbc.Driver
    druid:
      # 主库数据源
      master:
        url: ****************************************************************************************************************************************************************************
        username: jazz_516330
        password: IlS4TAP1yaPPXppV1++1zpCkalh85qZoiGFvZkt096mYPnb9OfdJaPNqNZpb7OUotYL+uui7G+MiGEWgd9UDNg==
#        url: ***************************************************************************************************************************************************
#        username: csdn_data
#        password: U/Rvrm+u2HJhKeIm4T+YEUO6HE6+6uQWOtpiN07Q22H7hTywg7+xUO6xXezbXjXlu3+r2HX337S/p6OLTp53Ig==
      # 从库数据源
      slave:
        # 从数据源开关/默认关闭
        enabled: true
        url: ***********************************************************************************************************************************************************************************************************************************************
        username: qzzx
        password: Mz7x8HFceaoQKz2SAReznwe6VVvFWrQiyJArAEwSxbIVUTV1i3LoAlk25GP8ugVaiQocwtpCBOGcPaK+nHd30w==
      slaveqz:
        # 从数据源开关/默认关闭
        enabled: true
        url: **************************************************************************************************************************************************************************************************************************************************
        username: qzyth
        password: cRmq+pqgzrvM6R0H0RjnFgNMz7SSkp0FAyD5Y2QuCS0CN8/nGwfQQ1ePqrRXPMjU2kfks71nP0DL8ANBVHN8xQ==
      slavewz:
        # 从数据源开关/默认关闭
        enabled: true
        url: ***********************************************************************************************************************************************************************************************************************************************
        username: szjh
        password: EYHKmX8HBGWr8EgqLiWsv9F0Mv5jSU+U3CCkykq9LnuHvXZx6ea2aEHwyHYFT5ygIOtgc3DVC+iL2FJf8ON2ag==
      # 初始连接数
      initialSize: 5
      # 最小连接池数量
      minIdle: 20
      # 最大连接池数量
      maxActive: 50
      # 配置获取连接等待超时的时间
      maxWait: 60000
      # 配置间隔多久才进行一次检测，检测需要关闭的空闲连接，单位是毫秒
      timeBetweenEvictionRunsMillis: 60000
      # 配置一个连接在池中最小生存的时间，单位是毫秒
      minEvictableIdleTimeMillis: 300000
      # 配置一个连接在池中最大生存的时间，单位是毫秒
      maxEvictableIdleTimeMillis: 900000
      # 配置检测连接是否有效
      validationQuery: SELECT 1 FROM DUAL
      testWhileIdle: true
      testOnBorrow: false
      testOnReturn: false
      connectProperties: config.decrypt=true;config.decrypt.key=MFwwDQYJKoZIhvcNAQEBBQADSwAwSAJBAJxFWT656BLpjNieR+d8Kan9V/eZbVWScsJpJy/3Fi6NX/WcpQd1ZMObcxJitTy+aPNEQKtem16ua4qtigxRN88CAwEAAQ==
      webStatFilter:
        enabled: true
      statViewServlet:
        enabled: true
        # 设置白名单，不填则允许所有访问
        allow:
        url-pattern: /druid/*
        # 控制台管理用户名和密码
        login-username: ruoyi
        login-password: 123456
      filter:
        config:
          # 是否配置加密
          enabled: true
        stat:
          enabled: true
          # 慢SQL记录
          log-slow-sql: true
          slow-sql-millis: 1000
          merge-sql: true
        wall:
          config:
            multi-statement-allow: true
