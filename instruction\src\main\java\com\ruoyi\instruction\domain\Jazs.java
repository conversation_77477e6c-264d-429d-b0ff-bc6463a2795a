package com.ruoyi.instruction.domain;

import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 金安指数对象 t_jazs
 *
 * <AUTHOR>
 * @date 2023-08-10
 */
@Data
public class Jazs extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 序号 */
    private Long id;

    /** 年份 */
    @Excel(name = "年份")
    private Integer year;

    /** 月份 */
    private Integer month;

    /** 县市区 */
    @Excel(name = "县市区", combo = {"婺城区","金东区","兰溪市","东阳市","义乌市","永康市","浦江县","武义县","磐安县","开发区"})
    private String county;

    /** 乡镇街道 */
    @Excel(name = "乡镇街道")
    private String street;

    /** 得分 */
    private Double score;

    /** 排名   */
    @Excel(name = "排名")
    private Long rank;

    @Excel(name = "1月")
    private String jan;

    private Long janRank;

    @Excel(name = "2月")
    private String feb;

    private Long febRank;

    @Excel(name = "3月")
    private String mar;

    private Long marRank;

    @Excel(name = "4月")
    private String apr;

    private Long aprRank;

    @Excel(name = "5月")
    private String may;

    private Long mayRank;

    @Excel(name = "6月")
    private String jun;

    private Long junRank;

    @Excel(name = "7月")
    private String jul;

    private Long julRank;

    @Excel(name = "8月")
    private String aug;

    private Long augRank;

    @Excel(name = "9月")
    private String sep;

    private Long sepRank;

    @Excel(name = "10月")
    private String oct;

    private Long octRank;

    @Excel(name = "11月")
    private String nov;

    private Long novRank;

    @Excel(name = "12月")
    private String dec;

    private Long decRank;

    private String rankName;

    private String rankType;

    /** 状态 1：正常  9：删除 */
    private Integer status;

    /**
     * 前十个数
     */
    private Integer topNum;

    /**
     * 后十个数
     */
    private Integer lowNum;

    private Long dynamicValue;

    public void setId(Long id)
    {
        this.id = id;
    }

    public Long getId()
    {
        return id;
    }
    public void setYear(Integer year)
    {
        this.year = year;
    }

    public Integer getYear()
    {
        return year;
    }
    public void setMonth(Integer month)
    {
        this.month = month;
    }

    public Integer getMonth()
    {
        return month;
    }
    public void setCounty(String county)
    {
        this.county = county;
    }

    public String getCounty()
    {
        return county;
    }
    public void setStreet(String street)
    {
        this.street = street;
    }

    public String getStreet()
    {
        return street;
    }
    public void setScore(Double score)
    {
        this.score = score;
    }

    public Double getScore()
    {
        return score;
    }
    public void setRank(Long rank)
    {
        this.rank = rank;
    }

    public Long getRank()
    {
        return rank;
    }
    public void setStatus(Integer status)
    {
        this.status = status;
    }

    public Integer getStatus()
    {
        return status;
    }

    public String getJan() {
        return jan;
    }

    public void setJan(String jan) {
        this.jan = jan;
    }

    public Long getJanRank() {
        return janRank;
    }

    public void setJanRank(Long janRank) {
        this.janRank = janRank;
    }

    public String getFeb() {
        return feb;
    }

    public void setFeb(String feb) {
        this.feb = feb;
    }

    public Long getFebRank() {
        return febRank;
    }

    public void setFebRank(Long febRank) {
        this.febRank = febRank;
    }

    public String getMar() {
        return mar;
    }

    public void setMar(String mar) {
        this.mar = mar;
    }

    public Long getMarRank() {
        return marRank;
    }

    public void setMarRank(Long marRank) {
        this.marRank = marRank;
    }

    public String getApr() {
        return apr;
    }

    public void setApr(String apr) {
        this.apr = apr;
    }

    public Long getAprRank() {
        return aprRank;
    }

    public void setAprRank(Long aprRank) {
        this.aprRank = aprRank;
    }

    public String getMay() {
        return may;
    }

    public void setMay(String may) {
        this.may = may;
    }

    public Long getMayRank() {
        return mayRank;
    }

    public void setMayRank(Long mayRank) {
        this.mayRank = mayRank;
    }

    public String getJun() {
        return jun;
    }

    public void setJun(String jun) {
        this.jun = jun;
    }

    public Long getJunRank() {
        return junRank;
    }

    public void setJunRank(Long junRank) {
        this.junRank = junRank;
    }

    public String getJul() {
        return jul;
    }

    public void setJul(String jul) {
        this.jul = jul;
    }

    public Long getJulRank() {
        return julRank;
    }

    public void setJulRank(Long julRank) {
        this.julRank = julRank;
    }

    public String getAug() {
        return aug;
    }

    public void setAug(String aug) {
        this.aug = aug;
    }

    public Long getAugRank() {
        return augRank;
    }

    public void setAugRank(Long augRank) {
        this.augRank = augRank;
    }

    public String getSep() {
        return sep;
    }

    public void setSep(String sep) {
        this.sep = sep;
    }

    public Long getSepRank() {
        return sepRank;
    }

    public void setSepRank(Long sepRank) {
        this.sepRank = sepRank;
    }

    public String getOct() {
        return oct;
    }

    public void setOct(String oct) {
        this.oct = oct;
    }

    public Long getOctRank() {
        return octRank;
    }

    public void setOctRank(Long octRank) {
        this.octRank = octRank;
    }

    public String getNov() {
        return nov;
    }

    public void setNov(String nov) {
        this.nov = nov;
    }

    public Long getNovRank() {
        return novRank;
    }

    public void setNovRank(Long novRank) {
        this.novRank = novRank;
    }

    public String getDec() {
        return dec;
    }

    public void setDec(String dec) {
        this.dec = dec;
    }

    public Long getDecRank() {
        return decRank;
    }

    public void setDecRank(Long decRank) {
        this.decRank = decRank;
    }

    public String getRankName() {
        return rankName;
    }

    public void setRankName(String rankName) {
        this.rankName = rankName;
    }

    public String getRankType() {
        return rankType;
    }

    public void setRankType(String rankType) {
        this.rankType = rankType;
    }

    public Integer getTopNum() {
        return topNum;
    }

    public void setTopNum(final Integer topNum) {
        this.topNum = topNum;
    }

    public Integer getLowNum() {
        return lowNum;
    }

    public void setLowNum(final Integer lowNum) {
        this.lowNum = lowNum;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("year", getYear())
            .append("month", getMonth())
            .append("county", getCounty())
            .append("street", getStreet())
            .append("score", getScore())
            .append("rank", getRank())
            .append("status", getStatus())
            .toString();
    }
}
