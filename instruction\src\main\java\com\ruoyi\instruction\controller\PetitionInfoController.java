package com.ruoyi.instruction.controller;

import java.util.Arrays;
import java.util.List;
import java.util.Map;
import javax.servlet.http.HttpServletResponse;

import com.ruoyi.instruction.domain.RiskDetails;
import com.ruoyi.instruction.mapper.PetitionInfoMapper;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.instruction.domain.PetitionInfo;
import com.ruoyi.instruction.service.IPetitionInfoService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;
import org.springframework.web.multipart.MultipartFile;

/**
 * 信访信息Controller
 *
 * <AUTHOR>
 * @date 2024-04-28
 */
@RestController
@RequestMapping("/petition/info")
public class PetitionInfoController extends BaseController
{
    @Autowired
    private IPetitionInfoService petitionInfoService;

    @Autowired
    private PetitionInfoMapper petitionInfoMapper;

    /**
     * 查询信访信息列表
     */
    @PreAuthorize("@ss.hasPermi('petition:info:list')")
    @GetMapping("/list")
    public TableDataInfo list(PetitionInfo petitionInfo)
    {
        if (petitionInfo.getContentType() != null && !petitionInfo.getContentType().isEmpty()) {
            Map<String, Object> params = petitionInfo.getParams();
            List<String> contentTypes = Arrays.asList(petitionInfo.getContentType().split(","));
            params.put("contentTypes", contentTypes);
            petitionInfo.setParams(params);
        }

        startPage();
        List<PetitionInfo> list = petitionInfoService.selectPetitionInfoList(petitionInfo);
        return getDataTable(list);
    }

    /**
     * 查询信访类型列表
     * @return
     */
    @GetMapping("/typeList")
    public AjaxResult typeList()
    {
        List<Map<Integer,String>> list = petitionInfoService.selectTypeList();
        return AjaxResult.success(list);
    }

    /**
     * 获取事项目的
     * @return
     */
    @GetMapping("/getEventGoalList")
    public AjaxResult getEventGoalList()
    {
        List<String> list =petitionInfoMapper.getEventGoalList();
        return AjaxResult.success(list);
    }

    /**
     * 导出信访信息列表
     */
    @PreAuthorize("@ss.hasPermi('petition:info:export')")
    @Log(title = "信访信息", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, PetitionInfo petitionInfo)
    {
        List<PetitionInfo> list = petitionInfoService.selectPetitionInfoList(petitionInfo);
        ExcelUtil<PetitionInfo> util = new ExcelUtil<PetitionInfo>(PetitionInfo.class);
        util.exportExcel(response, list, "信访信息数据");
    }

    /**
     * 获取信访信息详细信息
     */
    @PreAuthorize("@ss.hasPermi('petition:info:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(petitionInfoService.selectPetitionInfoById(id));
    }

    /**
     * 新增信访信息
     */
    @PreAuthorize("@ss.hasPermi('petition:info:add')")
    @Log(title = "信访信息", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody PetitionInfo petitionInfo)
    {
        return toAjax(petitionInfoService.insertPetitionInfo(petitionInfo));
    }

    /**
     * 修改信访信息
     */
    @PreAuthorize("@ss.hasPermi('petition:info:edit')")
    @Log(title = "信访信息", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody PetitionInfo petitionInfo)
    {
        return toAjax(petitionInfoService.updatePetitionInfo(petitionInfo));
    }

    /**
     * 删除信访信息
     */
    @PreAuthorize("@ss.hasPermi('petition:info:remove')")
    @Log(title = "信访信息", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(petitionInfoService.deletePetitionInfoByIds(ids));
    }


    /**
     * 下载导入模板
     *
     * @param response
     */
    @PostMapping("/importTemplate")
    public void importTemplate(HttpServletResponse response) {
        ExcelUtil<PetitionInfo> util = new ExcelUtil<PetitionInfo>(PetitionInfo.class);
        util.importTemplateExcel(response, "信访事件导入模板");
    }


    /**
     * 导入数据
     * @param file
     * @return
     * @throws Exception
     */
    @Log(title = "信访数据导入", businessType = BusinessType.IMPORT)
    @PostMapping("/importPetitionInfo")
    public AjaxResult importPetitionInfo(MultipartFile file) throws Exception {
        ExcelUtil<PetitionInfo> util = new ExcelUtil<PetitionInfo>(PetitionInfo.class);
        List<PetitionInfo> petitionInfoList = util.importExcel(file.getInputStream());
        return petitionInfoService.importPetitionInfoData(petitionInfoList);
    }

}
