package com.ruoyi.instruction.domain.rspVo;

import lombok.Data;

/**
 * 市级指令办理情况概览响应
 *
 * <AUTHOR>
 * @date 2025-01-20
 */
@Data
public class CityHandlingOverviewRsp {

    /**
     * 超时接收数量
     */
    private Long timeoutReceiveCount;

    /**
     * 超时处置数量
     */
    private Long timeoutDisposeCount;

    /**
     * 应处置未处置单位数
     */
    private Long unprocessedUnitCount;

    /**
     * 应处置未处置部门数
     */
    private Long unprocessedDeptCount;

    /**
     * 待反馈7天数量（特急）
     */
    private Long pendingFeedback7Days;

    /**
     * 待反馈15天数量（紧急）
     */
    private Long pendingFeedback15Days;

    /**
     * 待反馈30天数量（一般）
     */
    private Long pendingFeedback30Days;

    /**
     * 统计时间范围开始
     */
    private String startTime;

    /**
     * 统计时间范围结束
     */
    private String endTime;

    /**
     * 当前用户所属区域
     */
    private String currentArea;
}
