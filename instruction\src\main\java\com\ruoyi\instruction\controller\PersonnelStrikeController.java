package com.ruoyi.instruction.controller;

import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.IDCardUtils;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.instruction.domain.PersonnelStrike;
import com.ruoyi.instruction.domain.PetitionInfo;
import com.ruoyi.instruction.domain.rspVo.PersonnelStrikeRspVo;
import com.ruoyi.instruction.service.IPersonnelStrikeService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.List;


/**
 * 人员打击Controller
 *
 * <AUTHOR>
 * @date 2024-06-11
 */
@RestController
@RequestMapping("/personnel/strike")
public class PersonnelStrikeController extends BaseController {
    @Autowired
    private IPersonnelStrikeService personnelStrikeService;

    /**
     * 下载导入模板
     *
     * @param response
     */
    @PostMapping("/importTemplate")
    public void importTemplate(HttpServletResponse response) {
        ExcelUtil<PersonnelStrike> util = new ExcelUtil<PersonnelStrike>(PersonnelStrike.class);
        util.importTemplateExcel(response, "人员打击导入模板");
    }

    /**
     * 导入数据
     * @param file
     * @return
     * @throws Exception
     */
    @Log(title = "人员打击数据导入", businessType = BusinessType.IMPORT)
    @PostMapping("/importPersonnelStrike")
    public AjaxResult importPersonnelStrike(MultipartFile file) throws Exception {
        ExcelUtil<PersonnelStrike> util = new ExcelUtil<PersonnelStrike>(PersonnelStrike.class);
        List<PersonnelStrike> personnelStrikeList = util.importExcel(file.getInputStream());
        for(int i = 0;i<personnelStrikeList.size();i++){
            PersonnelStrike personnelStrike = personnelStrikeList.get(i);
            // 补充存在合并单元格的内容
            if(i>0 && StringUtils.isEmpty(personnelStrike.getStrikeUnit())){
                personnelStrike.setStrikeUnit(personnelStrikeList.get(i-1).getStrikeUnit());
            }
            // 身份证号码加密
            if(StringUtils.isNotEmpty(personnelStrike.getSfzh())){
                String idCard = personnelStrike.getSfzh().toLowerCase();
                String encryptedIDCard = IDCardUtils.encryptIDCard(idCard);
                personnelStrike.setSfzh(encryptedIDCard);
            }

        }
        return personnelStrikeService.importPersonnelStrikeData(personnelStrikeList);
    }

    /**
     * 导出人员打击列表
     */
    @Log(title = "人员打击", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, @RequestBody PersonnelStrike personnelStrike)
    {
        List<PersonnelStrikeRspVo> list = personnelStrikeService.selectPersonnelStrikeList(personnelStrike);
        ExcelUtil<PersonnelStrikeRspVo> util = new ExcelUtil<PersonnelStrikeRspVo>(PersonnelStrikeRspVo.class);
        util.exportExcel(response, list, "信访信息数据");
    }

    /**
     * 查询人员打击列表
     */
    @GetMapping("/list")
    public TableDataInfo list(PersonnelStrike personnelStrike)
    {

        startPage();
        List<PersonnelStrikeRspVo> list = personnelStrikeService.selectPersonnelStrikeList(personnelStrike);
        return getDataTable(list);
    }

    /**
     * 新增打击人员
     */
    @Log(title = "打击人员", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody PersonnelStrike personnelStrike)
    {
        return toAjax(personnelStrikeService.insertPersonnelStrike(personnelStrike));
    }


}
