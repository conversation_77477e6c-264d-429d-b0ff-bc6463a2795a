package com.ruoyi.instruction.mapper;

import java.util.List;
import com.ruoyi.instruction.domain.RiskFeedback;

/**
 * 风险交办-反馈Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-02-24
 */
public interface RiskFeedbackMapper 
{
    /**
     * 查询风险交办-反馈
     * 
     * @param id 风险交办-反馈主键
     * @return 风险交办-反馈
     */
    public RiskFeedback selectRiskFeedbackById(Long id);

    /**
     * 查询风险交办-反馈列表
     * 
     * @param riskFeedback 风险交办-反馈
     * @return 风险交办-反馈集合
     */
    public List<RiskFeedback> selectRiskFeedbackList(RiskFeedback riskFeedback);

    /**
     * 新增风险交办-反馈
     * 
     * @param riskFeedback 风险交办-反馈
     * @return 结果
     */
    public int insertRiskFeedback(RiskFeedback riskFeedback);

    /**
     * 修改风险交办-反馈
     * 
     * @param riskFeedback 风险交办-反馈
     * @return 结果
     */
    public int updateRiskFeedback(RiskFeedback riskFeedback);

    /**
     * 删除风险交办-反馈
     * 
     * @param id 风险交办-反馈主键
     * @return 结果
     */
    public int deleteRiskFeedbackById(Long id);

    /**
     * 批量删除风险交办-反馈
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteRiskFeedbackByIds(Long[] ids);
}
