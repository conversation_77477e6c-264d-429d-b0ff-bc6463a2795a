package com.ruoyi.instruction.mapper;

import java.util.Date;
import java.util.List;
import com.ruoyi.instruction.domain.ForenwarnRecord;
import org.apache.ibatis.annotations.Param;

/**
 * 预警提醒记录Mapper接口
 *
 * <AUTHOR>
 * @date 2024-07-31
 */
public interface ForenwarnRecordMapper
{
    /**
     * 查询预警提醒记录
     *
     * @param id 预警提醒记录主键
     * @return 预警提醒记录
     */
    public ForenwarnRecord selectForenwarnRecordById(Long id);

    /**
     * 查询预警提醒记录列表
     *
     * @param forenwarnRecord 预警提醒记录
     * @return 预警提醒记录集合
     */
    public List<ForenwarnRecord> selectForenwarnRecordList(ForenwarnRecord forenwarnRecord);

    /**
     * 新增预警提醒记录
     *
     * @param forenwarnRecord 预警提醒记录
     * @return 结果
     */
    public int insertForenwarnRecord(ForenwarnRecord forenwarnRecord);

    /**
     * 修改预警提醒记录
     *
     * @param forenwarnRecord 预警提醒记录
     * @return 结果
     */
    public int updateForenwarnRecord(ForenwarnRecord forenwarnRecord);

    /**
     * 删除预警提醒记录
     *
     * @param id 预警提醒记录主键
     * @return 结果
     */
    public int deleteForenwarnRecordById(Long id);

    /**
     * 批量删除预警提醒记录
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteForenwarnRecordByIds(Long[] ids);

    /**
     * 批量新增
     * @param forenwarnRecordList
     */
    void batchInsertForenwarnRecords(List<ForenwarnRecord> forenwarnRecordList);

    /**
     * 查询新增预警提醒列表
     * @param foreWarnType
     * @param lastRemindTime
     * @return
     */
    List<ForenwarnRecord> findRemindList(@Param("foreWarnType") String foreWarnType,@Param("lastRemindTime") Date lastRemindTime);

    /**
     * 根据人员id更新预警提醒处置状态
     * @param forenwarnRecord
     */
    void updateForenwarnRecordByPersonId(ForenwarnRecord forenwarnRecord);
}
