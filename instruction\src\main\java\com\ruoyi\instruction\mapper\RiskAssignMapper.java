package com.ruoyi.instruction.mapper;

import java.util.List;
import com.ruoyi.instruction.domain.RiskAssign;

/**
 * 风险交办单页面Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-02-26
 */
public interface RiskAssignMapper 
{
    /**
     * 查询风险交办单页面
     * 
     * @param id 风险交办单页面主键
     * @return 风险交办单页面
     */
    public RiskAssign selectRiskAssignById(Long id);

    /**
     * 查询风险交办单页面列表
     * 
     * @param riskAssign 风险交办单页面
     * @return 风险交办单页面集合
     */
    public List<RiskAssign> selectRiskAssignList(RiskAssign riskAssign);

    /**
     * 新增风险交办单页面
     * 
     * @param riskAssign 风险交办单页面
     * @return 结果
     */
    public int insertRiskAssign(RiskAssign riskAssign);

    /**
     * 修改风险交办单页面
     * 
     * @param riskAssign 风险交办单页面
     * @return 结果
     */
    public int updateRiskAssign(RiskAssign riskAssign);

    /**
     * 删除风险交办单页面
     * 
     * @param id 风险交办单页面主键
     * @return 结果
     */
    public int deleteRiskAssignById(Long id);

    /**
     * 批量删除风险交办单页面
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteRiskAssignByIds(Long[] ids);
}
