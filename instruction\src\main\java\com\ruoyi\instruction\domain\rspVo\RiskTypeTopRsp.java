package com.ruoyi.instruction.domain.rspVo;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * 风险隐患类型
 * 
 * <AUTHOR>
 * @date 2023-08-07
 */
@Data
public class RiskTypeTopRsp implements Serializable
{
    private static final long serialVersionUID = 1L;
    /**
     * 风险类型名称
     */
    private String typeName;
    /**
     * 数量
     */
    private Integer num;
}
