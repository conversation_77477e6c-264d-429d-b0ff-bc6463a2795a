package com.ruoyi.instruction.controller;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.alibaba.excel.write.metadata.WriteTable;
import com.alibaba.fastjson.JSON;
import com.ruoyi.common.constant.CacheConstants;
import com.ruoyi.common.constant.Constants;
import com.ruoyi.common.constant.InstructionRolesConstants;
import com.ruoyi.common.core.domain.entity.SysDept;
import com.ruoyi.common.core.domain.entity.SysRole;
import com.ruoyi.common.core.domain.entity.SysUser;
import com.ruoyi.common.core.domain.model.LoginUser;
import com.ruoyi.common.core.redis.RedisCache;
import com.ruoyi.common.helper.DingtalkHelper;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.IDCardUtils;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.framework.web.domain.server.LogVo;
import com.ruoyi.framework.web.service.TokenService;
import com.ruoyi.instruction.domain.*;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.common.utils.easyexcel.EasyExcelUtil;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.instruction.domain.excelVo.PersonLevelChangeExcelVo;
import com.ruoyi.instruction.domain.reqVo.InspectionReportParam;
import com.ruoyi.instruction.domain.reqVo.InstructionFlowTestVo;
import com.ruoyi.instruction.domain.rspVo.*;
import com.ruoyi.instruction.enums.Report;
import com.ruoyi.instruction.mapper.InstructionCountyFeedbackMapper;
import com.ruoyi.instruction.mapper.InstructionFeedbackMapper;
import com.ruoyi.instruction.mapper.InstructionInfoMapper;
import com.ruoyi.instruction.service.IInstructionFollowService;
import com.ruoyi.instruction.service.ISensitiveRecordService;
import com.ruoyi.instruction.service.impl.TOpenUndercoverInspectionServiceImpl;
import com.ruoyi.instruction.task.RemindRecordTask;
import com.ruoyi.system.mapper.SysDeptMapper;
import com.ruoyi.system.service.ISysUserService;
import com.ruoyi.system.service.impl.SysDeptServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.instruction.service.IInstructionInfoService;
import com.ruoyi.common.core.page.TableDataInfo;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import static com.ruoyi.common.constant.Constants.DING_JINHUACSDN;

/**
 * 指令基本信息Controller
 *
 * <AUTHOR>
 * @date 2022-12-15
 */
@RestController
@RequestMapping("/instruction/info")
@Slf4j
public class InstructionInfoController extends BaseController {

    public static final Long ZFW_DEPT_ID = 202L;

    @Autowired
    private IInstructionInfoService instructionInfoService;

    @Autowired
    private InstructionInfoMapper infoMapper;

    @Autowired
    private DingtalkHelper dingtalkHelper;

    @Autowired
    private SysDeptServiceImpl deptService;

    @Autowired
    private RedisCache redisCache;

    @Autowired
    private TokenService tokenService;

    @Autowired
    private ISensitiveRecordService sensitiveRecordService;

    @Autowired
    private ISysUserService userService;

    @Autowired
    private SysDeptMapper sysDeptMapper;

    @Autowired
    private InstructionFeedbackMapper feedbackMapper;

    @Autowired
    private InstructionCountyFeedbackMapper countyFeedbackMapper;

    @Autowired
    private IInstructionFollowService instructionFollowService;


    /**
     * 获取指令基本信息详细信息
     */
    @PreAuthorize("@ss.hasPermi('instruction:info:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id,@RequestParam(name = "type",required = false) Integer type,@RequestParam(name = "menuName",required = false) String menuName) {
        //当期用户信息
        SysUser user = SecurityUtils.getLoginUser().getUser();
        log.info(user.getNickName());
        LogVo logVo = new LogVo ();
        logVo.setUserId(user.getUserId()+"");
        logVo.setActionType(3);
        logVo.setAppCode("A330701373889202304000005");
        logVo.setAreaCode("330702001");
        logVo.setActionTime(DateUtils.getTime());
        logVo.setUserRole("政府工作人员");
        logVo.setActionId(""+id);
        log.info(JSON.toJSONString(logVo));
        InstructionInfo info = instructionInfoService.selectInstructionInfoById(id);

        if (type != null) {
            //查看信息脱敏
            SensitiveRecord sensitiveRecord = new SensitiveRecord();

            SysDept dept = user.getDept();
            SysDept parentDept = sysDeptMapper.selectDeptById(dept.getParentId());
            String deptName = parentDept.getDeptName().replace("委政法委", "").replace("政法委", "").replace("政法办", "");
            sensitiveRecord.setCreateDeptName(deptName + user.getDept().getDeptName());
            SysUser sysUser = userService.selectUserById(info.getCreatorId());
            sensitiveRecord.setInstructionId(id);
            sensitiveRecord.setInstructionTitle(info.getInstructionTitle());
            sensitiveRecord.setInstructionCreateBy(info.getCreateBy());
            sensitiveRecord.setInstructionCreateBy(sysUser.getUserName());
            sensitiveRecord.setInstructionCreateDept(sysUser.getDpName());
            sensitiveRecord.setCreateBy(user.getNickName());
            sensitiveRecord.setCreateTime(new Date());
            sensitiveRecord.setCreateUserId(user.getUserId());
            sensitiveRecord.setCreateDeptId(user.getDeptId());
            sensitiveRecord.setType(3);
            sensitiveRecord.setMenuName(menuName);
            sensitiveRecordService.insertSensitiveRecord(sensitiveRecord);
        }else {
            if (info.getBaseInfo() != null && !info.getBaseInfo().equals("")) {
                String baseInfo = info.getBaseInfo();
                info.setBaseInfo(IDCardUtils.maskSensitiveInfo(baseInfo));
            }
        }
        //查询我的关注
        InstructionFollow instructionFollow = instructionFollowService.selectInstructionFollowById(SecurityUtils.getUserId());
        if (instructionFollow != null && !instructionFollow.getInstructionIds().equals("")) {
            List<String> instructionIds = Arrays.asList(instructionFollow.getInstructionIds().split(","));
            String id1 = String.valueOf(info.getId());
            if (instructionIds.contains(id1)){
                info.setMyFollow(1);
            }
        }

        LogVo endLogVo = new LogVo ();
        endLogVo.setUserId(user.getUserId()+"");
        endLogVo.setActionType(4);
        endLogVo.setAppCode("A330701373889202304000005");
        endLogVo.setAreaCode("330702001");
        endLogVo.setActionTime(DateUtils.getTime());
        endLogVo.setUserRole("政府工作人员");
        endLogVo.setActionId(""+id);
        log.info(JSON.toJSONString(endLogVo));
        return success(info);
    }

    /**
     * 新增指令基本信息
     */
    @PreAuthorize("@ss.hasPermi('instruction:info:add')")
    @Log(title = "指令基本信息", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody InstructionInfo instructionInfo) {
        AjaxResult result = instructionInfoService.insertInstructionInfo(instructionInfo);
        return result;
    }

    /**
     * 新增指令基本信息
     */
    @PreAuthorize("@ss.hasPermi('instruction:info:add')")
    @Log(title = "mzx指令基本信息", businessType = BusinessType.INSERT)
    @PostMapping("/mzxAdd")
    public AjaxResult mzxAdd(@RequestBody InstructionInfo instructionInfo) {
        AjaxResult result = instructionInfoService.insertMzxInstructionInfo(instructionInfo);
        return result;
    }

    /**
     * 修改指令基本信息
     */
    @PreAuthorize("@ss.hasPermi('instruction:info:edit')")
    @Log(title = "指令基本信息", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody InstructionInfo instructionInfo) {
        return instructionInfoService.updateInstructionInfo(instructionInfo);
    }

    /**
     * 修改案件类型
     * @param instructionInfo
     * @return
     */
    @Log(title = "指令基本信息", businessType = BusinessType.UPDATE)
    @PutMapping("/updateCaseStatus")
    public AjaxResult updateCaseStatus(@RequestBody InstructionInfo instructionInfo) {
        instructionInfo.setCaseTime(new Date());
        int i = infoMapper.updateInstructionInfo(instructionInfo);
        return AjaxResult.success();
    }

    /**
     * 修改人员信息
     * @param instructionInfo
     * @return
     */
    @PreAuthorize("@ss.hasPermi('instruction:info:edit')")
    @Log(title = "修改指令人员信息", businessType = BusinessType.UPDATE)
    @PutMapping("/editPersonInfo")
    public AjaxResult editPersonInfo(@RequestBody InstructionInfo instructionInfo) {
        return instructionInfoService.editPersonInfo(instructionInfo);
    }


    @Log(title = "修改指令人员信息", businessType = BusinessType.UPDATE)
    @PostMapping("/editPersonInfoNew")
    public AjaxResult editPersonInfoNew(@RequestBody InstructionInfo instructionInfo) {
        return instructionInfoService.editPersonInfo(instructionInfo);
    }

    /**
     * 修改mzx指令基本信息
     */
    @PreAuthorize("@ss.hasPermi('instruction:info:edit')")
    @Log(title = "mzx指令基本信息", businessType = BusinessType.UPDATE)
    @PutMapping("/mzxEdit")
    public AjaxResult mzxEdit(@RequestBody InstructionInfo instructionInfo) {
        return instructionInfoService.updateMzxInstructionInfo(instructionInfo);
    }

    /**
     * 修改是否发布状态
     *
     * @return
     */
    @PreAuthorize("@ss.hasPermi('instruction:info:isRelease')")
    @PutMapping("editIsRelease")
    public AjaxResult editIsRelease(@RequestBody InstructionInfo instructionInfo) {
        return toAjax(instructionInfoService.updateIsRelease(instructionInfo));
    }

    /**
     * 删除指令基本信息
     */
    @PreAuthorize("@ss.hasPermi('instruction:info:remove')")
    @Log(title = "指令基本信息", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long ids) {
        return toAjax(instructionInfoService.deleteInstructionInfoById(ids));
    }


    /**
     * 测试提交流程接口
     *
     * @return
     */
    @PostMapping("/testSubmit")
    public AjaxResult testSubmit(@RequestBody InstructionFlowTestVo instructionFlowTestVo) {
        return instructionInfoService.testSubmit(instructionFlowTestVo);
    }

    /**
     * 市级mzx指令流程信息提交
     * @param instructionFlowTestVo
     * @return
     */
    @PostMapping("/mzxSubmit")
    public AjaxResult mzxSubmit(@RequestBody InstructionFlowTestVo instructionFlowTestVo) {
        return instructionInfoService.mzxSubmit(instructionFlowTestVo);
    }

    /**
     * @param type 1:审核通过 2:审核不通过 3:再次提交
     * @param id   指令id
     * @return
     */
    @GetMapping("/auditInstruction")
    public AjaxResult auditInstruction(String type, Long id) {
        return instructionInfoService.auditInstruction(type, id);
    }

    /**
     * 获取已审核/未审核指令集合
     *
     * @return
     */
    @GetMapping("/getNoAuditList")
    public TableDataInfo getNoAuditList(InstructionInfo info) {
        Long deptId = SecurityUtils.getDeptId();
        List<SysRole> roles = SecurityUtils.getLoginUser().getUser().getRoles();
        List<String> roleList = roles.stream().map(SysRole::getRoleKey).collect(Collectors.toList());
        if (roleList.contains("cityInstruction")) {
            deptId = Constants.JINHUA_CITY_DEPT_ID;
        } else if (roleList.contains("countyInstruction")) {
            SysDept dept = SecurityUtils.getLoginUser().getUser().getDept();
            dept = deptService.getCountyDeptInfo(dept);
            deptId = dept.getDeptId();
        }
        info.setCreateDeptId(deptId);
        // startPage();
        // List<InstructionInfo> instructionInfoList = instructionInfoService.selectInstructionInfoList(info);
        // return getDataTable(instructionInfoList);
        TableDataInfo tableDataInfo = instructionInfoService.testInstructionListNew(info);
        return tableDataInfo;
    }


    /**
     * 获取移动端指令列表
     *
     * @param info
     * @return
     */
    @GetMapping("/getYiDongInstructionList")
    public TableDataInfo getYiDongInstructionList(InstructionInfo info) {
        info.setCreateDeptId(202L);
        startPage();
        List<InstructionInfo> instructionInfoList = instructionInfoService.getYiDongInstructionList(info);
        instructionInfoList.stream().forEach(instructionInfo -> {
            String receiveUnit = instructionInfo.getReceiveUnit();
            String replace = receiveUnit.replace("[", "").replace("]", "");
            instructionInfo.setReceiveUnit(replace);
        });
        return getDataTable(instructionInfoList);
    }

    /**
     * 根据指令id获取指令流程时间
     *
     * @param id
     * @return
     */
    @GetMapping("/getInstructionTime/{id}")
    public AjaxResult getInstructionTime(@PathVariable Long id) {
        Map<String, Object> map = instructionInfoService.getInstructionTime(id);
        return AjaxResult.success(map);
    }

    /**
     * 获取移动端统计数
     *
     * @return
     */
    @GetMapping("/getYiDongStatistics")
    public AjaxResult getYiDongStatistics(InstructionInfo info) {
        Map<String, Object> map = instructionInfoService.getYiDongStatistics(info);
        return AjaxResult.success(map);
    }

    /**
     * 获取指令下发统计
     *
     * @return
     */
    @GetMapping("/getYiDongIssueStatistics")
    public AjaxResult getYiDongIssueStatistics() {
        List<Map<String, Object>> mapList = infoMapper.getYiDongIssueStatistics();
        return AjaxResult.success(mapList);
    }

    /**
     * 县市区提交指令信息
     *
     * @param instructionFlowTestVo
     * @return
     */
    @PostMapping("/submitForCounty")
    public AjaxResult submitForCounty(@RequestBody InstructionFlowTestVo instructionFlowTestVo) {
        return instructionInfoService.submitForCounty(instructionFlowTestVo);
    }


    /**
     * mzx县市区提交指令信息
     *
     * @param instructionFlowTestVo
     * @return
     */
    @PostMapping("/mzxSubmitForCounty")
    public AjaxResult mzxSubmitForCounty(@RequestBody InstructionFlowTestVo instructionFlowTestVo) {
        return instructionInfoService.submitForCounty(instructionFlowTestVo);
    }

    /**
     * 根据指令id查询县市区指令流程详情
     *
     * @param id
     * @param type
     * @return
     */
    @GetMapping("/getProcessForCountyById/{id}/{type}")
    public AjaxResult getProcessForCountyById(@PathVariable("id") Long id, @PathVariable("type") Integer type) {
        InstructionFlowTestVo flowTestVo = instructionInfoService.getProcessForCountyById(id, type);
        return AjaxResult.success(flowTestVo);
    }

    /**
     * 获取mzx指令详情接口（第三方页面获取）
     * @param id
     * @param type
     * @return
     */
    @GetMapping("/getMzxProcessById/{id}/{type}")
    public AjaxResult getMzxProcessById(@PathVariable("id") Long id, @PathVariable("type") Integer type) {
        type = 1;
        InstructionFlowTestVo flowTestVo = new InstructionFlowTestVo();
        //判断该指令是市级创建 还是县市区创建
        InstructionInfo info = infoMapper.selectInstructionInfoById(id);
        if (info.getCreateDeptId().equals(Constants.JINHUA_CITY_DEPT_ID)){
            //获取市级
            flowTestVo = instructionInfoService.testGetProcessById(id, type);
            flowTestVo.setType(1);
        }else {
            //获取县市区
            flowTestVo = instructionInfoService.getProcessForCountyById(id, type);
            flowTestVo.setType(2);
        }

        return AjaxResult.success(flowTestVo);
    }


    /**
     * 处置、反馈变为多条记录 获取指令流程记录
     *
     * @param id
     * @return
     */
    @GetMapping("testGetProcessById/{id}/{type}")
    public AjaxResult testGetProcessById(@PathVariable("id") Long id, @PathVariable("type") Integer type) {
        InstructionFlowTestVo flowTestVo = instructionInfoService.testGetProcessById(id, type);
        return AjaxResult.success(flowTestVo);
    }


    /**
     * 获取县市区指令列表
     *
     * @param instructionInfo
     * @return
     */
    @GetMapping("/countyInstructionList")
    public TableDataInfo countyInstructionList(InstructionInfo instructionInfo) {
        return instructionInfoService.countyInstructionList(instructionInfo);
    }

    /**
     * 获取mzx县市区指令列表
     * @param instructionInfo
     * @return
     */
    @GetMapping("/mzxCountyInstructionList")
    public TableDataInfo mzxCountyInstructionList(InstructionInfo instructionInfo) {
        return instructionInfoService.mzxCountyInstructionList(instructionInfo);
    }


    /**
     * 获取指令列表
     *
     * @param instructionInfo
     * @return
     */
    @GetMapping("/testInstructionListNew")
    public TableDataInfo testInstructionListNew(InstructionInfo instructionInfo) {
        //判断用户是否为市政法委、判断是否查看市本级指令列表
        // 县市区仅查看当前流程
        TableDataInfo tableDataInfo = new TableDataInfo();
        Long deptId = SecurityUtils.getDeptId();
        List<SysRole> roles = SecurityUtils.getLoginUser().getUser().getRoles();
        List<String> roleList = roles.stream().map(SysRole::getRoleKey).collect(Collectors.toList());
        if (!deptId.equals(Constants.JINHUA_CITY_DEPT_ID) && roleList.contains(InstructionRolesConstants.CITY_INSTRUCTION)) {
            //判断用户是否包含市级指令员角色
            deptId = Constants.JINHUA_CITY_DEPT_ID;
        }
        if (!deptId.equals(ZFW_DEPT_ID) && (instructionInfo.getPageType() == 1 ||instructionInfo.getPageType() == 5)) {
            //县市区查看市下发指令
            tableDataInfo = instructionInfoService.instructionListNewForCounty(instructionInfo);
        } else {
            tableDataInfo = instructionInfoService.testInstructionListNew(instructionInfo);
        }
        return tableDataInfo;
    }


    /**
     * 获取市级mzx指令列表
     *
     * @param instructionInfo
     * @return
     */
    @GetMapping("/mzxInstructionListNew")
    public TableDataInfo mzxInstructionListNew(InstructionInfo instructionInfo) {
        //判断用户是否为市政法委、判断是否查看市本级指令列表
        // 县市区仅查看当前流程
        TableDataInfo tableDataInfo = new TableDataInfo();
        Long deptId = SecurityUtils.getDeptId();
        List<SysRole> roles = SecurityUtils.getLoginUser().getUser().getRoles();
        List<String> roleList = roles.stream().map(SysRole::getRoleKey).collect(Collectors.toList());
        if (!deptId.equals(Constants.JINHUA_CITY_DEPT_ID) && roleList.contains(InstructionRolesConstants.CITY_INSTRUCTION)) {
            //判断用户是否包含市级指令员角色
            deptId = Constants.JINHUA_CITY_DEPT_ID;
        }
        if (!deptId.equals(ZFW_DEPT_ID) && instructionInfo.getPageType() == 1) {
            //县市区、协同部门查看市下发指令
            tableDataInfo = instructionInfoService.mzxInstructionListNewForCounty(instructionInfo);
        } else {
            tableDataInfo = instructionInfoService.mzxInstructionListNew(instructionInfo);
        }
        return tableDataInfo;
    }

    /**
     * 查询全部任务名称
     *
     * @return
     */
    @GetMapping("/getMissionNames")
    public AjaxResult getMissionNames(Integer type) {
        if (type == null) {
            type = 1;
        }
        String deptName = "";
        if (type == 2) {
            //县市平安指令页面
            SysDept dept = SecurityUtils.getLoginUser().getUser().getDept();
            if (!dept.getDeptId().equals(Constants.JINHUA_CITY_DEPT_ID) && !dept.getParentId().equals(Constants.JINHUA_CITY_DEPT_ID)) {
                //县市区、乡镇街道账号
                SysDept parentDept = deptService.selectDeptById(dept.getParentId());
                deptName = parentDept.getDeptName().replace("委政法委", "").replace("政法委", "").replace("政法办", "");
            }
        }
        List<String> missionNames = new ArrayList<>();
        missionNames = instructionInfoService.selectMissionNames(type,deptName);
        return AjaxResult.success(missionNames);
    }


    /**
     * 查询出人员代办事件
     *
     * @return
     */
    @GetMapping("/testRedStatistics")
    public AjaxResult testRedStatistics() {
        Map<String, List<Long>> ids = instructionInfoService.testRedStatistics();
        return AjaxResult.success()
                .put("cityIds", ids.get("cityIds").size())
                .put("countyIds", ids.get("countyIds").size())
                .put("paCityIds", ids.get("paCityIds").size())
                .put("paCountyIds", ids.get("paCountyIds").size())
                .put("mzxCityIds", ids.get("mzxCityIds").size())
                .put("mzxCountyIds", ids.get("mzxCountyIds").size())
                .put("yjIds", ids.get("yjIds").size());
    }

    @GetMapping("/testWorkNotice")
    public AjaxResult testWorkNotice() {
        instructionInfoService.sendMsgByDeptId(null, "测试发送工作通知20230814", 1);
        // List<String> list = new ArrayList<>();
        // list.add("17805893997");
        // dingtalkHelper.sendWorkNotificationMsg(list,"测试工作通知");
        return AjaxResult.success();
    }

    @GetMapping("/getScopes")
    public AjaxResult getScopes() {

        Map<String, Object> scopes = dingtalkHelper.getScopes(Long.valueOf(196729));
        dingtalkHelper.syncZzdUserData(DING_JINHUACSDN, Long.valueOf(196729), null);
        return AjaxResult.success(scopes);
    }


    @GetMapping("/toRemindInfo")
    public AjaxResult toRemindInfo() {
        //1、获取待提醒指令id集合
        List<Long> ids = infoMapper.findToRemind();
        //2、查询待提醒指令详情
        List<InstructionRemindVo> infos = infoMapper.ToRemindInfo(ids);
        infos.stream().forEach(info -> {
            Integer minutesDiff = info.getMinutesDiff();
            //判断待接收个数与已接收部门个数
            if (info.getIsReceiveCount() != null && info.getIsReceiveDept() != null && !info.getReceiveUnitCount().equals(info.getIsReceiveCount())) {
                //已有部门接收 查看是否与下发部门相同
                String[] receiveUnit = info.getReceiveUnit().split(",");
                String[] isReceiveUnit = info.getIsReceiveDept().split(",");
                // 将数组转换为集合
                Set<String> set1 = new HashSet<>(Arrays.asList(receiveUnit));
                Set<String> set2 = new HashSet<>(Arrays.asList(isReceiveUnit));
                // 求差值
                Set<String> difference = new HashSet<>(set1);
                difference.removeAll(set2);

                //发送通知
                if (minutesDiff > -15 && minutesDiff < -10) {
                    sendWorkNotice(difference, info.getInstructionTitle() + "要求" + info.getHandleTime().toString() + "之内反馈，即将在15分钟后到期，请确保尽快完成规定时间内的任务");
                } else if (minutesDiff > -10 && minutesDiff < -5) {
                    sendWorkNotice(difference, info.getInstructionTitle() + "要求" + info.getHandleTime().toString() + "之内反馈，即将在10分钟后到期，请确保尽快完成规定时间内的任务");
                } else if (minutesDiff > -5 && minutesDiff <= 0) {
                    sendWorkNotice(difference, info.getInstructionTitle() + "要求" + info.getHandleTime().toString() + "之内反馈，即将在5分钟后到期，请确保尽快完成规定时间内的任务");
                } else {
                    sendWorkNotice(difference, info.getInstructionTitle() + "未在办理期限之内反馈，已超出" + info.getMinutesDiff() + "天，请立即完成");
                }
            } else {
                //无部门接收
                String[] split = info.getReceiveUnit().split(",");
                Set<String> difference = new HashSet<>(Arrays.asList(split));
                //发送通知
                if (minutesDiff > -15 && minutesDiff < -10) {
                    sendWorkNotice(difference, info.getInstructionTitle() + "要求" + info.getHandleTime().toString() + "之内反馈，即将在15分钟后到期，请确保尽快完成规定时间内的任务");
                } else if (minutesDiff > -10 && minutesDiff < -5) {
                    sendWorkNotice(difference, info.getInstructionTitle() + "要求" + info.getHandleTime().toString() + "之内反馈，即将在10分钟后到期，请确保尽快完成规定时间内的任务");
                } else if (minutesDiff > -5 && minutesDiff <= 0) {
                    sendWorkNotice(difference, info.getInstructionTitle() + "要求" + info.getHandleTime().toString() + "之内反馈，即将在5分钟后到期，请确保尽快完成规定时间内的任务");
                } else {
                    sendWorkNotice(difference, info.getInstructionTitle() + "未在办理期限之内反馈，已超出" + info.getMinutesDiff() + "天，请立即完成");
                }
            }
        });
        return AjaxResult.success(infos);
    }

    /**
     * 到期提醒功能
     *
     * @return
     */
    @GetMapping("/expirationReminder")
    public AjaxResult expirationReminder() {
        List<Map<String, Object>> notReceiveList = instructionInfoService.expirationReminder();
        return AjaxResult.success(notReceiveList);
    }

    /**
     * 平安指令批量接收流转
     *
     * @param ids
     * @return
     */
    @GetMapping("/batchReceiveMove/{ids}")
    public AjaxResult batchReceiveMove(@PathVariable Long[] ids) {
        instructionInfoService.batchReceiveMove(ids);
        return AjaxResult.success();
    }

    /**
     * 平安指令批量销号
     *
     * @param ids
     * @return
     */
    @GetMapping("/batchEndForPa/{ids}")
    public AjaxResult batchEndForPa(@PathVariable Long[] ids, @RequestParam("content") String content) {
        String msg = instructionInfoService.batchEndForPa(ids, content);
        return AjaxResult.success(msg);
    }


    private void sendWorkNotice(final Set<String> places, final String msg) {


    }

    @Autowired
    private TOpenUndercoverInspectionServiceImpl topenUndercoverInspectionServiceImpl;

    @PostMapping("/export")
    public void export(HttpServletResponse response, InspectionReportParam inspectionReportParam) throws IOException {
        List<List<?>> list = null;
        List<String> sheetNames = new ArrayList<>();
        EasyExcelUtil util = new EasyExcelUtil();
        Report[] reports = Report.values();

        String reportType = inspectionReportParam.getReportType();
        String missionName = inspectionReportParam.getMissionName();
        if ("全部".equals(missionName)) {
            inspectionReportParam.setMissionName("");
        }

        if (StringUtils.isNotBlank(reportType)) {
            if (reportType.equals("全部")) {
                for (Report report : reports) {
                    Class<?> clazz = report.getClassName();
                    util.addClass(clazz);
                    sheetNames.add(report.getType());
                    util.addDynamicHeads(topenUndercoverInspectionServiceImpl.getDynamicHeads(report.getType()));
                }
                list = topenUndercoverInspectionServiceImpl.getExcelDataLists(inspectionReportParam);
            } else {
                for (Report report : reports) {
                    if (reportType.equals(report.getType())) {
                        Class<?> clazz = report.getClassName();
                        if ("乡镇街道反馈分析".equals(reportType)) {
                            String[] countyStrings = {"婺城区", "金东区", "兰溪市", "东阳市", "义乌市", "永康市", "浦江县", "武义县", "磐安县", "开发区"};
                            for (String countyStr : countyStrings) {
                                util.addClass(clazz);
                                sheetNames.add(countyStr);
                                util.addDynamicHeads(topenUndercoverInspectionServiceImpl.getDynamicHeads(reportType));
                            }
                        } else {
                            util.addClass(clazz);
                            sheetNames.add(report.getType());
                            util.addDynamicHeads(topenUndercoverInspectionServiceImpl.getDynamicHeads(reportType));
                        }
                        list = topenUndercoverInspectionServiceImpl.getExcelDataLists(inspectionReportParam);
                    }
                }
            }
        }

        util.exportEasyExcel(response, list, sheetNames);
    }

    /**
     * 导出市级维稳指令
     *
     * @return
     */
    @Log(title = "市级维稳指令导出", businessType = BusinessType.EXPORT)
    @PostMapping("/exportCityInstructionInfo")
    public void exportCityInstructionInfo(HttpServletResponse response, @RequestBody Map<String, Object> params) {
        ExcelUtil<CityInstructionExportRspVo> util = new ExcelUtil<CityInstructionExportRspVo>(CityInstructionExportRspVo.class);
        List<CityInstructionExportRspVo> rspVoList = instructionInfoService.exportCityInstructionInfo(params);
        util.exportExcel(response, rspVoList, "市级维稳指令");
    }

    @GetMapping("/testSendMsg")
    public void testSendMsg(String phone){
        List<String> phones = new ArrayList<>();
        phones.add(phone);
        dingtalkHelper.sendWorkNotificationMsg(phones,"测试发送浙政钉通知");

    }

    /**
     * 接收民转刑数据
     *
     * @param mzxData
     * @return
     */
    @PostMapping("/receiveMzxData")
    public AjaxResult handleManualJson(@RequestBody Map<String, Object> mzxData) {
        String id = (String) mzxData.get("id");
        String token = (String) mzxData.get("token");
        LoginUser loginUserByToken = tokenService.getLoginUserByToken(token);
        if (loginUserByToken == null) {
            return AjaxResult.error("token不存在");
        }
        //存储到Redis库中
        redisCache.setCacheObject(CacheConstants.MZX_ID + id, mzxData, 1, TimeUnit.DAYS);
        return AjaxResult.success("mzx数据传输成功id:" + id);
    }

    /**
     * 根据mzxId 获取民转刑数据
     * @param id
     * @return
     */
    @GetMapping("/getMzxDataById/{id}")
    public AjaxResult getMzxDataById(@PathVariable("id") Long id) {
        Map<String, Object> mzxData = redisCache.getCacheObject(CacheConstants.MZX_ID+id);
        return AjaxResult.success(mzxData);
    }

    /**
     * 获取人员关联的指令列表
     * @param id
     * @return
     */
    @GetMapping(value = "/getContactInstructionByPersonId/{id}")
    public AjaxResult getContactInstructionByPersonId(@PathVariable("id") Long id) {
        List<InstructionInfo> instructionInfoList = infoMapper.getContactInstructionByPersonId(id);
        return AjaxResult.success(instructionInfoList);
    }

    /**
     * 处理驳回、撤回指令
     * @param id
     * @param linkType 1:处置 2:反馈
     * @param instructionType 1:市级  2:县市区
     * @return
     */
    @GetMapping("/turnDownInstruction")
    public AjaxResult turnDownInstruction(@RequestParam("id") Long id, @RequestParam("linkType") Integer linkType,@RequestParam("instructionType") Integer instructionType,@RequestParam(value = "rejectReason", required = false) String rejectReason) {
        boolean isRejectOperation = (rejectReason != null && !rejectReason.isEmpty());
        if (isRejectOperation) {
            String username = SecurityUtils.getUsername();

            if (instructionType == 1 && linkType == 2) {
                // 县级反馈表 (county_feedback)
                InstructionCountyFeedback feedbackToReject = new InstructionCountyFeedback();
                feedbackToReject.setId(id);
                feedbackToReject.setIsEnd(3);
                feedbackToReject.setRejectReason(rejectReason);
                feedbackToReject.setRejectBy(username);
                feedbackToReject.setRejectTime(new Date());
                countyFeedbackMapper.updateInstructionCountyFeedback(feedbackToReject);

            } else if ((instructionType == 1 && linkType == 1) || (instructionType == 2 && linkType == 1)) {
                // 目标：反馈主表 (feedback)
                InstructionFeedback feedbackToReject = new InstructionFeedback();
                feedbackToReject.setId(id);
                feedbackToReject.setFeedbackIsEnd(3);
                feedbackToReject.setRejectReason(rejectReason);
                feedbackToReject.setRejectBy(username);
                feedbackToReject.setRejectTime(new Date());
                feedbackMapper.updateInstructionFeedback(feedbackToReject);
            }

        } else {
            if (instructionType == 1 && linkType == 2) {
                // 目标：县级反馈表 (county_feedback)
                InstructionCountyFeedback countyFeedback = new InstructionCountyFeedback();
                countyFeedback.setId(id);
                countyFeedback.setIsEnd(2); // 状态2: 撤销办结
                countyFeedbackMapper.updateInstructionCountyFeedback(countyFeedback);

            } else if ((instructionType == 1 && linkType == 1) || (instructionType == 2 && linkType == 1)) {
                // 目标：反馈主表 (feedback)
                InstructionFeedback feedback = new InstructionFeedback();
                feedback.setId(id);
                feedback.setFeedbackIsEnd(2); // 状态2: 撤销办结
                feedbackMapper.updateInstructionFeedback(feedback);
            }
        }
        return AjaxResult.success();
    }

    /**
     * 获取mzx统计数据
     * @return
     */
    @GetMapping("/getMzxStatistics")
    public AjaxResult getMzxStatistics(){
        AjaxResult ajaxResult = AjaxResult.success();
        //获取交办信息
        Map assign = infoMapper.getMzxAssign();
        //获取销号信息
        Map end =  infoMapper.getMzxEnd();
        //获取最新20条数据
        List<Map<String,Object>> list = infoMapper.getMzxInstructionLast20();
        ajaxResult.put("assign",assign);
        ajaxResult.put("end",end);
        ajaxResult.put("list",list);
        return ajaxResult;
    }

    /**
     * 获取双排双办、矛盾风险预警 化解中、超期化解数、销号数
     * @return
     */
    @GetMapping("/getReportOne")
    public AjaxResult getReportOne(InstructionInfo info){
        Map<String,Object> map = new HashMap<>();
        map.put("spsbHjz", 0);
        map.put("spsbCqhj", 0);
        map.put("spsbXh", 0);
        map.put("yjHjz", 0);
        map.put("yjCqhj", 0);
        map.put("yjXh", 0);
        //获取数据
        List<InstructionInfo> list = infoMapper.getReportOne(info);
        if (list != null && list.size() > 0) {
            long spsbHjz = 0;
            long spsbCqhj = 0;
            long spsbXh = 0;
            long yjHjz = 0;
            long yjCqhj = 0;
            long yjXh = 0;
            if (info.getCounty() != null) {
                Long countyDeptIdByName = StringUtils.getCountyDeptIdByName(info.getCounty());
                spsbHjz = list.stream().filter(item -> item.getInstructionType() == 3 && item.getCreateDeptId().equals(countyDeptIdByName) && item.getEndTime() == null).count();
                spsbCqhj = list.stream().filter(item -> item.getInstructionType() == 3 && item.getCreateDeptId().equals(countyDeptIdByName) && item.getRemark().equals("超期化解")).count();
                spsbXh = list.stream().filter(item -> item.getInstructionType() == 3 && item.getCreateDeptId().equals(countyDeptIdByName) && item.getRemark().equals("") && item.getEndTime() != null).count();
                yjHjz = list.stream().filter(item -> item.getInstructionType() == 6 && item.getCreateDeptId().equals(countyDeptIdByName) && item.getEndTime() == null).count();
                yjCqhj = list.stream().filter(item -> item.getInstructionType() == 6 && item.getCreateDeptId().equals(countyDeptIdByName) && item.getRemark().equals("超期化解")).count();
                yjXh = list.stream().filter(item -> item.getInstructionType() == 6 && item.getCreateDeptId().equals(countyDeptIdByName) && item.getRemark().equals("") && item.getEndTime() != null).count();

            } else {
                spsbHjz = list.stream().filter(item -> item.getInstructionType() == 3 && item.getEndTime() == null).count();
                spsbCqhj = list.stream().filter(item -> item.getInstructionType() == 3 && item.getRemark().equals("超期化解")).count();
                spsbXh = list.stream().filter(item -> item.getInstructionType() == 3 && item.getRemark().equals("") && item.getEndTime() != null).count();
                yjHjz = list.stream().filter(item -> item.getInstructionType() == 6 && item.getEndTime() == null).count();
                yjCqhj = list.stream().filter(item -> item.getInstructionType() == 6 && item.getRemark().equals("超期化解")).count();
                yjXh = list.stream().filter(item -> item.getInstructionType() == 6 && item.getRemark().equals("") && item.getEndTime() != null).count();
            }
            map.put("spsbHjz", spsbHjz);
            map.put("spsbCqhj", spsbCqhj);
            map.put("spsbXh", spsbXh);
            map.put("yjHjz", yjHjz);
            map.put("yjCqhj", yjCqhj);
            map.put("yjXh", yjXh);
        }
        return AjaxResult.success(map);
    }


    /**
     *
     * @param response
     * @param params
     * @throws IOException
     */
    @PostMapping("/exportPoorTracking")
    public void export(HttpServletResponse response, @RequestBody Map<String, Object> params) throws IOException {
        List<InstructionInfo> list1 = infoMapper.getReceivePoor(202L, "",params);
        List<InstructionInfo> list = getReceivePoorIds(list1);
        List<PoorTrackRspVo> poorTrackList = new ArrayList<>();
        if (list.size() != 0) {
            for (InstructionInfo instructionInfo : list) {
                PoorTrackRspVo poorTrackRspVo = new PoorTrackRspVo();
                BeanUtils.copyProperties(instructionInfo, poorTrackRspVo);
                if (instructionInfo.getInstructionType() == 3) {
                    poorTrackRspVo.setInstructionTypeStr("双排双办");
                } else if (instructionInfo.getInstructionType() == 6) {
                    poorTrackRspVo.setInstructionTypeStr("预警研判");
                }
                if (instructionInfo.getSpareNum() != null && instructionInfo.getSpareNum() == 1) {
                    poorTrackRspVo.setType("协办单位");
                } else if (instructionInfo.getSpareNum() != null && instructionInfo.getSpareNum() == 2) {
                    poorTrackRspVo.setType("主办单位");
                }
                poorTrackList.add(poorTrackRspVo);
            }
        }
        try {
            response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
            response.setCharacterEncoding("utf-8");
            // 这里URLEncoder.encode可以防止中文乱码 当然和easyexcel没有关系
            String fileName = URLEncoder.encode("测试", "UTF-8").replaceAll("\\+", "%20");
            response.setHeader("Content-disposition", "attachment;filename*=utf-8''" + fileName + ".xlsx");
            ExcelWriter excelWriter = EasyExcel.write(response.getOutputStream()).build();

            List<FeedbackPoorRsp> feedbackPoorRspList = infoMapper.getFeedbackPoor(params,1);

            //表格1:写入统计
            // 把sheet设置为不需要头 不然会输出sheet的头 这样看起来第一个table 就有2个头了
            WriteSheet threeSheet = EasyExcel.writerSheet("统计结果").needHead(Boolean.FALSE).build();
            // 这里必须指定需要头，table 会继承sheet的配置，sheet配置了不需要，table 默认也是不需要
            WriteTable writeTable0 = EasyExcel.writerTable(0).head(PoorTrackStatisRspVo.class).needHead(Boolean.TRUE).build();
            // 第二个对象 读取对象的excel实体类中的标题
            WriteTable writeTable1 = EasyExcel.writerTable(1).head(PoorFeedbackTrackStatisRspVo.class).needHead(Boolean.TRUE).build();

            // 1. 计算出“接收不力统计”的最终数据
            List<PoorTrackStatisRspVo> receiveStatisList = data(poorTrackList, params);

            // 2. 将“接收不力统计”的数据写入第一个表格
            excelWriter.write(receiveStatisList, threeSheet, writeTable0);

            // 3. 调用新的 getFeedbackData 方法，传入“接收统计”的结果作为基准
            List<PoorFeedbackTrackStatisRspVo> feedbackStatisList = getFeedbackData(receiveStatisList, feedbackPoorRspList);

            // 4. 将生成好的、符合您需求的数据写入第二个表格
            excelWriter.write(feedbackStatisList, threeSheet, writeTable1);

            //表格2:写入接收不力
            WriteSheet writeSheet = EasyExcel.writerSheet(0, "接收不力明细").head(PoorTrackRspVo.class).build();
            excelWriter.write(poorTrackList, writeSheet);

            //表格3:写入处置不力
            WriteSheet twoSheet = EasyExcel.writerSheet(1, "处置不力明细").head(FeedbackPoorRsp.class).build();
            excelWriter.write(feedbackPoorRspList, twoSheet);
            // 关闭 ExcelWriter
            excelWriter.finish();
        } catch (UnsupportedEncodingException e) {
            // 处理编码异常
            System.err.println("编码错误: " + e.getMessage());
        }

    }

    /**
     * 根据“接收统计”和“处置明细”生成最终的“处置统计”数据。
     * @param receiveStatisList “接收不力统计”的结果，作为数据基准。
     * @param feedbackPoorRspList “处置不力”的真实明细数据（可能为空）。
     * @return “处置不力统计”的最终列表
     */
    private List<PoorFeedbackTrackStatisRspVo> getFeedbackData(
            List<PoorTrackStatisRspVo> receiveStatisList,
            final List<FeedbackPoorRsp> feedbackPoorRspList) {

        // 如果基准数据（接收统计）为空，直接返回空结果
        if (receiveStatisList == null || receiveStatisList.isEmpty()) {
            return new ArrayList<>();
        }

        List<PoorFeedbackTrackStatisRspVo> allFeedbackStatis = new ArrayList<>();

        // 遍历“接收不力统计”中的每一个单位，先生成完整的处置统计列表
        for (PoorTrackStatisRspVo receiveStatis : receiveStatisList) {
            String dept = receiveStatis.getUnitName();
            if (dept == null) continue;

            PoorFeedbackTrackStatisRspVo feedbackStatis = new PoorFeedbackTrackStatisRspVo();
            feedbackStatis.setUnitName(dept);

            // “应处置指令数”完全等于“应接收指令数”
            feedbackStatis.setReceiveNum(Long.valueOf(receiveStatis.getReceiveNum()));
            feedbackStatis.setCollaborationNum(Long.valueOf(receiveStatis.getCollaborationNum()));

            // “未及时处置指令数”需要从真实的处置不力明细中计算
            long receiveTimeOutNum = 0;
            long collaborationTimeOutNum = 0;

            if (feedbackPoorRspList != null) {
                receiveTimeOutNum = feedbackPoorRspList.stream().filter(item -> item != null && Objects.equals(item.getReceiveDept(), dept) && "主办单位".equals(item.getIsMzx())).count();

                collaborationTimeOutNum = feedbackPoorRspList.stream().filter(item -> item != null && Objects.equals(item.getReceiveDept(), dept) && "协办单位".equals(item.getIsMzx())).count();
            }

            feedbackStatis.setReceiveTimeOutNum(receiveTimeOutNum);
            feedbackStatis.setCollaborationTimeOutNum(collaborationTimeOutNum);

            allFeedbackStatis.add(feedbackStatis);
        }

        // 使用 stream().filter() 过滤掉“未及时处置数”总和为0的记录
        List<PoorFeedbackTrackStatisRspVo> finalFeedbackStatisList = allFeedbackStatis.stream().filter(item -> (item.getReceiveTimeOutNum() + item.getCollaborationTimeOutNum()) > 0).collect(Collectors.toList());

        return finalFeedbackStatisList;
    }

    /**
     * 统计出接收不力数据
     * @param poorTrackList
     * @param params
     * @return
     */
    private List<PoorTrackStatisRspVo> data(final List<PoorTrackRspVo> poorTrackList, final Map<String, Object> params) {
        List<PoorTrackStatisRspVo> poorTrackStatisRspVos = new ArrayList<>();
        //获取接收数据
        List<PoorReceiveOneRsp> receiveData =infoMapper.getReceiveData(params);
        if (receiveData != null && receiveData.size() > 0) {
            //receiveData数据根据receiveDept进行去重 返回List<String>
            List<String> receiveDept = receiveData.stream().map(PoorReceiveOneRsp::getReceiveDept).distinct().collect(Collectors.toList());
            for (String dept : receiveDept) {
                PoorTrackStatisRspVo poorTrackStatisRspVo = new PoorTrackStatisRspVo();
                poorTrackStatisRspVo.setUnitName(dept);
                //查询出主责单位接收指令数
                Integer receiveNum = receiveData.stream().filter(item -> item.getReceiveDept().equals(dept) && item.getIsMzx() == 2).mapToInt(PoorReceiveOneRsp::getAcount).sum();
                poorTrackStatisRspVo.setReceiveNum(receiveNum);
                //查询出协同单位接收数据
                Integer collaborationNum = receiveData.stream().filter(item -> item.getReceiveDept().equals(dept) && item.getIsMzx() == 1).mapToInt(PoorReceiveOneRsp::getAcount).sum();
                poorTrackStatisRspVo.setCollaborationNum(collaborationNum);
                //查询出主责单位未及时接收指令数
                long receiveTimeOutNum = poorTrackList.stream().filter(item -> item != null && Objects.equals(item.getReceiveStr(), dept) && "主办单位".equals(item.getType())).count();
                poorTrackStatisRspVo.setReceiveTimeOutNum(receiveTimeOutNum);
                //查询出协同单位未及时接收指令数
                long collaborationTimeOutNum = poorTrackList.stream().filter(item -> item != null && Objects.equals(item.getReceiveStr(), dept) && "协办单位".equals(item.getType())).count();
                poorTrackStatisRspVo.setCollaborationTimeOutNum(collaborationTimeOutNum);
                poorTrackStatisRspVos.add(poorTrackStatisRspVo);
            }
        }
        return poorTrackStatisRspVos;
    }

    private List<InstructionInfo> getReceivePoorIds(final List<InstructionInfo> list) {
        List<InstructionInfo> ids = new ArrayList<>();
        if (list != null && list.size() > 0) {
            for (InstructionInfo info : list) {
                Date updateTime = info.getUpdateTime();
                if (info.getUpdateTime() == null) {
                    updateTime = new Date();
                }
                double timeInterval = DateUtils.calculateWorkHoursBetween(info.getAssignTime(), updateTime);
                if (info.getEmergencyDegree().equals("一般") && timeInterval > 24) {
                    ids.add(info);
                } else if (info.getEmergencyDegree().equals("紧急") && timeInterval > 12) {
                    ids.add(info);
                } else if (info.getEmergencyDegree().equals("特急") && timeInterval > 2) {
                    ids.add(info);
                }
            }
        }
        return ids;
    }

    @Autowired
    private RemindRecordTask remindRecordTask;

    @GetMapping("/testNewFeedback")
    public void testNewFeedback() {
        remindRecordTask.newFrequencyFeedbackWorkNotice();
    }


    // @PreAuthorize("@ss.hasPermi('instruction:info:transfer')")
    @Log(title = "指令移交", businessType = BusinessType.UPDATE)
    @PostMapping("/transfer/{id}/{reasonType}")
    public AjaxResult transfer(@PathVariable("id") Long instructionId,@PathVariable("reasonType") Integer transferReasonType) {
        instructionInfoService.transferInstructionToWarning(instructionId, transferReasonType);
        return AjaxResult.success("移交成功");
    }

}
