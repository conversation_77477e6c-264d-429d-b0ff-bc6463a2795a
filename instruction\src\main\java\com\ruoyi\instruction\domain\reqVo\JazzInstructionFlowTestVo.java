package com.ruoyi.instruction.domain.reqVo;

import com.ruoyi.instruction.domain.*;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/1/6 10:25
 */
@Data
public class JazzInstructionFlowTestVo {

    /**
     * 指令id
     */
    private Long infoId;

    /**
     * 交办栏信息
     */
    private JazzInstructionAssign assign;

    /**
     * 接收实体类集合
     */
    private List<JazzInstructionReceive> receiveList;


//    /**
//     * 处置信息实体类集合
//     */
//    private List<InstructionDispose> disposeList;


    /**
     * 反馈信息实体类集合
     */
    private List<JazzInstructionFeedback> feedbackList;

    /**
     * 销号实体类集合
     */
    private List<JazzInstructionEnd> instructionEnd;


}
