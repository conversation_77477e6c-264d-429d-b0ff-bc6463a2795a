package com.ruoyi.instruction.domain.rspVo;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import lombok.Data;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2025/4/23 9:54
 */
@Data
public class PoorTrackStatisRspVo {

    @ColumnWidth(20)
    @ExcelProperty({"接收不力统计", "单位名称"})
    private String unitName;

    @ColumnWidth(20)
    @ExcelProperty({"接收不力统计", "作为主责单位不力数据", "应接收指令数"})
    private Integer receiveNum;

    @ColumnWidth(25)
    @ExcelProperty({"接收不力统计", "作为主责单位不力数据", "未及时接收指令数"})
    private Long receiveTimeOutNum;

    @ColumnWidth(20)
    @ExcelProperty({"接收不力统计", "作为协同单位不力数据", "应接收指令数"})
    private Integer collaborationNum;

    @ColumnWidth(25)
    @ExcelProperty({"接收不力统计", "作为协同单位不力数据", "未及时接收指令数"})
    private Long collaborationTimeOutNum;

}
