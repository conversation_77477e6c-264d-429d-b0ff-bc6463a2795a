package com.ruoyi.instruction.controller;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import javax.servlet.http.HttpServletResponse;

import cn.hutool.http.HttpResponse;
import cn.hutool.http.HttpUtil;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.ruoyi.common.utils.AESUtil;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.instruction.domain.BizType;
import com.ruoyi.instruction.domain.BizYjSource;
import com.ruoyi.instruction.mapper.YjEventMapper;
import com.ruoyi.instruction.service.IYjEventPersonService;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.instruction.domain.YjEvent;
import com.ruoyi.instruction.service.IYjEventService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 人员风险值预估Controller
 *
 * <AUTHOR>
 * @date 2024-12-10
 */
@RestController
@RequestMapping("/wz/event")
public class YjEventController extends BaseController
{
    @Autowired
    private IYjEventService yjEventService;


    /**
     * 查询人员风险值预估列表
     */
    // @PreAuthorize("@ss.hasPermi('wz:event:list')")
    @GetMapping("/list")
    public TableDataInfo list(YjEvent yjEvent)
    {
        if (yjEvent.getUsername()!=null||yjEvent.getIdCard()!=null){
            List<String> eventIds = yjEventService.findEventIds(yjEvent);
            if (eventIds.size() > 0){
                Map<String, Object> params = yjEvent.getParams();
                params.put("eventIds", eventIds);
                yjEvent.setParams(params);
            }else {
                return getDataTable(new ArrayList<>());
            }
        }
        boolean flag = false;
        if (yjEvent.getAssignStatus()!=null&&yjEvent.getAssignStatus().equals("已移交")){
            yjEvent.setDealType(1);
            yjEvent.setAssignStatus(null);
            flag = true;
        }

        startPage();
        List<YjEvent> list = yjEventService.selectYjEventList(yjEvent);
        //查询出矛盾纠纷预警页面，同个人涉及过个事项id
        List<String> eventIds = yjEventService.findPersonEventIds();
        boolean finalFlag = flag;
        list.stream().forEach(event -> {
            if (eventIds.contains(event.getId())){
                event.setIsMore(1);
            }
            if (finalFlag && event.getDealType()==1){
                event.setAssignStatus("已移交");
            }
        });
        return getDataTable(list);
    }

    /**
     * 导出人员风险值预估列表
     */
    // @PreAuthorize("@ss.hasPermi('wz:event:export')")
    @Log(title = "人员风险值预估", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, YjEvent yjEvent)
    {
        List<YjEvent> list = yjEventService.selectYjEventList(yjEvent);
        ExcelUtil<YjEvent> util = new ExcelUtil<YjEvent>(YjEvent.class);
        util.exportExcel(response, list, "人员风险值预估数据");
    }

    /**
     * 获取人员风险值预估详细信息
     */
    // @PreAuthorize("@ss.hasPermi('wz:event:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        YjEvent yjEvent = yjEventService.selectYjEventById(id);
        if (yjEvent.getAreaCode() != null) {
            String areaCode = yjEvent.getAreaCode();
            if (areaCode.contains("330700")) {
                yjEvent.setDutyPlace("金华市");
            } else if (areaCode.contains("330702")) {
                yjEvent.setDutyPlace("婺城区");
            } else if (areaCode.contains("330703")) {
                yjEvent.setDutyPlace("金东区");
            } else if (areaCode.contains("330704")) {
                yjEvent.setDutyPlace("开发区");
            } else if (areaCode.contains("330723")) {
                yjEvent.setDutyPlace("武义县");
            } else if (areaCode.contains("330726")) {
                yjEvent.setDutyPlace("浦江县");
            } else if (areaCode.contains("330727")) {
                yjEvent.setDutyPlace("磐安县");
            } else if (areaCode.contains("330781")) {
                yjEvent.setDutyPlace("兰溪市");
            } else if (areaCode.contains("330782")) {
                yjEvent.setDutyPlace("义乌市");
            } else if (areaCode.contains("330783")) {
                yjEvent.setDutyPlace("东阳市");
            } else if (areaCode.contains("330784")) {
                yjEvent.setDutyPlace("永康市");
            }
        }
        return AjaxResult.success(yjEvent);
    }

    /**
     * 新增人员风险值预估
     */
    // @PreAuthorize("@ss.hasPermi('wz:event:add')")
    @Log(title = "人员风险值预估", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody YjEvent yjEvent)
    {
        return toAjax(yjEventService.insertYjEvent(yjEvent));
    }

    /**
     * 修改人员风险值预估
     */
    // @PreAuthorize("@ss.hasPermi('wz:event:edit')")
    @Log(title = "人员风险值预估", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody YjEvent yjEvent)
    {
        return toAjax(yjEventService.updateYjEvent(yjEvent));
    }

    /**
     * 删除人员风险值预估
     */
    // @PreAuthorize("@ss.hasPermi('wz:event:remove')")
    @Log(title = "人员风险值预估", businessType = BusinessType.DELETE)
	@DeleteMapping("/{id}")
    public AjaxResult remove(@PathVariable String id)
    {
        return toAjax(yjEventService.deleteYjEventById(id));
    }

    @DeleteMapping("/delFullEvent")
    public AjaxResult delFullEvent(YjEvent event)
    {
        return toAjax(yjEventService.delFullEvent(event));
    }


    /**
     * 获取事件类型
     * @return
     */
    @GetMapping("/getBizType")
    public AjaxResult getBizType(){
        List<Map<String,Object>> maps = yjEventService.getBizType();
        return AjaxResult.success(maps);
    }

    /**
     * 获取数据来源
     * @return
     */
    @GetMapping("/getBizSource")
    public AjaxResult getBizSource(){
        List<BizYjSource> yjSources = yjEventService.getYjSourceList();
        return AjaxResult.success(yjSources);
    }

    /**
     * 根据人员身份证获取人员信息
     * @param idCard
     * @return
     */
    @GetMapping("/getPersonInfoByIdCard")
    public AjaxResult getPersonInfoByIdCard(String idCard) throws Exception {
        long timestamp = System.currentTimeMillis();
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("timestemp",timestamp);
        jsonObject.put("idCard",idCard);
        //json转字符串
        String json = JSON.toJSONString(jsonObject);
        String encrypt = AESUtil.encrypt(json);
        HttpResponse response = HttpUtil.createPost("https://jazz.swzfw.jinhua.gov.cn:5443/shujinApi/fulEvent/getPersonInfoByIdcard").body(encrypt,"text/plain").execute();
        String body = response.body();
        System.out.println(body);
        //字符串转json
        JSONObject jsonObject1 = JSON.parseObject(body);
        String data = (String) jsonObject1.get("data");
        if (data == null){
            return AjaxResult.error("未查询到数据");
        }
        String decrypt = AESUtil.decrypt(data);
        //字符串转json
        JSONObject jsonObject2 = JSON.parseObject(decrypt);
        if (jsonObject2 != null) {
            String areaCode = (String) jsonObject2.get("areaCode");
            jsonObject2.put("area", getArea(areaCode));
        }
        return AjaxResult.success(jsonObject2);

    }

    /**
     * 根据Id获取事件详情
     * @param id
     * @return
     * @throws Exception
     */
    @GetMapping("/getFulEventById")
    public AjaxResult getFulEventById(String id) throws Exception {
        long timestamp = System.currentTimeMillis();
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("timestemp",timestamp);
        jsonObject.put("yjEvtId",id);
        //json转字符串
        String json = JSON.toJSONString(jsonObject);
        String encrypt = AESUtil.encrypt(json);
        HttpResponse response = HttpUtil.createPost("https://jazz.swzfw.jinhua.gov.cn:5443/shujinApi/fulEvent/findPage?current="+1+"&size="+100).body(encrypt,"text/plain").execute();
        String body = response.body();
        System.out.println(body);
        //字符串转json
        JSONObject jsonObject1 = JSON.parseObject(body);
        String data = (String) jsonObject1.get("data");
        if (data == null){
            return AjaxResult.error("未查询到数据");
        }
        String decrypt = AESUtil.decrypt(data);
        //字符串转json
        JSONObject jsonObject2 = JSON.parseObject(decrypt);
        return AjaxResult.success(jsonObject2);
    }

    /**
     * 根据人员身份证查询关联事件列表
     * @param idCard
     * @return
     */
    @GetMapping("/findEventPageByPersonPartIn")
    public AjaxResult findEventPageByPersonPartIn(String idCard,String current,String size) throws Exception {
        long timestamp = System.currentTimeMillis();
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("timestemp",timestamp);
        jsonObject.put("idCard",idCard);
        //json转字符串
        String json = JSON.toJSONString(jsonObject);
        String encrypt = AESUtil.encrypt(json);
        String url = "https://jazz.swzfw.jinhua.gov.cn:5443/shujinApi/fulEvent/findEventPageByPersonPartIn?current="+current+"&size="+size;
        HttpResponse response = HttpUtil.createPost(url).body(encrypt,"text/plain").execute();

        String body = response.body();
        //字符串转json
        JSONObject jsonObject1 = JSON.parseObject(body);
        String data = (String) jsonObject1.get("data");
        String decrypt = AESUtil.decrypt(data);
        //字符串转json
        JSONObject jsonObject2 = JSON.parseObject(decrypt);
        Integer total = (Integer) jsonObject2.get("total");
        if (total!=0){
            //查询事件类型表
            List<BizType> bizTypeList = yjEventService.getBizTypeList();
            JSONArray jsonArray = jsonObject2.getJSONArray("records");
            //获取数据来源映射信息
            List<BizYjSource> yjSources = yjEventService.getYjSourceList();
            //遍历
            for (int i = 0; i < jsonArray.size(); i++) {
                JSONObject jsonObject3 = jsonArray.getJSONObject(i);
                String areaCode = (String) jsonObject3.get("areaCode");
                jsonObject3.put("area", getArea(areaCode));
                String evtType = (String) jsonObject3.get("evtType");
                String evtTypeName = bizTypeList.stream().filter(bizType -> String.valueOf(bizType.getId()).equals(evtType)).map(BizType::getName).findFirst().orElse("");
                jsonObject3.put("evtTypeName",evtTypeName);
                String source = (String) jsonObject3.get("source");
                String sourceName = yjSources.stream().filter(bizYjSource -> bizYjSource.getSource().equals(source)).map(BizYjSource::getSouceName).findFirst().orElse("");
                jsonObject3.put("sourceName",sourceName);
            }
        }
        return AjaxResult.success(jsonObject2);
    }

    public String getArea(String areaCode){
        String area = "";
        if (StringUtils.isEmpty(areaCode)){
            area = "金华市";
        }else if (areaCode.contains("330700")){
            area = "金华市";
        }else if (areaCode.contains("330702")){
            area = "婺城区";
        }else if (areaCode.contains("330703")){
            area = "金东区";
        }else if (areaCode.contains("330704")){
            area = "开发区";
        }else if (areaCode.contains("330723")){
            area = "武义县";
        }else if (areaCode.contains("330726")){
            area = "浦江县";
        }else if (areaCode.contains("330727")){
            area = "磐安县";
        }else if (areaCode.contains("330781")){
            area = "兰溪市";
        }else if (areaCode.contains("330782")){
            area = "义乌市";
        }else if (areaCode.contains("330783")){
            area = "东阳市";
        }else if (areaCode.contains("330784")){
            area = "永康市";
        }
        return area;
    }
}
