package com.ruoyi.instruction.controller;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import javax.servlet.http.HttpServletResponse;

import com.ruoyi.common.core.domain.entity.SysDictData;
import com.ruoyi.common.utils.IDCardUtils;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.instruction.domain.GkPerson;
import com.ruoyi.instruction.service.IGkPersonService;
import com.ruoyi.system.service.ISysDictDataService;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.instruction.domain.QbtbZwwRyyj;
import com.ruoyi.instruction.service.IQbtbZwwRyyjService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 人员预警Controller
 *
 * <AUTHOR>
 * @date 2023-06-03
 */
@RestController
@RequestMapping("/instruction/ryyj")
public class QbtbZwwRyyjController extends BaseController {
    @Autowired
    private IQbtbZwwRyyjService qbtbZwwRyyjService;

    @Autowired
    private IGkPersonService gkPersonService;

    @Autowired
    private ISysDictDataService dictDataService;

    /**
     * 查询人员预警列表
     */
    @PreAuthorize("@ss.hasPermi('instruction:ryyj:list')")
    @GetMapping("/list")
    public TableDataInfo list(QbtbZwwRyyj qbtbZwwRyyj) {
        List<QbtbZwwRyyj> list = new ArrayList<>();
        //查询未撤销布控的人员身份证
        GkPerson gkPerson = new GkPerson();
        gkPerson.setLkzllx("11");
        gkPerson.setStatus("1");
        List<GkPerson> gkPeople = gkPersonService.selectGkPersonList(gkPerson);
        List<String> ids = new ArrayList<>();
        if (gkPeople != null && gkPeople.size() > 0) {
            gkPeople.stream().forEach(gkPerson1 -> {
                //解密身份证
                String idCard = IDCardUtils.decryptIDCard(gkPerson1.getSfzh());
                ids.add(idCard);
            });
        } else {
            return getDataTable(list);
        }
        qbtbZwwRyyj.setIds(ids);
        //查询已经处理人员预警事件id
        List<Map<String, Integer>> disposeMap = gkPersonService.findDisposeMap();
        List<String> gongan_event_id = disposeMap.stream().map(m -> m.get("gongan_event_id").toString()).collect(Collectors.toList());
        qbtbZwwRyyj.setDisposeIds(gongan_event_id);
        startPage();
        list = qbtbZwwRyyjService.selectQbtbZwwRyyjList(qbtbZwwRyyj);
        list.stream().forEach(qbtbZwwRyyj1 -> {
            Long aLong = qbtbZwwRyyj1.getnXh();
            for (Map<String, Integer> map : disposeMap) {
                Long gongan_event_id1 = Long.valueOf(map.get("gongan_event_id"));
                if (gongan_event_id1.equals(aLong)) {
                    String type = String.valueOf(map.get("type"));
                    String infoId = String.valueOf(map.get("infoId"));
                    qbtbZwwRyyj1.setInfoId(Integer.valueOf(infoId));
                    qbtbZwwRyyj1.setDisposeType(Integer.valueOf(type));
                    continue;
                }
            }
        });
        return getDataTable(list);
    }

    /**
     * 查看预警信息
     * @param qbtbZwwRyyj
     * @return
     */
    @GetMapping("/listForBigScreen")
    public TableDataInfo listForBigScreen(QbtbZwwRyyj qbtbZwwRyyj) {
        List<QbtbZwwRyyj> list = new ArrayList<>();
        //查询未撤销布控的人员身份证
        GkPerson gkPerson = new GkPerson();
        gkPerson.setLkzllx("11");
        gkPerson.setStatus("1");
        List<GkPerson> gkPeople = gkPersonService.selectGkPersonList(gkPerson);
        List<String> ids = new ArrayList<>();
        if (gkPeople != null && gkPeople.size() > 0) {
            gkPeople.stream().forEach(gkPerson1 -> {
                //解密身份证
                String idCard = IDCardUtils.decryptIDCard(gkPerson1.getSfzh());
                ids.add(idCard);
            });
        } else {
            return getDataTable(list);
        }
        qbtbZwwRyyj.setIds(ids);
        //查询已经处理人员预警事件id
        List<Map<String, Integer>> disposeMap = gkPersonService.findDisposeMap();
        List<String> gongan_event_id = disposeMap.stream().map(m -> m.get("gongan_event_id").toString()).collect(Collectors.toList());
        qbtbZwwRyyj.setDisposeIds(gongan_event_id);
        SysDictData dictData = new SysDictData();
        dictData.setDictType("controller_person_type");
        List<SysDictData> dictDataList = dictDataService.selectDictDataList(dictData);
        startPage();
        list = qbtbZwwRyyjService.selectQbtbZwwRyyjList(qbtbZwwRyyj);
        list.stream().forEach(qbtbZwwRyyj1 -> {
            String rylb = qbtbZwwRyyj1.getRylb();
            SysDictData sysDictData1 = dictDataList.stream().filter(sysDictData -> sysDictData.getDictValue().equals(rylb)).findFirst().orElse(null);
            if (sysDictData1!=null){
                qbtbZwwRyyj1.setTslx(sysDictData1.getDictLabel());
            }
            qbtbZwwRyyj1.setSjmc(qbtbZwwRyyj1.getHdxgxx());
            String hdfsddxz = qbtbZwwRyyj1.getHdfsddxz();
            String chineseSubstring = StringUtils.getChineseSubstring(hdfsddxz);
            if (!StringUtils.isEmpty(chineseSubstring)){
                String stringBeforeUnderscore = StringUtils.getStringBeforeUnderscore(chineseSubstring);
                qbtbZwwRyyj1.setHdfsddxz(stringBeforeUnderscore);
                Long aLong = qbtbZwwRyyj1.getnXh();
                for (Map<String, Integer> map : disposeMap) {
                    Long gongan_event_id1 = Long.valueOf(map.get("gongan_event_id"));
                    if (gongan_event_id1.equals(aLong)) {
                        String type = String.valueOf(map.get("type"));
                        String infoId = String.valueOf(map.get("infoId"));
                        qbtbZwwRyyj1.setInfoId(Integer.valueOf(infoId));
                        qbtbZwwRyyj1.setDisposeType(Integer.valueOf(type));
                        continue;
                    }
                }
            }

        });
        return getDataTable(list);
    }

    /**
     * 导出人员预警列表
     */
    @PreAuthorize("@ss.hasPermi('instruction:ryyj:export')")
    @Log(title = "人员预警", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, QbtbZwwRyyj qbtbZwwRyyj) {
        List<QbtbZwwRyyj> list = qbtbZwwRyyjService.selectQbtbZwwRyyjList(qbtbZwwRyyj);
        ExcelUtil<QbtbZwwRyyj> util = new ExcelUtil<QbtbZwwRyyj>(QbtbZwwRyyj.class);
        util.exportExcel(response, list, "人员预警数据");
    }

    /**
     * 获取人员预警详细信息
     */
    @PreAuthorize("@ss.hasPermi('instruction:ryyj:query')")
    @GetMapping(value = "/{nXh}")
    public AjaxResult getInfo(@PathVariable("nXh") Long nXh) {
        return success(qbtbZwwRyyjService.selectQbtbZwwRyyjByNXh(nXh));
    }

    /**
     * 新增人员预警
     */
    @PreAuthorize("@ss.hasPermi('instruction:ryyj:add')")
    @Log(title = "人员预警", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody QbtbZwwRyyj qbtbZwwRyyj) {
        return toAjax(qbtbZwwRyyjService.insertQbtbZwwRyyj(qbtbZwwRyyj));
    }

    /**
     * 修改人员预警
     */
    @PreAuthorize("@ss.hasPermi('instruction:ryyj:edit')")
    @Log(title = "人员预警", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody QbtbZwwRyyj qbtbZwwRyyj) {
        return toAjax(qbtbZwwRyyjService.updateQbtbZwwRyyj(qbtbZwwRyyj));
    }

    /**
     * 删除人员预警
     */
    @PreAuthorize("@ss.hasPermi('instruction:ryyj:remove')")
    @Log(title = "人员预警", businessType = BusinessType.DELETE)
    @DeleteMapping("/{nXhs}")
    public AjaxResult remove(@PathVariable Long[] nXhs) {
        return toAjax(qbtbZwwRyyjService.deleteQbtbZwwRyyjByNXhs(nXhs));
    }


}
