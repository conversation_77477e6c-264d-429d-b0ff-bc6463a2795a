package com.ruoyi.instruction.controller;

import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.examineAir.service.IExamineService;
import com.ruoyi.instruction.domain.InstrucationPerson;
import com.ruoyi.instruction.domain.InstructionEvent;
import com.ruoyi.instruction.domain.rspVo.BigScreenJazzCommonVo;
import com.ruoyi.instruction.domain.rspVo.BigScreenJazzPageJadsjCountVo;
import com.ruoyi.instruction.domain.rspVo.BigScreenJazzPageYearCountVo;
import com.ruoyi.instruction.mapper.InstructionJazzWwEventMapper;
import com.ruoyi.instruction.service.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.time.LocalDate;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 金安智治驾驶舱金安大数据社会安全
 * <AUTHOR> @version 1.0
 * @date 2
 *
 */
@RestController
@RequestMapping("/bigScreen/jazz/societySafe")
public class BigScreenJazzSocietySafeController extends BaseController {

    @Autowired
    private IInstructionEventService instructionEventService;

    @Autowired
    private IInstrucationPersonService iInstrucationPersonService;
    @Autowired
    private IExamineService examineService;

    @Autowired
    private IInstructionEndService instructionEndService;
    @Autowired
    private IInstructionAssignService instructionAssignService;
    @Autowired
    private IInstructionInfoService instructionInfoService;
    @Autowired
    private  IJazzInstructionEventService jazzInstructionEventService;

    @Autowired
    private InstructionJazzWwEventMapper instructionJazzWwEventMapper;

    @Autowired
    private  ITJazzHomicideCaseService jazzHomicideCaseService;


    /**
     * 重点人员
     * @return
     */
    @GetMapping("/pointPerson")
    public AjaxResult pointPerson() {
        InstrucationPerson instrucationPerson=new InstrucationPerson();
        LocalDate current_date = LocalDate.now();
        int current_Year = current_date.getYear();
        instrucationPerson.setCreateTime(DateUtils.getYearFirst(current_Year));
        instrucationPerson.setUpdateTime(new Date());
        List<BigScreenJazzCommonVo> bigScreenJazzCommonVos = iInstrucationPersonService.countPersonByEvent(instrucationPerson);
        return AjaxResult.success(bigScreenJazzCommonVos);
    }


    /**
     * 电信网络诈骗
     * @return
     */
    @GetMapping("/internetFraud")
    public AjaxResult internetFraud() {
       List<BigScreenJazzCommonVo>  list=jazzInstructionEventService.internetFraud();
        return  AjaxResult.success(list);
    }


    /**
     * 事件预警
     * @return
     */
    @GetMapping("/eventWarn")
    public AjaxResult eventWarn() {
        InstructionEvent instructionEvent=new InstructionEvent();
        instructionEvent.setType("9");
        instructionEvent.setInfoCategory("预警信息");
        List<InstructionEvent> instructionEvents = instructionJazzWwEventMapper.selectCountByTypeForDetails(instructionEvent);
        return  AjaxResult.success(instructionEvents);
    }


    /**
     * 命案分析
     * @param type (1人员分析，2区域分析)
     * @return
     */
    @GetMapping("/homicideCasesAnalysis")
    public AjaxResult homicideCasesAnalysis(Integer type) {
       return  AjaxResult.success(jazzHomicideCaseService.homicideCasesAnalysis(type));
    }








}
