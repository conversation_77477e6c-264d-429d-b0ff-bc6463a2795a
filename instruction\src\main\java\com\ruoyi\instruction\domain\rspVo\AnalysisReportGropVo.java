package com.ruoyi.instruction.domain.rspVo;

import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * 群体分析报告
 * <AUTHOR>
 */
@Data
public class AnalysisReportGropVo {
    /**
     * 创建时间
     */
    private String createTime;
    /**
     * 群体名称
     */
    private  String groupName;
    /**
     * 群体关联事件数
     */
    private Integer eventCount;

    /**
     * 群体关联人数
     */
    private Integer personCount;
    /**
     * 群体关联人次
     */
    private Integer personNum;
    /**
     * 挑头人员
     */
    private Integer leadPersonCount;
    /**
     * 挑头人次
     */
    private Integer leadPersonNum;
    /**
     * 挑头人员详情
     */
    private String leadPersonDetails;

    /**
     * 响应人员
     */
    private Integer respPersonCount;
    /**
     * 响应人次
     */
    private Integer respPersonNum;
    /**
     * 响应人员详情
     */
    private String respPersonDetails;
    /**
     * 实地上访人员数
     */
    private Integer  siteVisitsCount;
    /**
     * 实地上访人员数
     */
    private String  siteVisitsDetails;
    /**
     * 来京上访人员数
     */
    private Integer  comeJingCount;
    /**
     * 来省上访人员数
     */
    private Integer  comeProvinceCount;
    /**
     * 来市上访人员数
     */
    private Integer  comeCityCount;
    /**
     * 属地数量
     */
    private Integer  regionCount;
    /**
     * 属地详情
     */
    private String  regionDetails;
    /**
     * 事件
     */
    List<AnalysisReportEventVo> eventList;

    /**
     * 事件属性详情
     */
    private String   eventPropertiesDetails;
    /**
     * 市外人数
     */
    private  String outsidePersonDetails;
}
