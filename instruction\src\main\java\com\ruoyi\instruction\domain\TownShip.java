package com.ruoyi.instruction.domain;

import com.ruoyi.common.annotation.Excel;
import lombok.Data;

@Data
public class TownShip {
    /**
     * 姓名
     */
    @Excel(name = "姓名")
    private String name;

    /**
     * 身份证号
     */
    @Excel(name = "身份证号")
    private String personCard;

    /**
     * 户籍地址
     */
    @Excel(name = "户籍地")
    private String residenceAddress;

    /**
     * 居住地址
     */
    @Excel(name = "现住址")
    private String residentialAddress;

    /**
     * 区县
     */
    @Excel(name = "管控属地")
    private String county;

    /**
     * 乡镇街道
     */
    @Excel(name = "责任乡镇")
    private String street;

    /**
     * 电话号码
     */
    @Excel(name = "联系方式")
    private String phone;

    /**
     * 危险性
     */
    @Excel(name = "类别")
    private String risk;
}
