package com.ruoyi.instruction.controller;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;
import javax.servlet.http.HttpServletResponse;

import com.ruoyi.common.constant.CommonConstant;
import com.ruoyi.common.constant.Constants;
import com.ruoyi.common.constant.InstructionRolesConstants;
import com.ruoyi.common.core.domain.entity.SysRole;
import com.ruoyi.common.utils.IDCardUtils;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.instruction.domain.InstrucationPerson;
import com.ruoyi.instruction.domain.rspVo.InstructionPersonRspVo;
import com.ruoyi.instruction.service.impl.JazzInstructionEventServiceImpl;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.instruction.domain.GkPerson;
import com.ruoyi.instruction.service.IGkPersonService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;
import org.springframework.web.multipart.MultipartFile;

/**
 * 管控人员Controller
 *
 * <AUTHOR>
 * @date 2023-06-01
 */
@RestController
@RequestMapping("/instruction/gkPerson")
public class GkPersonController extends BaseController {


    private static final Logger log = LoggerFactory.getLogger(GkPersonController.class);

    @Autowired
    private IGkPersonService gkPersonService;

    /**
     * 查询管控人员列表
     */
    @PreAuthorize("@ss.hasPermi('instruction:gkPerson:list')")
    @GetMapping("/list")
    public TableDataInfo list(GkPerson gkPerson) {
        //数据过滤 如果为市委政法委则可以看到所有布控人员,其他部门无法查看
        Long deptId = SecurityUtils.getDeptId();
        //根据角色判断是否查询
        List<SysRole> roles = SecurityUtils.getLoginUser().getUser().getRoles();
        List<String> roleList = roles.stream().map(SysRole::getRoleKey).collect(Collectors.toList());
        gkPerson.setCreateDeptId(deptId);
        if (roleList.contains(InstructionRolesConstants.GK_PERSON_ADMIN) || deptId.equals(Constants.JINHUA_CITY_DEPT_ID)) {
            gkPerson.setCreateDeptId(null);
        }
        if (gkPerson.getShzt() != null && gkPerson.getShzt().equals("4")) {
            gkPerson.setShzt(null);
        }
        startPage();
        List<GkPerson> list = gkPersonService.selectGkPersonList(gkPerson);
        return getDataTable(list);
    }

    /**
     * 导出管控人员列表
     */
    @PreAuthorize("@ss.hasPermi('instruction:gkPerson:export')")
    @Log(title = "管控人员", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, GkPerson gkPerson) {
        List<GkPerson> list = gkPersonService.selectGkPersonList(gkPerson);
        list.stream().forEach(gkPerson1 -> {
            //解密人员身份证 并隐藏后六位
            try {
                //审核人身份证加密
                if (gkPerson1.getSqrsfzh() != null && gkPerson1.getSqrsfzh().length() > 7) {
                    String sqrsfzh = gkPerson1.getSqrsfzh();
                    String finalyIdCard1 = sqrsfzh.substring(0, sqrsfzh.length() - 6) + "******";
                    gkPerson1.setSqrsfzh(finalyIdCard1);
                }
                String idCard = IDCardUtils.decryptIDCard(gkPerson1.getSfzh());
                gkPerson1.setSfzh(idCard);
                if (idCard.length() > 6) {
                    String finalyIdCard = idCard.substring(0, idCard.length() - 6) + "******";
                    gkPerson1.setSfzh(finalyIdCard);
                }
            } catch (Exception e) {
                log.error(e.getMessage());
            }

        });
        ExcelUtil<GkPerson> util = new ExcelUtil<GkPerson>(GkPerson.class);
        util.exportExcel(response, list, "管控人员数据");
    }

    /**
     * 获取管控人员详细信息
     */
    @PreAuthorize("@ss.hasPermi('instruction:gkPerson:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id) {
        return success(gkPersonService.selectGkPersonById(id));
    }

    /**
     * 新增管控人员
     */
    @PreAuthorize("@ss.hasPermi('instruction:gkPerson:add')")
    @Log(title = "管控人员", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody GkPerson gkPerson) {
        return toAjax(gkPersonService.insertGkPerson(gkPerson));
    }

    /**
     * 修改管控人员
     */
    @PreAuthorize("@ss.hasPermi('instruction:gkPerson:edit')")
    @Log(title = "管控人员", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody GkPerson gkPerson) {
        return toAjax(gkPersonService.updateGkPerson(gkPerson));
    }

    /**
     * 删除管控人员
     */
    @PreAuthorize("@ss.hasPermi('instruction:gkPerson:remove')")
    @Log(title = "管控人员", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids) {
        return toAjax(gkPersonService.deleteGkPersonByIds(ids));
    }

    /**
     * 审核申请管控人员
     *
     * @param gkPerson
     * @return
     */
    @PreAuthorize("@ss.hasPermi('instruction:gkPerson:audit')")
    @Log(title = "管控人员", businessType = BusinessType.INSERT)
    @PutMapping("/audit")
    public AjaxResult audit(@RequestBody GkPerson gkPerson) {
        return toAjax(gkPersonService.auditGkPerson(gkPerson));
    }


    /**
     * 下载人员导入模板
     *
     * @param response
     */
    @PostMapping("/importTemplate")
    public void importTemplate(HttpServletResponse response) {
        ExcelUtil<GkPerson> util = new ExcelUtil<GkPerson>(GkPerson.class);
        util.importTemplateExcel(response, "布控人员模板");
    }

    /**
     * 导入布控人员数据
     */
    @Log(title = "重点人员", businessType = BusinessType.IMPORT)
    @PostMapping("/importPersonData")
    public AjaxResult importPersonData(MultipartFile file, GkPerson gkPerson) throws Exception {
        ExcelUtil<GkPerson> util = new ExcelUtil<GkPerson>(GkPerson.class);
        List<GkPerson> gkPersonList = util.importExcel(file.getInputStream());
        //存入数据
        String msg = gkPersonService.importPerson(gkPersonList, gkPerson);
        return AjaxResult.success(msg);
    }

    /**
     * 根据重点人员ids进行布控
     *
     * @param gkPerson
     * @return
     */
    @PostMapping("/addByPersonIds")
    public AjaxResult addByPersonIds(@RequestBody GkPerson gkPerson) {
        return gkPersonService.addByPersonIds(gkPerson);
    }

    /**
     * 重点人员取消布控
     *
     * @param gkPerson
     * @return
     */
    @PostMapping("/cancelGkByPersonIds")
    public AjaxResult cancelGkByPersonIds(@RequestBody GkPerson gkPerson) {
        return gkPersonService.cancelGkByPersonIds(gkPerson);
    }

    /**
     * 通过人员id批量布控人员
     *
     * @param gkPerson
     * @return
     */
    @PostMapping("/confirmGkByIds")
    public AjaxResult confirmGkByIds(@RequestBody GkPerson gkPerson) {
        return gkPersonService.confirmGkByIds(gkPerson);
    }

    /**
     * 通过人员ids批量驳回
     *
     * @param ids
     * @param info
     * @return
     */
    @PostMapping("/turnDownByPersonIds")
    public AjaxResult turnDownByPersonIds(String ids, String info) {
        return gkPersonService.turnDownByPersonIds(ids, info);
    }

    /**
     * 取消管控by管控人员id
     *
     * @param gkPerson
     * @return
     */
    @PostMapping("/cancelGkByGkId")
    public AjaxResult cancelGkByGkId(@RequestBody GkPerson gkPerson) {
        return gkPersonService.cancelGkByGkId(gkPerson);
    }

    /**
     * 市本级确认县市区取消布控
     */
    @PostMapping("/confirmCancelGkByGkId")
    public AjaxResult confirmCancelGkByGkId(@RequestBody GkPerson gkPerson) {
        gkPersonService.confirmCancelGkByGkId(gkPerson);
        return AjaxResult.success();
    }

}
