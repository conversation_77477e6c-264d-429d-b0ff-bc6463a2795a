package com.ruoyi.instruction.mapper;

import com.ruoyi.instruction.domain.RiskCheck;
import com.ruoyi.instruction.domain.RiskCheckVo;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 金安稳风险排查Mapper接口
 *
 * <AUTHOR>
 * @date 2025/3/5
 */
@Mapper
public interface JawRiskCheckingMapper {
    List<RiskCheckVo> selectrisklevel(RiskCheck riskCheck);

    List<RiskCheckVo> selectRiskType(RiskCheck riskCheck);
    List<RiskCheckVo> selectCategory();

    List<RiskCheckVo> selectRiskCounties(RiskCheck riskCheck);

    RiskCheckVo selectRiskSum(RiskCheck riskCheck);

    List<RiskCheckVo> selectRiskLevelResolve(RiskCheck riskCheck);

    RiskCheckVo selectRiskLevelResolvePt(RiskCheckVo riskCheckVo);

    List<RiskCheckVo> selectRiskDistributionResolveLeft(RiskCheck riskCheck);

    RiskCheckVo selectRiskDistributionResolveLeftPt(RiskCheckVo riskCheckVo);

    List<RiskCheckVo> selectRiskDistributionResolveRight(RiskCheck riskCheck);

    RiskCheckVo selectRiskDistributionResolveRightPt(RiskCheckVo riskCheckVo);
}
