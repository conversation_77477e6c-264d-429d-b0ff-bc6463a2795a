package com.ruoyi.instruction.domain;

import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * 三感三率对象 t_three_senses
 *
 * <AUTHOR>
 * @date 2024-04-02
 */
@Data
public class ThreeSenses extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键id */
    private Long id;

    /** 区域 */
    @Excel(name = "区域",combo = {"浙江省","金华市","婺城区","金东区","兰溪市","东阳市","武义县","义乌市","浦江县","永康市","磐安县","开发区"})
    @NotBlank(message = "区域不能为空")
    private String area;

    /** 年度 */
    @Excel(name = "年度")
    @NotNull(message = "年度不能为空")
    private Long yearNum;

    /** 满意度 */
    @Excel(name = "满意度")
    @NotNull(message = "满意度不能为空")
    private Double satisfaction;

    /** 知晓度 */
    @Excel(name = "知晓度")
    @NotNull(message = "知晓度不能为空")
    private Double awareness;

    /** 参与度 */
    @Excel(name = "参与度")
    @NotNull(message = "参与度不能为空")
    private Double participation;

    /** 1:正常  9:删除 */
    private Integer status;

    /** 是否是浙江省 0：不是 1：是 */
    private Integer isProvince;

    /**
     * 排序字段
     * 1:年度降序 2:年度升序 3:满意度降序 4:满意度升序 5:知晓度降序 6:知晓度升序 7:参与度降序 8:参与度升序
     */
    private Integer orderNum;
}
