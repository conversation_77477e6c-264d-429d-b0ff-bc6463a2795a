<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.instruction.mapper.CityInstructionHandlingMapper">

    <!-- 获取超时接收数量 -->
    <select id="getTimeoutReceiveCount" resultType="java.lang.Long">
        SELECT
        a.*,
        b.receive_dept AS receive_str,
        b.receive_time AS update_time,
        b.is_mzx AS spare_num
        FROM
        (
        SELECT
        id,
        instruction_title,
        instruction_type,
        emergency_degree,
        assign_time,
        handle_time,
        CASE
        WHEN emergency_degree = '一般' THEN
        DATE_ADD( assign_time, INTERVAL 24 HOUR )
        WHEN emergency_degree = '紧急' THEN
        DATE_ADD( assign_time, INTERVAL 12 HOUR )
        WHEN emergency_degree = '特急' THEN
        DATE_ADD( assign_time, INTERVAL 2 HOUR ) ELSE assign_time
        END AS create_time
        FROM
        t_instruction_info
        WHERE
        STATUS = 1
        AND instruction_type IN ( 3, 6 )
        <if test="params != null and params.beginTime != null and params.beginTime != ''"><!-- 开始时间检索 -->
            AND date_format(assign_time,'%y%m%d') &gt;= date_format(#{params.beginTime},'%y%m%d')
        </if>
        <if test="params != null and params.endTime != null and params.endTime != ''"><!-- 结束时间检索 -->
            AND date_format(assign_time,'%y%m%d') &lt;= date_format(#{params.endTime},'%y%m%d')
        </if>
        <if test="params != null and params.isInclude != null and params.isInclude == 0">
            AND instruction_type = 3
        </if>
        <if test='deptId != null  and deptId != "" and deptId != "-1"'>AND create_dept_id = #{deptId}</if>
        <if test='deptId != null  and deptId != "" and deptId == "-1"'>AND create_dept_id != 202</if>
        ) a
        LEFT JOIN ( SELECT id, receive_dept, IFNULL(receive_time,NOW()) now_time,receive_time, instrucation_id,is_mzx
        FROM t_instruction_receive WHERE STATUS = 1 ) b ON a.id = b.instrucation_id
        where a.create_time &lt; b.now_time
        <if test="deptName != null  and deptName != ''">and b.receive_dept = #{deptName}</if>
    </select>

    <!-- 获取超时处置数量 -->
    <select id="getTimeoutDisposeCount" resultType="java.lang.Long">
        SELECT COUNT(DISTINCT a.id) as count
        FROM t_instruction_info a
        INNER JOIN t_instruction_receive b ON a.id = b.instrucation_id
        LEFT JOIN (
            SELECT instruction_id, receive_id, MAX(feedback_time) as last_feedback_time
            FROM t_instruction_feedback
            WHERE status = 1
            GROUP BY instruction_id, receive_id
        ) f ON a.id = f.instruction_id AND b.id = f.receive_id
        WHERE a.status = 1
          AND a.create_dept_id = 202
          AND a.instruction_type IN (3, 6)
          AND a.end_time IS NULL
          AND b.status = 1
          AND b.receive_time IS NOT NULL
          AND (f.last_feedback_time IS NULL OR
               DATEDIFF(NOW(), f.last_feedback_time) >
               CASE a.emergency_degree
                   WHEN '特急' THEN 7
                   WHEN '紧急' THEN 15
                   WHEN '一般' THEN 30
                   ELSE 30
               END)
        <if test="params.startTime != null and params.startTime != ''">
            AND DATE_FORMAT(a.assign_time, '%Y-%m-%d') &gt;= #{params.startTime}
        </if>
        <if test="params.endTime != null and params.endTime != ''">
            AND DATE_FORMAT(a.assign_time, '%Y-%m-%d') &lt;= #{params.endTime}
        </if>
        <if test="params.currentArea != null and params.currentArea != ''">
            AND b.receive_dept LIKE CONCAT(#{params.currentArea}, '%')
        </if>
    </select>

    <!-- 获取应处置未处置数据 -->
    <select id="getUnprocessedData" resultType="java.util.Map">
        SELECT
            COUNT(DISTINCT a.id) as unitCount,
            COUNT(DISTINCT b.receive_dept) as deptCount
        FROM t_instruction_info a
        INNER JOIN t_instruction_receive b ON a.id = b.instrucation_id
        LEFT JOIN (
            SELECT instruction_id, receive_id, MAX(feedback_time) as last_feedback_time
            FROM t_instruction_feedback
            WHERE status = 1 AND feedback_is_end != 1
            GROUP BY instruction_id, receive_id
        ) f ON a.id = f.instruction_id AND b.id = f.receive_id
        WHERE a.status = 1
          AND a.create_dept_id = 202
          AND a.instruction_type IN (3, 6)
          AND a.end_time IS NULL
          AND b.status = 1
          AND b.receive_time IS NOT NULL
          AND (f.last_feedback_time IS NULL OR
               DATEDIFF(NOW(), COALESCE(f.last_feedback_time, b.receive_time)) >
               CASE a.emergency_degree
                   WHEN '特急' THEN 7
                   WHEN '紧急' THEN 15
                   WHEN '一般' THEN 30
                   ELSE 30
               END)
        <if test="params.startTime != null and params.startTime != ''">
            AND DATE_FORMAT(a.assign_time, '%Y-%m-%d') &gt;= #{params.startTime}
        </if>
        <if test="params.endTime != null and params.endTime != ''">
            AND DATE_FORMAT(a.assign_time, '%Y-%m-%d') &lt;= #{params.endTime}
        </if>
        <if test="params.currentArea != null and params.currentArea != ''">
            AND b.receive_dept LIKE CONCAT(#{params.currentArea}, '%')
        </if>
    </select>

    <!-- 获取应处置未处置数据（区分部门和镇街） -->
    <select id="getUnprocessedDataDetailed" resultType="java.util.Map">
        SELECT
            -- 县级部门统计
            COUNT(DISTINCT CASE
                WHEN b.receive_dept IN ('磐安县', '兰溪市', '东阳市', '义乌市', '浦江县', '永康市', '金东区', '婺城区', '开发区', '武义县')
                THEN b.receive_dept
                ELSE NULL
            END) as unprocessedCountyDeptCount,
            COUNT(DISTINCT CASE
                WHEN b.receive_dept IN ('磐安县', '兰溪市', '东阳市', '义乌市', '浦江县', '永康市', '金东区', '婺城区', '开发区', '武义县')
                THEN a.id
                ELSE NULL
            END) as unprocessedCountyInstructionCount,
            -- 乡镇街道统计
            COUNT(DISTINCT CASE
                WHEN b.receive_dept NOT IN ('磐安县', '兰溪市', '东阳市', '义乌市', '浦江县', '永康市', '金东区', '婺城区', '开发区', '武义县')
                THEN b.receive_dept
                ELSE NULL
            END) as unprocessedTownDeptCount,
            COUNT(DISTINCT CASE
                WHEN b.receive_dept NOT IN ('磐安县', '兰溪市', '东阳市', '义乌市', '浦江县', '永康市', '金东区', '婺城区', '开发区', '武义县')
                THEN a.id
                ELSE NULL
            END) as unprocessedTownInstructionCount
        FROM t_instruction_info a
        INNER JOIN t_instruction_receive b ON a.id = b.instrucation_id
        LEFT JOIN (
            SELECT instruction_id, receive_id, MAX(feedback_time) as last_feedback_time
            FROM t_instruction_feedback
            WHERE status = 1 AND feedback_is_end != 1
            GROUP BY instruction_id, receive_id
        ) f ON a.id = f.instruction_id AND b.id = f.receive_id
        WHERE a.status = 1
          AND a.create_dept_id = 202
          AND a.instruction_type IN (3, 6)
          AND a.end_time IS NULL
          AND b.status = 1
          AND b.receive_time IS NOT NULL
          AND (f.last_feedback_time IS NULL OR
               DATEDIFF(NOW(), COALESCE(f.last_feedback_time, b.receive_time)) >
               CASE a.emergency_degree
                   WHEN '特急' THEN 7
                   WHEN '紧急' THEN 15
                   WHEN '一般' THEN 30
                   ELSE 30
               END)
        <if test="params.startTime != null and params.startTime != ''">
            AND DATE_FORMAT(a.assign_time, '%Y-%m-%d') &gt;= #{params.startTime}
        </if>
        <if test="params.endTime != null and params.endTime != ''">
            AND DATE_FORMAT(a.assign_time, '%Y-%m-%d') &lt;= #{params.endTime}
        </if>
        <if test="params.currentArea != null and params.currentArea != ''">
            AND b.receive_dept LIKE CONCAT(#{params.currentArea}, '%')
        </if>
    </select>

    <!-- 获取待反馈数据（按紧急程度分类） -->
    <select id="getPendingFeedbackData" resultType="java.util.Map">
        SELECT
            SUM(CASE WHEN a.emergency_degree = '特急' THEN 1 ELSE 0 END) as days7,
            SUM(CASE WHEN a.emergency_degree = '紧急' THEN 1 ELSE 0 END) as days15,
            SUM(CASE WHEN a.emergency_degree = '一般' THEN 1 ELSE 0 END) as days30
        FROM t_instruction_info a
        INNER JOIN t_instruction_receive b ON a.id = b.instrucation_id
        INNER JOIN t_instruction_transfer t ON b.id = t.receive_id
        LEFT JOIN (
            SELECT instruction_id, receive_id, MAX(feedback_time) as last_feedback_time,
                   MAX(CASE WHEN feedback_is_end = 1 THEN 1 ELSE 0 END) as is_completed
            FROM t_instruction_feedback
            WHERE status = 1
            GROUP BY instruction_id, receive_id
        ) f ON a.id = f.instruction_id AND b.id = f.receive_id
        WHERE a.status = 1
          AND a.create_dept_id = 202
          AND a.instruction_type IN (3, 6)
          AND a.end_time IS NULL
          AND b.status = 1
          AND t.status = 1
          AND b.receive_time IS NOT NULL
          AND (f.is_completed IS NULL OR f.is_completed = 0)
          AND (
              (f.last_feedback_time IS NOT NULL AND
               DATEDIFF(NOW(), f.last_feedback_time) >=
               CASE a.emergency_degree
                   WHEN '特急' THEN 7
                   WHEN '紧急' THEN 15
                   WHEN '一般' THEN 30
                   ELSE 30
               END)
              OR
              (f.last_feedback_time IS NULL AND t.transfer_time IS NOT NULL AND
               DATEDIFF(NOW(), t.transfer_time) >=
               CASE a.emergency_degree
                   WHEN '特急' THEN 7
                   WHEN '紧急' THEN 15
                   WHEN '一般' THEN 30
                   ELSE 30
               END)
          )
        <if test="params.startTime != null and params.startTime != ''">
            AND DATE_FORMAT(a.assign_time, '%Y-%m-%d') &gt;= #{params.startTime}
        </if>
        <if test="params.endTime != null and params.endTime != ''">
            AND DATE_FORMAT(a.assign_time, '%Y-%m-%d') &lt;= #{params.endTime}
        </if>
        <if test="params.currentArea != null and params.currentArea != ''">
            AND b.receive_dept LIKE CONCAT(#{params.currentArea}, '%')
        </if>
    </select>

    <!-- 获取超时接收指令列表 -->
    <select id="getTimeoutReceiveList" resultType="java.util.Map">
        SELECT DISTINCT
            a.id,
            a.instruction_title as instructionTitle,
            a.emergency_degree as emergencyDegree,
            CASE a.instruction_type
                WHEN 3 THEN '双排双办'
                WHEN 6 THEN '预警研判'
                ELSE '其他'
            END as instructionType,
            a.assign_time as assignTime,
            a.handle_time as handleTime,
            (SELECT b2.receive_dept FROM t_instruction_receive b2
             WHERE b2.instrucation_id = a.id AND b2.status = 1
             AND b2.receive_time IS NULL LIMIT 1) as receiveDept,
            (SELECT CASE b2.is_mzx
                WHEN 1 THEN '协办单位'
                WHEN 2 THEN '主办单位'
                ELSE '未知'
            END FROM t_instruction_receive b2
             WHERE b2.instrucation_id = a.id AND b2.status = 1
             AND b2.receive_time IS NULL LIMIT 1) as unitType,
            DATEDIFF(NOW(), a.assign_time) as overdueDays
        FROM t_instruction_info a
        INNER JOIN t_instruction_receive b ON a.id = b.instrucation_id
        WHERE a.status = 1
          AND a.create_dept_id = 202
          AND a.instruction_type IN (3, 6)
          AND b.status = 1
          AND b.receive_time IS NULL
          AND DATEDIFF(NOW(), a.assign_time) >
              CASE a.emergency_degree
                  WHEN '特急' THEN 1
                  WHEN '紧急' THEN 2
                  WHEN '一般' THEN 3
                  ELSE 3
              END
        <if test="params.startTime != null and params.startTime != ''">
            AND DATE_FORMAT(a.assign_time, '%Y-%m-%d') &gt;= #{params.startTime}
        </if>
        <if test="params.endTime != null and params.endTime != ''">
            AND DATE_FORMAT(a.assign_time, '%Y-%m-%d') &lt;= #{params.endTime}
        </if>
        <if test="params.currentArea != null and params.currentArea != ''">
            AND b.receive_dept LIKE CONCAT(#{params.currentArea}, '%')
        </if>
        ORDER BY a.assign_time DESC
    </select>

    <!-- 获取超时处置指令列表 -->
    <select id="getTimeoutDisposeList" resultType="java.util.Map">
        SELECT DISTINCT
            a.id,
            a.instruction_title as instructionTitle,
            a.emergency_degree as emergencyDegree,
            CASE a.instruction_type
                WHEN 3 THEN '双排双办'
                WHEN 6 THEN '预警研判'
                ELSE '其他'
            END as instructionType,
            a.assign_time as assignTime,
            a.handle_time as handleTime,
            (SELECT b2.receive_dept FROM t_instruction_receive b2
             LEFT JOIN (
                 SELECT instruction_id, receive_id, MAX(feedback_time) as last_feedback_time
                 FROM t_instruction_feedback
                 WHERE status = 1
                 GROUP BY instruction_id, receive_id
             ) f2 ON b2.instrucation_id = f2.instruction_id AND b2.id = f2.receive_id
             WHERE b2.instrucation_id = a.id AND b2.status = 1
             AND b2.receive_time IS NOT NULL
             AND (f2.last_feedback_time IS NULL OR
                  DATEDIFF(NOW(), f2.last_feedback_time) >
                  CASE a.emergency_degree
                      WHEN '特急' THEN 7
                      WHEN '紧急' THEN 15
                      WHEN '一般' THEN 30
                      ELSE 30
                  END)
             <if test="params.currentArea != null and params.currentArea != ''">
                 AND b2.receive_dept LIKE CONCAT(#{params.currentArea}, '%')
             </if>
             LIMIT 1) as receiveDept,
            (SELECT CASE b2.is_mzx
                WHEN 1 THEN '协办单位'
                WHEN 2 THEN '主办单位'
                ELSE '未知'
            END FROM t_instruction_receive b2
             LEFT JOIN (
                 SELECT instruction_id, receive_id, MAX(feedback_time) as last_feedback_time
                 FROM t_instruction_feedback
                 WHERE status = 1
                 GROUP BY instruction_id, receive_id
             ) f2 ON b2.instrucation_id = f2.instruction_id AND b2.id = f2.receive_id
             WHERE b2.instrucation_id = a.id AND b2.status = 1
             AND b2.receive_time IS NOT NULL
             AND (f2.last_feedback_time IS NULL OR
                  DATEDIFF(NOW(), f2.last_feedback_time) >
                  CASE a.emergency_degree
                      WHEN '特急' THEN 7
                      WHEN '紧急' THEN 15
                      WHEN '一般' THEN 30
                      ELSE 30
                  END)
             <if test="params.currentArea != null and params.currentArea != ''">
                 AND b2.receive_dept LIKE CONCAT(#{params.currentArea}, '%')
             </if>
             LIMIT 1) as unitType,
            (SELECT MAX(f2.last_feedback_time) FROM t_instruction_receive b2
             LEFT JOIN (
                 SELECT instruction_id, receive_id, MAX(feedback_time) as last_feedback_time
                 FROM t_instruction_feedback
                 WHERE status = 1
                 GROUP BY instruction_id, receive_id
             ) f2 ON b2.instrucation_id = f2.instruction_id AND b2.id = f2.receive_id
             WHERE b2.instrucation_id = a.id AND b2.status = 1
             AND b2.receive_time IS NOT NULL
             AND (f2.last_feedback_time IS NULL OR
                  DATEDIFF(NOW(), f2.last_feedback_time) >
                  CASE a.emergency_degree
                      WHEN '特急' THEN 7
                      WHEN '紧急' THEN 15
                      WHEN '一般' THEN 30
                      ELSE 30
                  END)
             <if test="params.currentArea != null and params.currentArea != ''">
                 AND b2.receive_dept LIKE CONCAT(#{params.currentArea}, '%')
             </if>) as lastFeedbackTime,
            (SELECT MAX(DATEDIFF(NOW(), COALESCE(f2.last_feedback_time, b2.receive_time)))
             FROM t_instruction_receive b2
             LEFT JOIN (
                 SELECT instruction_id, receive_id, MAX(feedback_time) as last_feedback_time
                 FROM t_instruction_feedback
                 WHERE status = 1
                 GROUP BY instruction_id, receive_id
             ) f2 ON b2.instrucation_id = f2.instruction_id AND b2.id = f2.receive_id
             WHERE b2.instrucation_id = a.id AND b2.status = 1
             AND b2.receive_time IS NOT NULL
             AND (f2.last_feedback_time IS NULL OR
                  DATEDIFF(NOW(), f2.last_feedback_time) >
                  CASE a.emergency_degree
                      WHEN '特急' THEN 7
                      WHEN '紧急' THEN 15
                      WHEN '一般' THEN 30
                      ELSE 30
                  END)
             <if test="params.currentArea != null and params.currentArea != ''">
                 AND b2.receive_dept LIKE CONCAT(#{params.currentArea}, '%')
             </if>) as overdueDays
        FROM t_instruction_info a
        INNER JOIN t_instruction_receive b ON a.id = b.instrucation_id
        LEFT JOIN (
            SELECT instruction_id, receive_id, MAX(feedback_time) as last_feedback_time
            FROM t_instruction_feedback
            WHERE status = 1
            GROUP BY instruction_id, receive_id
        ) f ON a.id = f.instruction_id AND b.id = f.receive_id
        WHERE a.status = 1
          AND a.create_dept_id = 202
          AND a.instruction_type IN (3, 6)
          AND a.end_time IS NULL
          AND b.status = 1
          AND b.receive_time IS NOT NULL
          AND (f.last_feedback_time IS NULL OR
               DATEDIFF(NOW(), f.last_feedback_time) >
               CASE a.emergency_degree
                   WHEN '特急' THEN 7
                   WHEN '紧急' THEN 15
                   WHEN '一般' THEN 30
                   ELSE 30
               END)
        <if test="params.startTime != null and params.startTime != ''">
            AND DATE_FORMAT(a.assign_time, '%Y-%m-%d') &gt;= #{params.startTime}
        </if>
        <if test="params.endTime != null and params.endTime != ''">
            AND DATE_FORMAT(a.assign_time, '%Y-%m-%d') &lt;= #{params.endTime}
        </if>
        <if test="params.currentArea != null and params.currentArea != ''">
            AND b.receive_dept LIKE CONCAT(#{params.currentArea}, '%')
        </if>
        ORDER BY a.assign_time DESC
    </select>

    <!-- 获取应处置未处置指令列表 -->
    <select id="getUnprocessedList" resultType="java.util.Map">
        SELECT DISTINCT
            a.id,
            a.instruction_title as instructionTitle,
            a.emergency_degree as emergencyDegree,
            CASE a.instruction_type
                WHEN 3 THEN '双排双办'
                WHEN 6 THEN '预警研判'
                ELSE '其他'
            END as instructionType,
            a.assign_time as assignTime,
            a.handle_time as handleTime,
            (SELECT b2.receive_dept FROM t_instruction_receive b2
             LEFT JOIN (
                 SELECT instruction_id, receive_id, MAX(feedback_time) as last_feedback_time
                 FROM t_instruction_feedback
                 WHERE status = 1 AND feedback_is_end != 1
                 GROUP BY instruction_id, receive_id
             ) f2 ON b2.instrucation_id = f2.instruction_id AND b2.id = f2.receive_id
             WHERE b2.instrucation_id = a.id AND b2.status = 1
             AND b2.receive_time IS NOT NULL
             AND (f2.last_feedback_time IS NULL OR
                  DATEDIFF(NOW(), COALESCE(f2.last_feedback_time, b2.receive_time)) >
                  CASE a.emergency_degree
                      WHEN '特急' THEN 7
                      WHEN '紧急' THEN 15
                      WHEN '一般' THEN 30
                      ELSE 30
                  END)
             <if test="params.currentArea != null and params.currentArea != ''">
                 AND b2.receive_dept LIKE CONCAT(#{params.currentArea}, '%')
             </if>
             LIMIT 1) as receiveDept,
            (SELECT CASE b2.is_mzx
                WHEN 1 THEN '协办单位'
                WHEN 2 THEN '主办单位'
                ELSE '未知'
            END FROM t_instruction_receive b2
             LEFT JOIN (
                 SELECT instruction_id, receive_id, MAX(feedback_time) as last_feedback_time
                 FROM t_instruction_feedback
                 WHERE status = 1 AND feedback_is_end != 1
                 GROUP BY instruction_id, receive_id
             ) f2 ON b2.instrucation_id = f2.instruction_id AND b2.id = f2.receive_id
             WHERE b2.instrucation_id = a.id AND b2.status = 1
             AND b2.receive_time IS NOT NULL
             AND (f2.last_feedback_time IS NULL OR
                  DATEDIFF(NOW(), COALESCE(f2.last_feedback_time, b2.receive_time)) >
                  CASE a.emergency_degree
                      WHEN '特急' THEN 7
                      WHEN '紧急' THEN 15
                      WHEN '一般' THEN 30
                      ELSE 30
                  END)
             <if test="params.currentArea != null and params.currentArea != ''">
                 AND b2.receive_dept LIKE CONCAT(#{params.currentArea}, '%')
             </if>
             LIMIT 1) as unitType,
            (SELECT MAX(f2.last_feedback_time) FROM t_instruction_receive b2
             LEFT JOIN (
                 SELECT instruction_id, receive_id, MAX(feedback_time) as last_feedback_time
                 FROM t_instruction_feedback
                 WHERE status = 1 AND feedback_is_end != 1
                 GROUP BY instruction_id, receive_id
             ) f2 ON b2.instrucation_id = f2.instruction_id AND b2.id = f2.receive_id
             WHERE b2.instrucation_id = a.id AND b2.status = 1
             AND b2.receive_time IS NOT NULL
             AND (f2.last_feedback_time IS NULL OR
                  DATEDIFF(NOW(), COALESCE(f2.last_feedback_time, b2.receive_time)) >
                  CASE a.emergency_degree
                      WHEN '特急' THEN 7
                      WHEN '紧急' THEN 15
                      WHEN '一般' THEN 30
                      ELSE 30
                  END)
             <if test="params.currentArea != null and params.currentArea != ''">
                 AND b2.receive_dept LIKE CONCAT(#{params.currentArea}, '%')
             </if>) as lastFeedbackTime,
            (SELECT MAX(DATEDIFF(NOW(), COALESCE(f2.last_feedback_time, b2.receive_time)))
             FROM t_instruction_receive b2
             LEFT JOIN (
                 SELECT instruction_id, receive_id, MAX(feedback_time) as last_feedback_time
                 FROM t_instruction_feedback
                 WHERE status = 1 AND feedback_is_end != 1
                 GROUP BY instruction_id, receive_id
             ) f2 ON b2.instrucation_id = f2.instruction_id AND b2.id = f2.receive_id
             WHERE b2.instrucation_id = a.id AND b2.status = 1
             AND b2.receive_time IS NOT NULL
             AND (f2.last_feedback_time IS NULL OR
                  DATEDIFF(NOW(), COALESCE(f2.last_feedback_time, b2.receive_time)) >
                  CASE a.emergency_degree
                      WHEN '特急' THEN 7
                      WHEN '紧急' THEN 15
                      WHEN '一般' THEN 30
                      ELSE 30
                  END)
             <if test="params.currentArea != null and params.currentArea != ''">
                 AND b2.receive_dept LIKE CONCAT(#{params.currentArea}, '%')
             </if>) as overdueDays
        FROM t_instruction_info a
        INNER JOIN t_instruction_receive b ON a.id = b.instrucation_id
        LEFT JOIN (
            SELECT instruction_id, receive_id, MAX(feedback_time) as last_feedback_time
            FROM t_instruction_feedback
            WHERE status = 1 AND feedback_is_end != 1
            GROUP BY instruction_id, receive_id
        ) f ON a.id = f.instruction_id AND b.id = f.receive_id
        WHERE a.status = 1
          AND a.create_dept_id = 202
          AND a.instruction_type IN (3, 6)
          AND a.end_time IS NULL
          AND b.status = 1
          AND b.receive_time IS NOT NULL
          AND (f.last_feedback_time IS NULL OR
               DATEDIFF(NOW(), COALESCE(f.last_feedback_time, b.receive_time)) >
               CASE a.emergency_degree
                   WHEN '特急' THEN 7
                   WHEN '紧急' THEN 15
                   WHEN '一般' THEN 30
                   ELSE 30
               END)
        <if test="params.startTime != null and params.startTime != ''">
            AND DATE_FORMAT(a.assign_time, '%Y-%m-%d') &gt;= #{params.startTime}
        </if>
        <if test="params.endTime != null and params.endTime != ''">
            AND DATE_FORMAT(a.assign_time, '%Y-%m-%d') &lt;= #{params.endTime}
        </if>
        <if test="params.currentArea != null and params.currentArea != ''">
            AND b.receive_dept LIKE CONCAT(#{params.currentArea}, '%')
        </if>
        ORDER BY a.assign_time DESC
    </select>

    <!-- 获取待反馈指令列表（按紧急程度分类） -->
    <select id="getPendingFeedbackList" resultType="java.util.Map">
        SELECT DISTINCT
            a.id,
            a.instruction_title as instructionTitle,
            a.emergency_degree as emergencyDegree,
            CASE a.instruction_type
                WHEN 3 THEN '双排双办'
                WHEN 6 THEN '预警研判'
                ELSE '其他'
            END as instructionType,
            a.assign_time as assignTime,
            a.handle_time as handleTime,
            (SELECT b2.receive_dept FROM t_instruction_receive b2
             INNER JOIN t_instruction_transfer t2 ON b2.id = t2.receive_id
             LEFT JOIN (
                 SELECT instruction_id, receive_id, MAX(feedback_time) as last_feedback_time,
                        MAX(CASE WHEN feedback_is_end = 1 THEN 1 ELSE 0 END) as is_completed
                 FROM t_instruction_feedback
                 WHERE status = 1
                 GROUP BY instruction_id, receive_id
             ) f2 ON b2.instrucation_id = f2.instruction_id AND b2.id = f2.receive_id
             WHERE b2.instrucation_id = a.id AND b2.status = 1 AND t2.status = 1
             AND b2.receive_time IS NOT NULL
             AND (f2.is_completed IS NULL OR f2.is_completed = 0)
             <if test="params.currentArea != null and params.currentArea != ''">
                 AND b2.receive_dept LIKE CONCAT(#{params.currentArea}, '%')
             </if>
             LIMIT 1) as receiveDept,
            (SELECT t2.transfer_dept FROM t_instruction_receive b2
             INNER JOIN t_instruction_transfer t2 ON b2.id = t2.receive_id
             LEFT JOIN (
                 SELECT instruction_id, receive_id, MAX(feedback_time) as last_feedback_time,
                        MAX(CASE WHEN feedback_is_end = 1 THEN 1 ELSE 0 END) as is_completed
                 FROM t_instruction_feedback
                 WHERE status = 1
                 GROUP BY instruction_id, receive_id
             ) f2 ON b2.instrucation_id = f2.instruction_id AND b2.id = f2.receive_id
             WHERE b2.instrucation_id = a.id AND b2.status = 1 AND t2.status = 1
             AND b2.receive_time IS NOT NULL
             AND (f2.is_completed IS NULL OR f2.is_completed = 0)
             <if test="params.currentArea != null and params.currentArea != ''">
                 AND b2.receive_dept LIKE CONCAT(#{params.currentArea}, '%')
             </if>
             LIMIT 1) as transferDept,
            (SELECT CASE b2.is_mzx
                WHEN 1 THEN '协办单位'
                WHEN 2 THEN '主办单位'
                ELSE '未知'
            END FROM t_instruction_receive b2
             INNER JOIN t_instruction_transfer t2 ON b2.id = t2.receive_id
             LEFT JOIN (
                 SELECT instruction_id, receive_id, MAX(feedback_time) as last_feedback_time,
                        MAX(CASE WHEN feedback_is_end = 1 THEN 1 ELSE 0 END) as is_completed
                 FROM t_instruction_feedback
                 WHERE status = 1
                 GROUP BY instruction_id, receive_id
             ) f2 ON b2.instrucation_id = f2.instruction_id AND b2.id = f2.receive_id
             WHERE b2.instrucation_id = a.id AND b2.status = 1 AND t2.status = 1
             AND b2.receive_time IS NOT NULL
             AND (f2.is_completed IS NULL OR f2.is_completed = 0)
             <if test="params.currentArea != null and params.currentArea != ''">
                 AND b2.receive_dept LIKE CONCAT(#{params.currentArea}, '%')
             </if>
             LIMIT 1) as unitType,
            (SELECT MAX(f2.last_feedback_time) FROM t_instruction_receive b2
             INNER JOIN t_instruction_transfer t2 ON b2.id = t2.receive_id
             LEFT JOIN (
                 SELECT instruction_id, receive_id, MAX(feedback_time) as last_feedback_time,
                        MAX(CASE WHEN feedback_is_end = 1 THEN 1 ELSE 0 END) as is_completed
                 FROM t_instruction_feedback
                 WHERE status = 1
                 GROUP BY instruction_id, receive_id
             ) f2 ON b2.instrucation_id = f2.instruction_id AND b2.id = f2.receive_id
             WHERE b2.instrucation_id = a.id AND b2.status = 1 AND t2.status = 1
             AND b2.receive_time IS NOT NULL
             AND (f2.is_completed IS NULL OR f2.is_completed = 0)
             <if test="params.currentArea != null and params.currentArea != ''">
                 AND b2.receive_dept LIKE CONCAT(#{params.currentArea}, '%')
             </if>) as lastFeedbackTime,
            (SELECT t2.transfer_time FROM t_instruction_receive b2
             INNER JOIN t_instruction_transfer t2 ON b2.id = t2.receive_id
             LEFT JOIN (
                 SELECT instruction_id, receive_id, MAX(feedback_time) as last_feedback_time,
                        MAX(CASE WHEN feedback_is_end = 1 THEN 1 ELSE 0 END) as is_completed
                 FROM t_instruction_feedback
                 WHERE status = 1
                 GROUP BY instruction_id, receive_id
             ) f2 ON b2.instrucation_id = f2.instruction_id AND b2.id = f2.receive_id
             WHERE b2.instrucation_id = a.id AND b2.status = 1 AND t2.status = 1
             AND b2.receive_time IS NOT NULL
             AND (f2.is_completed IS NULL OR f2.is_completed = 0)
             <if test="params.currentArea != null and params.currentArea != ''">
                 AND b2.receive_dept LIKE CONCAT(#{params.currentArea}, '%')
             </if>
             LIMIT 1) as transferTime,
            CASE a.emergency_degree
                WHEN '特急' THEN 7
                WHEN '紧急' THEN 15
                WHEN '一般' THEN 30
                ELSE 30
            END as requiredDays,
            (SELECT MAX(DATEDIFF(NOW(), COALESCE(f2.last_feedback_time, t2.transfer_time)))
             FROM t_instruction_receive b2
             INNER JOIN t_instruction_transfer t2 ON b2.id = t2.receive_id
             LEFT JOIN (
                 SELECT instruction_id, receive_id, MAX(feedback_time) as last_feedback_time,
                        MAX(CASE WHEN feedback_is_end = 1 THEN 1 ELSE 0 END) as is_completed
                 FROM t_instruction_feedback
                 WHERE status = 1
                 GROUP BY instruction_id, receive_id
             ) f2 ON b2.instrucation_id = f2.instruction_id AND b2.id = f2.receive_id
             WHERE b2.instrucation_id = a.id AND b2.status = 1 AND t2.status = 1
             AND b2.receive_time IS NOT NULL
             AND (f2.is_completed IS NULL OR f2.is_completed = 0)
             <if test="params.currentArea != null and params.currentArea != ''">
                 AND b2.receive_dept LIKE CONCAT(#{params.currentArea}, '%')
             </if>) as daysSinceLastAction
        FROM t_instruction_info a
        INNER JOIN t_instruction_receive b ON a.id = b.instrucation_id
        INNER JOIN t_instruction_transfer t ON b.id = t.receive_id
        LEFT JOIN (
            SELECT instruction_id, receive_id, MAX(feedback_time) as last_feedback_time,
                   MAX(CASE WHEN feedback_is_end = 1 THEN 1 ELSE 0 END) as is_completed
            FROM t_instruction_feedback
            WHERE status = 1
            GROUP BY instruction_id, receive_id
        ) f ON a.id = f.instruction_id AND b.id = f.receive_id
        WHERE a.status = 1
          AND a.create_dept_id = 202
          AND a.instruction_type IN (3, 6)
          AND a.end_time IS NULL
          AND b.status = 1
          AND t.status = 1
          AND b.receive_time IS NOT NULL
          AND (f.is_completed IS NULL OR f.is_completed = 0)
          AND (
              (f.last_feedback_time IS NOT NULL AND
               DATEDIFF(NOW(), f.last_feedback_time) >=
               CASE a.emergency_degree
                   WHEN '特急' THEN 7
                   WHEN '紧急' THEN 15
                   WHEN '一般' THEN 30
                   ELSE 30
               END)
              OR
              (f.last_feedback_time IS NULL AND t.transfer_time IS NOT NULL AND
               DATEDIFF(NOW(), t.transfer_time) >=
               CASE a.emergency_degree
                   WHEN '特急' THEN 7
                   WHEN '紧急' THEN 15
                   WHEN '一般' THEN 30
                   ELSE 30
               END)
          )
        <if test="params.emergencyType != null and params.emergencyType != ''">
            <choose>
                <when test="params.emergencyType == '7'">
                    AND a.emergency_degree = '特急'
                </when>
                <when test="params.emergencyType == '15'">
                    AND a.emergency_degree = '紧急'
                </when>
                <when test="params.emergencyType == '30'">
                    AND a.emergency_degree = '一般'
                </when>
            </choose>
        </if>
        <if test="params.startTime != null and params.startTime != ''">
            AND DATE_FORMAT(a.assign_time, '%Y-%m-%d') &gt;= #{params.startTime}
        </if>
        <if test="params.endTime != null and params.endTime != ''">
            AND DATE_FORMAT(a.assign_time, '%Y-%m-%d') &lt;= #{params.endTime}
        </if>
        <if test="params.currentArea != null and params.currentArea != ''">
            AND b.receive_dept LIKE CONCAT(#{params.currentArea}, '%')
        </if>
        ORDER BY a.assign_time DESC
    </select>

    <!-- ==================== 县市层级指令办理情况 ==================== -->

    <!-- 获取县市层级应接收未接收统计数据 -->
    <select id="getCountyNoReceiveStats" resultType="java.util.Map">
        SELECT
            COUNT(DISTINCT CASE WHEN dept_level = 'county' THEN receive_dept END) as countyDeptCount,
            COUNT(DISTINCT CASE WHEN dept_level = 'town' THEN receive_dept END) as townDeptCount,
            COUNT(DISTINCT CASE WHEN dept_level = 'county' THEN instruction_id END) as countyInstructionCount,
            COUNT(DISTINCT CASE WHEN dept_level = 'town' THEN instruction_id END) as townInstructionCount
        FROM (
            SELECT DISTINCT
                a.id as instruction_id,
                b.receive_dept,
                CASE
                    WHEN b.receive_dept like '%政法委%' or b.receive_dept like '%局%'  THEN 'county'
                    ELSE 'town'
                END as dept_level
            FROM t_instruction_info a
            INNER JOIN t_instruction_receive b ON a.id = b.instrucation_id
            WHERE a.status = 1
              AND a.create_dept_id = 202
              AND a.instruction_type IN (3, 6)
              AND b.status = 1
              AND b.receive_time IS NULL
              AND (
                    (a.emergency_degree = '一般' AND TIMESTAMPDIFF(HOUR, a.assign_time, NOW()) > 24)
                    OR (a.emergency_degree = '紧急' AND TIMESTAMPDIFF(HOUR, a.assign_time, NOW()) > 12)
                    OR (a.emergency_degree = '特急' AND TIMESTAMPDIFF(HOUR, a.assign_time, NOW()) > 2)
                  )
            <if test="params.countyName != null and params.countyName != ''">
                AND b.receive_dept LIKE CONCAT(#{params.countyName}, '%')
            </if>
            <if test="params.startTime != null and params.startTime != ''">
                AND DATE_FORMAT(a.assign_time, '%Y-%m-%d') &gt;= #{params.startTime}
            </if>
            <if test="params.endTime != null and params.endTime != ''">
                AND DATE_FORMAT(a.assign_time, '%Y-%m-%d') &lt;= #{params.endTime}
            </if>
        ) stats
    </select>

    <!-- 获取县市层级应处置未处置统计数据 -->
    <select id="getCountyNoDisposeStats" resultType="java.util.Map">
        SELECT
            COUNT(DISTINCT CASE WHEN dept_level = 'county' THEN feedback_dept END) as countyDeptCount,
            COUNT(DISTINCT CASE WHEN dept_level = 'town' THEN feedback_dept END) as townDeptCount,
            COUNT(DISTINCT CASE WHEN dept_level = 'county' THEN instruction_id END) as countyInstructionCount,
            COUNT(DISTINCT CASE WHEN dept_level = 'town' THEN instruction_id END) as townInstructionCount
        FROM (
            SELECT DISTINCT
                a.id as instruction_id,
                f.feedback_dept,
                CASE
                    WHEN f.feedback_dept like '%政法委%' or f.feedback_dept like '%局%' THEN 'county'
                    ELSE 'town'
                END as dept_level
            FROM t_instruction_info a
            INNER JOIN t_instruction_receive c ON a.id = c.instrucation_id
            INNER JOIN (
                SELECT
                    f.instruction_id,
                    f.receive_id,
                    f.feedback_dept,
                    f.feedback_time,
                    f.is_mzx
                FROM t_instruction_feedback f
                INNER JOIN (
                    SELECT
                        instruction_id,
                        receive_id,
                        feedback_dept_id,
                        MAX(feedback_time) AS max_feedback_time
                    FROM t_instruction_feedback
                    WHERE status = 1
                      AND feedback_dept_id IS NOT NULL
                      AND receive_id IS NOT NULL
                    GROUP BY instruction_id, receive_id, feedback_dept_id
                ) latest_feedback ON f.instruction_id = latest_feedback.instruction_id
                  AND f.receive_id = latest_feedback.receive_id
                  AND f.feedback_dept_id = latest_feedback.feedback_dept_id
                  AND f.feedback_time = latest_feedback.max_feedback_time
                WHERE f.status = 1
                  AND f.feedback_is_end = 2
            ) f ON c.id = f.receive_id AND a.id = f.instruction_id
            WHERE a.status = 1
              AND a.create_dept_id = 202
              AND a.instruction_type IN (3, 6)
              AND c.status = 1
            <if test="params.countyName != null and params.countyName != ''">
                AND f.feedback_dept LIKE CONCAT(#{params.countyName}, '%')
            </if>
            <if test="params.startTime != null and params.startTime != ''">
                AND DATE_FORMAT(a.assign_time, '%Y-%m-%d') &gt;= #{params.startTime}
            </if>
            <if test="params.endTime != null and params.endTime != ''">
                AND DATE_FORMAT(a.assign_time, '%Y-%m-%d') &lt;= #{params.endTime}
            </if>
        ) stats
    </select>

    <!-- 获取县市层级应接收未接收详细列表 -->
    <select id="getCountyNoReceiveList" resultType="com.ruoyi.instruction.domain.rspVo.CountyInstructionHandlingRsp">
        SELECT DISTINCT
            a.id,
            a.instruction_title as instructionTitle,
            a.emergency_degree as emergencyDegree,
            CASE a.instruction_type
                WHEN 3 THEN '双排双办'
                WHEN 6 THEN '预警研判'
                END as instructionType,
            a.assign_time as assignTime,
            a.handle_time as handleTime,
            b.receive_dept as deptName,
            CASE
                WHEN b.receive_dept like '%政法委%' or b.receive_dept like '%局%' THEN '县级部门'
                ELSE '乡镇街道'
            END as deptType,
            CASE b.is_mzx
                WHEN 1 THEN '协办单位'
                WHEN 2 THEN '主办单位'
                END as unitType,
            b.receive_time as receiveTime,
            NULL as lastFeedbackTime,
            CASE a.emergency_degree
                WHEN '特急' THEN TIMESTAMPDIFF(HOUR, a.assign_time, NOW()) - 2
                WHEN '紧急' THEN TIMESTAMPDIFF(HOUR, a.assign_time, NOW()) - 12
                WHEN '一般' THEN TIMESTAMPDIFF(HOUR, a.assign_time, NOW()) - 24
                ELSE 0
            END as overdueDays,
            CASE
                WHEN b.receive_dept LIKE '磐安县%' THEN '磐安县'
                WHEN b.receive_dept LIKE '兰溪市%' THEN '兰溪市'
                WHEN b.receive_dept LIKE '东阳市%' THEN '东阳市'
                WHEN b.receive_dept LIKE '义乌市%' THEN '义乌市'
                WHEN b.receive_dept LIKE '浦江县%' THEN '浦江县'
                WHEN b.receive_dept LIKE '永康市%' THEN '永康市'
                WHEN b.receive_dept LIKE '金东区%' THEN '金东区'
                WHEN b.receive_dept LIKE '婺城区%' THEN '婺城区'
                WHEN b.receive_dept LIKE '开发区%' THEN '开发区'
                WHEN b.receive_dept LIKE '武义县%' THEN '武义县'
                ELSE '金华市'
            END as countyName
        FROM t_instruction_info a
        INNER JOIN t_instruction_receive b ON a.id = b.instrucation_id
        WHERE a.status = 1
          AND a.create_dept_id = 202
          AND a.instruction_type IN (3, 6)
          AND b.status = 1
          AND b.receive_time IS NULL
          AND (
                (a.emergency_degree = '一般' AND TIMESTAMPDIFF(HOUR, a.assign_time, NOW()) > 24)
                OR (a.emergency_degree = '紧急' AND TIMESTAMPDIFF(HOUR, a.assign_time, NOW()) > 12)
                OR (a.emergency_degree = '特急' AND TIMESTAMPDIFF(HOUR, a.assign_time, NOW()) > 2)
              )
        <if test="params.deptType != null and params.deptType != ''">
            <if test="params.deptType == 'county'">
                AND (b.receive_dept like '%政法委%' or b.receive_dept like '%局%')
            </if>
            <if test="params.deptType == 'town'">
                AND (b.receive_dept like '%街道%' or b.receive_dept like '%镇%')
            </if>
        </if>
        <if test="params.countyName != null and params.countyName != ''">
            AND b.receive_dept LIKE CONCAT(#{params.countyName}, '%')
        </if>
        <if test="params.startTime != null and params.startTime != ''">
            AND DATE_FORMAT(a.assign_time, '%Y-%m-%d') &gt;= #{params.startTime}
        </if>
        <if test="params.endTime != null and params.endTime != ''">
            AND DATE_FORMAT(a.assign_time, '%Y-%m-%d') &lt;= #{params.endTime}
        </if>
        ORDER BY a.assign_time DESC
    </select>

    <!-- 获取县市层级应处置未处置详细列表 -->
    <select id="getCountyNoDisposeList" resultType="com.ruoyi.instruction.domain.rspVo.CountyInstructionHandlingRsp">
        SELECT DISTINCT
            a.id,
            a.instruction_title as instructionTitle,
            a.emergency_degree as emergencyDegree,
            CASE a.instruction_type
                WHEN 3 THEN '双排双办'
                WHEN 6 THEN '预警研判'
                END as instructionType,
            a.assign_time as assignTime,
            a.handle_time as handleTime,
            f.feedback_dept as deptName,
            CASE
                WHEN (f.feedback_dept like '%政法委%' or f.feedback_dept like '%局%') THEN '县级部门'
                ELSE '乡镇街道'
            END as deptType,
            CASE f.is_mzx
                WHEN 1 THEN '协办单位'
                WHEN 2 THEN '主办单位'
                END as unitType,
            c.receive_time as receiveTime,
            f.feedback_time as lastFeedbackTime,
            DATEDIFF(NOW(), COALESCE(f.feedback_time, c.receive_time)) as overdueDays,
            CASE
                WHEN f.feedback_dept LIKE '磐安县%' THEN '磐安县'
                WHEN f.feedback_dept LIKE '兰溪市%' THEN '兰溪市'
                WHEN f.feedback_dept LIKE '东阳市%' THEN '东阳市'
                WHEN f.feedback_dept LIKE '义乌市%' THEN '义乌市'
                WHEN f.feedback_dept LIKE '浦江县%' THEN '浦江县'
                WHEN f.feedback_dept LIKE '永康市%' THEN '永康市'
                WHEN f.feedback_dept LIKE '金东区%' THEN '金东区'
                WHEN f.feedback_dept LIKE '婺城区%' THEN '婺城区'
                WHEN f.feedback_dept LIKE '开发区%' THEN '开发区'
                WHEN f.feedback_dept LIKE '武义县%' THEN '武义县'
                ELSE '金华市'
            END as countyName
        FROM t_instruction_info a
        INNER JOIN t_instruction_receive c ON a.id = c.instrucation_id
        INNER JOIN (
            SELECT
                f.instruction_id,
                f.receive_id,
                f.feedback_dept,
                f.feedback_time,
                f.is_mzx
            FROM t_instruction_feedback f
            INNER JOIN (
                SELECT
                    instruction_id,
                    receive_id,
                    feedback_dept_id,
                    MAX(feedback_time) AS max_feedback_time
                FROM t_instruction_feedback
                WHERE status = 1
                  AND feedback_dept_id IS NOT NULL
                  AND receive_id IS NOT NULL
                GROUP BY instruction_id, receive_id, feedback_dept_id
            ) latest_feedback ON f.instruction_id = latest_feedback.instruction_id
              AND f.receive_id = latest_feedback.receive_id
              AND f.feedback_dept_id = latest_feedback.feedback_dept_id
              AND f.feedback_time = latest_feedback.max_feedback_time
            WHERE f.status = 1
              AND f.feedback_is_end = 2
        ) f ON c.id = f.receive_id AND a.id = f.instruction_id
        WHERE a.status = 1
          AND a.create_dept_id = 202
          AND a.instruction_type IN (3, 6)
          AND c.status = 1
        <if test="params.deptType != null and params.deptType != ''">
            <choose>
                <when test="params.deptType == 'county'">
                    AND (f.feedback_dept like '%政法委%' or f.feedback_dept like '%局%')
                </when>
                <otherwise>
                    AND (f.feedback_dept LIKE '%街道%' or f.feedback_dept LIKE '%局%')
                </otherwise>
            </choose>
        </if>
        <if test="params.countyName != null and params.countyName != ''">
            AND f.feedback_dept LIKE CONCAT(#{params.countyName}, '%')
        </if>
        <if test="params.startTime != null and params.startTime != ''">
            AND DATE_FORMAT(a.assign_time, '%Y-%m-%d') &gt;= #{params.startTime}
        </if>
        <if test="params.endTime != null and params.endTime != ''">
            AND DATE_FORMAT(a.assign_time, '%Y-%m-%d') &lt;= #{params.endTime}
        </if>
        ORDER BY a.assign_time DESC
    </select>

</mapper>
