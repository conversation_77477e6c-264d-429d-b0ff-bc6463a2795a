package com.ruoyi.instruction.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.instruction.domain.InstructionDispose;
import com.ruoyi.instruction.service.IInstructionDisposeService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 指令处置Controller
 * 
 * <AUTHOR>
 * @date 2022-12-19
 */
@RestController
@RequestMapping("/instruction/dispose")
public class InstructionDisposeController extends BaseController
{
    @Autowired
    private IInstructionDisposeService instructionDisposeService;

    /**
     * 查询指令处置列表
     */
    @PreAuthorize("@ss.hasPermi('instruction:dispose:list')")
    @GetMapping("/list")
    public TableDataInfo list(InstructionDispose instructionDispose)
    {
        startPage();
        List<InstructionDispose> list = instructionDisposeService.selectInstructionDisposeList(instructionDispose);
        return getDataTable(list);
    }

    /**
     * 导出指令处置列表
     */
    @PreAuthorize("@ss.hasPermi('instruction:dispose:export')")
    @Log(title = "指令处置", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, InstructionDispose instructionDispose)
    {
        List<InstructionDispose> list = instructionDisposeService.selectInstructionDisposeList(instructionDispose);
        ExcelUtil<InstructionDispose> util = new ExcelUtil<InstructionDispose>(InstructionDispose.class);
        util.exportExcel(response, list, "指令处置数据");
    }

    /**
     * 获取指令处置详细信息
     */
    @PreAuthorize("@ss.hasPermi('instruction:dispose:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(instructionDisposeService.selectInstructionDisposeById(id));
    }

    /**
     * 新增指令处置
     */
    @PreAuthorize("@ss.hasPermi('instruction:dispose:add')")
    @Log(title = "指令处置", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody InstructionDispose instructionDispose)
    {
        return toAjax(instructionDisposeService.insertInstructionDispose(instructionDispose));
    }

    /**
     * 修改指令处置
     */
    @PreAuthorize("@ss.hasPermi('instruction:dispose:edit')")
    @Log(title = "指令处置", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody InstructionDispose instructionDispose)
    {
        return toAjax(instructionDisposeService.updateInstructionDispose(instructionDispose));
    }

    /**
     * 删除指令处置
     */
    @PreAuthorize("@ss.hasPermi('instruction:dispose:remove')")
    @Log(title = "指令处置", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(instructionDisposeService.deleteInstructionDisposeByIds(ids));
    }
}
