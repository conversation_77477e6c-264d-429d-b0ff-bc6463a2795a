package com.ruoyi.instruction.domain;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.common.annotation.Excel;
import lombok.Data;

import java.util.Date;

@Data
public class HealthCommission {

    /**
     * 区县
     */
    @Excel(name = "区县")
    private String county;

    /**
     * 乡镇街道
     */
    @Excel(name = "乡镇街道")
    private String street;

    /**
     * 姓名
     */
    @Excel(name = "姓名")
    private String name;

    /**
     * 身份证号
     */
    @Excel(name = "身份证号")
    private String personCard;

    /**
     * 电话号码
     */
    @Excel(name = "电话号码")
    private String phone;

    /**
     * 户籍地址
     */
    @Excel(name = "户籍地址")
    private String residenceAddress;

    /**
     * 居住地址
     */
    @Excel(name = "居住地址")
    private String residentialAddress;

    /**
     * 目前诊断
     */
    @Excel(name = "目前诊断")
    private String currentDiagnosis;

    /**
     * 纳入管理时间
     */
    @Excel(name = "纳入管理时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date manageTime;

    /**
     * 随访日期
     */
    @Excel(name = "随访日期")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date followDate;

    /**
     * 危险性
     */
    @Excel(name = "危险性")
    private String risk;
}
