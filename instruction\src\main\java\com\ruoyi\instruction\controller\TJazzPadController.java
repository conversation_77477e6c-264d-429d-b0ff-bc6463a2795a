package com.ruoyi.instruction.controller;

import java.util.List;

import com.ruoyi.instruction.domain.Pazs;
import com.ruoyi.instruction.domain.unqualifiedPadResVo;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.instruction.domain.TJazzPad;
import com.ruoyi.instruction.service.ITJazzPadService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import lombok.extern.slf4j.Slf4j;
import com.ruoyi.common.core.page.TableDataInfo;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;

/**
 * 金安智治-平安金华-平安鼎Controller
 *
 * <AUTHOR>
 * @date 2023-07-11
 */
@RestController
@RequestMapping("/instruction/pad")
@Slf4j
public class TJazzPadController extends BaseController
{
    @Autowired
    private ITJazzPadService tJazzPadService;

    /**
     * 查询金安智治-平安金华-平安鼎列表
     */
    @PreAuthorize("@ss.hasPermi('instruction:pad:list')")
    @GetMapping("/list")
    public TableDataInfo list(TJazzPad tJazzPad)
    {
        startPage();
        List<TJazzPad> list = tJazzPadService.selectTJazzPadList(tJazzPad);
        return getDataTable(list);
    }

    /**
     * 导出金安智治-平安金华-平安鼎列表
     */
    @PreAuthorize("@ss.hasPermi('instruction:pad:export')")
    @Log(title = "金安智治-平安金华-平安鼎", businessType = BusinessType.EXPORT)
    @GetMapping("/export")
    public AjaxResult export(TJazzPad tJazzPad)
    {
        List<TJazzPad> list = tJazzPadService.selectTJazzPadList(tJazzPad);
        ExcelUtil<TJazzPad> util = new ExcelUtil<TJazzPad>(TJazzPad.class);
        return util.exportExcel(list, "pad");
    }

    /**
     * 不合格平安鼎导出
     */
    @GetMapping("/unqualified/export")
    public AjaxResult unqualifiedexport(TJazzPad tJazzPad)
    {
        List<unqualifiedPadResVo> list = tJazzPadService.selectTJazzunPadList(tJazzPad);
        ExcelUtil<unqualifiedPadResVo> util = new ExcelUtil<unqualifiedPadResVo>(unqualifiedPadResVo.class);
        return util.exportExcel(list, "pad");
    }

    /**
     * 获取金安智治-平安金华-平安鼎详细信息
     */
    @PreAuthorize("@ss.hasPermi('instruction:pad:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return AjaxResult.success(tJazzPadService.selectTJazzPadById(id));
    }

    /**
     * 新增金安智治-平安金华-平安鼎
     */
    @PreAuthorize("@ss.hasPermi('instruction:pad:add')")
    @Log(title = "金安智治-平安金华-平安鼎", businessType = BusinessType.INSERT)
    @PostMapping("/add")
    public AjaxResult add(@RequestBody TJazzPad tJazzPad)
    {
        return toAjax(tJazzPadService.insertTJazzPad(tJazzPad));
    }

    /**
     * 修改金安智治-平安金华-平安鼎
     */
    @PreAuthorize("@ss.hasPermi('instruction:pad:edit')")
    @Log(title = "金安智治-平安金华-平安鼎", businessType = BusinessType.UPDATE)
    @PostMapping("/edit")
    public AjaxResult edit(@RequestBody TJazzPad tJazzPad)
    {
        return toAjax(tJazzPadService.updateTJazzPad(tJazzPad));
    }

    /**
     * 删除金安智治-平安金华-平安鼎
     */
    @PreAuthorize("@ss.hasPermi('instruction:pad:remove')")
    @Log(title = "金安智治-平安金华-平安鼎", businessType = BusinessType.DELETE)
	@DeleteMapping("/remove/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(tJazzPadService.deleteTJazzPadByIds(ids));
    }


    /**
     * 查询金安智治-平安金华-平安鼎列表(新)
     */
    @GetMapping("/newlist")
    public TableDataInfo newlist(TJazzPad tJazzPad)
    {
        startPage();
        return getDataTable(tJazzPadService.selectTJazzPadListByYear(tJazzPad));
    }

    /**
     * 查询金安智治-平安金华-平安鼎当前情况(新)
     */
    @GetMapping("/currentSituation")
    public AjaxResult currentSituation(String type,String condition)
    {
        return AjaxResult.success(tJazzPadService.selectCurrentPad(type,condition));
    }

    /**
     * 下载平安鼎导入模板
     *
     * @param response
     */
    @PostMapping("/importTemplate")
    public void importTemplate(HttpServletResponse response) {
        ExcelUtil<TJazzPad> util = new ExcelUtil<TJazzPad>(TJazzPad.class);
        util.importTemplateExcel(response, "平安鼎导入模板");
    }


    /**
     * 导入平安指数数据
     */
    @PostMapping("/importPadData")
    public AjaxResult importPadData(MultipartFile file) throws Exception {
        ExcelUtil<TJazzPad> util = new ExcelUtil<TJazzPad>(TJazzPad.class);
        List<TJazzPad> padList = util.importExcel(file.getInputStream());
        //存入数据
        String msg = tJazzPadService.importPad(padList);
        return AjaxResult.success(msg);
    }

}
