package com.ruoyi.instruction.mapper;

import java.util.List;
import com.ruoyi.instruction.domain.InstructionHandle;

/**
 * 督办单Mapper接口
 * 
 * <AUTHOR>
 * @date 2023-05-17
 */
public interface InstructionHandleMapper 
{
    /**
     * 查询督办单
     * 
     * @param id 督办单主键
     * @return 督办单
     */
    public InstructionHandle selectInstructionHandleById(Long id);

    /**
     * 查询督办单列表
     * 
     * @param instructionHandle 督办单
     * @return 督办单集合
     */
    public List<InstructionHandle> selectInstructionHandleList(InstructionHandle instructionHandle);

    /**
     * 新增督办单
     * 
     * @param instructionHandle 督办单
     * @return 结果
     */
    public int insertInstructionHandle(InstructionHandle instructionHandle);

    /**
     * 修改督办单
     * 
     * @param instructionHandle 督办单
     * @return 结果
     */
    public int updateInstructionHandle(InstructionHandle instructionHandle);

    /**
     * 删除督办单
     * 
     * @param id 督办单主键
     * @return 结果
     */
    public int deleteInstructionHandleById(Long id);

    /**
     * 批量删除督办单
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteInstructionHandleByIds(Long[] ids);

    /**
     * 查询下一个自增id
     * @return
     */
    Long findNextNumber();

}
