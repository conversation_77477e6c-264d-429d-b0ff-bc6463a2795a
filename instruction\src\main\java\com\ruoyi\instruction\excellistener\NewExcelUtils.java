package com.ruoyi.instruction.excellistener;

import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.common.utils.file.FileUtils;
import com.ruoyi.common.utils.uuid.UUID;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.util.IOUtils;
import org.apache.poi.xssf.streaming.SXSSFSheet;
import org.apache.poi.xssf.streaming.SXSSFWorkbook;
import org.apache.poi.xssf.usermodel.*;
import org.springframework.util.CollectionUtils;

import java.io.ByteArrayInputStream;
import java.io.FileOutputStream;
import java.io.InputStream;
import java.net.URL;
import java.net.URLConnection;
import java.net.URLEncoder;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class NewExcelUtils {
    /**
     * @param sheetTitle excel表格名称
     * @param head       excel表头
     * @param result     excel表格数据
     * @return
     */
    public static SXSSFWorkbook getSxssfwbExcel(SXSSFWorkbook wb, SXSSFSheet sheet, String sheetTitle, List<Object> head, List<List<String>> result) {
        if (wb == null) {
            //创建一个工作薄
            wb = new SXSSFWorkbook();
            //int sheetNum = 0;// 记录额外创建的sheet数量
            //创建excel表
            sheet = wb.createSheet(sheetTitle);
            // wb.setSheetName(sheetNum, sheetTitle+sheetNum);
        }
        sheet.setDefaultColumnWidth(15);
        sheet.setDefaultRowHeight((short) (8 * 128));

        int rownum = 0;
        Row row = sheet.createRow(rownum);
        Cell cell;
        // 创建标题,此时row=0,即第一行
        for (int j = 0; j < head.size(); j++) {
            cell = row.createCell(j);
            cell.setCellValue(head.get(j).toString());
        }

        // 遍历集合数据，创建excel内容,产生数据行
        if (result != null) {
            int index = 1;
            List<String> m = null;
            for (int i = 0; i < result.size(); i++) {
                row = sheet.createRow(index);
                int cellIndex = 0;
                m = result.get(i);
                for (String str : m) {
                    row.createCell((short) cellIndex).setCellValue(str);
                    cellIndex++;
                }
                index++;
            }
        }
        return wb;
    }

    /**
     * excel根据指定行列放入图片
     *
     * @param workbook 工作薄
     * @param sheet    excel表
     * @param fileUrl  图片链接
     * @param row      表格行
     * @param col      表格列
     * @param scaleX   图片在表格中横坐标 默认1.0
     * @param scaleY   图片在表格中横坐标 默认1.0
     */
    public static void toLeadPicture(SXSSFWorkbook workbook, SXSSFSheet sheet, String fileUrl, int row, int col, double scaleX, double scaleY) {
        try {
            //防止URL地址有中文，解码
            String head = fileUrl.substring(0, fileUrl.lastIndexOf("/") + 1);
            String suffix = fileUrl.substring(fileUrl.lastIndexOf("/") + 1);
            String link = head + URLEncoder.encode(suffix, "UTF-8");
            // 构造URL
            URL url = new URL(link);
            // 打开连接
            URLConnection con = url.openConnection();
            //设置请求超时为5s
            con.setConnectTimeout(800000 * 1000);
            // 输入流
            InputStream is = con.getInputStream();


            byte[] bytes = IOUtils.toByteArray(is);
            @SuppressWarnings("static-access")
            //图片输入流转成字节数组放到workbook 中
            int pictureIdx = workbook.addPicture(bytes, workbook.PICTURE_TYPE_PNG);
            CreationHelper helper = workbook.getCreationHelper();
            Drawing drawing = sheet.createDrawingPatriarch();
            ClientAnchor anchor = helper.createClientAnchor();
            // 图片插入坐标
            anchor.setCol1(col);
            anchor.setRow1(row);
            // 插入图片
            Picture pict = drawing.createPicture(anchor, pictureIdx);
            pict.resize(scaleX, scaleY);
            // 设置宽度  这里如果设置图片的宽度，则excel表格设置的参数将失效
            //      sheet.setColumnWidth(row, 10 * 256);
            // 设置高度 这里如果设置图片的高度，则excel表格设置的参数将失效
            Row rowl = sheet.createRow(col);
//            rowl.setHeight((short) (30 * 20));

        } catch (Exception e) {
            // TODO Auto-generated catch block
            e.printStackTrace();
        }
    }

    /**
     * excel导入
     * 获取excel表格中图片
     *
     * @param xssfSheet
     * @return
     */
    public static Map<String, XSSFPictureData> getPictures(XSSFSheet xssfSheet) {

        Map<String, XSSFPictureData> map = new HashMap<>();
        List<XSSFShape> list = xssfSheet.getDrawingPatriarch().getShapes();
        for (XSSFShape shape : list) {
            XSSFPicture picture = (XSSFPicture) shape;
            //行号-列号
            XSSFClientAnchor xssfClientAnchor = (XSSFClientAnchor) picture.getAnchor();
            //获取图片
            XSSFPictureData pdata = picture.getPictureData();
            // 行号-列号
            String key = xssfClientAnchor.getRow1() + "-" + xssfClientAnchor.getCol1();
            map.put(key, pdata);
            byte[] data = pdata.getData();
            InputStream inputStream = new ByteArrayInputStream(data);
            String fileNam1e = "E:\\新建文件夹\\"+ UUID.randomUUID()+".jpg";
            try {
                byte[] imageBytes = IOUtils.toByteArray(inputStream);
                FileOutputStream fos = new FileOutputStream(fileNam1e);
                byte[] mybytes = imageBytes;
                fos.write(mybytes);
                fos.flush();
                FileUtils.close(fos);
            }catch (Exception e){
                e.printStackTrace();
            }


//            try {
//                MockMultipartFile file = new MockMultipartFile(key, inputStream);
//            } catch (Exception e) {
//                e.printStackTrace();
//            }
        }
        return map;
    }

    /**
     * excel导入
     * 获取excel表格中图片
     *
     * @param xssfSheet
     * @return
     */
    public static Map<Integer, String> getPicturesString(XSSFSheet xssfSheet,String path) {
        Map<Integer, String> map = new HashMap<>();
        if (xssfSheet==null||xssfSheet.getDrawingPatriarch()==null){
            return  map;
        }
        List<XSSFShape> list = xssfSheet.getDrawingPatriarch().getShapes();
        if (CollectionUtils.isEmpty(list)){
            return  map;
        }
        for (XSSFShape shape : list) {
            XSSFPicture picture = (XSSFPicture) shape;
            //行号-列号
            XSSFClientAnchor xssfClientAnchor = (XSSFClientAnchor) picture.getAnchor();
            //获取图片
            XSSFPictureData pdata = picture.getPictureData();
            // 行号-列号
            Integer key = xssfClientAnchor.getRow1();

            byte[] data = pdata.getData();
            InputStream inputStream = new ByteArrayInputStream(data);
            String url="https://csdn.dsjj.jinhua.gov.cn:8303/file";
            String uuid= StringUtils.getUUid()+".jpg";
            String fileNam1e = path+"/headUrl/"+ uuid;
            map.put(key, url+"/upload/headUrl/"+ uuid);
            FileOutputStream fos=null;
            try {
                byte[] imageBytes = IOUtils.toByteArray(inputStream);
                 fos = new FileOutputStream(fileNam1e);
                byte[] mybytes = imageBytes;
                fos.write(mybytes);
                fos.flush();

            }catch (Exception e){
                e.printStackTrace();
            }finally {
                FileUtils.close(fos);
            }
        }
        return map;
    }


}
