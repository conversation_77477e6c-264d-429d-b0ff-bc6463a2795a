package com.ruoyi.instruction.mapper;

import java.util.List;
import java.util.Map;

import com.ruoyi.instruction.domain.*;
import com.ruoyi.instruction.domain.reqVo.InspectionReportParam;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * 暗访督察列Mapper接口
 *
 * <AUTHOR>
 * @date 2023-07-20
 */
@Mapper
public interface TOpenUndercoverInspectionMapper
{
    /**
     * 查询暗访督察列
     *
     * @param id 暗访督察列ID
     * @return 暗访督察列
     */
    public TOpenUndercoverInspection selectTOpenUndercoverInspectionById(Long id);

    /**
     * 查询暗访督察列列表
     *
     * @param tOpenUndercoverInspection 暗访督察列
     * @return 暗访督察列集合
     */
    public List<TOpenUndercoverInspection> selectTOpenUndercoverInspectionList(TOpenUndercoverInspection tOpenUndercoverInspection);

    /**
     * 新增暗访督察列
     *
     * @param tOpenUndercoverInspection 暗访督察列
     * @return 结果
     */
    public int insertTOpenUndercoverInspection(TOpenUndercoverInspection tOpenUndercoverInspection);

    /**
     * 修改暗访督察列
     *
     * @param tOpenUndercoverInspection 暗访督察列
     * @return 结果
     */
    public int updateTOpenUndercoverInspection(TOpenUndercoverInspection tOpenUndercoverInspection);

    /**
     * 删除暗访督察列
     *
     * @param id 暗访督察列ID
     * @return 结果
     */
    public int deleteTOpenUndercoverInspectionById(Long id);

    /**
     * 批量删除暗访督察列
     *
     * @param ids 需要删除的数据ID
     * @return 结果
     */
    public int deleteTOpenUndercoverInspectionByIds(Long[] ids);

    /**
     * 获取数据通过UniqueNo
     * @param UniqueNo
     * @return
     */
    TOpenUndercoverInspection selectTOpenUndercoverInspectionByUniqueNo(@Param("UniqueNo") String UniqueNo);

    /**
     * 通过内部id查询数据
     * @param ids inspection_id
     * @return
     */
    List<TOpenUndercoverInspection> selectOpenUndercoverInspectionByInspectionIds(Long[] ids);

    /**
     * 获取乡镇
     * @param code
     * @return
     */
    String getTown(@Param("code") String code);

    /**
     * 查询明查暗访对应指令、事件info
     * @return
     */
    List<Map<String, Object>> findDealInfo();

    /**
     * selectNeedCorrect
     * @return
     */
    List<TOpenUndercoverInspection> selectNeedCorrect();

    TOpenUndercoverInspection selectTOpenUndercoverInspectionByOpenId(Long id);

    /**
     * 通过unique_no 查询inspectionId
     * @param details
     * @return
     */
    List<String> findInspectionIdByUniqueNo(@Param("details") List<String> details);

    /**
     * 查询合格点位
     * @return
     */
    List<String> findBelowStandard();

    /**
     * 查询督察点位企业类别
     * @return
     */
    List<String> listCompanyCategory();

    /**
     * 根据企业雷迅查询督察点位
     *
     * @param category
     * @return
     */
    List<TOpenUndercoverInspection> listByCompanyCategory(@Param("category") String category);

    /**
     * 查询明察暗访全部任务
     * @return
     * @param type
     * @param deptName
     */
    List<String> selectMissionNameList(@Param("type") final Integer type,@Param("deptName") final String deptName);

    /**
     * 查询整体分析报告
     * @param county
     * @return
     */
    InspectionReport selectTotalInspectionAnalysis(InspectionReportParam inspectionReportParam);

    /**
     * 查询反馈率分析
     * @param county
     * @return
     */
    FeedbackAnalysis selectFeedbackAnalysis(InspectionReportParam inspectionReportParam);

    /**
     * 查询点位类别合格分析
     * @param county
     * @param company_category
     * @return
     */
    PointAnalysis selectPointAnalysis(InspectionReportParam inspectionReportParam);

    /**
     * 查询点位类别问题分析
     * @param county
     * @param company_category
     * @return
     */
    ProblemAnalysis selectProblemAnalysis(InspectionReportParam inspectionReportParam);

    /**
     * 查询问题类别分析
     * @param county
     * @param category
     * @return
     */
    ProblemCategory selectProblemCategory(InspectionReportParam inspectionReportParam);

    /**
     * 获取某区县全部乡镇街道
     * @param county
     * @return
     */
    List<String> getStreetsByCounty(@Param("county") String county);

    /**
     * 查询乡镇街道反馈分析
     * @param town
     * @return
     */
    StreetFeedback selectStreetFeedback(InspectionReportParam inspectionReportParam);

    /**
     * 根据unique更新暗访点位
     * @param mcafId
     * @param transferDept
     */
    void updateTownByUnique(@Param("uniqueNo") String mcafId, @Param("transferDept") String transferDept);
}
