package com.ruoyi.instruction.mapper;

import java.util.Date;
import java.util.List;
import java.util.Map;

import com.ruoyi.instruction.domain.InstructionFeedback;
import com.ruoyi.instruction.domain.rspVo.FeedbackRemindVO;
import org.apache.ibatis.annotations.Param;

/**
 * 指令反馈Mapper接口
 *
 * <AUTHOR>
 * @date 2022-12-19
 */
public interface InstructionFeedbackMapper
{
    /**
     * 查询指令反馈
     *
     * @param id 指令反馈主键
     * @return 指令反馈
     */
    public InstructionFeedback selectInstructionFeedbackById(Long id);

    /**
     * 查询指令反馈列表
     *
     * @param instructionFeedback 指令反馈
     * @return 指令反馈集合
     */
    public List<InstructionFeedback> selectInstructionFeedbackList(InstructionFeedback instructionFeedback);

    /**
     * 新增指令反馈
     *
     * @param instructionFeedback 指令反馈
     * @return 结果
     */
    public int insertInstructionFeedback(InstructionFeedback instructionFeedback);

    /**
     * 修改指令反馈
     *
     * @param instructionFeedback 指令反馈
     * @return 结果
     */
    public int updateInstructionFeedback(InstructionFeedback instructionFeedback);

    /**
     * 删除指令反馈
     *
     * @param id 指令反馈主键
     * @return 结果
     */
    public int deleteInstructionFeedbackById(Long id);

    /**
     * 批量删除指令反馈
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteInstructionFeedbackByIds(Long[] ids);

    /**
     * 根据处置表id查询反馈信息
     * @param id
     * @return
     */
    InstructionFeedback selectInstructionFeedbackByDisposeId(Long id);

    /**
     * 通过反馈部门查询出指令id
     * @param deptName
     * @return
     */
    List<Long> selectInstructionIds(@Param("deptName") String deptName);

    /**
     * 查询指令id最后反馈时间
     * @param id
     * @return
     */
    Date selectFeedbackTimeByInstructionId(Long id);

    /**
     * 查询最完结束的反馈
     * @param id
     * @return
     */
    Date findFeedBackTime(@Param("id") Long id);

    /**
     * 测试 获取反馈最后时间
     * @param id
     * @return
     */
    Date testFindTime(@Param("id") Long id);

    /**
     * 根据转交记录id删除反馈记录
     * @param id
     */
    void deleteByTransferId(Long id);

    /**
     * 根据指令id查询该条指令对应的反馈信息
     * @param infoId
     * @return
     */
    String findFeedBackInfosByInstructionId(@Param("instructionId") Long infoId);

    /**
     * 根据指令id、销号时间查询销号后已办结的反馈部门数
     * @param endTime
     * @param id
     * @return
     */
    int findEndFeedDeptCount(@Param("endTime") Date endTime,@Param("instructionId") Long id);

    /**
     * 更新反馈记录是否办结状态
     * @param transferId
     */
    void updateFeedBackStatus(@Param("transferId") Long transferId);

    /**
     * 查询指令id已反馈部门
     * @param id
     * @return
     */
    String selectFeedBackDeptByInstructionId(@Param("id") Long id);

    /**
     * 删除反馈记录
     * @param id
     */
    void deleteInstructionFeedbackByInstructionId(@Param("id") Long id);

    /**
     * 根据指令id、部门名称更新是否办结状态
     * @param instructionId
     * @param feedbackObject
     */
    void updateIsEnd(@Param("instructionId") Long instructionId, @Param("feedbackObject") String feedbackObject);

    /**
     * 判断指令反馈部门个数是否与接收单位个数相同 1:相同可以销号  2:不相同不可销号
     * @param id
     * @return
     */
    Integer isFeedBackToEnd(@Param("id") Long id);

    /**
     * 根据接收Id判断是否可以向县市区反馈部门发送通知
     * @param receiveId
     * @return
     */
    Integer JudgeToCountyFeedBack(@Param("receiveId") Long receiveId);

    /**
     * 判断是否可以给指令创建部门发送销号信息
     * @param id
     * @return
     */
    Integer judgeToEnd(@Param("instructionId") Long id);

    /**
     * 根据接收Id删除转交记录
     * @param id
     */
    void deleteByReceiveId(@Param("receiveId") Long id);

    /**
     * 根据转交部门删除处置记录
     * @param id
     */
    void delFeedBackByTransferId(@Param("transferId") Long id);

    /**
     * 查询出所有未处置指令
     * @return
     */
    List<Map<String, Object>> findNoFeedbackList();

    /**
     * 查询维稳未处置指令
     * @return
     */
    List<Map<String, Object>> findWeiWenNoFeedbackList();

    /**
     * 查询反馈要求为定期反馈指令且状态为已处置未反馈
     * @return
     */
    List<Map<String, Object>> findFrequencyFeedback();

    /**
     * mzx判断是否可以销号
     * @param id
     * @return
     */
    Integer mzxJudgeToEnd(@Param("instructionId") Long id);

    /**
     * 查询新待反馈指令
     * @return
     */
    List<FeedbackRemindVO> findNewFrequencyFeedback();

    /**
     * 查询出已流转未反馈的指令
     * @return
     */
    List<FeedbackRemindVO> findNewNoFeedbackList();


}
