package com.ruoyi.instruction.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;

import com.ruoyi.instruction.domain.rspVo.DailyNewsCategoryVo;
import com.ruoyi.instruction.domain.rspVo.DailyNewsMonthVo;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.instruction.domain.DailyNews;
import com.ruoyi.instruction.service.IDailyNewsService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 每日要闻列Controller
 * 
 * <AUTHOR>
 * @date 2023-08-16
 */
@RestController
@RequestMapping("/instruction/dailyNews")
public class DailyNewsController extends BaseController
{
    @Autowired
    private IDailyNewsService dailyNewsService;

    /**
     * 查询每日要闻列列表
     */
    @PreAuthorize("@ss.hasPermi('instruction:dailyNews:list')")
    @GetMapping("/list")
    public TableDataInfo list(DailyNews dailyNews)
    {
        startPage();
        List<DailyNews> list = dailyNewsService.selectDailyNewsList(dailyNews);
        return getDataTable(list);
    }

    /**
     * 导出每日要闻列列表
     */
    @PreAuthorize("@ss.hasPermi('instruction:dailyNews:export')")
    @Log(title = "每日要闻列", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, DailyNews dailyNews)
    {
        List<DailyNews> list = dailyNewsService.selectDailyNewsList(dailyNews);
        ExcelUtil<DailyNews> util = new ExcelUtil<DailyNews>(DailyNews.class);
        util.exportExcel(response, list, "每日要闻列数据");
    }

    /**
     * 获取每日要闻列详细信息
     */
//    @PreAuthorize("@ss.hasPermi('instruction:dailyNews:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(dailyNewsService.selectDailyNewsById(id));
    }

    /**
     * 新增每日要闻列
     */
    @PreAuthorize("@ss.hasPermi('instruction:dailyNews:add')")
    @Log(title = "每日要闻列", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody DailyNews dailyNews)
    {
        return toAjax(dailyNewsService.insertDailyNews(dailyNews));
    }

    /**
     * 修改每日要闻列
     */
    @PreAuthorize("@ss.hasPermi('instruction:dailyNews:edit')")
    @Log(title = "每日要闻列", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody DailyNews dailyNews)
    {
        return toAjax(dailyNewsService.updateDailyNews(dailyNews));
    }

    /**
     * 删除每日要闻列
     */
    @PreAuthorize("@ss.hasPermi('instruction:dailyNews:remove')")
    @Log(title = "每日要闻列", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(dailyNewsService.deleteDailyNewsByIds(ids));
    }


    /**
     * 查询每日要闻（获取前一天数据）
     */
    @GetMapping("/everyDay")
    public AjaxResult everyDay()
    {
        DailyNews dailyNews = dailyNewsService.everyDay();
        return AjaxResult.success(dailyNews);
    }

    /**
     * 查询每日要闻获取分类
     */
    @GetMapping("/category")
    public AjaxResult category(String param)
    {
        List<DailyNewsCategoryVo> category = dailyNewsService.category(param);
        return AjaxResult.success(category);
    }

    /**
     * 查询每日要闻当月列表
     */
    @GetMapping("/month")
    public AjaxResult month()
    {
        List<DailyNewsMonthVo> month = dailyNewsService.month();
        return AjaxResult.success(month);
    }

    /**
     * 新增修改每日要闻列
     */
    @Log(title = "每日要闻列", businessType = BusinessType.INSERT)
    @PostMapping("addAndUpdate")
    public AjaxResult addAndUpdate(@RequestBody DailyNews dailyNews)
    {
        AjaxResult ajaxResult = dailyNewsService.addAndUpdate(dailyNews);
        return ajaxResult;
    }
}
