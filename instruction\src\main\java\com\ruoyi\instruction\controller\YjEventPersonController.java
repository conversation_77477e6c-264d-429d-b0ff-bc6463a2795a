package com.ruoyi.instruction.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;

import com.ruoyi.instruction.domain.YjEvent;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.instruction.domain.YjEventPerson;
import com.ruoyi.instruction.service.IYjEventPersonService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 基层治理-人员关系Controller
 *
 * <AUTHOR>
 * @date 2024-12-10
 */
@RestController
@RequestMapping("/wz/eventPerson")
public class YjEventPersonController extends BaseController
{
    @Autowired
    private IYjEventPersonService yjEventPersonService;

    /**
     * 查询基层治理-人员关系列表
     */
    // @PreAuthorize("@ss.hasPermi('wz:eventPerson:list')")
    @GetMapping("/list")
    public TableDataInfo list(YjEventPerson yjEventPerson)
    {
        startPage();
        List<YjEventPerson> list = yjEventPersonService.selectYjEventPersonList(yjEventPerson);
        for (YjEventPerson person : list
        ) {
            if (person.getAreaCode() != null) {
                String areaCode = person.getAreaCode();
                if (areaCode.contains("330700")) {
                    person.setDutyPlace("金华市");
                } else if (areaCode.contains("330702")) {
                    person.setDutyPlace("婺城区");
                } else if (areaCode.contains("330703")) {
                    person.setDutyPlace("金东区");
                } else if (areaCode.contains("330704")) {
                    person.setDutyPlace("开发区");
                } else if (areaCode.contains("330723")) {
                    person.setDutyPlace("武义县");
                } else if (areaCode.contains("330726")) {
                    person.setDutyPlace("浦江县");
                } else if (areaCode.contains("330727")) {
                    person.setDutyPlace("磐安县");
                } else if (areaCode.contains("330781")) {
                    person.setDutyPlace("兰溪市");
                } else if (areaCode.contains("330782")) {
                    person.setDutyPlace("义乌市");
                } else if (areaCode.contains("330783")) {
                    person.setDutyPlace("东阳市");
                } else if (areaCode.contains("330784")) {
                    person.setDutyPlace("永康市");
                }
            }
        }
        return getDataTable(list);
    }

    /**
     * 导出基层治理-人员关系列表
     */
    @PreAuthorize("@ss.hasPermi('wz:eventPerson:export')")
    @Log(title = "基层治理-人员关系", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, YjEventPerson yjEventPerson)
    {
        List<YjEventPerson> list = yjEventPersonService.selectYjEventPersonList(yjEventPerson);
        ExcelUtil<YjEventPerson> util = new ExcelUtil<YjEventPerson>(YjEventPerson.class);
        util.exportExcel(response, list, "基层治理-人员关系数据");
    }

    /**
     * 获取基层治理-人员关系详细信息
     */
    @PreAuthorize("@ss.hasPermi('wz:eventPerson:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(yjEventPersonService.selectYjEventPersonById(id));
    }

    /**
     * 新增基层治理-人员关系
     */
    @PreAuthorize("@ss.hasPermi('wz:eventPerson:add')")
    @Log(title = "基层治理-人员关系", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody YjEventPerson yjEventPerson)
    {
        return toAjax(yjEventPersonService.insertYjEventPerson(yjEventPerson));
    }

    /**
     * 修改基层治理-人员关系
     */
    @PreAuthorize("@ss.hasPermi('wz:eventPerson:edit')")
    @Log(title = "基层治理-人员关系", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody YjEventPerson yjEventPerson)
    {
        return toAjax(yjEventPersonService.updateYjEventPerson(yjEventPerson));
    }

    /**
     * 删除基层治理-人员关系
     */
    @PreAuthorize("@ss.hasPermi('wz:eventPerson:remove')")
    @Log(title = "基层治理-人员关系", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(yjEventPersonService.deleteYjEventPersonByIds(ids));
    }
}
