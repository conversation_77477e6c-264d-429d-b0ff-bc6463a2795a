package com.ruoyi.instruction.domain.rspVo;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import lombok.Data;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2025/7/24 9:29
 */
@Data
public class RiskStatisExportVo {

    @ColumnWidth(20)
    @ExcelProperty("属地")
    private String unitName;

    @ColumnWidth(25)
    @ExcelProperty({"超时类型", "超3个月未跟进"})
    private Long moreThreeMonthNum;

    @ColumnWidth(25)
    @ExcelProperty({"超时类型", "超2-3个月"})
    private Long moreTwoMonthNum;

    @ColumnWidth(25)
    @ExcelProperty({"超时类型", "超1-2个月"})
    private Long moreOneMonthNum;


    @ColumnWidth(25)
    @ExcelProperty({"超时类型", "超半个月-1个月"})
    private Long oneMonthNum;


    @ColumnWidth(25)
    @ExcelProperty({"超时类型", "超1周-2周"})
    private Long oneWeekNum;

}
