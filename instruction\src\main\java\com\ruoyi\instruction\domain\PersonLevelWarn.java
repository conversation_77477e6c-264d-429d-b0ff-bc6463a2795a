package com.ruoyi.instruction.domain;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 人员调档预警对象 t_person_level_warn
 *
 * <AUTHOR>
 * @date 2024-10-22
 */
@Data
public class PersonLevelWarn extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键id */
    private Long id;

    /** 姓名 */
    @Excel(name = "姓名")
    private String personName;

    /** 属地 */
    @Excel(name = "属地")
    private String dutyPlace;

    /** 责任乡镇 */
    @Excel(name = "责任乡镇")
    private String personTown;

    /** 预警类型：升级预警、降级预警 */
    @Excel(name = "预警类型：升级预警、降级预警")
    private String wranType;

    /** 当前等级-1：无 0：撤档1：黄 2：橙  3：红  */
    @Excel(name = "当前等级-1：无 0：撤档1：黄 2：橙  3：红 ")
    private Integer nowLevel;

    /** 预警等级-1：无 0：撤档1：黄 2：橙  3：红  */
    @Excel(name = "预警等级-1：无 0：撤档1：黄 2：橙  3：红 ")
    private Integer warnLevel;

    /** 预警原因 */
    @Excel(name = "预警原因")
    private String warnReason;

    /** 触发时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "触发时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date touchTime;

    /** 处置时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "处置时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date disposeTime;

    /** 处置状态 */
    @Excel(name = "处置状态")
    private String disposeStatus;

    /** 人员id */
    @Excel(name = "人员id")
    private Long personId;

    /** 处置理由 */
    @Excel(name = "处置理由")
    private String disposeReason;

    /** 状态 1：正常 9：删除 */
    @Excel(name = "状态 1：正常 9：删除")
    private String status;

    /** 文件ids */
    @Excel(name = "文件ids")
    private String fileIds;

    /** 提醒截止时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "提醒截止时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date remindTime;

    /** 免提醒天数 */
    @Excel(name = "免提醒天数")
    private Long remindDays;

    /** 处置人 */
    @Excel(name = "处置人")
    private String disposePersonName;

    /** 处置人id */
    @Excel(name = "处置人id")
    private Long disposePersonId;

}
