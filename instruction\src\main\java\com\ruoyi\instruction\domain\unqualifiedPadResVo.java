package com.ruoyi.instruction.domain;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.common.annotation.Excel;

import java.math.BigDecimal;
import java.util.Date;

public class unqualifiedPadResVo {

    private static final long serialVersionUID = 1L;

    /** 年 */
    @Excel(name = "年")
    private String year;

    /** 区域 */
    @Excel(name = "县市区", combo = {"金华市","婺城区","金东区","兰溪市","东阳市","义乌市","永康市","浦江县","武义县","磐安县","开发区"})
    private String area;

    /** 排名 */
    @Excel(name = "排名")
    private Long ranking;

    /** 得分 */
    @Excel(name = "得分")
    private BigDecimal score;


    /** 奖励 */
    @Excel(name = "获鼎情况",combo = {"塑鼎","铜鼎","银鼎","金鼎","一星金鼎","二星金鼎","三星金鼎"})
    private String prize;

}
