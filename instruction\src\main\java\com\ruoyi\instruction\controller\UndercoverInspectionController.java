package com.ruoyi.instruction.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.instruction.domain.UndercoverInspection;
import com.ruoyi.instruction.service.IUndercoverInspectionService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 暗访督察Controller
 * 
 * <AUTHOR>
 * @date 2023-05-23
 */
@RestController
@RequestMapping("/instruction/undercoverInspection")
public class UndercoverInspectionController extends BaseController
{
    @Autowired
    private IUndercoverInspectionService undercoverInspectionService;

    /**
     * 查询暗访督察列表
     */
//    @PreAuthorize("@ss.hasPermi('instruction:undercoverInspection:list')")
    @GetMapping("/list")
    public TableDataInfo list(UndercoverInspection undercoverInspection)
    {
        startPage();
        List<UndercoverInspection> list = undercoverInspectionService.selectUndercoverInspectionList(undercoverInspection);
        return getDataTable(list);
    }

    /**
     * 导出暗访督察列表
     */
//    @PreAuthorize("@ss.hasPermi('instruction:undercoverInspection:export')")
    @Log(title = "暗访督察", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, UndercoverInspection undercoverInspection)
    {
        List<UndercoverInspection> list = undercoverInspectionService.selectUndercoverInspectionList(undercoverInspection);
        ExcelUtil<UndercoverInspection> util = new ExcelUtil<UndercoverInspection>(UndercoverInspection.class);
        util.exportExcel(response, list, "暗访督察数据");
    }

    /**
     * 获取暗访督察详细信息
     */
//    @PreAuthorize("@ss.hasPermi('instruction:undercoverInspection:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(undercoverInspectionService.selectUndercoverInspectionById(id));
    }

    /**
     * 新增暗访督察
     */
//    @PreAuthorize("@ss.hasPermi('instruction:undercoverInspection:add')")
    @Log(title = "暗访督察", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody UndercoverInspection undercoverInspection)
    {
        return toAjax(undercoverInspectionService.insertUndercoverInspection(undercoverInspection));
    }

    /**
     * 修改暗访督察
     */
//    @PreAuthorize("@ss.hasPermi('instruction:undercoverInspection:edit')")
    @Log(title = "暗访督察", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody UndercoverInspection undercoverInspection)
    {
        return toAjax(undercoverInspectionService.updateUndercoverInspection(undercoverInspection));
    }

    /**
     * 删除暗访督察
     */
//    @PreAuthorize("@ss.hasPermi('instruction:undercoverInspection:remove')")
    @Log(title = "暗访督察", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(undercoverInspectionService.deleteUndercoverInspectionByIds(ids));
    }
}
