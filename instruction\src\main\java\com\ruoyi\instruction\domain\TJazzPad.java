package com.ruoyi.instruction.domain;

import java.math.BigDecimal;
import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.common.annotation.Excel;
import lombok.Data;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 金安智治-平安金华-平安鼎对象 t_jazz_pad
 *
 * <AUTHOR>
 * @date 2023-07-11
 */
public class TJazzPad extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** $column.columnComment */
    private Long id;

    /** 年 */
    @Excel(name = "年")
    private String year;

    /** 区域 */
    @Excel(name = "县市区", combo = {"金华市","婺城区","金东区","兰溪市","东阳市","义乌市","永康市","浦江县","武义县","磐安县","开发区"})
    private String area;

    /** 排名 */
    @Excel(name = "排名")
    private Long ranking;

    /** 得分 */
    @Excel(name = "得分")
    private BigDecimal score;

    /** 创建时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date cTime;

    /** 修改时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date uTime;

    /** 奖励 */
    @Excel(name = "获鼎情况",combo = {"塑鼎","铜鼎","银鼎","金鼎","一星金鼎","二星金鼎","三星金鼎"})
    private String prize;

    /** 是否被抽查 */
    @Excel(name = "是否被抽查",combo = {"是","否"})
    private String sfbcc;

    /**被抽查区域*/
    private String csarea;

    /**
     * 排序类型，1按年排序
     */
    private Integer orderType;

    /**
     *
     * 排序类型，按排名排序 1正序 2倒序
     */
    private Integer orderRanking;

    /**
     * 查询类别：年度、区县
     */
    private String type;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getYear() {
        return year;
    }

    public void setYear(String year) {
        this.year = year;
    }

    public String getArea() {
        return area;
    }

    public void setArea(String area) {
        this.area = area;
    }

    public Long getRanking() {
        return ranking;
    }

    public void setRanking(Long ranking) {
        this.ranking = ranking;
    }

    public BigDecimal getScore() {
        return score;
    }

    public void setScore(BigDecimal score) {
        this.score = score;
    }

    public Date getcTime() {
        return cTime;
    }

    public void setcTime(Date cTime) {
        this.cTime = cTime;
    }

    public Date getuTime() {
        return uTime;
    }

    public void setuTime(Date uTime) {
        this.uTime = uTime;
    }

    public String getPrize() {
        return prize;
    }

    public void setPrize(String prize) {
        this.prize = prize;
    }

    public String getSfbcc() {
        return sfbcc;
    }

    public void setSfbcc(String sfbcc) {
        this.sfbcc = sfbcc;
    }

    public Integer getOrderType() {
        return orderType;
    }

    public void setOrderType(Integer orderType) {
        this.orderType = orderType;
    }

    public Integer getOrderRanking() {
        return orderRanking;
    }

    public void setOrderRanking(Integer orderRanking) {
        this.orderRanking = orderRanking;
    }

    public String getType() {
        return type;
    }
    public void setType(String type) {
        this.type = type;
    }

    public String getCsarea() {
        return csarea;
    }

    public void setCsarea(String csarea) {
        this.csarea = csarea;
    }
}
