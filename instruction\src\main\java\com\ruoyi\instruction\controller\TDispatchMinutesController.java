package com.ruoyi.instruction.controller;

import java.util.List;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.instruction.domain.TDispatchMinutes;
import com.ruoyi.instruction.service.ITDispatchMinutesService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import lombok.extern.slf4j.Slf4j;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 调度纪要Controller
 * 
 * <AUTHOR>
 * @date 2023-09-18
 */
@RestController
@RequestMapping("/instruction/dispatchMinutes")
@Slf4j
public class TDispatchMinutesController extends BaseController
{
    @Autowired
    private ITDispatchMinutesService tDispatchMinutesService;

    /**
     * 查询调度纪要列表
     */
    @PreAuthorize("@ss.hasPermi('instruction:dispatchMinutes:list')")
    @GetMapping("/list")
    public TableDataInfo list(TDispatchMinutes tDispatchMinutes)
    {
        startPage();
        List<TDispatchMinutes> list = tDispatchMinutesService.selectTDispatchMinutesList(tDispatchMinutes);
        return getDataTable(list);
    }

    /**
     * 导出调度纪要列表
     */
    @PreAuthorize("@ss.hasPermi('instruction:dispatchMinutes:export')")
    @Log(title = "调度纪要", businessType = BusinessType.EXPORT)
    @GetMapping("/export")
    public AjaxResult export(TDispatchMinutes tDispatchMinutes)
    {
        List<TDispatchMinutes> list = tDispatchMinutesService.selectTDispatchMinutesList(tDispatchMinutes);
        ExcelUtil<TDispatchMinutes> util = new ExcelUtil<TDispatchMinutes>(TDispatchMinutes.class);
        return util.exportExcel(list, "dispatchMinutes");
    }

    /**
     * 获取调度纪要详细信息
     */
    @PreAuthorize("@ss.hasPermi('instruction:dispatchMinutes:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return AjaxResult.success(tDispatchMinutesService.selectTDispatchMinutesById(id));
    }

    /**
     * 新增调度纪要
     */
    @PreAuthorize("@ss.hasPermi('instruction:dispatchMinutes:add')")
    @Log(title = "调度纪要", businessType = BusinessType.INSERT)
    @PostMapping("/add")
    public AjaxResult add(@RequestBody TDispatchMinutes tDispatchMinutes)
    {
        return toAjax(tDispatchMinutesService.insertTDispatchMinutes(tDispatchMinutes));
    }

    /**
     * 修改调度纪要
     */
    @PreAuthorize("@ss.hasPermi('instruction:dispatchMinutes:edit')")
    @Log(title = "调度纪要", businessType = BusinessType.UPDATE)
    @PostMapping("/edit")
    public AjaxResult edit(@RequestBody TDispatchMinutes tDispatchMinutes)
    {
        return toAjax(tDispatchMinutesService.updateTDispatchMinutes(tDispatchMinutes));
    }

    /**
     * 删除调度纪要
     */
    @PreAuthorize("@ss.hasPermi('instruction:dispatchMinutes:remove')")
    @Log(title = "调度纪要", businessType = BusinessType.DELETE)
	@PostMapping("/remove/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(tDispatchMinutesService.deleteTDispatchMinutesByIds(ids));
    }
}
