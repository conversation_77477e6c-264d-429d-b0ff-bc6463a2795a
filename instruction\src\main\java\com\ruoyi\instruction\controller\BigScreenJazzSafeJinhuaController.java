package com.ruoyi.instruction.controller;

import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.instruction.domain.JazzPajhAreaStatistics;
import com.ruoyi.instruction.domain.JazzPajhTown;
import com.ruoyi.instruction.domain.TJazzPad;
import com.ruoyi.instruction.domain.reqVo.JazzPajhAreaStatisticsVo;
import com.ruoyi.instruction.service.IJazzPajhAreaStatisticsService;
import com.ruoyi.instruction.service.IJazzPajhTownService;
import com.ruoyi.instruction.service.ITJazzPadService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 金安智治驾驶舱平安金华
 * <AUTHOR> @version 1.0
 * @date 2
 *
 */
@RestController
@RequestMapping("/bigScreen/jazz/SafeJinhua")
public class BigScreenJazzSafeJinhuaController extends BaseController {

    @Autowired
    private IJazzPajhAreaStatisticsService jazzPajhAreaStatisticsService;

    @Autowired
    private IJazzPajhTownService jazzPajhTownService;
    @Autowired
    private ITJazzPadService itJazzPadService;
    /**
     * 省考情况
     */
    @GetMapping("/provinceSituation")
    public AjaxResult provinceSituation() {
        JazzPajhAreaStatistics jazzPajhAreaStatistics=new JazzPajhAreaStatistics();
        jazzPajhAreaStatistics.setType(3L);
        jazzPajhAreaStatistics.setArea("金华市");
         jazzPajhAreaStatistics = packageParameter1(jazzPajhAreaStatistics);
        JazzPajhAreaStatistics jazzPajhAreaStatistics1 = jazzPajhAreaStatisticsService.selectAreaNew(jazzPajhAreaStatistics);
        JazzPajhAreaStatistics jazzPajhAreaStatistics2 = jazzPajhAreaStatisticsService.selectMAx(jazzPajhAreaStatistics);
        HashMap hashMap=new HashMap();
        hashMap.put("area",jazzPajhAreaStatistics1==null?null:jazzPajhAreaStatistics1.getScore().stripTrailingZeros().toPlainString());
        hashMap.put("ranking",jazzPajhAreaStatistics1==null?null:jazzPajhAreaStatistics1.getRanking());
//        hashMap.put("maxRanking",jazzPajhAreaStatistics2==null?null:jazzPajhAreaStatistics2.getScore().stripTrailingZeros().toPlainString());
        hashMap.put("maxRanking",jazzPajhAreaStatistics2==null?null:jazzPajhAreaStatistics2.getRanking());
        return  AjaxResult.success(hashMap);
    }

//    /**
//     * 排名情况
//     * @param type  2地市，1县市区
//     * @return
//     */
//    @GetMapping("/rankingSituation")
//    public AjaxResult rankingSituation(Integer type) {
//        JazzPajhAreaStatisticsVo jazzPajhAreaStatisticsVo=new JazzPajhAreaStatisticsVo();
//        jazzPajhAreaStatisticsVo=packageParameter(jazzPajhAreaStatisticsVo);
//        jazzPajhAreaStatisticsVo.setType(type==2?3L:type);
//        jazzPajhAreaStatisticsVo.setOrderType(1);
//        List<JazzPajhAreaStatistics> list = jazzPajhAreaStatisticsService.selectList(jazzPajhAreaStatisticsVo);
//        return AjaxResult.success(list);
//    }

    /**
     * 排名情况
     * @param type  2地市，1县市区
     * @return
     */
    @GetMapping("/rankingSituation")
    public AjaxResult rankingSituationNew(Integer type) {
        JazzPajhAreaStatisticsVo jazzPajhAreaStatisticsVo=new JazzPajhAreaStatisticsVo();
        jazzPajhAreaStatisticsVo=packageParameter(jazzPajhAreaStatisticsVo);
        jazzPajhAreaStatisticsVo.setType(type==2?3L:type);
        jazzPajhAreaStatisticsVo.setOrderType(1);
        List<JazzPajhAreaStatistics> list = jazzPajhAreaStatisticsService.selectList(jazzPajhAreaStatisticsVo);
        return AjaxResult.success(list);
    }

    /**
     * 部门评价
     * @param type  1top5 2 全部
     * @return
     */
    @GetMapping("/sectoralEvaluate")
    public AjaxResult sectoralEvaluate(Integer type) {
        JazzPajhAreaStatisticsVo jazzPajhAreaStatisticsVo=new JazzPajhAreaStatisticsVo();
        jazzPajhAreaStatisticsVo=packageParameter(jazzPajhAreaStatisticsVo);
        jazzPajhAreaStatisticsVo.setType(2L);
        List<JazzPajhAreaStatistics> list = jazzPajhAreaStatisticsService.selectList(jazzPajhAreaStatisticsVo);
        if (type==1){
            JazzPajhAreaStatistics jazzPajhAreaStatistics = list.get(0);
            String str="";
            if (jazzPajhAreaStatistics!=null&&jazzPajhAreaStatistics.getScore()!=null){
                 str=String.format("%s今年在本月工作突出，值得各单位学习借鉴", jazzPajhAreaStatistics.getArea());
            }

            HashMap hashMap=new HashMap();
            hashMap.put("notice",str);
           list = list.subList(0, 5);
            hashMap.put("list",list);
            return AjaxResult.success(hashMap);
        }
        return AjaxResult.success(list);
    }

    /**
     * 得分趋势
     * @param  areaId 县市区列表id
     * @return
     */
    @GetMapping("/scoreTrend")
    public AjaxResult scoreTrend(Long areaId) {
        HashMap result=new HashMap();
        JazzPajhAreaStatistics jazzPajhAreaStatistics=new JazzPajhAreaStatistics();
        LocalDate current_date = LocalDate.now();
        String year = current_date.getYear() + "";
        jazzPajhAreaStatistics.setYear(year);
        jazzPajhAreaStatistics.setType(1L);
        jazzPajhAreaStatistics.setAreaId(areaId);
        List<JazzPajhAreaStatistics> area = jazzPajhAreaStatisticsService.selectJazzPajhAreaStatisticsList(jazzPajhAreaStatistics);
        jazzPajhAreaStatistics.setType(3L);
        jazzPajhAreaStatistics.setAreaId(null);
        jazzPajhAreaStatistics.setArea("金华市");
        List<JazzPajhAreaStatistics> jinhua = jazzPajhAreaStatisticsService.selectJazzPajhAreaStatisticsList(jazzPajhAreaStatistics);
        Map<String, JazzPajhAreaStatistics> areamap = area.stream().collect(Collectors.toMap(JazzPajhAreaStatistics::getMonth, Function.identity()));
        Map<String, JazzPajhAreaStatistics> jinhuamap = jinhua.stream().collect(Collectors.toMap(JazzPajhAreaStatistics::getMonth, Function.identity()));
        List<String> oneMonth = DateUtils.getOneMonth(3);
        List list=new ArrayList();
        Integer i=0;
        BigDecimal totalArea=BigDecimal.ZERO;
        BigDecimal totalCity=BigDecimal.ZERO;
        for (String s:oneMonth){
            HashMap hashMap=new HashMap();
            hashMap.put("month",s+"月");
            BigDecimal areaScore=areamap.get(s)==null?BigDecimal.ZERO:areamap.get(s).getScore();
            BigDecimal cityScore=jinhuamap.get(s)==null?BigDecimal.ZERO:jinhuamap.get(s).getScore();
            hashMap.put("area",areaScore);
            hashMap.put("city",cityScore);
            list.add(hashMap);
            if (areaScore.compareTo(cityScore)==1){
                i=i+1;
            }
            totalArea=totalArea.add(areaScore);
            totalCity=totalCity.add(cityScore);
        }
        String s="";
        if (totalCity.compareTo(BigDecimal.ZERO)==0){
            s="高于";
        }else {
            s=totalArea.divide(new BigDecimal(areamap.size())).compareTo(  totalCity.divide(new BigDecimal(jinhuamap.size())))==1?"高于":"低于";
        }
        String str=String.format("%s，超过平均分%s次，年度平均分%s全市得分", area.get(0).getArea(),i,s);
        result.put("notice",str);
        result.put("list",list);
        return AjaxResult.success(result);
    }

    /**
     * 获取县市区列表
     */
//    @PreAuthorize("@ss.hasPermi('instruction:town:list')")
    @GetMapping("/areaList")
    public AjaxResult areaList()
    {
        JazzPajhTown jazzPajhTown=new JazzPajhTown();
        jazzPajhTown.setType(1);
        List<JazzPajhTown> list = jazzPajhTownService.selectJazzPajhTownList(jazzPajhTown);
        return AjaxResult.success(list);
    }


    /**
     * 乡镇得分（当月）
     */
//    @PreAuthorize("@ss.hasPermi('instruction:town:list')")
    @GetMapping("/townScoreMonth")
    public AjaxResult townScoreMonth(JazzPajhAreaStatisticsVo jazzPajhAreaStatisticsVo){
        HashMap hashMap=new HashMap();
        LocalDate current_date = LocalDate.now();
        String year = current_date.getYear() + "";
        jazzPajhAreaStatisticsVo.setYear(year);
//        JazzPajhAreaStatisticsVo jazzPajhAreaStatisticsVo=new JazzPajhAreaStatisticsVo();
//        jazzPajhAreaStatisticsVo = packageParameter(jazzPajhAreaStatisticsVo);
        jazzPajhAreaStatisticsVo.setType(4L);
        List<JazzPajhAreaStatistics> jinhua = jazzPajhAreaStatisticsService.selectList(jazzPajhAreaStatisticsVo);
        hashMap.put("top10",jinhua.subList(0,jinhua.size()<10?jinhua.size():10));
        hashMap.put("Bottom10",jinhua.subList((jinhua.size()-10)>0?(jinhua.size()-10):0,jinhua.size()));
        return  AjaxResult.success(hashMap);
    }

    /**
     * 乡镇得分（升降）
     */
//    @PreAuthorize("@ss.hasPermi('instruction:town:list')")
    @GetMapping("/townScoreMonthLifting")
    public AjaxResult townScoreMonthLifting(){
        HashMap hashMap=new HashMap();
        JazzPajhAreaStatisticsVo jazzPajhAreaStatisticsVo=new JazzPajhAreaStatisticsVo();
        jazzPajhAreaStatisticsVo = packageParameter(jazzPajhAreaStatisticsVo);
        jazzPajhAreaStatisticsVo.setType(4L);
        List<JazzPajhAreaStatistics> jinhua = jazzPajhAreaStatisticsService.selectList(jazzPajhAreaStatisticsVo);

        int mouth = Integer.parseInt(jazzPajhAreaStatisticsVo.getMonth().replace("月", ""))-1;
        if (mouth==0){
            jazzPajhAreaStatisticsVo.setMonth(12+"");
            jazzPajhAreaStatisticsVo.setYear((Integer.parseInt(jazzPajhAreaStatisticsVo.getYear().replace("年", ""))-1)+"");
        }
        //获取上月数据
        List<JazzPajhAreaStatistics> shangyue = jazzPajhAreaStatisticsService.selectList(jazzPajhAreaStatisticsVo);
        Map<Long, JazzPajhAreaStatistics> collect = shangyue.stream().filter(x -> x.getId() != null || x.getScore() != null).collect(Collectors.toMap(JazzPajhAreaStatistics::getTownId, Function.identity()));
        List<JazzPajhAreaStatistics> up=new ArrayList<>();
        List<JazzPajhAreaStatistics> down=new ArrayList<>();
        for (JazzPajhAreaStatistics j:jinhua){
            if (j.getRanking()!=null){
                JazzPajhAreaStatistics jazzPajhAreaStatistics = collect.get(j.getTownId());
                if (jazzPajhAreaStatistics!=null&&jazzPajhAreaStatistics.getScore()!=null){
                    j.setRanking(j.getRanking()-jazzPajhAreaStatistics.getRanking());
                    if (j.getRanking()>0){
                        up.add(j);
                    }else  if (j.getRanking()<0){
                        j.setRanking(Math.abs(j.getRanking()));
                        down.add(j);
                    }

                }
            }
        }

        List<JazzPajhAreaStatistics> collect1 = up.stream().sorted(Comparator.comparing(JazzPajhAreaStatistics::getRanking)).collect(Collectors.toList());
        List<JazzPajhAreaStatistics> collect2 = down.stream().sorted(Comparator.comparing(JazzPajhAreaStatistics::getRanking).reversed()).collect(Collectors.toList());
        hashMap.put("upload",collect1.subList(0,collect1.size()>10?10:collect1.size()));
        hashMap.put("down",collect2.subList(collect1.size()>10?(collect1.size()-10):0,collect1.size()));
        return  AjaxResult.success(hashMap);
    }

    /**
     * 平安金华-金安指数-获取第一名和最后一名消息
     * @param month
     * @return
     */
    @GetMapping("/getFirstAndLast")
    public  AjaxResult getFirstAndLast(String month){
        JazzPajhAreaStatisticsVo jazzPajhAreaStatisticsVo=new JazzPajhAreaStatisticsVo();
        jazzPajhAreaStatisticsVo.setType(4L);
        jazzPajhAreaStatisticsVo.setMonth(month);
        //获取当前年月
        LocalDate current_date = LocalDate.now();
        jazzPajhAreaStatisticsVo.setYear(current_date.getYear() + "");
        List<JazzPajhAreaStatistics> jinhua = jazzPajhAreaStatisticsService.selectListNew(jazzPajhAreaStatisticsVo);
        HashMap hashMap=new HashMap();
        hashMap.put("first",jinhua.get(0));
        hashMap.put("last",jinhua.get(jinhua.size()-1));
        return  AjaxResult.success(hashMap);
    }

    /**
     * 平安金华-金安指数列表
     * @param month
     * @param type 类型，1地区，3地级市，4乡镇街道
     * @return
     */
    @GetMapping("/getPazsList")
    public  AjaxResult getPazsList(String month,Long type){
        JazzPajhAreaStatisticsVo jazzPajhAreaStatisticsVo=new JazzPajhAreaStatisticsVo();
        jazzPajhAreaStatisticsVo.setType(type);
        jazzPajhAreaStatisticsVo.setMonth(month);
        //获取当前年月
        LocalDate current_date = LocalDate.now();
        jazzPajhAreaStatisticsVo.setYear(current_date.getYear() + "");
        List<JazzPajhAreaStatistics> jinhua = jazzPajhAreaStatisticsService.selectList(jazzPajhAreaStatisticsVo);
        return  AjaxResult.success(jinhua);
    }

    /**
     * 平安金华-平安鼎区县情况
     * @return
     */
    @GetMapping("/getPadList")
    public  AjaxResult getPadList(){
        List<TJazzPad> padList = itJazzPadService.getPadList();
        boolean b=false;
        for(TJazzPad t:padList){
            if ("开发区".equals(t.getArea())){
                b=true;
                break;
            }
        }
        if (!b){
            TJazzPad tJazzPad=new TJazzPad();
            tJazzPad.setArea("开发区");
            padList.add(tJazzPad);
        }
        return   AjaxResult.success(padList);
    }

    /**
     * 平安金华-平安鼎区县列表
     * @return
     */
    @GetMapping("/getPadListForArea")
    public  AjaxResult getPadListForArea(String area){
        TJazzPad tJazzPad=new TJazzPad();
        tJazzPad.setArea(area);
        tJazzPad.setOrderType(1);
        return   AjaxResult.success(itJazzPadService.selectTJazzPadList(tJazzPad));
    }
    /**
     * 封装参数
     * @return
     */
    public JazzPajhAreaStatisticsVo  packageParameter( JazzPajhAreaStatisticsVo jazzPajhAreaStatistics){

        JazzPajhAreaStatistics jazzPajhAreaStatistics1 = jazzPajhAreaStatisticsService.selectNew(jazzPajhAreaStatistics.getType());
        //获取最近的时间
        if (jazzPajhAreaStatistics1 != null) {
            jazzPajhAreaStatistics.setYear(jazzPajhAreaStatistics1.getYear());
            jazzPajhAreaStatistics.setMonth(jazzPajhAreaStatistics1.getMonth());
        } else {
            //获取当前年月
            LocalDate current_date = LocalDate.now();
            jazzPajhAreaStatistics.setYear(current_date.getYear() + "");
            jazzPajhAreaStatistics.setMonth(current_date.getMonth().getValue() + "");
        }
        return  jazzPajhAreaStatistics;
    }
    /**
     * 封装参数
     * @return
     */
    public JazzPajhAreaStatistics  packageParameter1(JazzPajhAreaStatistics jazzPajhAreaStatistics){

        JazzPajhAreaStatistics jazzPajhAreaStatistics1 = jazzPajhAreaStatisticsService.selectNew(2L);
        //获取最近的时间
        if (jazzPajhAreaStatistics1 != null) {
            jazzPajhAreaStatistics.setYear(jazzPajhAreaStatistics1.getYear());
            jazzPajhAreaStatistics.setMonth(jazzPajhAreaStatistics1.getMonth());
        } else {
            //获取当前年月
            LocalDate current_date = LocalDate.now();
            jazzPajhAreaStatistics.setYear(current_date.getYear() + "");
            jazzPajhAreaStatistics.setMonth(current_date.getMonth().getValue() + "");
        }
        return  jazzPajhAreaStatistics;
    }
}
