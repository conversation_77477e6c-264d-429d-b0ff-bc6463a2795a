package com.ruoyi.examineAir.mapper;

import java.util.List;
import com.ruoyi.examineAir.domain.ExamineConfig;

/**
 * 考核晾晒动态配置信息Mapper接口
 * 
 * <AUTHOR>
 * @date 2023-04-19
 */
public interface ExamineConfigMapper 
{
    /**
     * 查询考核晾晒动态配置信息
     * 
     * @param id 考核晾晒动态配置信息主键
     * @return 考核晾晒动态配置信息
     */
    public ExamineConfig selectExamineConfigById(Long id);

    /**
     * 查询考核晾晒动态配置信息列表
     * 
     * @param examineConfig 考核晾晒动态配置信息
     * @return 考核晾晒动态配置信息集合
     */
    public List<ExamineConfig> selectExamineConfigList(ExamineConfig examineConfig);

    /**
     * 新增考核晾晒动态配置信息
     * 
     * @param examineConfig 考核晾晒动态配置信息
     * @return 结果
     */
    public int insertExamineConfig(ExamineConfig examineConfig);

    /**
     * 修改考核晾晒动态配置信息
     * 
     * @param examineConfig 考核晾晒动态配置信息
     * @return 结果
     */
    public int updateExamineConfig(ExamineConfig examineConfig);

    /**
     * 删除考核晾晒动态配置信息
     * 
     * @param id 考核晾晒动态配置信息主键
     * @return 结果
     */
    public int deleteExamineConfigById(Long id);

    /**
     * 批量删除考核晾晒动态配置信息
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteExamineConfigByIds(Long[] ids);
}
