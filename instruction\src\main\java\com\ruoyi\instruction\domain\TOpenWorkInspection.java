package com.ruoyi.instruction.domain;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;
import com.ruoyi.instruction.domain.rspVo.OpenWorkImage;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.util.Date;
import java.util.List;

/**
 * 工作督查数据对接对象 t_open_work_inspection
 *
 * <AUTHOR>
 * @date 2024-08-08
 */
@Data
public class TOpenWorkInspection extends BaseEntity
{private static final long serialVersionUID = 1L;

    /**  */
    private Long id;

    /** 任务id */
    private String missionId;

    /** 任务名称 */
    @Excel(name = "任务名称")
    private String missionName;

    /** 区县 */
    @Excel(name = "县(市、区)")
    private String county;

    /** 乡镇 */
    @Excel(name = "乡镇(街道)")
    private String town;


    /** 单位名称 */
    @Excel(name = "检查单位名称")
    private String unitName;

    /** 地址 */
    @Excel(name = "详细地址")
    private String address;

    /** 检查时间 */
    @Excel(name = "检查时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;


    /** 状态 */
    @Excel(name = "督查结果")
    private String status;

    /** 问题 */
    @Excel(name = "问题描述")
    private String problem;


    /** 处置环节 */
    @Excel(name = "处置状态")
    private String dealStep;

    /**
     * 整改结果
     */
    @Excel(name = "整改结果")
    private String dealResult;


    /** 反馈情况 */
    @Excel(name = "整改情况")
    private String feedbackSituation;


    /** 预估完成时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "预估完成时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date forecastTime;


    /** 反馈时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "整改完成时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date feedbackTime;


    /** 整改人 */
    @Excel(name = "整改人")
    private String dealPerson;

    /** 任务开始时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "任务开始时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date missionStartTime;

    /** 任务结束时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "任务结束时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date missionEndTime;

    /** 区域编码 */
    private String missionAreaCode;

    /** 工作名称 */
    private String workerName;

    /** 工作手机号 */
    private String workerPhone;

    /** 区域编码 */
    private String areaCode;

    private String imgIds;

    private String imgUrls;

    /** 接收单位 */
    private String receiveUnit;

    /** 办理期限 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date handleTime;

    /** 反馈要求 */
    private String feedback;

    private List<OpenWorkImage> imgList;

    /** 图片id */
    private String dealFiles;

    /** 指令id */
    private Long instructionId;

    /**
     * 检查时间
     */
    private Long createDate;




}
