package com.ruoyi.instruction.domain;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 群体处置记录对象 t_group_dispose
 * 
 * <AUTHOR>
 * @date 2024-12-19
 */
public class GroupDispose extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键id */
    private Long id;

    /** 处置日期 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "处置日期", width = 30, dateFormat = "yyyy-MM-dd")
    private Date disposeTime;

    /** 主要责任单位 */
    @Excel(name = "主要责任单位")
    private String majorUnit;

    /** 处置描述 */
    @Excel(name = "处置描述")
    private String disposeDescription;

    /** 状态 1：正常 9：删除 */
    @Excel(name = "状态 1：正常 9：删除")
    private String status;

    /** 附件ids */
    @Excel(name = "附件ids")
    private String fileIds;

    /** 群体id */
    @Excel(name = "群体id")
    private Long groupId;

    public void setId(Long id) 
    {
        this.id = id;
    }

    public Long getId() 
    {
        return id;
    }
    public void setDisposeTime(Date disposeTime) 
    {
        this.disposeTime = disposeTime;
    }

    public Date getDisposeTime() 
    {
        return disposeTime;
    }
    public void setMajorUnit(String majorUnit) 
    {
        this.majorUnit = majorUnit;
    }

    public String getMajorUnit() 
    {
        return majorUnit;
    }
    public void setDisposeDescription(String disposeDescription) 
    {
        this.disposeDescription = disposeDescription;
    }

    public String getDisposeDescription() 
    {
        return disposeDescription;
    }
    public void setStatus(String status) 
    {
        this.status = status;
    }

    public String getStatus() 
    {
        return status;
    }
    public void setFileIds(String fileIds) 
    {
        this.fileIds = fileIds;
    }

    public String getFileIds() 
    {
        return fileIds;
    }
    public void setGroupId(Long groupId) 
    {
        this.groupId = groupId;
    }

    public Long getGroupId() 
    {
        return groupId;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("disposeTime", getDisposeTime())
            .append("createTime", getCreateTime())
            .append("majorUnit", getMajorUnit())
            .append("disposeDescription", getDisposeDescription())
            .append("status", getStatus())
            .append("fileIds", getFileIds())
            .append("groupId", getGroupId())
            .toString();
    }
}
