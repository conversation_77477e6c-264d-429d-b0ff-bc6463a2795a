package com.ruoyi.instruction.mapper;

import java.util.List;
import com.ruoyi.instruction.domain.ProvinceYj;

/**
 * 省级预警Mapper接口
 * 
 * <AUTHOR>
 * @date 2023-09-21
 */
public interface ProvinceYjMapper 
{
    /**
     * 查询省级预警
     * 
     * @param id 省级预警主键
     * @return 省级预警
     */
    public ProvinceYj selectProvinceYjById(Long id);

    /**
     * 查询省级预警列表
     * 
     * @param provinceYj 省级预警
     * @return 省级预警集合
     */
    public List<ProvinceYj> selectProvinceYjList(ProvinceYj provinceYj);

    /**
     * 新增省级预警
     * 
     * @param provinceYj 省级预警
     * @return 结果
     */
    public int insertProvinceYj(ProvinceYj provinceYj);

    /**
     * 修改省级预警
     * 
     * @param provinceYj 省级预警
     * @return 结果
     */
    public int updateProvinceYj(ProvinceYj provinceYj);

    /**
     * 删除省级预警
     * 
     * @param id 省级预警主键
     * @return 结果
     */
    public int deleteProvinceYjById(Long id);

    /**
     * 批量删除省级预警
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteProvinceYjByIds(Long[] ids);

    /**
     * 获取预警月份
     * @param provinceYj
     * @return
     */
    List<String> getMonth(ProvinceYj provinceYj);
}
