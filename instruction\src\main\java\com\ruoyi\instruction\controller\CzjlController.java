package com.ruoyi.instruction.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.instruction.domain.Czjl;
import com.ruoyi.instruction.service.ICzjlService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 政法委转情指（流程）Controller
 * 
 * <AUTHOR>
 * @date 2023-06-07
 */
@RestController
@RequestMapping("/instruction/czjl")
public class CzjlController extends BaseController
{
    @Autowired
    private ICzjlService czjlService;

    /**
     * 查询政法委转情指（流程）列表
     */
    @PreAuthorize("@ss.hasPermi('instruction:czjl:list')")
    @GetMapping("/list")
    public TableDataInfo list(Czjl czjl)
    {
        startPage();
        List<Czjl> list = czjlService.selectCzjlList(czjl);
        return getDataTable(list);
    }

    /**
     * 导出政法委转情指（流程）列表
     */
    @PreAuthorize("@ss.hasPermi('instruction:czjl:export')")
    @Log(title = "政法委转情指（流程）", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, Czjl czjl)
    {
        List<Czjl> list = czjlService.selectCzjlList(czjl);
        ExcelUtil<Czjl> util = new ExcelUtil<Czjl>(Czjl.class);
        util.exportExcel(response, list, "政法委转情指（流程）数据");
    }

    /**
     * 获取政法委转情指（流程）详细信息
     */
    @PreAuthorize("@ss.hasPermi('instruction:czjl:query')")
    @GetMapping(value = "/{qzid}")
    public AjaxResult getInfo(@PathVariable("qzid") String qzid)
    {
        return success(czjlService.selectCzjlByQzid(qzid));
    }

    /**
     * 新增政法委转情指（流程）
     */
    @PreAuthorize("@ss.hasPermi('instruction:czjl:add')")
    @Log(title = "政法委转情指（流程）", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody Czjl czjl)
    {
        return toAjax(czjlService.insertCzjl(czjl));
    }

    /**
     * 修改政法委转情指（流程）
     */
    @PreAuthorize("@ss.hasPermi('instruction:czjl:edit')")
    @Log(title = "政法委转情指（流程）", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody Czjl czjl)
    {
        return toAjax(czjlService.updateCzjl(czjl));
    }

    /**
     * 删除政法委转情指（流程）
     */
    @PreAuthorize("@ss.hasPermi('instruction:czjl:remove')")
    @Log(title = "政法委转情指（流程）", businessType = BusinessType.DELETE)
	@DeleteMapping("/{qzids}")
    public AjaxResult remove(@PathVariable String[] qzids)
    {
        return toAjax(czjlService.deleteCzjlByQzids(qzids));
    }
}
