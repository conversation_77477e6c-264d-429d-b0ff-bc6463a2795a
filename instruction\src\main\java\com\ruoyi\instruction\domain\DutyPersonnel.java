package com.ruoyi.instruction.domain;

import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

/**
 * 值班人员对象 t_duty_personnel
 * 
 * <AUTHOR>
 * @date 2023-08-04
 */
public class DutyPersonnel extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** $column.columnComment */
    private Long id;

    /** 姓名 */
    @Excel(name = "姓名")
    private String name;

    /** 手机号 */
    @Excel(name =  "手机号")
    private String phone;

    /** 职责 */
    @Excel(name = "职务")
    private String duties;

    /** 部门id */
//    @ExcelProperty("部门id")
    private Long deptId;

    /** 头像 */
    @Excel(name =  "头像")
    private String userUrl;
    /**
     * 区域
     */
    @Excel(name = "县市区")
    private String area;
    /**
     * 乡镇街道
     */
    @Excel(name = "乡镇街道")
    private String town;
    /**
     * 是否发布，1已发布，2未发布
     */
    private Integer isRelease;
    @Excel(name = "部门")
    private String deptName;

    public void setId(Long id) 
    {
        this.id = id;
    }

    public Long getId() 
    {
        return id;
    }
    public void setName(String name) 
    {
        this.name = name;
    }

    public String getName() 
    {
        return name;
    }
    public void setPhone(String phone) 
    {
        this.phone = phone;
    }

    public String getPhone() 
    {
        return phone;
    }
    public void setDuties(String duties) 
    {
        this.duties = duties;
    }

    public String getDuties() 
    {
        return duties;
    }
    public void setDeptId(Long deptId) 
    {
        this.deptId = deptId;
    }

    public Long getDeptId() 
    {
        return deptId;
    }
    public void setUserUrl(String userUrl) 
    {
        this.userUrl = userUrl;
    }

    public String getUserUrl() 
    {
        return userUrl;
    }

    public String getArea() {
        return area;
    }

    public void setArea(String area) {
        this.area = area;
    }

    public String getTown() {
        return town;
    }

    public void setTown(String town) {
        this.town = town;
    }

    public Integer getIsRelease() {
        return isRelease;
    }

    public void setIsRelease(Integer isRelease) {
        this.isRelease = isRelease;
    }

    public String getDeptName() {
        return deptName;
    }

    public void setDeptName(String deptName) {
        this.deptName = deptName;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("name", getName())
            .append("phone", getPhone())
            .append("duties", getDuties())
            .append("deptId", getDeptId())
            .append("userUrl", getUserUrl())
            .toString();
    }
}
