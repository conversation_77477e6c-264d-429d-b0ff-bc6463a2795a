package com.ruoyi.instruction.domain.rspVo;

import com.deepoove.poi.data.ChartMultiSeriesRenderData;
import com.deepoove.poi.data.TableRenderData;
import lombok.Data;

import java.util.List;

/**
 * 事件分析报告
 * <AUTHOR>
 */
@Data
public class EventAnalysisReportNewVo {
    /**
     * 创建时间
     */
    private String createTime;
    /**
     * 事件名称
     */
    private  String typeName;
    /**
     * 群体关联事件数
     */
    private Integer eventCount;
    /**
     * 群体数
     */
    private Integer groupCount;
    /**
     * 群体详情
     */
    private String groupDetails;

    /**
     * 群体关联人数
     */
    private Integer personCount;
    /**
     * 群体关联人次
     */
    private Integer personNum;


    /**
     * 响应人员
     */
    private Integer respPersonCount;

    /**
     * 属地数量
     */
    private Integer  regionCount;
    /**
     * 属地数量
     */
    private String  regionDetails;
    /**
     * 事件属性详情
     */
    private String  eventPropertiesDetails;
    /**
     * 事件
     */
    List<EventAnalysisReportEventVo> eventList;

    TableRenderData renderData;
    TableRenderData renderData1;

    private ChartMultiSeriesRenderData barChart;
}
