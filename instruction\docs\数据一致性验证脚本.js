/**
 * 市级指令办理情况数据一致性验证脚本
 * 用于验证统计数据和详细列表的数量是否一致
 */

// 基础配置
const BASE_URL = 'http://localhost:8080';
const API_PREFIX = '/instruction/cityHandling';

/**
 * 发送HTTP请求
 */
async function fetchAPI(endpoint, params = {}) {
    const url = new URL(BASE_URL + API_PREFIX + endpoint);
    Object.keys(params).forEach(key => {
        if (params[key]) {
            url.searchParams.append(key, params[key]);
        }
    });

    try {
        const response = await fetch(url, {
            method: 'GET',
            headers: {
                'Authorization': 'Bearer YOUR_TOKEN', // 替换为实际的token
                'Content-Type': 'application/json'
            }
        });

        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }

        const result = await response.json();
        if (result.code !== 200) {
            throw new Error(`API error! code: ${result.code}, message: ${result.msg}`);
        }

        return result.data;
    } catch (error) {
        console.error(`请求失败 ${endpoint}:`, error);
        throw error;
    }
}

/**
 * 验证数据一致性
 */
async function validateDataConsistency(params = {}) {
    console.log('开始验证数据一致性...');
    console.log('查询参数:', params);

    try {
        // 获取统计数据
        console.log('\n1. 获取统计数据...');
        const statsData = await fetchAPI('/data', params);
        console.log('统计数据:', statsData);

        // 获取详细列表
        console.log('\n2. 获取详细列表...');
        const listData = await fetchAPI('/list', params);
        console.log('详细列表数据结构:', Object.keys(listData));

        // 验证数据一致性
        console.log('\n3. 验证数据一致性:');
        
        const validations = [
            {
                name: '超时接收',
                statKey: 'timeoutReceiveCount',
                listKey: 'timeoutReceiveList',
                statValue: statsData.timeoutReceiveCount,
                listLength: listData.timeoutReceiveList ? listData.timeoutReceiveList.length : 0
            },
            {
                name: '超时处置',
                statKey: 'timeoutDisposeCount',
                listKey: 'timeoutDisposeList',
                statValue: statsData.timeoutDisposeCount,
                listLength: listData.timeoutDisposeList ? listData.timeoutDisposeList.length : 0
            },
            {
                name: '应处置未处置单位',
                statKey: 'unprocessedUnitCount',
                listKey: 'unprocessedList',
                statValue: statsData.unprocessedUnitCount,
                listLength: listData.unprocessedList ? listData.unprocessedList.length : 0
            },
            {
                name: '特急待反馈(7天)',
                statKey: 'pendingFeedback7Days',
                listKey: 'pendingFeedback7DaysList',
                statValue: statsData.pendingFeedback7Days,
                listLength: listData.pendingFeedback7DaysList ? listData.pendingFeedback7DaysList.length : 0
            },
            {
                name: '紧急待反馈(15天)',
                statKey: 'pendingFeedback15Days',
                listKey: 'pendingFeedback15DaysList',
                statValue: statsData.pendingFeedback15Days,
                listLength: listData.pendingFeedback15DaysList ? listData.pendingFeedback15DaysList.length : 0
            },
            {
                name: '一般待反馈(30天)',
                statKey: 'pendingFeedback30Days',
                listKey: 'pendingFeedback30DaysList',
                statValue: statsData.pendingFeedback30Days,
                listLength: listData.pendingFeedback30DaysList ? listData.pendingFeedback30DaysList.length : 0
            }
        ];

        let allConsistent = true;
        validations.forEach(validation => {
            const isConsistent = validation.statValue === validation.listLength;
            const status = isConsistent ? '✅ 一致' : '❌ 不一致';
            
            console.log(`${validation.name}: ${status}`);
            console.log(`  统计值(${validation.statKey}): ${validation.statValue}`);
            console.log(`  列表长度(${validation.listKey}): ${validation.listLength}`);
            
            if (!isConsistent) {
                allConsistent = false;
                console.log(`  ⚠️  差异: ${Math.abs(validation.statValue - validation.listLength)}`);
            }
            console.log('');
        });

        // 总结
        console.log('='.repeat(50));
        if (allConsistent) {
            console.log('🎉 所有数据一致性验证通过！');
        } else {
            console.log('⚠️  发现数据不一致，请检查SQL查询逻辑');
        }
        console.log('='.repeat(50));

        return {
            consistent: allConsistent,
            statsData,
            listData,
            validations
        };

    } catch (error) {
        console.error('验证过程中发生错误:', error);
        throw error;
    }
}

/**
 * 运行多种场景的验证
 */
async function runAllValidations() {
    console.log('🚀 开始运行所有验证场景...\n');

    const scenarios = [
        {
            name: '全量数据验证',
            params: {}
        },
        {
            name: '近30天数据验证',
            params: {
                startTime: '2025-01-01',
                endTime: '2025-01-31'
            }
        },
        {
            name: '近7天数据验证',
            params: {
                startTime: '2025-01-25',
                endTime: '2025-01-31'
            }
        }
    ];

    for (const scenario of scenarios) {
        console.log(`\n📊 ${scenario.name}`);
        console.log('-'.repeat(30));
        
        try {
            await validateDataConsistency(scenario.params);
        } catch (error) {
            console.error(`${scenario.name} 验证失败:`, error.message);
        }
        
        console.log('\n');
    }
}

// 导出函数供外部使用
if (typeof module !== 'undefined' && module.exports) {
    module.exports = {
        validateDataConsistency,
        runAllValidations,
        fetchAPI
    };
}

// 如果在浏览器环境中直接运行
if (typeof window !== 'undefined') {
    window.CityInstructionValidator = {
        validateDataConsistency,
        runAllValidations,
        fetchAPI
    };
    
    console.log('数据一致性验证工具已加载到 window.CityInstructionValidator');
    console.log('使用方法:');
    console.log('1. 验证全量数据: await CityInstructionValidator.validateDataConsistency()');
    console.log('2. 验证指定时间范围: await CityInstructionValidator.validateDataConsistency({startTime: "2025-01-01", endTime: "2025-01-31"})');
    console.log('3. 运行所有验证: await CityInstructionValidator.runAllValidations()');
}
