package com.ruoyi.instruction.domain;

import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 敏感信息对象 t_sensitive_record
 *
 * <AUTHOR>
 * @date 2024-07-10
 */
@Data
public class SensitiveRecord extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键id */
    private Long id;

    /** 创建用户id */
    @Excel(name = "创建用户id")
    private Long createUserId;

    /** 创建部门id */
    @Excel(name = "创建部门id")
    private Long createDeptId;

    /** 创建部门 */
    @Excel(name = "创建部门")
    private String createDeptName;

    /** 被查看人员姓名 */
    @Excel(name = "被查看人员姓名")
    private String personName;

    /** 被查看人员属地 */
    @Excel(name = "被查看人员属地")
    private String dutyPlace;

    /** 被查看人员id */
    @Excel(name = "被查看人员id")
    private Long personId;

    /** 被查看人员责任乡镇 */
    @Excel(name = "被查看人员责任乡镇")
    private String dutyTown;

    /** 预警信息标题 */
    @Excel(name = "预警信息标题")
    private String rjTitle;

    /** 预警信息id */
    @Excel(name = "预警信息id")
    private String rjQzid;

    /** 预警信息推送单位 */
    @Excel(name = "预警信息推送单位")
    private String rjTsdw;

    /** 指令id */
    @Excel(name = "指令id")
    private Long instructionId;

    /** 指令标题 */
    @Excel(name = "指令标题")
    private String instructionTitle;

    /** 指令交办人 */
    @Excel(name = "指令交办人")
    private String instructionCreateBy;

    /** 指令交办单位 */
    @Excel(name = "指令交办单位")
    private String instructionCreateDept;

    /** 类型 1：查看人员信息  2：查看预警  3：查看指令 */
    @Excel(name = "类型 1：查看人员信息  2：查看预警  3：查看指令")
    private Integer type;

    /** 1:正常  9:删除 */
    @Excel(name = "1:正常  9:删除")
    private Integer status;

    /** 菜单名称 */
    @Excel(name = "菜单名称")
    private String menuName;

}
